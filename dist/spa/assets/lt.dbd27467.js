function e(i,a){const t=i%10===1&&i%100!==11?0:i%10>=2&&i%10<=9&&(i%100<10||i%100>=20)?1:2;return a[t].replace(/{}/g,i)}var s={isoName:"lt",nativeName:"Lithuanian",label:{clear:"I\u0161valyti",ok:"Gerai",cancel:"At\u0161aukti",close:"U\u017Edaryti",set:"Nustatyti",select:"Pasirinkti",reset:"Atkurti",remove:"Pa\u0161alinti",update:"Atnaujinti",create:"Sukurti",search:"Ie\u0161koti",filter:"Filtruoti",refresh:"Atnaujinti",expand:i=>i?`I\u0161skleisti "${i}"`:"I\u0161skleisti",collapse:i=>i?`<PERSON><PERSON><PERSON><PERSON> "${i}"`:"<PERSON><PERSON><PERSON><PERSON>"},date:{days:"Sekmadienis_Pirmadienis_Antradienis_Tre\u010Diadienis_Ketvirtadienis_Penktadienis_\u0160e\u0161tadienis".split("_"),daysShort:"S_P_A_T_K_Pn_\u0160".split("_"),months:"Sausis_Vasaris_Kovas_Balandis_Gegu\u017E\u0117_Bir\u017Eelis_Liepa_Rugpj\u016Btis_Rugs\u0117jis_Spalis_Lapkritis_Gruodis".split("_"),monthsShort:"Sau_Vas_Kov_Bal_Geg_Bir_Lie_Rgp_Rgs_Spa_Lap_Gru".split("_"),firstDayOfWeek:1,format24h:!0,pluralDay:"dienos"},table:{noData:"N\u0117ra duomen\u0173",noResults:"\u012Era\u0161\u0173 nerasta",loading:"\u012Ekeliama...",selectedRecords:i=>i>0?e(i,["Pasirinktas {} \u012Fra\u0161as","Pasirinkti {} \u012Fra\u0161ai","Pasirinkta {} \u012Fra\u0161\u0173"])+".":"Nepasirinktas joks \u012Fra\u0161as.",recordsPerPage:"Puslapyje:",allRows:"Visi",pagination:(i,a,t)=>i+"-"+a+" i\u0161 "+t,columns:"Stulpeliai"},editor:{url:"URL",bold:"Pary\u0161kintasis",italic:"Kursyvas",strikethrough:"Perbraukimas",underline:"Pabrauktasis",unorderedList:"\u017Denkleliai",orderedList:"Numeravimas",subscript:"Apatinis indeksas",superscript:"Vir\u0161utinis indeksas",hyperlink:"\u012Eterpti Hipersait\u0105",toggleFullscreen:"\u012Ejungti pilno ekrano re\u017Eim\u0105",quote:"Cituoti",left:"Lygiuoti kair\u0117je",center:"Centrin\u0117 lygiuot\u0117",right:"Lygiuoti de\u0161in\u0117je",justify:"Abipus\u0117 lygiuot\u0117",print:"Spausdinti",outdent:"Ma\u017Einti \u012Ftrauk\u0105",indent:"Didinti \u012Ftrauk\u0105",removeFormat:"Valyti formatavim\u0105",formatting:"Formatavimas",fontSize:"\u0160rifto dydis",align:"Lygiuoti",hr:"\u012Eterpti horizontali\u0105 linij\u0105",undo:"Anuliuoti veiksm\u0105",redo:"Perdaryti veiksm\u0105",heading1:"Antra\u0161t\u0117 1",heading2:"Antra\u0161t\u0117 2",heading3:"Antra\u0161t\u0117 3",heading4:"Antra\u0161t\u0117 4",heading5:"Antra\u0161t\u0117 5",heading6:"Antra\u0161t\u0117 6",paragraph:"Pastraipa",code:"Kodas",size1:"Ma\u017Eiausias",size2:"Ma\u017Eas",size3:"Normalus",size4:"Vidutinis",size5:"Didelis",size6:"Labai didelis",size7:"Did\u017Eiausias",defaultFont:"Numatytasis \u0161riftas",viewSource:"Per\u017Ei\u016Br\u0117ti kodo re\u017Eimu"},tree:{noNodes:"N\u0117ra element\u0173",noResults:"Element\u0173 nerasta"}};export{s as default};
