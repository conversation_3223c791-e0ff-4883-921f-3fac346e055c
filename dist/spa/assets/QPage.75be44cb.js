import{c as g,h}from"./QBtn.c6cd36c1.js";import{i as r,j as t,l as p,C as d,c as s,h as f,g as y}from"./index.c6ba88b2.js";var Q=g({name:"QPage",props:{padding:<PERSON><PERSON><PERSON>,styleFn:Function},setup(a,{slots:i}){const{proxy:{$q:o}}=y(),e=r(p,t);if(e===t)return console.error("QPage needs to be a deep child of QLayout"),t;if(r(d,t)===t)return console.error("QPage needs to be child of QPageContainer"),t;const c=s(()=>{const n=(e.header.space===!0?e.header.size:0)+(e.footer.space===!0?e.footer.size:0);if(typeof a.styleFn=="function"){const l=e.isContainer.value===!0?e.containerHeight.value:o.screen.height;return a.styleFn(n,l)}return{minHeight:e.isContainer.value===!0?e.containerHeight.value-n+"px":o.screen.height===0?n!==0?`calc(100vh - ${n}px)`:"100vh":o.screen.height-n+"px"}}),u=s(()=>`q-page${a.padding===!0?" q-layout-padding":""}`);return()=>f("main",{class:u.value,style:c.value},h(i.default))}});export{Q};
