import{Q as j}from"./QSeparator.3707aaf2.js";import{e as W,f as X,h as Y,u as Z,c as A,Q as z,l as ve}from"./QBtn.c6cd36c1.js";import{a as O,u as N}from"./use-dark.0de4478a.js";import{r as $,c as u,h as d,g as E,ag as L,y as R,H as x,I as ee,J as l,X as s,O as m,F as I,$ as V,M as fe,N as h,ac as k,f as C,S as ge,U as be,o as ye,W as he,K,L as P}from"./index.c6ba88b2.js";import{u as G}from"./vue-i18n.esm-bundler.5dea4c24.js";function te(e,r){const t=$(null),v=u(()=>e.disable===!0?null:d("span",{ref:t,class:"no-outline",tabindex:-1}));function f(b){const n=r.value;b!==void 0&&b.type.indexOf("key")===0?n!==null&&document.activeElement!==n&&n.contains(document.activeElement)===!0&&n.focus():t.value!==null&&(b===void 0||n!==null&&n.contains(b.target)===!0)&&t.value.focus()}return{refocusTargetEl:v,refocusTarget:f}}const oe={name:String};function ne(e={}){return(r,t,v)=>{r[t](d("input",{class:"hidden"+(v||""),...e.value}))}}var ae={xs:30,sm:35,md:40,lg:50,xl:60};const le={...N,...Z,...oe,modelValue:{required:!0,default:null},val:{},trueValue:{default:!0},falseValue:{default:!1},indeterminateValue:{default:null},checkedIcon:String,uncheckedIcon:String,indeterminateIcon:String,toggleOrder:{type:String,validator:e=>e==="tf"||e==="ft"},toggleIndeterminate:Boolean,label:String,leftLabel:Boolean,color:String,keepColor:Boolean,dense:Boolean,disable:Boolean,tabindex:[String,Number]},re=["update:modelValue"];function se(e,r){const{props:t,slots:v,emit:f,proxy:b}=E(),{$q:n}=b,o=O(t,n),g=$(null),{refocusTargetEl:c,refocusTarget:a}=te(t,g),y=W(t,ae),q=u(()=>t.val!==void 0&&Array.isArray(t.modelValue)),H=u(()=>{const i=L(t.val);return q.value===!0?t.modelValue.findIndex(T=>L(T)===i):-1}),S=u(()=>q.value===!0?H.value>-1:L(t.modelValue)===L(t.trueValue)),M=u(()=>q.value===!0?H.value===-1:L(t.modelValue)===L(t.falseValue)),_=u(()=>S.value===!1&&M.value===!1),F=u(()=>t.disable===!0?-1:t.tabindex||0),Q=u(()=>`q-${e} cursor-pointer no-outline row inline no-wrap items-center`+(t.disable===!0?" disabled":"")+(o.value===!0?` q-${e}--dark`:"")+(t.dense===!0?` q-${e}--dense`:"")+(t.leftLabel===!0?" reverse":"")),p=u(()=>{const i=S.value===!0?"truthy":M.value===!0?"falsy":"indet",T=t.color!==void 0&&(t.keepColor===!0||(e==="toggle"?S.value===!0:M.value!==!0))?` text-${t.color}`:"";return`q-${e}__inner relative-position non-selectable q-${e}__inner--${i}${T}`}),w=u(()=>{const i={type:"checkbox"};return t.name!==void 0&&Object.assign(i,{".checked":S.value,"^checked":S.value===!0?"checked":void 0,name:t.name,value:q.value===!0?t.val:t.trueValue}),i}),B=ne(w),ue=u(()=>{const i={tabindex:F.value,role:e==="toggle"?"switch":"checkbox","aria-label":t.label,"aria-checked":_.value===!0?"mixed":S.value===!0?"true":"false"};return t.disable===!0&&(i["aria-disabled"]="true"),i});function D(i){i!==void 0&&(R(i),a(i)),t.disable!==!0&&f("update:modelValue",de(),i)}function de(){if(q.value===!0){if(S.value===!0){const i=t.modelValue.slice();return i.splice(H.value,1),i}return t.modelValue.concat([t.val])}if(S.value===!0){if(t.toggleOrder!=="ft"||t.toggleIndeterminate===!1)return t.falseValue}else if(M.value===!0){if(t.toggleOrder==="ft"||t.toggleIndeterminate===!1)return t.trueValue}else return t.toggleOrder!=="ft"?t.trueValue:t.falseValue;return t.indeterminateValue}function ce(i){(i.keyCode===13||i.keyCode===32)&&R(i)}function pe(i){(i.keyCode===13||i.keyCode===32)&&D(i)}const me=r(S,_);return Object.assign(b,{toggle:D}),()=>{const i=me();t.disable!==!0&&B(i,"unshift",` q-${e}__native absolute q-ma-none q-pa-none`);const T=[d("div",{class:p.value,style:y.value,"aria-hidden":"true"},i)];c.value!==null&&T.push(c.value);const U=t.label!==void 0?X(v.default,[t.label]):Y(v.default);return U!==void 0&&T.push(d("div",{class:`q-${e}__label q-anchor--skip`},U)),d("div",{ref:g,class:Q.value,...ue.value,onClick:D,onKeydown:ce,onKeyup:pe},T)}}var ke=A({name:"QToggle",props:{...le,icon:String,iconColor:String},emits:re,setup(e){function r(t,v){const f=u(()=>(t.value===!0?e.checkedIcon:v.value===!0?e.indeterminateIcon:e.uncheckedIcon)||e.icon),b=u(()=>t.value===!0?e.iconColor:null);return()=>[d("div",{class:"q-toggle__track"}),d("div",{class:"q-toggle__thumb absolute flex flex-center no-wrap"},f.value!==void 0?[d(z,{name:f.value,color:b.value})]:void 0)]}return se("toggle",r)}});const qe=x({name:"ListComponent",props:{propListData:Array,propFirstLevel:{type:String,default:"ul"},propUlClass:{type:String,default:null},propMarginFirstLevel:{type:String,default:"2rem"},propMarginSecondLevel:{type:String,default:"2rem"},propMarginThirdLevel:{type:String,default:"2rem"},propIcon:{type:String,default:null},propIconColor:{type:String,default:"green"},propIconSize:{type:String,default:"2.5rem"}},setup(){}}),Se={key:0},Ie=["innerHTML","data-aos-delay"],Ce=["data-aos-delay"],Le={class:"row no-wrap items-start"},Te=["innerHTML"],Me=["innerHTML","data-aos-delay"],_e=["innerHTML","data-aos-delay"],Ve={key:1},$e=["innerHTML","data-aos-delay"],ze=["data-aos-delay"],He={class:"row no-wrap items-start"},we=["innerHTML"],Be=["innerHTML","data-aos-delay"],Re=["innerHTML","data-aos-delay"];function Ae(e,r,t,v,f,b){return e.propFirstLevel=="ul"?(l(),s("section",Se,[m("ul",{class:fe(e.propUlClass)},[(l(!0),s(I,null,V(e.propListData,(n,o)=>(l(),s(I,null,[typeof n=="string"&&!e.propIcon?(l(),s("li",{innerHTML:n,key:o,style:k(o>0?"margin-top:"+e.propMarginFirstLevel:null),"data-aos":"zoom-out","data-aos-delay":o*150},null,12,Ie)):h("",!0),typeof n=="string"&&e.propIcon?(l(),s("li",{key:o,style:k([{"list-style-type":"none"},o>0?"margin-top:"+e.propMarginFirstLevel:null]),"data-aos":"zoom-out","data-aos-delay":o*150},[m("div",Le,[C(z,{class:"q-mr-sm",color:e.propIconColor,size:e.propIconSize,name:e.propIcon},null,8,["color","size","name"]),m("span",{class:"q-mt-sm",innerHTML:n},null,8,Te)])],12,Ce)):h("",!0),typeof n=="object"?(l(),s("ul",{key:o,style:k(o>0?"margin-top:"+e.propMarginSecondLevel:null)},[(l(!0),s(I,null,V(n,(g,c)=>(l(),s(I,null,[typeof g=="string"?(l(),s("li",{innerHTML:g,key:c,style:k("margin-top:"+e.propMarginSecondLevel),"data-aos":"zoom-out","data-aos-delay":c*150},null,12,Me)):h("",!0),typeof g=="object"?(l(),s("ul",{key:c,style:k("margin-top:"+e.propMarginThirdLevel)},[(l(!0),s(I,null,V(g,(a,y)=>(l(),s("li",{innerHTML:a,key:y,style:k("margin-top:"+e.propMarginThirdLevel),"data-aos":"zoom-out","data-aos-delay":y*150},null,12,_e))),128))],4)):h("",!0)],64))),256))],4)):h("",!0)],64))),256))],2)])):e.propFirstLevel=="p"?(l(),s("section",Ve,[(l(!0),s(I,null,V(e.propListData,(n,o)=>(l(),s(I,null,[typeof n=="string"&&!e.propIcon?(l(),s("p",{innerHTML:n,key:o,style:k(o>0?"margin-top:"+e.propMarginFirstLevel:null),"data-aos":"zoom-out","data-aos-delay":o*150},null,12,$e)):h("",!0),typeof n=="string"&&e.propIcon?(l(),s("div",{key:o,style:k([{"list-style-type":"none"},o>0?"margin-top:"+e.propMarginFirstLevel:null]),"data-aos":"zoom-out","data-aos-delay":o*150},[m("div",He,[C(z,{class:"q-mr-sm",color:e.propIconColor,size:e.propIconSize,name:e.propIcon},null,8,["color","size","name"]),m("span",{innerHTML:n},null,8,we)])],12,ze)):h("",!0),typeof n=="object"?(l(),s("ul",{key:o,style:k(o>0?"margin-top:"+e.propMarginSecondLevel:null)},[(l(!0),s(I,null,V(n,(g,c)=>(l(),s(I,null,[typeof g=="string"?(l(),s("li",{innerHTML:g,key:c,style:k("margin-top:"+e.propMarginSecondLevel),"data-aos":"zoom-out","data-aos-delay":c*150},null,12,Be)):h("",!0),typeof g=="object"?(l(),s("ul",{key:c,style:k("margin-top:"+e.propMarginThirdLevel)},[(l(!0),s(I,null,V(g,(a,y)=>(l(),s("li",{innerHTML:a,key:y,style:k("margin-top:"+e.propMarginThirdLevel),"data-aos":"zoom-out","data-aos-delay":y*150},null,12,Re))),128))],4)):h("",!0)],64))),256))],4)):h("",!0)],64))),256))])):h("",!0)}var Fe=ee(qe,[["render",Ae]]);const Qe=d("svg",{key:"svg",class:"q-radio__bg absolute non-selectable",viewBox:"0 0 24 24"},[d("path",{d:"M12,22a10,10 0 0 1 -10,-10a10,10 0 0 1 10,-10a10,10 0 0 1 10,10a10,10 0 0 1 -10,10m0,-22a12,12 0 0 0 -12,12a12,12 0 0 0 12,12a12,12 0 0 0 12,-12a12,12 0 0 0 -12,-12"}),d("path",{class:"q-radio__check",d:"M12,6a6,6 0 0 0 -6,6a6,6 0 0 0 6,6a6,6 0 0 0 6,-6a6,6 0 0 0 -6,-6"})]);var De=A({name:"QRadio",props:{...N,...Z,...oe,modelValue:{required:!0},val:{required:!0},label:String,leftLabel:Boolean,checkedIcon:String,uncheckedIcon:String,color:String,keepColor:Boolean,dense:Boolean,disable:Boolean,tabindex:[String,Number]},emits:["update:modelValue"],setup(e,{slots:r,emit:t}){const{proxy:v}=E(),f=O(e,v.$q),b=W(e,ae),n=$(null),{refocusTargetEl:o,refocusTarget:g}=te(e,n),c=u(()=>L(e.modelValue)===L(e.val)),a=u(()=>"q-radio cursor-pointer no-outline row inline no-wrap items-center"+(e.disable===!0?" disabled":"")+(f.value===!0?" q-radio--dark":"")+(e.dense===!0?" q-radio--dense":"")+(e.leftLabel===!0?" reverse":"")),y=u(()=>{const p=e.color!==void 0&&(e.keepColor===!0||c.value===!0)?` text-${e.color}`:"";return`q-radio__inner relative-position q-radio__inner--${c.value===!0?"truthy":"falsy"}${p}`}),q=u(()=>(c.value===!0?e.checkedIcon:e.uncheckedIcon)||null),H=u(()=>e.disable===!0?-1:e.tabindex||0),S=u(()=>{const p={type:"radio"};return e.name!==void 0&&Object.assign(p,{".checked":c.value===!0,"^checked":c.value===!0?"checked":void 0,name:e.name,value:e.val}),p}),M=ne(S);function _(p){p!==void 0&&(R(p),g(p)),e.disable!==!0&&c.value!==!0&&t("update:modelValue",e.val,p)}function F(p){(p.keyCode===13||p.keyCode===32)&&R(p)}function Q(p){(p.keyCode===13||p.keyCode===32)&&_(p)}return Object.assign(v,{set:_}),()=>{const p=q.value!==null?[d("div",{key:"icon",class:"q-radio__icon-container absolute-full flex flex-center no-wrap"},[d(z,{class:"q-radio__icon",name:q.value})])]:[Qe];e.disable!==!0&&M(p,"unshift"," q-radio__native q-ma-none q-pa-none");const w=[d("div",{class:y.value,style:b.value,"aria-hidden":"true"},p)];o.value!==null&&w.push(o.value);const B=e.label!==void 0?X(r.default,[e.label]):Y(r.default);return B!==void 0&&w.push(d("div",{class:"q-radio__label q-anchor--skip"},B)),d("div",{ref:n,class:a.value,tabindex:H.value,role:"radio","aria-label":e.label,"aria-checked":c.value===!0?"true":"false","aria-disabled":e.disable===!0?"true":void 0,onClick:_,onKeydown:F,onKeyup:Q},w)}}});const je=d("div",{key:"svg",class:"q-checkbox__bg absolute"},[d("svg",{class:"q-checkbox__svg fit absolute-full",viewBox:"0 0 24 24"},[d("path",{class:"q-checkbox__truthy",fill:"none",d:"M1.73,12.91 8.1,19.28 22.79,4.59"}),d("path",{class:"q-checkbox__indet",d:"M4,14H20V10H4"})])]);var Oe=A({name:"QCheckbox",props:le,emits:re,setup(e){function r(t,v){const f=u(()=>(t.value===!0?e.checkedIcon:v.value===!0?e.indeterminateIcon:e.uncheckedIcon)||null);return()=>f.value!==null?[d("div",{key:"icon",class:"q-checkbox__icon-container absolute-full flex flex-center no-wrap"},[d(z,{class:"q-checkbox__icon",name:f.value})])]:[je]}return se("checkbox",r)}});const ie={radio:De,checkbox:Oe,toggle:ke},Ne=Object.keys(ie);var J=A({name:"QOptionGroup",props:{...N,modelValue:{required:!0},options:{type:Array,validator:e=>e.every(r=>"value"in r&&"label"in r)},name:String,type:{default:"radio",validator:e=>Ne.includes(e)},color:String,keepColor:Boolean,dense:Boolean,size:String,leftLabel:Boolean,inline:Boolean,disable:Boolean},emits:["update:modelValue"],setup(e,{emit:r,slots:t}){const{proxy:{$q:v}}=E(),f=Array.isArray(e.modelValue);e.type==="radio"?f===!0&&console.error("q-option-group: model should not be array"):f===!1&&console.error("q-option-group: model should be array in your case");const b=O(e,v),n=u(()=>ie[e.type]),o=u(()=>"q-option-group q-gutter-x-sm"+(e.inline===!0?" q-option-group--inline":"")),g=u(()=>{const a={role:"group"};return e.type==="radio"&&(a.role="radiogroup",e.disable===!0&&(a["aria-disabled"]="true")),a});function c(a){r("update:modelValue",a)}return()=>d("div",{class:o.value,...g.value},e.options.map((a,y)=>{const q=t["label-"+y]!==void 0?()=>t["label-"+y](a):t.label!==void 0?()=>t.label(a):void 0;return d("div",[d(n.value,{modelValue:e.modelValue,val:a.value,name:a.name===void 0?e.name:a.name,disable:e.disable||a.disable,label:q===void 0?a.label:null,leftLabel:a.leftLabel===void 0?e.leftLabel:a.leftLabel,color:a.color===void 0?e.color:a.color,checkedIcon:a.checkedIcon,uncheckedIcon:a.uncheckedIcon,dark:a.dark||b.value,size:a.size===void 0?e.size:a.size,dense:e.dense,keepColor:a.keepColor===void 0?e.keepColor:a.keepColor,"onUpdate:modelValue":c},q)])}))}});const Ee=x({name:"QuestionCard",props:{questionId:{type:String,default:null},questionIdAttempt:{type:String,default:""},questionsArray:{type:Array},submitButtonText:{type:String},defaultColor:{type:String,default:"secondary"},alternativeTitle:{type:String,default:null},feedbackType:{type:String,default:"universal"},reSubmit:{type:Boolean,default:!1},headerInsideQuestionCard:{type:Boolean,default:!0}},components:{ListComponent:Fe},setup(e){const r=ge(),t=be(),{locale:v}=G({useScope:"global"}),{t:f,tm:b}=G(),n=$(),o=$(),g=u(()=>r.getInteractionSubmitted(e.questionIdAttempt+e.questionId).submitted),c=u(()=>r.getInteractionSubmitted(e.questionIdAttempt+e.questionId).result),a=()=>{r.setInteraction(e.questionIdAttempt+e.questionId,o.value,n.value.correctResponse,!0),t.scrollDown(300)};return ye(()=>{n.value=t.findQuestionById(e.questionId,e.questionsArray),e.alternativeTitle&&(n.value.title=e.alternativeTitle);const y=r.getInteractionSubmitted(e.questionIdAttempt+e.questionId).learner_response;y?typeof n.value.correctResponse=="object"&&!Array.isArray(y)?o.value=y.split(","):o.value=y:typeof n.value.correctResponse=="object"?o.value=[]:o.value=null}),{locale:v,t:f,tm:b,showCorrectAnswer:$(!0),question:n,submitQuestion:a,learnerResponse:o,questionSubmitted:g,questionResult:c}}}),Ue=["id"],Ke={key:0,class:"row"},Pe={class:"col-12","data-aos":"zoom-out"},Ge=["innerHTML"],Je={key:1,class:"row q-col-gutter-xl q-mb-xl items-center justify-center","data-aos":"zoom-out"},We={class:"col-12 col-lg-6"},Xe={class:"rounded-borders bg-primary q-px-xl q-pt-xxl q-pb-xl relative-position"},Ye=m("img",{"aria-hidden":"true",src:"assets/global/questioncard_icon.svg",style:{width:"96px",position:"absolute",top:"-47px",left:"50%",transform:"translatex(-50%)"},"data-aos":"flip-right"},null,-1),Ze=["innerHTML"],xe={class:"col-12 col-lg-6"},et=["innerHTML"],tt={class:"row q-col-gutter-xl"},ot={class:"col-lg-6","data-aos":"fade-right"},nt={class:"text-h5 Citi-Sans-Display-Regular text-secondary q-mb-lg"},at=["innerHTML"],lt=["innerHTML"],rt={class:"col-lg-6","data-aos":"fade-left"},st=["innerHTML"],it=["innerHTML"],ut={key:2},dt={key:0},ct={class:"row items-center"},pt=["innerHTML"];function mt(e,r,t,v,f,b){const n=he("ListComponent");return e.question?(l(),s("div",{key:0,ref:e.questionIdAttempt+e.questionId,id:e.questionIdAttempt+e.questionId},[e.headerInsideQuestionCard?(l(),s("div",Je,[m("div",We,[m("div",Xe,[Ye,m("h4",{class:"Citi-Sans-Display-Bold text-white text-center",innerHTML:e.t("assessment.knowledge_check.title1")},null,8,Ze)])]),m("div",xe,[m("h5",{class:"Citi-Sans-Display-Bold",innerHTML:e.question.title},null,8,et)])])):(l(),s("div",Ke,[m("div",Pe,[m("h3",{class:"text-h4 Citi-Sans-Display-Regular",innerHTML:e.question.title},null,8,Ge),C(j,{class:"q-mt-md q-mb-lg","aria-hidden":"true",size:"2px",color:e.defaultColor,"data-aos":"zoom-in"},null,8,["color"])])])),m("div",tt,[m("div",ot,[m("p",nt,[C(n,{propFirstLevel:"p",propListData:e.question.text,propMarginFirstLevel:"1rem",propMarginSecondLevel:"0.5rem"},null,8,["propListData"])]),m("p",{class:"q-mt-lg Citi-Sans-Text-Bold",innerHTML:e.question.instructions},null,8,at),e.showCorrectAnswer?(l(),s("p",{key:0,class:"text-negative",innerHTML:"Just for testing: "+e.question.correctResponse},null,8,lt)):h("",!0)]),m("div",rt,[typeof e.question.correctResponse=="object"?(l(),K(J,{key:0,id:"options_"+(e.questionIdAttempt+e.questionId),disable:e.questionSubmitted&&!e.reSubmit||e.questionSubmitted&&e.questionResult=="correct",modelValue:e.learnerResponse,"onUpdate:modelValue":r[0]||(r[0]=o=>e.learnerResponse=o),type:"checkbox",options:e.question.options},{label:P(o=>[m("span",{innerHTML:o.label},null,8,st)]),_:1},8,["id","disable","modelValue","options"])):(l(),K(J,{key:1,id:"options_"+(e.questionIdAttempt+e.questionId),disable:e.questionSubmitted&&!e.reSubmit||e.questionSubmitted&&e.questionResult=="correct",modelValue:e.learnerResponse,"onUpdate:modelValue":r[1]||(r[1]=o=>e.learnerResponse=o),type:"radio",options:e.question.options},{label:P(o=>[m("span",{innerHTML:o.label},null,8,it)]),_:1},8,["id","disable","modelValue","options"])),C(ve,{disable:e.questionSubmitted&&!e.reSubmit||e.questionSubmitted&&e.questionResult=="correct"||!e.learnerResponse||e.learnerResponse.length<=0,onClick:r[2]||(r[2]=o=>e.submitQuestion()),padding:"sm xl","no-caps":"",rounded:"",outline:"",color:e.defaultColor,class:"q-mt-md q-mb-xl btn-fixed-width",label:e.submitButtonText},null,8,["disable","color","label"]),e.feedbackType!=="none"&&e.questionSubmitted?(l(),s("div",ut,[e.feedbackType!=="correct_only"||e.feedbackType=="correct_only"&&e.questionResult=="correct"?(l(),s("section",dt,[m("div",ct,[C(z,{class:"q-mr-md",size:"55px",name:e.questionResult=="correct"?"bi-check-circle":"bi-x-circle"},null,8,["name"]),m("p",{class:"text-h5",innerHTML:e.questionResult=="correct"?e.question.correctFeedbackTitle:e.question.incorrectFeedbackTitle},null,8,pt)]),C(j,{class:"q-my-lg","aria-hidden":"true",size:"2px",color:e.defaultColor,"data-aos":"zoom-in"},null,8,["color"]),C(n,{propFirstLevel:"p",propListData:e.feedbackType=="universal"?e.question.universalFeedbackText:e.questionResult=="correct"?e.question.correctFeedbackText:e.question.incorrectFeedbackText,propMarginFirstLevel:"1rem",propMarginSecondLevel:"0.5rem"},null,8,["propListData"]),C(j,{class:"q-my-lg","aria-hidden":"true",size:"2px",color:e.defaultColor,"data-aos":"zoom-in"},null,8,["color"])])):h("",!0)])):h("",!0)])])],8,Ue)):h("",!0)}var ht=ee(Ee,[["render",mt]]);export{Fe as L,ht as Q,ke as a};
