var i={isoName:"es",nativeName:"Espa\xF1ol",label:{clear:"Borrar",ok:"OK",cancel:"Cancelar",close:"Cerra<PERSON>",set:"Establecer",select:"Seleccionar",reset:"Restablecer",remove:"Eliminar",update:"Actualizar",create:"Crear",search:"Buscar",filter:"Filtrar",refresh:"Actualizar",expand:e=>e?`Expandir "${e}"`:"Expandir",collapse:e=>e?`Ocultar "${e}"`:"Colapsar"},date:{days:"Domingo_Lunes_Martes_Mi\xE9rcoles_Jueves_Viernes_S\xE1bado".split("_"),daysShort:"Dom_Lun_Mar_Mi\xE9_Jue_Vie_S\xE1b".split("_"),months:"Enero_Febrero_Marzo_Abril_Mayo_Junio_Julio_Agosto_Septiembre_Octubre_Noviembre_Diciembre".split("_"),monthsShort:"Ene_Feb_Mar_Abr_May_Jun_Jul_Ago_Sep_Oct_Nov_Dic".split("_"),firstDayOfWeek:1,format24h:!0,pluralDay:"d\xEDas"},table:{noData:"Sin datos disponibles",noResults:"No se han encontrado resultados",loading:"Cargando...",selectedRecords:e=>e>1?e+" filas seleccionadas.":(e===0?"Sin":"1")+" fila seleccionada.",recordsPerPage:"Filas por p\xE1gina:",allRows:"Todas",pagination:(e,a,r)=>e+"-"+a+" de "+r,columns:"Columnas"},editor:{url:"URL",bold:"Negrita",italic:"Cursiva",strikethrough:"Tachada",underline:"Subrayada",unorderedList:"Lista Desordenada",orderedList:"Lista Ordenada",subscript:"Sub\xEDndice",superscript:"Super\xEDndice",hyperlink:"Hiperv\xEDnculo",toggleFullscreen:"Alternar pantalla completa",quote:"Cita",left:"Alineaci\xF3n izquierda",center:"Alineaci\xF3n centro",right:"Alineaci\xF3n derecha",justify:"Justificar alineaci\xF3n",print:"Imprimir",outdent:"Disminuir indentaci\xF3n",indent:"Aumentar indentaci\xF3n",removeFormat:"Eliminar formato",formatting:"Formato",fontSize:"Tama\xF1o de Fuente",align:"Alinear",hr:"Insertar l\xEDnea horizontal",undo:"Deshacer",redo:"Rehacer",heading1:"Encabezado 1",heading2:"Encabezado 2",heading3:"Encabezado 3",heading4:"Encabezado 4",heading5:"Encabezado 5",heading6:"Encabezado 6",paragraph:"P\xE1rrafo",code:"C\xF3digo",size1:"Muy peque\xF1o",size2:"Peque\xF1o",size3:"Normal",size4:"Mediano",size5:"Grande",size6:"Muy grande",size7:"M\xE1ximo",defaultFont:"Fuente por defecto",viewSource:"Ver fuente"},tree:{noNodes:"No hay nodos disponibles",noResults:"No se encontraron los nodos correspondientes"}};export{i as default};
