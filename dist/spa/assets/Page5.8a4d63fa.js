import{Q as Z}from"./QSeparator.3707aaf2.js";import{Q as se}from"./QImg.f55ff82e.js";import{R as ct,Q as ce,f as ut,c as ue,h as de,b as dt,w as vt,d as ft,l as gt}from"./QBtn.c6cd36c1.js";import{Q as mt}from"./QPage.75be44cb.js";import{u as pt}from"./use-quasar.de58b715.js";import{i as bt,j as he,aj as Fe,r as k,c as q,k as Ne,o as Me,A as _t,h as x,y as oe,a3 as ht,ak as yt,g as ve,al as qt,w as ae,B as Tt,ab as wt,am as Lt,m as te,n as Ct,p as Mt,q as re,s as ye,v as Be,x as qe,z as kt,Z as xt,an as Pt,H as We,S as Oe,I as Ke,W as we,J as y,X as M,O as t,f as C,L as ne,F as X,$ as Y,K as Le,ac as ee,N as O,U as De,V as je,M as Ee}from"./index.c6ba88b2.js";import{u as St}from"./uid.42677368.js";import{Q as Ht,g as Ve,s as Qe,c as $t}from"./selection.3c67842b.js";import{a as Te,u as ze}from"./use-tick.9c8d097a.js";import{u as At,a as It}from"./use-dark.0de4478a.js";import{Q as Rt}from"./QCard.8d7aab57.js";import{V as Xe,g as Bt}from"./graphic_globe.e2a7f483.js";import{c as Dt}from"./modalcheckciti.cc5533a7.js";import{c as Ye,g as jt}from"./graphic_callout1.59e8f0e2.js";import{u as Ce}from"./vue-i18n.esm-bundler.5dea4c24.js";var Et="assets/tab1_ar.613a141f.svg",Vt="assets/tab1_en-US.3e8af90f.svg",Qt="assets/tab1_es.f589ac4c.svg",zt="assets/tab1_fr.6cb43eae.svg",Ut="assets/tab1_id.2b6fde0d.svg",Ft="assets/tab1_ru.fd53ffa9.svg",Nt="assets/tab2_ar.553ada10.svg",Wt="assets/tab2_en-US.0826fd69.svg",Ot="assets/tab2_es.f3629e2c.svg",Kt="assets/tab2_fr.aaa69b05.svg",Xt="assets/tab2_id.10bf1b45.svg",Yt="assets/tab2_ru.5d2db67d.svg",Gt="assets/tab3_ar.1895126e.svg",Jt="assets/tab3_en-US.9ebf56e5.svg",Zt="assets/tab3_es.4fe71a10.svg",es="assets/tab3_fr.780a66b5.svg",ts="assets/tab3_id.332589a5.svg",ss="assets/tab3_ru.209030b8.svg",as="assets/tab4_ar.58ad4a8f.svg",ns="assets/tab4_en-US.c02dd53e.svg",os="assets/tab4_es.c5c98d50.svg",ls="assets/tab4_fr.2ef66fd6.svg",is="assets/tab4_id.9e965794.svg",rs="assets/tab4_ru.22e3c659.svg";let cs=0;const us=["click","keydown"],ds={icon:String,label:[Number,String],alert:[Boolean,String],alertIcon:String,name:{type:[Number,String],default:()=>`t_${cs++}`},noCaps:Boolean,tabindex:[String,Number],disable:Boolean,contentClass:String,ripple:{type:[Boolean,Object],default:!0}};function vs(e,n,g,r){const o=bt(Fe,he);if(o===he)return console.error("QTab/QRouteTab component needs to be child of QTabs"),he;const{proxy:a}=ve(),l=k(null),v=k(null),u=k(null),A=q(()=>e.disable===!0||e.ripple===!1?!1:Object.assign({keyCodes:[13,32],early:!0},e.ripple===!0?{}:e.ripple)),p=q(()=>o.currentModel.value===e.name),R=q(()=>"q-tab relative-position self-stretch flex flex-center text-center"+(p.value===!0?" q-tab--active"+(o.tabProps.value.activeClass?" "+o.tabProps.value.activeClass:"")+(o.tabProps.value.activeColor?` text-${o.tabProps.value.activeColor}`:"")+(o.tabProps.value.activeBgColor?` bg-${o.tabProps.value.activeBgColor}`:""):" q-tab--inactive")+(e.icon&&e.label&&o.tabProps.value.inlineLabel===!1?" q-tab--full":"")+(e.noCaps===!0||o.tabProps.value.noCaps===!0?" q-tab--no-caps":"")+(e.disable===!0?" disabled":" q-focusable q-hoverable cursor-pointer")+(r!==void 0?r.linkClass.value:"")),h=q(()=>"q-tab__content self-stretch flex-center relative-position q-anchor--skip non-selectable "+(o.tabProps.value.inlineLabel===!0?"row no-wrap q-tab__content--inline":"column")+(e.contentClass!==void 0?` ${e.contentClass}`:"")),T=q(()=>e.disable===!0||o.hasFocus.value===!0||p.value===!1&&o.hasActiveTab.value===!0?-1:e.tabindex||0);function S(c,H){if(H!==!0&&l.value!==null&&l.value.focus(),e.disable===!0){r!==void 0&&r.hasRouterLink.value===!0&&oe(c);return}if(r===void 0){o.updateModel({name:e.name}),g("click",c);return}if(r.hasRouterLink.value===!0){const B=(P={})=>{let $;const E=P.to===void 0||qt(P.to,e.to)===!0?o.avoidRouteWatcher=St():null;return r.navigateToRouterLink(c,{...P,returnRouterError:!0}).catch(Q=>{$=Q}).then(Q=>{if(E===o.avoidRouteWatcher&&(o.avoidRouteWatcher=!1,$===void 0&&(Q===void 0||Q.message.startsWith("Avoided redundant navigation")===!0)&&o.updateModel({name:e.name})),P.returnRouterError===!0)return $!==void 0?Promise.reject($):Q})};g("click",c,B),c.defaultPrevented!==!0&&B();return}g("click",c)}function j(c){ht(c,[13,32])?S(c,!0):yt(c)!==!0&&c.keyCode>=35&&c.keyCode<=40&&c.altKey!==!0&&c.metaKey!==!0&&o.onKbdNavigate(c.keyCode,a.$el)===!0&&oe(c),g("keydown",c)}function V(){const c=o.tabProps.value.narrowIndicator,H=[],B=x("div",{ref:u,class:["q-tab__indicator",o.tabProps.value.indicatorClass]});e.icon!==void 0&&H.push(x(ce,{class:"q-tab__icon",name:e.icon})),e.label!==void 0&&H.push(x("div",{class:"q-tab__label"},e.label)),e.alert!==!1&&H.push(e.alertIcon!==void 0?x(ce,{class:"q-tab__alert-icon",color:e.alert!==!0?e.alert:void 0,name:e.alertIcon}):x("div",{class:"q-tab__alert"+(e.alert!==!0?` text-${e.alert}`:"")})),c===!0&&H.push(B);const P=[x("div",{class:"q-focus-helper",tabindex:-1,ref:l}),x("div",{class:h.value},ut(n.default,H))];return c===!1&&P.push(B),P}const z={name:q(()=>e.name),rootRef:v,tabIndicatorRef:u,routeData:r};Ne(()=>{o.unregisterTab(z)}),Me(()=>{o.registerTab(z)});function K(c,H){const B={ref:v,class:R.value,tabindex:T.value,role:"tab","aria-selected":p.value===!0?"true":"false","aria-disabled":e.disable===!0?"true":void 0,onClick:S,onKeydown:j,...H};return _t(x(c,B,V()),[[ct,A.value]])}return{renderTab:K,$tabs:o}}var fs=ue({name:"QTab",props:ds,emits:us,setup(e,{slots:n,emit:g}){const{renderTab:r}=vs(e,n,g);return()=>r("div")}});let Ge=!1;{const e=document.createElement("div");e.setAttribute("dir","rtl"),Object.assign(e.style,{width:"1px",height:"1px",overflow:"auto"});const n=document.createElement("div");Object.assign(n.style,{width:"1000px",height:"1px"}),document.body.appendChild(e),e.appendChild(n),e.scrollLeft=-1e3,Ge=e.scrollLeft>=0,e.remove()}function gs(e,n,g){const r=g===!0?["left","right"]:["top","bottom"];return`absolute-${n===!0?r[0]:r[1]}${e?` text-${e}`:""}`}const ms=["left","center","right","justify"];var ps=ue({name:"QTabs",props:{modelValue:[Number,String],align:{type:String,default:"center",validator:e=>ms.includes(e)},breakpoint:{type:[String,Number],default:600},vertical:Boolean,shrink:Boolean,stretch:Boolean,activeClass:String,activeColor:String,activeBgColor:String,indicatorColor:String,leftIcon:String,rightIcon:String,outsideArrows:Boolean,mobileArrows:Boolean,switchIndicator:Boolean,narrowIndicator:Boolean,inlineLabel:Boolean,noCaps:Boolean,dense:Boolean,contentClass:String,"onUpdate:modelValue":[Function,Array]},setup(e,{slots:n,emit:g}){const{proxy:r}=ve(),{$q:o}=r,{registerTick:a}=Te(),{registerTick:l}=Te(),{registerTick:v}=Te(),{registerTimeout:u,removeTimeout:A}=ze(),{registerTimeout:p,removeTimeout:R}=ze(),h=k(null),T=k(null),S=k(e.modelValue),j=k(!1),V=k(!0),z=k(!1),K=k(!1),c=[],H=k(0),B=k(!1);let P=null,$=null,E;const Q=q(()=>({activeClass:e.activeClass,activeColor:e.activeColor,activeBgColor:e.activeBgColor,indicatorClass:gs(e.indicatorColor,e.switchIndicator,e.vertical),narrowIndicator:e.narrowIndicator,inlineLabel:e.inlineLabel,noCaps:e.noCaps})),fe=q(()=>{const s=H.value,i=S.value;for(let d=0;d<s;d++)if(c[d].name.value===i)return!0;return!1}),ge=q(()=>`q-tabs__content--align-${j.value===!0?"left":K.value===!0?"justify":e.align}`),me=q(()=>`q-tabs row no-wrap items-center q-tabs--${j.value===!0?"":"not-"}scrollable q-tabs--${e.vertical===!0?"vertical":"horizontal"} q-tabs__arrows--${e.outsideArrows===!0?"outside":"inside"} q-tabs--mobile-with${e.mobileArrows===!0?"":"out"}-arrows`+(e.dense===!0?" q-tabs--dense":"")+(e.shrink===!0?" col-shrink":"")+(e.stretch===!0?" self-stretch":"")),f=q(()=>"q-tabs__content scroll--mobile row no-wrap items-center self-stretch hide-scrollbar relative-position "+ge.value+(e.contentClass!==void 0?` ${e.contentClass}`:"")),_=q(()=>e.vertical===!0?{container:"height",content:"offsetHeight",scroll:"scrollHeight"}:{container:"width",content:"offsetWidth",scroll:"scrollWidth"}),D=q(()=>e.vertical!==!0&&o.lang.rtl===!0),F=q(()=>Ge===!1&&D.value===!0);ae(D,J),ae(()=>e.modelValue,s=>{pe({name:s,setCurrent:!0,skipEmit:!0})}),ae(()=>e.outsideArrows,le);function pe({name:s,setCurrent:i,skipEmit:d}){S.value!==s&&(d!==!0&&e["onUpdate:modelValue"]!==void 0&&g("update:modelValue",s),(i===!0||e["onUpdate:modelValue"]===void 0)&&(Je(S.value,s),S.value=s))}function le(){a(()=>{ke({width:h.value.offsetWidth,height:h.value.offsetHeight})})}function ke(s){if(_.value===void 0||T.value===null)return;const i=s[_.value.container],d=Math.min(T.value[_.value.scroll],Array.prototype.reduce.call(T.value.children,(L,b)=>L+(b[_.value.content]||0),0)),w=i>0&&d>i;j.value=w,w===!0&&l(J),K.value=i<parseInt(e.breakpoint,10)}function Je(s,i){const d=s!=null&&s!==""?c.find(L=>L.name.value===s):null,w=i!=null&&i!==""?c.find(L=>L.name.value===i):null;if(d&&w){const L=d.tabIndicatorRef.value,b=w.tabIndicatorRef.value;P!==null&&(clearTimeout(P),P=null),L.style.transition="none",L.style.transform="none",b.style.transition="none",b.style.transform="none";const m=L.getBoundingClientRect(),I=b.getBoundingClientRect();b.style.transform=e.vertical===!0?`translate3d(0,${m.top-I.top}px,0) scale3d(1,${I.height?m.height/I.height:1},1)`:`translate3d(${m.left-I.left}px,0,0) scale3d(${I.width?m.width/I.width:1},1,1)`,v(()=>{P=setTimeout(()=>{P=null,b.style.transition="transform .25s cubic-bezier(.4, 0, .2, 1)",b.style.transform="none"},70)})}w&&j.value===!0&&G(w.rootRef.value)}function G(s){const{left:i,width:d,top:w,height:L}=T.value.getBoundingClientRect(),b=s.getBoundingClientRect();let m=e.vertical===!0?b.top-w:b.left-i;if(m<0){T.value[e.vertical===!0?"scrollTop":"scrollLeft"]+=Math.floor(m),J();return}m+=e.vertical===!0?b.height-L:b.width-d,m>0&&(T.value[e.vertical===!0?"scrollTop":"scrollLeft"]+=Math.ceil(m),J())}function J(){const s=T.value;if(s===null)return;const i=s.getBoundingClientRect(),d=e.vertical===!0?s.scrollTop:Math.abs(s.scrollLeft);D.value===!0?(V.value=Math.ceil(d+i.width)<s.scrollWidth-1,z.value=d>0):(V.value=d>0,z.value=e.vertical===!0?Math.ceil(d+i.height)<s.scrollHeight:Math.ceil(d+i.width)<s.scrollWidth)}function xe(s){$!==null&&clearInterval($),$=setInterval(()=>{tt(s)===!0&&N()},5)}function Pe(){xe(F.value===!0?Number.MAX_SAFE_INTEGER:0)}function Se(){xe(F.value===!0?0:Number.MAX_SAFE_INTEGER)}function N(){$!==null&&(clearInterval($),$=null)}function Ze(s,i){const d=Array.prototype.filter.call(T.value.children,I=>I===i||I.matches&&I.matches(".q-tab.q-focusable")===!0),w=d.length;if(w===0)return;if(s===36)return G(d[0]),d[0].focus(),!0;if(s===35)return G(d[w-1]),d[w-1].focus(),!0;const L=s===(e.vertical===!0?38:37),b=s===(e.vertical===!0?40:39),m=L===!0?-1:b===!0?1:void 0;if(m!==void 0){const I=D.value===!0?-1:1,U=d.indexOf(i)+m*I;return U>=0&&U<w&&(G(d[U]),d[U].focus({preventScroll:!0})),!0}}const et=q(()=>F.value===!0?{get:s=>Math.abs(s.scrollLeft),set:(s,i)=>{s.scrollLeft=-i}}:e.vertical===!0?{get:s=>s.scrollTop,set:(s,i)=>{s.scrollTop=i}}:{get:s=>s.scrollLeft,set:(s,i)=>{s.scrollLeft=i}});function tt(s){const i=T.value,{get:d,set:w}=et.value;let L=!1,b=d(i);const m=s<b?-1:1;return b+=m*5,b<0?(L=!0,b=0):(m===-1&&b<=s||m===1&&b>=s)&&(L=!0,b=s),w(i,b),J(),L}function He(s,i){for(const d in s)if(s[d]!==i[d])return!1;return!0}function st(){let s=null,i={matchedLen:0,queryDiff:9999,hrefLen:0};const d=c.filter(m=>m.routeData!==void 0&&m.routeData.hasRouterLink.value===!0),{hash:w,query:L}=r.$route,b=Object.keys(L).length;for(const m of d){const I=m.routeData.exact.value===!0;if(m.routeData[I===!0?"linkIsExactActive":"linkIsActive"].value!==!0)continue;const{hash:U,query:be,matched:it,href:rt}=m.routeData.resolvedLink.value,_e=Object.keys(be).length;if(I===!0){if(U!==w||_e!==b||He(L,be)===!1)continue;s=m.name.value;break}if(U!==""&&U!==w||_e!==0&&He(be,L)===!1)continue;const W={matchedLen:it.length,queryDiff:b-_e,hrefLen:rt.length-U.length};if(W.matchedLen>i.matchedLen){s=m.name.value,i=W;continue}else if(W.matchedLen!==i.matchedLen)continue;if(W.queryDiff<i.queryDiff)s=m.name.value,i=W;else if(W.queryDiff!==i.queryDiff)continue;W.hrefLen>i.hrefLen&&(s=m.name.value,i=W)}s===null&&c.some(m=>m.routeData===void 0&&m.name.value===S.value)===!0||pe({name:s,setCurrent:!0})}function at(s){if(A(),B.value!==!0&&h.value!==null&&s.target&&typeof s.target.closest=="function"){const i=s.target.closest(".q-tab");i&&h.value.contains(i)===!0&&(B.value=!0,j.value===!0&&G(i))}}function nt(){u(()=>{B.value=!1},30)}function ie(){Ae.avoidRouteWatcher===!1?p(st):R()}function $e(){if(E===void 0){const s=ae(()=>r.$route.fullPath,ie);E=()=>{s(),E=void 0}}}function ot(s){c.push(s),H.value++,le(),s.routeData===void 0||r.$route===void 0?p(()=>{if(j.value===!0){const i=S.value,d=i!=null&&i!==""?c.find(w=>w.name.value===i):null;d&&G(d.rootRef.value)}}):($e(),s.routeData.hasRouterLink.value===!0&&ie())}function lt(s){c.splice(c.indexOf(s),1),H.value--,le(),E!==void 0&&s.routeData!==void 0&&(c.every(i=>i.routeData===void 0)===!0&&E(),ie())}const Ae={currentModel:S,tabProps:Q,hasFocus:B,hasActiveTab:fe,registerTab:ot,unregisterTab:lt,verifyRouteModel:ie,updateModel:pe,onKbdNavigate:Ze,avoidRouteWatcher:!1};Tt(Fe,Ae);function Ie(){P!==null&&clearTimeout(P),N(),E!==void 0&&E()}let Re;return Ne(Ie),wt(()=>{Re=E!==void 0,Ie()}),Lt(()=>{Re===!0&&$e(),le()}),()=>x("div",{ref:h,class:me.value,role:"tablist",onFocusin:at,onFocusout:nt},[x(Ht,{onResize:ke}),x("div",{ref:T,class:f.value,onScroll:J},de(n.default)),x(ce,{class:"q-tabs__arrow q-tabs__arrow--left absolute q-tab__icon"+(V.value===!0?"":" q-tabs__arrow--faded"),name:e.leftIcon||o.iconSet.tabs[e.vertical===!0?"up":"left"],onMousedownPassive:Pe,onTouchstartPassive:Pe,onMouseupPassive:N,onMouseleavePassive:N,onTouchendPassive:N}),x(ce,{class:"q-tabs__arrow q-tabs__arrow--right absolute q-tab__icon"+(z.value===!0?"":" q-tabs__arrow--faded"),name:e.rightIcon||o.iconSet.tabs[e.vertical===!0?"down":"right"],onMousedownPassive:Se,onTouchstartPassive:Se,onMouseupPassive:N,onMouseleavePassive:N,onTouchendPassive:N})])}});function bs(e){const n=[.06,6,50];return typeof e=="string"&&e.length&&e.split(":").forEach((g,r)=>{const o=parseFloat(g);o&&(n[r]=o)}),n}var _s=dt({name:"touch-swipe",beforeMount(e,{value:n,arg:g,modifiers:r}){if(r.mouse!==!0&&te.has.touch!==!0)return;const o=r.mouseCapture===!0?"Capture":"",a={handler:n,sensitivity:bs(g),direction:Ve(r),noop:Ct,mouseStart(l){Qe(l,a)&&Mt(l)&&(re(a,"temp",[[document,"mousemove","move",`notPassive${o}`],[document,"mouseup","end","notPassiveCapture"]]),a.start(l,!0))},touchStart(l){if(Qe(l,a)){const v=l.target;re(a,"temp",[[v,"touchmove","move","notPassiveCapture"],[v,"touchcancel","end","notPassiveCapture"],[v,"touchend","end","notPassiveCapture"]]),a.start(l)}},start(l,v){te.is.firefox===!0&&ye(e,!0);const u=Be(l);a.event={x:u.left,y:u.top,time:Date.now(),mouse:v===!0,dir:!1}},move(l){if(a.event===void 0)return;if(a.event.dir!==!1){oe(l);return}const v=Date.now()-a.event.time;if(v===0)return;const u=Be(l),A=u.left-a.event.x,p=Math.abs(A),R=u.top-a.event.y,h=Math.abs(R);if(a.event.mouse!==!0){if(p<a.sensitivity[1]&&h<a.sensitivity[1]){a.end(l);return}}else if(window.getSelection().toString()!==""){a.end(l);return}else if(p<a.sensitivity[2]&&h<a.sensitivity[2])return;const T=p/v,S=h/v;a.direction.vertical===!0&&p<h&&p<100&&S>a.sensitivity[0]&&(a.event.dir=R<0?"up":"down"),a.direction.horizontal===!0&&p>h&&h<100&&T>a.sensitivity[0]&&(a.event.dir=A<0?"left":"right"),a.direction.up===!0&&p<h&&R<0&&p<100&&S>a.sensitivity[0]&&(a.event.dir="up"),a.direction.down===!0&&p<h&&R>0&&p<100&&S>a.sensitivity[0]&&(a.event.dir="down"),a.direction.left===!0&&p>h&&A<0&&h<100&&T>a.sensitivity[0]&&(a.event.dir="left"),a.direction.right===!0&&p>h&&A>0&&h<100&&T>a.sensitivity[0]&&(a.event.dir="right"),a.event.dir!==!1?(oe(l),a.event.mouse===!0&&(document.body.classList.add("no-pointer-events--children"),document.body.classList.add("non-selectable"),$t(),a.styleCleanup=j=>{a.styleCleanup=void 0,document.body.classList.remove("non-selectable");const V=()=>{document.body.classList.remove("no-pointer-events--children")};j===!0?setTimeout(V,50):V()}),a.handler({evt:l,touch:a.event.mouse!==!0,mouse:a.event.mouse,direction:a.event.dir,duration:v,distance:{x:p,y:h}})):a.end(l)},end(l){a.event!==void 0&&(qe(a,"temp"),te.is.firefox===!0&&ye(e,!1),a.styleCleanup!==void 0&&a.styleCleanup(!0),l!==void 0&&a.event.dir!==!1&&oe(l),a.event=void 0)}};if(e.__qtouchswipe=a,r.mouse===!0){const l=r.mouseCapture===!0||r.mousecapture===!0?"Capture":"";re(a,"main",[[e,"mousedown","mouseStart",`passive${l}`]])}te.has.touch===!0&&re(a,"main",[[e,"touchstart","touchStart",`passive${r.capture===!0?"Capture":""}`],[e,"touchmove","noop","notPassiveCapture"]])},updated(e,n){const g=e.__qtouchswipe;g!==void 0&&(n.oldValue!==n.value&&(typeof n.value!="function"&&g.end(),g.handler=n.value),g.direction=Ve(n.modifiers))},beforeUnmount(e){const n=e.__qtouchswipe;n!==void 0&&(qe(n,"main"),qe(n,"temp"),te.is.firefox===!0&&ye(e,!1),n.styleCleanup!==void 0&&n.styleCleanup(),delete e.__qtouchswipe)}});function hs(){const e=new Map;return{getCache:function(n,g){return e[n]===void 0?e[n]=g:e[n]},getCacheWithFn:function(n,g){return e[n]===void 0?e[n]=g():e[n]}}}const ys={name:{required:!0},disable:Boolean},Ue={setup(e,{slots:n}){return()=>x("div",{class:"q-panel scroll",role:"tabpanel"},de(n.default))}},qs={modelValue:{required:!0},animated:Boolean,infinite:Boolean,swipeable:Boolean,vertical:Boolean,transitionPrev:String,transitionNext:String,transitionDuration:{type:[String,Number],default:300},keepAlive:Boolean,keepAliveInclude:[String,Array,RegExp],keepAliveExclude:[String,Array,RegExp],keepAliveMax:Number},Ts=["update:modelValue","beforeTransition","transition"];function ws(){const{props:e,emit:n,proxy:g}=ve(),{getCacheWithFn:r}=hs();let o,a;const l=k(null),v=k(null);function u(f){const _=e.vertical===!0?"up":"left";$((g.$q.lang.rtl===!0?-1:1)*(f.direction===_?1:-1))}const A=q(()=>[[_s,u,void 0,{horizontal:e.vertical!==!0,vertical:e.vertical,mouse:!0}]]),p=q(()=>e.transitionPrev||`slide-${e.vertical===!0?"down":"right"}`),R=q(()=>e.transitionNext||`slide-${e.vertical===!0?"up":"left"}`),h=q(()=>`--q-transition-duration: ${e.transitionDuration}ms`),T=q(()=>typeof e.modelValue=="string"||typeof e.modelValue=="number"?e.modelValue:String(e.modelValue)),S=q(()=>({include:e.keepAliveInclude,exclude:e.keepAliveExclude,max:e.keepAliveMax})),j=q(()=>e.keepAliveInclude!==void 0||e.keepAliveExclude!==void 0);ae(()=>e.modelValue,(f,_)=>{const D=c(f)===!0?H(f):-1;a!==!0&&P(D===-1?0:D<H(_)?-1:1),l.value!==D&&(l.value=D,n("beforeTransition",f,_),kt(()=>{n("transition",f,_)}))});function V(){$(1)}function z(){$(-1)}function K(f){n("update:modelValue",f)}function c(f){return f!=null&&f!==""}function H(f){return o.findIndex(_=>_.props.name===f&&_.props.disable!==""&&_.props.disable!==!0)}function B(){return o.filter(f=>f.props.disable!==""&&f.props.disable!==!0)}function P(f){const _=f!==0&&e.animated===!0&&l.value!==-1?"q-transition--"+(f===-1?p.value:R.value):null;v.value!==_&&(v.value=_)}function $(f,_=l.value){let D=_+f;for(;D>-1&&D<o.length;){const F=o[D];if(F!==void 0&&F.props.disable!==""&&F.props.disable!==!0){P(f),a=!0,n("update:modelValue",F.props.name),setTimeout(()=>{a=!1});return}D+=f}e.infinite===!0&&o.length!==0&&_!==-1&&_!==o.length&&$(f,f===-1?o.length:-1)}function E(){const f=H(e.modelValue);return l.value!==f&&(l.value=f),!0}function Q(){const f=c(e.modelValue)===!0&&E()&&o[l.value];return e.keepAlive===!0?[x(Pt,S.value,[x(j.value===!0?r(T.value,()=>({...Ue,name:T.value})):Ue,{key:T.value,style:h.value},()=>f)])]:[x("div",{class:"q-panel scroll",style:h.value,key:T.value,role:"tabpanel"},[f])]}function fe(){if(o.length!==0)return e.animated===!0?[x(xt,{name:v.value},Q)]:Q()}function ge(f){return o=vt(de(f.default,[])).filter(_=>_.props!==null&&_.props.slot===void 0&&c(_.props.name)===!0),o.length}function me(){return o}return Object.assign(g,{next:V,previous:z,goTo:K}),{panelIndex:l,panelDirectives:A,updatePanelsList:ge,updatePanelIndex:E,getPanelContent:fe,getEnabledPanels:B,getPanels:me,isValidPanelName:c,keepAliveProps:S,needsUniqueKeepAliveWrapper:j,goToPanelByOffset:$,goToPanel:K,nextPanel:V,previousPanel:z}}var Ls=ue({name:"QTabPanel",props:ys,setup(e,{slots:n}){return()=>x("div",{class:"q-tab-panel",role:"tabpanel"},de(n.default))}}),Cs=ue({name:"QTabPanels",props:{...qs,...At},emits:Ts,setup(e,{slots:n}){const g=ve(),r=It(e,g.proxy.$q),{updatePanelsList:o,getPanelContent:a,panelDirectives:l}=ws(),v=q(()=>"q-tab-panels q-panel-parent"+(r.value===!0?" q-tab-panels--dark q-dark":""));return()=>(o(n),ft("div",{class:v.value},a(),"pan",e.swipeable,()=>l.value))}});const Ms=We({name:"AccordionComponent",components:{Vue3Lottie:Xe},data(){return{check:Dt,check1:Ye,tab:k(0)}},props:{interactionId:{type:String,default:null},contentArray:{type:Array}},setup(e){const{locale:n}=Ce({useScope:"global"}),g=Oe(),r={card_0:!0};function o(l){return`${new URL({"../assets/page5/tab1_ar.svg":Et,"../assets/page5/tab1_en-US.svg":Vt,"../assets/page5/tab1_es.svg":Qt,"../assets/page5/tab1_fr.svg":zt,"../assets/page5/tab1_id.svg":Ut,"../assets/page5/tab1_ru.svg":Ft,"../assets/page5/tab2_ar.svg":Nt,"../assets/page5/tab2_en-US.svg":Wt,"../assets/page5/tab2_es.svg":Ot,"../assets/page5/tab2_fr.svg":Kt,"../assets/page5/tab2_id.svg":Xt,"../assets/page5/tab2_ru.svg":Yt,"../assets/page5/tab3_ar.svg":Gt,"../assets/page5/tab3_en-US.svg":Jt,"../assets/page5/tab3_es.svg":Zt,"../assets/page5/tab3_fr.svg":es,"../assets/page5/tab3_id.svg":ts,"../assets/page5/tab3_ru.svg":ss,"../assets/page5/tab4_ar.svg":as,"../assets/page5/tab4_en-US.svg":ns,"../assets/page5/tab4_es.svg":os,"../assets/page5/tab4_fr.svg":ls,"../assets/page5/tab4_id.svg":is,"../assets/page5/tab4_ru.svg":rs}[`../assets/page5/tab${parseInt(l+1)}_${n.value}.svg`],self.location)}`}const a=l=>{if(console.log(l),r["card_"+l]=!0,console.log(r),Object.keys(r).length==e.contentArray.length){if(g.getInteractionSubmitted(e.interactionId).submitted)return!0;g.setInteraction(e.interactionId,null,null,!0)}};return Me(()=>{}),{visited:r,checkComplete:a,getImageUrl:o,locale:n}}}),ks={class:"q-pa-md"},xs={class:"q-gutter-y-md"},Ps={key:1,style:{width:"44.56px",height:"44.56px"}},Ss=["innerHTML"],Hs=["innerHTML"],$s=["innerHTML"];function As(e,n,g,r,o,a){const l=we("Vue3Lottie");return y(),M("div",ks,[t("div",xs,[C(Rt,null,{default:ne(()=>[C(ps,{"no-caps":"",dense:"",align:"justify",modelValue:e.tab,"onUpdate:modelValue":[n[0]||(n[0]=v=>e.tab=v),e.checkComplete],class:"text-grey","active-color":"primary","indicator-color":"secondary"},{default:ne(()=>[(y(!0),M(X,null,Y(e.contentArray,(v,u)=>(y(),M("div",{key:u,class:"q-px-md q-px-sm-none",style:{width:"325px"}},[e.visited["card_"+u]?(y(),Le(l,{key:0,class:"lottie",style:ee([e.locale=="ar"&&!e.$q.screen.xs?"margin-left:60px":e.$q.screen.xs?"margin-left:10px":"margin-left:8px;",{cursor:"default"}]),loop:!1,scale:1,width:"44.56px",height:"44.56px",animationData:e.check},null,8,["style","animationData"])):O("",!0),e.visited["card_"+u]?O("",!0):(y(),M("div",Ps)),C(fs,{class:"q-mr-md q-mr-lg-xl",name:u,label:e.$q.screen.xs?u+1:v.title},null,8,["name","label"])]))),128))]),_:1},8,["modelValue","onUpdate:modelValue"]),C(Z,{"aria-hidden":"true"}),C(Cs,{modelValue:e.tab,"onUpdate:modelValue":n[1]||(n[1]=v=>e.tab=v),animated:""},{default:ne(()=>[(y(!0),M(X,null,Y(e.contentArray,(v,u)=>(y(),Le(Ls,{class:"q-py-xl",name:u,key:u},{default:ne(()=>[t("h3",{class:"",innerHTML:v.title_full},null,8,Ss),t("ul",null,[(y(!0),M(X,null,Y(v.text,(A,p)=>(y(),M("li",{key:p,innerHTML:A,class:"q-pb-sm last-in-list"},null,8,Hs))),128))]),C(se,{loading:"eager","no-transition":"",src:e.getImageUrl(u),width:"100%","spinner-color":"white"},null,8,["src"]),t("p",{class:"sr-only",innerHTML:v.alt},null,8,$s)]),_:2},1032,["name"]))),128))]),_:1},8,["modelValue"])]),_:1})])])}var Is=Ke(Ms,[["render",As],["__scopeId","data-v-601579d6"]]),Rs="assets/4.d581854d.png",Bs="assets/5.5831a877.png",Ds="assets/1.7e7e15c0.png",js="assets/6.5cc3f9ff.png";const Es=We({name:"PageFive",components:{Vue3Lottie:Xe,TabsC:Is},setup(){const e=Oe(),n={...De()},g={...je()},{locale:r}=Ce({useScope:"global"}),{t:o,tm:a}=Ce();pt();const l=k(!1),v=()=>{n.checkNetwork(()=>u())},u=()=>{n.completePageAndRoute("page5","/page6")};return Me(()=>{console.log("***------ PageFive - onMounted ------***"),e.rte_load("page5"),n.setFocusById("a11yIntro",400),g.refreshAOS()}),{icon:k(!1),bar:k(!1),bar2:k(!1),toolbar:k(!1),store:e,check1:Ye,graphic1:jt,globe:Bt,...De(),...je(),locale:r,t:o,tm:a,animations:k(!1),openTranscript:l,continueClicked:v}}}),Vs={class:"sr-only q-py-md"},Qs=["innerHTML"],zs=["innerHTML"],Us={class:"container","data-aos":"zoom-out"},Fs={class:"row justify-start"},Ns={class:"col-lg-8 q-pt-xl offset-md-1"},Ws=["innerHTML"],Os={class:"row justify-center q-mt-xl"},Ks={class:"row justify-center"},Xs={class:"row justify-center"},Ys={class:"col-md-10 q-py-lg q-mb-xl q-mb-sm-none"},Gs=["innerHTML"],Js={class:"row justify-center q-mt-lg q-mt-lg-xl"},Zs={class:"col-md-6 q-pr-md-md"},ea=["innerHTML"],ta={class:"col-10 col-md-4 q-pt-lg q-pt-md-none"},sa={class:"row justify-center q-pt-lg q-pt-md-xxl"},aa={class:"col-10 col-md-4 q-mb-md q-mb-md-lg q-pb-md order-last order-md-first"},na={class:"col-md-6 q-pl-md-xl q-mb-lg q-pb-sm-md order-first order-md-last"},oa=["innerHTML"],la=["innerHTML"],ia=["innerHTML"],ra={class:"row justify-center"},ca={class:"col-md-5 q-pr-md-md q-pb-lg q-pb-md-none"},ua={class:"q-pa-lg-md"},da=["innerHTML"],va=["innerHTML"],fa={class:"col-md-5 q-pl-md-md"},ga={class:"q-pa-lg-md"},ma=["innerHTML"],pa=["innerHTML"],ba={class:"col-md-10 q-pt-lg"},_a={style:{"background-color":"#f7fafb","border-radius":"8px"},class:"q-pa-lg shadow-1"},ha={class:"q-pa-lg-md"},ya=["innerHTML"],qa=["innerHTML"],Ta={class:"q-py-lg q-mt-md q-mb-lg col-md-10"},wa=["innerHTML"],La={key:1,class:"q-mt-sm"},Ca={class:"list-unstyled"},Ma=["innerHTML"],ka={class:"row justify-center items-center q-py-xl q-my-md"},xa={class:"col-md-5 q-pr-lg-lg","data-aos":"fade-right"},Pa=["innerHTML"],Sa={class:"offset-md-1 col-md-4","data-aos":"fade-left"},Ha={class:"col-10 col-md-4 offset-md-1 q-pb-lg q-pb-md-none","data-aos":"fade-rigbt"},$a={class:"col-md-5 q-pl-lg-xl","data-aos":"fade-left"},Aa=["innerHTML"],Ia=["innerHTML"],Ra=["innerHTML"],Ba={class:"col-md-10"},Da={class:"col-md-6 q-pa-xl shadow-1",style:{background:"#f0f5f7","border-radius":"8px",opacity:"0.98"}},ja=["innerHTML"],Ea={class:"container"},Va={class:"row justify-center items-center q-mb-md-xl",style:{position:"relative"}},Qa={class:"col-12 col-md-10 col-lg-8",style:{position:"relative"},"data-aos":"fade-left"},za={class:"shadow-1 q-py-lg q-px-lg q-px-md-xl q-my-lg",style:{background:"#255be3","border-radius":"7.5px"}},Ua=["innerHTML"],Fa={class:"container"},Na={class:"row items-center q-py-lg"},Wa={class:"offset-md-1 col-md-5 q-pr-lg-lg","data-aos":"fade-right"},Oa=["innerHTML"],Ka={class:"col-md-4","data-aos":"fade-left"},Xa={class:"row justify-start"},Ya={class:"offset-md-1 col-md-10 q-pb-xl","data-aos":"fade"},Ga=["innerHTML"],Ja={class:"offset-lg-1 col-12 col-lg-11"},Za={key:0,style:{"background-color":"#f7fafb"},class:"q-pt-md q-mt-xl","data-aos":"fade"},en={class:"container q-pt-xl q-pb-xs"},tn={class:"row justify-center"},sn={style:{background:"linear-gradient( to bottom, #255be3 100px, #ffffff 0% )","border-radius":"8px"},class:"col-md-10 shadow-1 q-px-lg q-px-md-none","data-aos":"fade"},an={class:"row justify-center"},nn={class:"col-md-10 q-py-lg q-mb-lg q-mb-sm-xl q-mb-sm-none"},on=["innerHTML"],ln={class:"col-md-6 q-pr-md-lg q-pb-lg q-mb-md"},rn=["innerHTML"],cn=["innerHTML"],un=["innerHTML"],dn={class:"col-10 col-md-4 q-pb-lg q-mb-md"},vn={class:"row items-center q-py-xl q-my-md"},fn={class:"offset-md-1 col-md-4","data-aos":"fade-right"},gn={class:"col-md-5 q-pl-lg-lg","data-aos":"fade-left"},mn={class:"q-py-lg q-mt-md q-mb-lg col-md-10"},pn=["innerHTML"],bn={key:1,class:"q-mt-sm"},_n={class:"list-unstyled"},hn=["innerHTML"],yn={key:1,class:"q-py-none q-pb-lg-xl bg-footer q-pt-xl"},qn={class:"q-pt-lg q-pb-xl container row justify-center"},Tn={class:"col-12 col-lg-10 text-center q-pb-xl q-mb-xl q-mb-xl-lg"},wn=["innerHTML"];function Ln(e,n,g,r,o,a){const l=we("Vue3Lottie"),v=we("tabs-c");return y(),Le(mt,{class:"overflow-hidden"},{default:ne(()=>[t("div",Vs,[t("p",{class:"q-mb-md",innerHTML:e.t("ui.pageLoaded"),id:"a11yIntro",style:{outline:"0"},tabindex:"0"},null,8,Qs),t("p",{innerHTML:(e.store.getPagesCompleted?e.store.getPagesCompleted:0)+e.$t("ui.of")+e.store.manifest.content.length+e.t("ui.pagesCompleted")},null,8,zs)]),t("div",Us,[t("div",Fs,[t("div",Ns,[t("h1",{innerHTML:e.tm("page5.sectionTop.title1"),class:"q-mb-lg q-pt-lg q-mt-md"},null,8,Ws)])]),t("div",Os,[t("div",Ks,[t("div",{style:ee([{"border-radius":"8px"},e.$q.screen.gt.sm?"background: linear-gradient( to bottom, #255be3 130px, #ffffff 0% );":"background: linear-gradient( to bottom, #255be3 200px, #ffffff 0% );"]),class:"col-lg-10 shadow-1 q-px-lg q-px-md-none"},[t("div",Xs,[t("div",Ys,[t("h2",{innerHTML:e.tm("page5.section2.title1"),class:"text-white"},null,8,Gs)]),C(Z,{"aria-hidden":"true",size:e.$q.screen.gt.xs?"150px":"0px"},null,8,["size"]),t("div",Js,[t("div",Zs,[t("p",{innerHTML:e.t("page5.section2.text2")},null,8,ea)]),t("div",ta,[C(se,{loading:"eager","no-transition":"",src:Rs,"spinner-color":"white"})])])]),t("div",sa,[t("div",aa,[C(se,{loading:"eager","no-transition":"",src:Bs,"spinner-color":"white"})]),t("div",na,[t("h3",{class:"q-mb-md",innerHTML:e.tm("page5.section2.text5")},null,8,oa),t("p",{class:"q-mb-md",innerHTML:e.tm("page5.section2.text6")},null,8,la),t("p",{class:"q-mb-md",innerHTML:e.t("page5.section2.text7")},null,8,ia)])]),t("div",ra,[t("div",ca,[t("div",{style:ee([e.$q.screen.gt.sm?e.locale=="ru"?"height:450px":"height:325px":null,{"background-color":"#f7fafb","border-radius":"8px"}]),class:"q-pa-lg shadow-1"},[t("div",ua,[t("p",{class:"q-mb-xs text-secondary",innerHTML:e.t("page5.section2.text8")},null,8,da),t("p",{class:"",innerHTML:e.t("page5.section2.list2[0]")},null,8,va)])],4)]),t("div",fa,[t("div",{style:ee([e.$q.screen.gt.sm?e.locale=="ru"?"height:450px":"height:325px":null,{"background-color":"#f7fafb","border-radius":"8px"}]),class:"q-pa-lg shadow-1"},[t("div",ga,[t("p",{class:"q-mb-xs text-secondary",innerHTML:e.t("page5.section2.text9")},null,8,ma),t("p",{class:"",innerHTML:e.t("page5.section2.list3[0]")},null,8,pa)])],4)]),t("div",ba,[t("div",_a,[t("div",ha,[t("p",{class:"q-mb-xs text-secondary",innerHTML:e.t("page5.section2.text10")},null,8,ya),t("p",{class:"",innerHTML:e.t("page5.section2.list4[0]")},null,8,qa)])])]),t("div",Ta,[(y(!0),M(X,null,Y(e.tm("page5.section2.paragraphs_11"),(u,A)=>(y(),M("div",{key:A,class:"q-mb-md"},[typeof u=="string"?(y(),M("p",{key:0,innerHTML:u,class:"last-in-list"},null,8,wa)):O("",!0),typeof u=="object"&&Array.isArray(u)?(y(),M("div",La,[t("ul",Ca,[(y(!0),M(X,null,Y(u,(p,R)=>(y(),M("li",{key:R,innerHTML:p,class:"q-mb-sm last-in-list"},null,8,Ma))),128))])])):O("",!0)]))),128))])])],4)]),t("div",ka,[t("div",xa,[t("h3",{innerHTML:e.t("page5.section2.text12"),class:"q-mb-lg q-mb-md-md"},null,8,Pa)]),t("div",Sa,[C(l,{"auto-play":e.store.aosEnable,"pause-animation":!e.store.aosEnable,class:"lottie",loop:!0,scale:1,animationData:e.graphic1},null,8,["auto-play","pause-animation","animationData"])])])]),t("div",{class:Ee([e.$q.screen.lt.md?"justify-center":"justify-start","row items-start q-pt-xl"])},[t("div",Ha,[C(se,{loading:"eager","no-transition":"",src:Ds,width:"100%","spinner-color":"white"})]),t("div",$a,[t("h2",{class:"q-mb-md",innerHTML:e.tm("page5.section1.title1")},null,8,Aa),t("p",{class:"q-mb-md",innerHTML:e.t("page5.section1.text1")},null,8,Ia),t("p",{class:"q-mb-md",innerHTML:e.t("page5.section1.text2")},null,8,Ra)])],2),t("div",{class:"row justify-start items-center bg-tax4 q-mt-lg q-mt-sm-xxl q-mb-lg q-pb-sm-xl",style:ee(e.$q.screen.gt.xs?"height: 460px;":""),"data-aos":"fade"},[t("div",Ba,[t("div",{class:Ee(["row",e.locale=="ar"?"justify-center":"justify-end"])},[t("div",Da,[t("p",{class:"",innerHTML:e.t("page5.section1.text3")},null,8,ja)])],2)])],4)]),C(Z,{"aria-hidden":"true",size:e.$q.screen.gt.sm?"100px":"50px",color:"transparent"},null,8,["size"]),t("div",Ea,[C(Z,{"aria-hidden":"true",size:e.$q.screen.gt.sm?"75px":"25px",color:"transparent"},null,8,["size"]),t("div",Va,[t("div",{class:"col-md-4",style:ee(e.$q.screen.gt.xs&&e.locale!="ar"?"position: absolute; left: 2%;":e.$q.screen.gt.xs&&e.locale=="ar"?"position: absolute; right: 2%;":"position: absolute; top:-50px;"),"data-aos":"fade-right"},[C(l,{"auto-play":e.store.aosEnable,"pause-animation":!e.store.aosEnable,class:"lottie",loop:!0,scale:1,animationData:e.globe},null,8,["auto-play","pause-animation","animationData"])],4),t("div",Qa,[t("div",za,[t("p",{class:"text-white Citi-Sans-Regular",innerHTML:e.t("page5.section1.callout1")},null,8,Ua)])])]),C(Z,{"aria-hidden":"true",size:e.$q.screen.gt.sm?"75px":"175px",color:"transparent"},null,8,["size"])]),t("div",Fa,[t("div",Na,[t("div",Wa,[t("p",{class:"q-mb-md",innerHTML:e.t("page5.section1.text7")},null,8,Oa)]),t("div",Ka,[C(l,{"auto-play":e.store.aosEnable,"pause-animation":!e.store.aosEnable,class:"lottie",loop:!0,scale:1,animationData:e.graphic1},null,8,["auto-play","pause-animation","animationData"])])]),t("div",Xa,[t("div",Ya,[t("p",{class:"text-bold",innerHTML:e.t("page5.section1.cta_tabs")},null,8,Ga)]),t("div",Ja,[C(v,{interactionId:"p5_tabs_1",contentArray:e.tm("page5.section1.tabs1")},null,8,["contentArray"])])])]),e.store.getInteractionSubmitted("p5_tabs_1").submitted?(y(),M("div",Za,[t("div",en,[t("div",tn,[t("div",sn,[t("div",an,[t("div",nn,[t("h2",{innerHTML:e.t("page5.section3.title"),class:"text-white"},null,8,on)]),C(Z,{"aria-hidden":"true",size:e.$q.screen.gt.xs?"150px":"0px"},null,8,["size"]),t("div",ln,[t("h3",{innerHTML:e.t("page5.section3.subtitle"),class:"q-pb-lg Citi-Sans-Display-Regular"},null,8,rn),t("p",{innerHTML:e.t("page5.section3.paragraphs[0]"),class:"q-pb-md Citi-Sans-Display-Regular"},null,8,cn),t("p",{innerHTML:e.t("page5.section3.paragraphs[1]"),class:"Citi-Sans-Display-Regular"},null,8,un)]),t("div",dn,[C(se,{loading:"eager","no-transition":"",src:js,"spinner-color":"white"})])])])]),t("div",vn,[t("div",fn,[C(l,{"auto-play":e.store.aosEnable,"pause-animation":!e.store.aosEnable,class:"lottie",loop:!0,scale:1,animationData:e.graphic1},null,8,["auto-play","pause-animation","animationData"])]),t("div",gn,[t("div",mn,[(y(!0),M(X,null,Y(e.tm("page5.section4.paragraphs"),(u,A)=>(y(),M("div",{key:A,class:"q-mb-md Citi-Sans-Display-Regular"},[typeof u=="string"?(y(),M("p",{key:0,innerHTML:u,class:"last-in-list"},null,8,pn)):O("",!0),typeof u=="object"&&Array.isArray(u)?(y(),M("div",bn,[t("ul",_n,[(y(!0),M(X,null,Y(u,(p,R)=>(y(),M("li",{key:R,innerHTML:p,class:"q-mb-sm last-in-list"},null,8,hn))),128))])])):O("",!0)]))),128))])])])])])):O("",!0),e.store.getInteractionSubmitted("p5_tabs_1").submitted?(y(),M("div",yn,[t("div",qn,[t("div",Tn,[t("h3",{class:"q-mb-lg",innerHTML:e.tm("page5.footer.text1")},null,8,wn),C(gt,{onClick:n[0]||(n[0]=u=>e.continueClicked()),label:e.t("ui.continue"),style:{"border-radius":"7.5px"},"no-caps":"",outline:"",padding:"sm xl",class:"btn-fixed-width bg-white",color:"secondary"},null,8,["label"])])])])):O("",!0)]),_:1})}var Vn=Ke(Es,[["render",Ln],["__scopeId","data-v-520b054a"]]);export{Vn as default};
