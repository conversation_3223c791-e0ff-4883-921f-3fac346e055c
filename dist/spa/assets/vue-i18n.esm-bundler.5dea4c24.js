import{h as Pt,e as Bt,i as Jt,o as Qt,a as qt,r as Le,c as Ne,w as Xe,F as Zt,g as Dt,d as zt,f as en,T as tn}from"./index.c6ba88b2.js";/*!
  * shared v9.2.2
  * (c) 2022 ka<PERSON><PERSON>
  * Released under the MIT License.
  */const Ge=typeof window!="undefined",nn=typeof Symbol=="function"&&typeof Symbol.toStringTag=="symbol",ae=e=>nn?Symbol(e):e,rn=(e,t,n)=>an({l:e,k:t,s:n}),an=e=>JSON.stringify(e).replace(/\u2028/g,"\\u2028").replace(/\u2029/g,"\\u2029").replace(/\u0027/g,"\\u0027"),x=e=>typeof e=="number"&&isFinite(e),sn=e=>ze(e)==="[object Date]",ye=e=>ze(e)==="[object RegExp]",ke=e=>R(e)&&Object.keys(e).length===0;function ln(e,t){typeof console!="undefined"&&(console.warn("[intlify] "+e),t&&console.warn(t.stack))}const X=Object.assign;function lt(e){return e.replace(/</g,"&lt;").replace(/>/g,"&gt;").replace(/"/g,"&quot;").replace(/'/g,"&apos;")}const cn=Object.prototype.hasOwnProperty;function Ze(e,t){return cn.call(e,t)}const W=Array.isArray,$=e=>typeof e=="function",C=e=>typeof e=="string",U=e=>typeof e=="boolean",M=e=>e!==null&&typeof e=="object",Rt=Object.prototype.toString,ze=e=>Rt.call(e),R=e=>ze(e)==="[object Object]",on=e=>e==null?"":W(e)||R(e)&&e.toString===Rt?JSON.stringify(e,null,2):String(e);/*!
  * message-compiler v9.2.2
  * (c) 2022 kazuya kawaguchi
  * Released under the MIT License.
  */const F={EXPECTED_TOKEN:1,INVALID_TOKEN_IN_PLACEHOLDER:2,UNTERMINATED_SINGLE_QUOTE_IN_PLACEHOLDER:3,UNKNOWN_ESCAPE_SEQUENCE:4,INVALID_UNICODE_ESCAPE_SEQUENCE:5,UNBALANCED_CLOSING_BRACE:6,UNTERMINATED_CLOSING_BRACE:7,EMPTY_PLACEHOLDER:8,NOT_ALLOW_NEST_PLACEHOLDER:9,INVALID_LINKED_FORMAT:10,MUST_HAVE_MESSAGES_IN_PLURAL:11,UNEXPECTED_EMPTY_LINKED_MODIFIER:12,UNEXPECTED_EMPTY_LINKED_KEY:13,UNEXPECTED_LEXICAL_ANALYSIS:14,__EXTEND_POINT__:15};function Pe(e,t,n={}){const{domain:a,messages:l,args:s}=n,o=e,m=new SyntaxError(String(o));return m.code=e,t&&(m.location=t),m.domain=a,m}function un(e){throw e}function fn(e,t,n){return{line:e,column:t,offset:n}}function je(e,t,n){const a={start:e,end:t};return n!=null&&(a.source=n),a}const ne=" ",mn="\r",j=`
`,_n=String.fromCharCode(8232),dn=String.fromCharCode(8233);function En(e){const t=e;let n=0,a=1,l=1,s=0;const o=k=>t[k]===mn&&t[k+1]===j,m=k=>t[k]===j,i=k=>t[k]===dn,E=k=>t[k]===_n,O=k=>o(k)||m(k)||i(k)||E(k),b=()=>n,N=()=>a,y=()=>l,P=()=>s,A=k=>o(k)||i(k)||E(k)?j:t[k],S=()=>A(n),u=()=>A(n+s);function L(){return s=0,O(n)&&(a++,l=0),o(n)&&n++,n++,l++,t[n]}function T(){return o(n+s)&&s++,s++,t[n+s]}function g(){n=0,a=1,l=1,s=0}function d(k=0){s=k}function I(){const k=n+s;for(;k!==n;)L();s=0}return{index:b,line:N,column:y,peekOffset:P,charAt:A,currentChar:S,currentPeek:u,next:L,peek:T,reset:g,resetPeek:d,skipToPeek:I}}const re=void 0,ct="'",gn="tokenizer";function Ln(e,t={}){const n=t.location!==!1,a=En(e),l=()=>a.index(),s=()=>fn(a.line(),a.column(),a.index()),o=s(),m=l(),i={currentType:14,offset:m,startLoc:o,endLoc:o,lastType:14,lastOffset:m,lastStartLoc:o,lastEndLoc:o,braceNest:0,inLinked:!1,text:""},E=()=>i,{onError:O}=t;function b(r,c,_,...h){const D=E();if(c.column+=_,c.offset+=_,O){const v=je(D.startLoc,c),q=Pe(r,v,{domain:gn,args:h});O(q)}}function N(r,c,_){r.endLoc=s(),r.currentType=c;const h={type:c};return n&&(h.loc=je(r.startLoc,r.endLoc)),_!=null&&(h.value=_),h}const y=r=>N(r,14);function P(r,c){return r.currentChar()===c?(r.next(),c):(b(F.EXPECTED_TOKEN,s(),0,c),"")}function A(r){let c="";for(;r.currentPeek()===ne||r.currentPeek()===j;)c+=r.currentPeek(),r.peek();return c}function S(r){const c=A(r);return r.skipToPeek(),c}function u(r){if(r===re)return!1;const c=r.charCodeAt(0);return c>=97&&c<=122||c>=65&&c<=90||c===95}function L(r){if(r===re)return!1;const c=r.charCodeAt(0);return c>=48&&c<=57}function T(r,c){const{currentType:_}=c;if(_!==2)return!1;A(r);const h=u(r.currentPeek());return r.resetPeek(),h}function g(r,c){const{currentType:_}=c;if(_!==2)return!1;A(r);const h=r.currentPeek()==="-"?r.peek():r.currentPeek(),D=L(h);return r.resetPeek(),D}function d(r,c){const{currentType:_}=c;if(_!==2)return!1;A(r);const h=r.currentPeek()===ct;return r.resetPeek(),h}function I(r,c){const{currentType:_}=c;if(_!==8)return!1;A(r);const h=r.currentPeek()===".";return r.resetPeek(),h}function k(r,c){const{currentType:_}=c;if(_!==9)return!1;A(r);const h=u(r.currentPeek());return r.resetPeek(),h}function H(r,c){const{currentType:_}=c;if(!(_===8||_===12))return!1;A(r);const h=r.currentPeek()===":";return r.resetPeek(),h}function w(r,c){const{currentType:_}=c;if(_!==10)return!1;const h=()=>{const v=r.currentPeek();return v==="{"?u(r.peek()):v==="@"||v==="%"||v==="|"||v===":"||v==="."||v===ne||!v?!1:v===j?(r.peek(),h()):u(v)},D=h();return r.resetPeek(),D}function Y(r){A(r);const c=r.currentPeek()==="|";return r.resetPeek(),c}function J(r){const c=A(r),_=r.currentPeek()==="%"&&r.peek()==="{";return r.resetPeek(),{isModulo:_,hasSpace:c.length>0}}function oe(r,c=!0){const _=(D=!1,v="",q=!1)=>{const te=r.currentPeek();return te==="{"?v==="%"?!1:D:te==="@"||!te?v==="%"?!0:D:te==="%"?(r.peek(),_(D,"%",!0)):te==="|"?v==="%"||q?!0:!(v===ne||v===j):te===ne?(r.peek(),_(!0,ne,q)):te===j?(r.peek(),_(!0,j,q)):!0},h=_();return c&&r.resetPeek(),h}function le(r,c){const _=r.currentChar();return _===re?re:c(_)?(r.next(),_):null}function Te(r){return le(r,_=>{const h=_.charCodeAt(0);return h>=97&&h<=122||h>=65&&h<=90||h>=48&&h<=57||h===95||h===36})}function Re(r){return le(r,_=>{const h=_.charCodeAt(0);return h>=48&&h<=57})}function Fe(r){return le(r,_=>{const h=_.charCodeAt(0);return h>=48&&h<=57||h>=65&&h<=70||h>=97&&h<=102})}function ee(r){let c="",_="";for(;c=Re(r);)_+=c;return _}function he(r){S(r);const c=r.currentChar();return c!=="%"&&b(F.EXPECTED_TOKEN,s(),0,c),r.next(),"%"}function Ie(r){let c="";for(;;){const _=r.currentChar();if(_==="{"||_==="}"||_==="@"||_==="|"||!_)break;if(_==="%")if(oe(r))c+=_,r.next();else break;else if(_===ne||_===j)if(oe(r))c+=_,r.next();else{if(Y(r))break;c+=_,r.next()}else c+=_,r.next()}return c}function ve(r){S(r);let c="",_="";for(;c=Te(r);)_+=c;return r.currentChar()===re&&b(F.UNTERMINATED_CLOSING_BRACE,s(),0),_}function Me(r){S(r);let c="";return r.currentChar()==="-"?(r.next(),c+=`-${ee(r)}`):c+=ee(r),r.currentChar()===re&&b(F.UNTERMINATED_CLOSING_BRACE,s(),0),c}function we(r){S(r),P(r,"'");let c="",_="";const h=v=>v!==ct&&v!==j;for(;c=le(r,h);)c==="\\"?_+=st(r):_+=c;const D=r.currentChar();return D===j||D===re?(b(F.UNTERMINATED_SINGLE_QUOTE_IN_PLACEHOLDER,s(),0),D===j&&(r.next(),P(r,"'")),_):(P(r,"'"),_)}function st(r){const c=r.currentChar();switch(c){case"\\":case"'":return r.next(),`\\${c}`;case"u":return Ce(r,c,4);case"U":return Ce(r,c,6);default:return b(F.UNKNOWN_ESCAPE_SEQUENCE,s(),0,c),""}}function Ce(r,c,_){P(r,c);let h="";for(let D=0;D<_;D++){const v=Fe(r);if(!v){b(F.INVALID_UNICODE_ESCAPE_SEQUENCE,s(),0,`\\${c}${h}${r.currentChar()}`);break}h+=v}return`\\${c}${h}`}function Ue(r){S(r);let c="",_="";const h=D=>D!=="{"&&D!=="}"&&D!==ne&&D!==j;for(;c=le(r,h);)_+=c;return _}function We(r){let c="",_="";for(;c=Te(r);)_+=c;return _}function Ve(r){const c=(_=!1,h)=>{const D=r.currentChar();return D==="{"||D==="%"||D==="@"||D==="|"||!D||D===ne?h:D===j?(h+=D,r.next(),c(_,h)):(h+=D,r.next(),c(!0,h))};return c(!1,"")}function _e(r){S(r);const c=P(r,"|");return S(r),c}function de(r,c){let _=null;switch(r.currentChar()){case"{":return c.braceNest>=1&&b(F.NOT_ALLOW_NEST_PLACEHOLDER,s(),0),r.next(),_=N(c,2,"{"),S(r),c.braceNest++,_;case"}":return c.braceNest>0&&c.currentType===2&&b(F.EMPTY_PLACEHOLDER,s(),0),r.next(),_=N(c,3,"}"),c.braceNest--,c.braceNest>0&&S(r),c.inLinked&&c.braceNest===0&&(c.inLinked=!1),_;case"@":return c.braceNest>0&&b(F.UNTERMINATED_CLOSING_BRACE,s(),0),_=ue(r,c)||y(c),c.braceNest=0,_;default:let D=!0,v=!0,q=!0;if(Y(r))return c.braceNest>0&&b(F.UNTERMINATED_CLOSING_BRACE,s(),0),_=N(c,1,_e(r)),c.braceNest=0,c.inLinked=!1,_;if(c.braceNest>0&&(c.currentType===5||c.currentType===6||c.currentType===7))return b(F.UNTERMINATED_CLOSING_BRACE,s(),0),c.braceNest=0,Ee(r,c);if(D=T(r,c))return _=N(c,5,ve(r)),S(r),_;if(v=g(r,c))return _=N(c,6,Me(r)),S(r),_;if(q=d(r,c))return _=N(c,7,we(r)),S(r),_;if(!D&&!v&&!q)return _=N(c,13,Ue(r)),b(F.INVALID_TOKEN_IN_PLACEHOLDER,s(),0,_.value),S(r),_;break}return _}function ue(r,c){const{currentType:_}=c;let h=null;const D=r.currentChar();switch((_===8||_===9||_===12||_===10)&&(D===j||D===ne)&&b(F.INVALID_LINKED_FORMAT,s(),0),D){case"@":return r.next(),h=N(c,8,"@"),c.inLinked=!0,h;case".":return S(r),r.next(),N(c,9,".");case":":return S(r),r.next(),N(c,10,":");default:return Y(r)?(h=N(c,1,_e(r)),c.braceNest=0,c.inLinked=!1,h):I(r,c)||H(r,c)?(S(r),ue(r,c)):k(r,c)?(S(r),N(c,12,We(r))):w(r,c)?(S(r),D==="{"?de(r,c)||h:N(c,11,Ve(r))):(_===8&&b(F.INVALID_LINKED_FORMAT,s(),0),c.braceNest=0,c.inLinked=!1,Ee(r,c))}}function Ee(r,c){let _={type:14};if(c.braceNest>0)return de(r,c)||y(c);if(c.inLinked)return ue(r,c)||y(c);switch(r.currentChar()){case"{":return de(r,c)||y(c);case"}":return b(F.UNBALANCED_CLOSING_BRACE,s(),0),r.next(),N(c,3,"}");case"@":return ue(r,c)||y(c);default:if(Y(r))return _=N(c,1,_e(r)),c.braceNest=0,c.inLinked=!1,_;const{isModulo:D,hasSpace:v}=J(r);if(D)return v?N(c,0,Ie(r)):N(c,4,he(r));if(oe(r))return N(c,0,Ie(r));break}return _}function Oe(){const{currentType:r,offset:c,startLoc:_,endLoc:h}=i;return i.lastType=r,i.lastOffset=c,i.lastStartLoc=_,i.lastEndLoc=h,i.offset=l(),i.startLoc=s(),a.currentChar()===re?N(i,14):Ee(a,i)}return{nextToken:Oe,currentOffset:l,currentPosition:s,context:E}}const Nn="parser",pn=/(?:\\\\|\\'|\\u([0-9a-fA-F]{4})|\\U([0-9a-fA-F]{6}))/g;function bn(e,t,n){switch(e){case"\\\\":return"\\";case"\\'":return"'";default:{const a=parseInt(t||n,16);return a<=55295||a>=57344?String.fromCodePoint(a):"\uFFFD"}}}function Tn(e={}){const t=e.location!==!1,{onError:n}=e;function a(u,L,T,g,...d){const I=u.currentPosition();if(I.offset+=g,I.column+=g,n){const k=je(T,I),H=Pe(L,k,{domain:Nn,args:d});n(H)}}function l(u,L,T){const g={type:u,start:L,end:L};return t&&(g.loc={start:T,end:T}),g}function s(u,L,T,g){u.end=L,g&&(u.type=g),t&&u.loc&&(u.loc.end=T)}function o(u,L){const T=u.context(),g=l(3,T.offset,T.startLoc);return g.value=L,s(g,u.currentOffset(),u.currentPosition()),g}function m(u,L){const T=u.context(),{lastOffset:g,lastStartLoc:d}=T,I=l(5,g,d);return I.index=parseInt(L,10),u.nextToken(),s(I,u.currentOffset(),u.currentPosition()),I}function i(u,L){const T=u.context(),{lastOffset:g,lastStartLoc:d}=T,I=l(4,g,d);return I.key=L,u.nextToken(),s(I,u.currentOffset(),u.currentPosition()),I}function E(u,L){const T=u.context(),{lastOffset:g,lastStartLoc:d}=T,I=l(9,g,d);return I.value=L.replace(pn,bn),u.nextToken(),s(I,u.currentOffset(),u.currentPosition()),I}function O(u){const L=u.nextToken(),T=u.context(),{lastOffset:g,lastStartLoc:d}=T,I=l(8,g,d);return L.type!==12?(a(u,F.UNEXPECTED_EMPTY_LINKED_MODIFIER,T.lastStartLoc,0),I.value="",s(I,g,d),{nextConsumeToken:L,node:I}):(L.value==null&&a(u,F.UNEXPECTED_LEXICAL_ANALYSIS,T.lastStartLoc,0,z(L)),I.value=L.value||"",s(I,u.currentOffset(),u.currentPosition()),{node:I})}function b(u,L){const T=u.context(),g=l(7,T.offset,T.startLoc);return g.value=L,s(g,u.currentOffset(),u.currentPosition()),g}function N(u){const L=u.context(),T=l(6,L.offset,L.startLoc);let g=u.nextToken();if(g.type===9){const d=O(u);T.modifier=d.node,g=d.nextConsumeToken||u.nextToken()}switch(g.type!==10&&a(u,F.UNEXPECTED_LEXICAL_ANALYSIS,L.lastStartLoc,0,z(g)),g=u.nextToken(),g.type===2&&(g=u.nextToken()),g.type){case 11:g.value==null&&a(u,F.UNEXPECTED_LEXICAL_ANALYSIS,L.lastStartLoc,0,z(g)),T.key=b(u,g.value||"");break;case 5:g.value==null&&a(u,F.UNEXPECTED_LEXICAL_ANALYSIS,L.lastStartLoc,0,z(g)),T.key=i(u,g.value||"");break;case 6:g.value==null&&a(u,F.UNEXPECTED_LEXICAL_ANALYSIS,L.lastStartLoc,0,z(g)),T.key=m(u,g.value||"");break;case 7:g.value==null&&a(u,F.UNEXPECTED_LEXICAL_ANALYSIS,L.lastStartLoc,0,z(g)),T.key=E(u,g.value||"");break;default:a(u,F.UNEXPECTED_EMPTY_LINKED_KEY,L.lastStartLoc,0);const d=u.context(),I=l(7,d.offset,d.startLoc);return I.value="",s(I,d.offset,d.startLoc),T.key=I,s(T,d.offset,d.startLoc),{nextConsumeToken:g,node:T}}return s(T,u.currentOffset(),u.currentPosition()),{node:T}}function y(u){const L=u.context(),T=L.currentType===1?u.currentOffset():L.offset,g=L.currentType===1?L.endLoc:L.startLoc,d=l(2,T,g);d.items=[];let I=null;do{const w=I||u.nextToken();switch(I=null,w.type){case 0:w.value==null&&a(u,F.UNEXPECTED_LEXICAL_ANALYSIS,L.lastStartLoc,0,z(w)),d.items.push(o(u,w.value||""));break;case 6:w.value==null&&a(u,F.UNEXPECTED_LEXICAL_ANALYSIS,L.lastStartLoc,0,z(w)),d.items.push(m(u,w.value||""));break;case 5:w.value==null&&a(u,F.UNEXPECTED_LEXICAL_ANALYSIS,L.lastStartLoc,0,z(w)),d.items.push(i(u,w.value||""));break;case 7:w.value==null&&a(u,F.UNEXPECTED_LEXICAL_ANALYSIS,L.lastStartLoc,0,z(w)),d.items.push(E(u,w.value||""));break;case 8:const Y=N(u);d.items.push(Y.node),I=Y.nextConsumeToken||null;break}}while(L.currentType!==14&&L.currentType!==1);const k=L.currentType===1?L.lastOffset:u.currentOffset(),H=L.currentType===1?L.lastEndLoc:u.currentPosition();return s(d,k,H),d}function P(u,L,T,g){const d=u.context();let I=g.items.length===0;const k=l(1,L,T);k.cases=[],k.cases.push(g);do{const H=y(u);I||(I=H.items.length===0),k.cases.push(H)}while(d.currentType!==14);return I&&a(u,F.MUST_HAVE_MESSAGES_IN_PLURAL,T,0),s(k,u.currentOffset(),u.currentPosition()),k}function A(u){const L=u.context(),{offset:T,startLoc:g}=L,d=y(u);return L.currentType===14?d:P(u,T,g,d)}function S(u){const L=Ln(u,X({},e)),T=L.context(),g=l(0,T.offset,T.startLoc);return t&&g.loc&&(g.loc.source=u),g.body=A(L),T.currentType!==14&&a(L,F.UNEXPECTED_LEXICAL_ANALYSIS,T.lastStartLoc,0,u[T.offset]||""),s(g,L.currentOffset(),L.currentPosition()),g}return{parse:S}}function z(e){if(e.type===14)return"EOF";const t=(e.value||"").replace(/\r?\n/gu,"\\n");return t.length>10?t.slice(0,9)+"\u2026":t}function hn(e,t={}){const n={ast:e,helpers:new Set};return{context:()=>n,helper:s=>(n.helpers.add(s),s)}}function ot(e,t){for(let n=0;n<e.length;n++)et(e[n],t)}function et(e,t){switch(e.type){case 1:ot(e.cases,t),t.helper("plural");break;case 2:ot(e.items,t);break;case 6:et(e.key,t),t.helper("linked"),t.helper("type");break;case 5:t.helper("interpolate"),t.helper("list");break;case 4:t.helper("interpolate"),t.helper("named");break}}function In(e,t={}){const n=hn(e);n.helper("normalize"),e.body&&et(e.body,n);const a=n.context();e.helpers=Array.from(a.helpers)}function Cn(e,t){const{sourceMap:n,filename:a,breakLineCode:l,needIndent:s}=t,o={source:e.loc.source,filename:a,code:"",column:1,line:1,offset:0,map:void 0,breakLineCode:l,needIndent:s,indentLevel:0},m=()=>o;function i(A,S){o.code+=A}function E(A,S=!0){const u=S?l:"";i(s?u+"  ".repeat(A):u)}function O(A=!0){const S=++o.indentLevel;A&&E(S)}function b(A=!0){const S=--o.indentLevel;A&&E(S)}function N(){E(o.indentLevel)}return{context:m,push:i,indent:O,deindent:b,newline:N,helper:A=>`_${A}`,needIndent:()=>o.needIndent}}function On(e,t){const{helper:n}=e;e.push(`${n("linked")}(`),me(e,t.key),t.modifier?(e.push(", "),me(e,t.modifier),e.push(", _type")):e.push(", undefined, _type"),e.push(")")}function An(e,t){const{helper:n,needIndent:a}=e;e.push(`${n("normalize")}([`),e.indent(a());const l=t.items.length;for(let s=0;s<l&&(me(e,t.items[s]),s!==l-1);s++)e.push(", ");e.deindent(a()),e.push("])")}function Sn(e,t){const{helper:n,needIndent:a}=e;if(t.cases.length>1){e.push(`${n("plural")}([`),e.indent(a());const l=t.cases.length;for(let s=0;s<l&&(me(e,t.cases[s]),s!==l-1);s++)e.push(", ");e.deindent(a()),e.push("])")}}function yn(e,t){t.body?me(e,t.body):e.push("null")}function me(e,t){const{helper:n}=e;switch(t.type){case 0:yn(e,t);break;case 1:Sn(e,t);break;case 2:An(e,t);break;case 6:On(e,t);break;case 8:e.push(JSON.stringify(t.value),t);break;case 7:e.push(JSON.stringify(t.value),t);break;case 5:e.push(`${n("interpolate")}(${n("list")}(${t.index}))`,t);break;case 4:e.push(`${n("interpolate")}(${n("named")}(${JSON.stringify(t.key)}))`,t);break;case 9:e.push(JSON.stringify(t.value),t);break;case 3:e.push(JSON.stringify(t.value),t);break}}const kn=(e,t={})=>{const n=C(t.mode)?t.mode:"normal",a=C(t.filename)?t.filename:"message.intl",l=!!t.sourceMap,s=t.breakLineCode!=null?t.breakLineCode:n==="arrow"?";":`
`,o=t.needIndent?t.needIndent:n!=="arrow",m=e.helpers||[],i=Cn(e,{mode:n,filename:a,sourceMap:l,breakLineCode:s,needIndent:o});i.push(n==="normal"?"function __msg__ (ctx) {":"(ctx) => {"),i.indent(o),m.length>0&&(i.push(`const { ${m.map(b=>`${b}: _${b}`).join(", ")} } = ctx`),i.newline()),i.push("return "),me(i,e),i.deindent(o),i.push("}");const{code:E,map:O}=i.context();return{ast:e,code:E,map:O?O.toJSON():void 0}};function Pn(e,t={}){const n=X({},t),l=Tn(n).parse(e);return In(l,n),kn(l,n)}/*!
  * devtools-if v9.2.2
  * (c) 2022 kazuya kawaguchi
  * Released under the MIT License.
  */const Dn={I18nInit:"i18n:init",FunctionTranslate:"function:translate"};/*!
  * core-base v9.2.2
  * (c) 2022 kazuya kawaguchi
  * Released under the MIT License.
  */const se=[];se[0]={w:[0],i:[3,0],["["]:[4],o:[7]};se[1]={w:[1],["."]:[2],["["]:[4],o:[7]};se[2]={w:[2],i:[3,0],[0]:[3,0]};se[3]={i:[3,0],[0]:[3,0],w:[1,1],["."]:[2,1],["["]:[4,1],o:[7,1]};se[4]={["'"]:[5,0],['"']:[6,0],["["]:[4,2],["]"]:[1,3],o:8,l:[4,0]};se[5]={["'"]:[4,0],o:8,l:[5,0]};se[6]={['"']:[4,0],o:8,l:[6,0]};const Rn=/^\s?(?:true|false|-?[\d.]+|'[^']*'|"[^"]*")\s?$/;function Fn(e){return Rn.test(e)}function vn(e){const t=e.charCodeAt(0),n=e.charCodeAt(e.length-1);return t===n&&(t===34||t===39)?e.slice(1,-1):e}function Mn(e){if(e==null)return"o";switch(e.charCodeAt(0)){case 91:case 93:case 46:case 34:case 39:return e;case 95:case 36:case 45:return"i";case 9:case 10:case 13:case 160:case 65279:case 8232:case 8233:return"w"}return"i"}function wn(e){const t=e.trim();return e.charAt(0)==="0"&&isNaN(parseInt(e))?!1:Fn(t)?vn(t):"*"+t}function Un(e){const t=[];let n=-1,a=0,l=0,s,o,m,i,E,O,b;const N=[];N[0]=()=>{o===void 0?o=m:o+=m},N[1]=()=>{o!==void 0&&(t.push(o),o=void 0)},N[2]=()=>{N[0](),l++},N[3]=()=>{if(l>0)l--,a=4,N[0]();else{if(l=0,o===void 0||(o=wn(o),o===!1))return!1;N[1]()}};function y(){const P=e[n+1];if(a===5&&P==="'"||a===6&&P==='"')return n++,m="\\"+P,N[0](),!0}for(;a!==null;)if(n++,s=e[n],!(s==="\\"&&y())){if(i=Mn(s),b=se[a],E=b[i]||b.l||8,E===8||(a=E[0],E[1]!==void 0&&(O=N[E[1]],O&&(m=s,O()===!1))))return;if(a===7)return t}}const ut=new Map;function Wn(e,t){return M(e)?e[t]:null}function Vn(e,t){if(!M(e))return null;let n=ut.get(t);if(n||(n=Un(t),n&&ut.set(t,n)),!n)return null;const a=n.length;let l=e,s=0;for(;s<a;){const o=l[n[s]];if(o===void 0)return null;l=o,s++}return l}const xn=e=>e,$n=e=>"",Xn="text",Gn=e=>e.length===0?"":e.join(""),jn=on;function it(e,t){return e=Math.abs(e),t===2?e?e>1?1:0:1:e?Math.min(e,2):0}function Hn(e){const t=x(e.pluralIndex)?e.pluralIndex:-1;return e.named&&(x(e.named.count)||x(e.named.n))?x(e.named.count)?e.named.count:x(e.named.n)?e.named.n:t:t}function Kn(e,t){t.count||(t.count=e),t.n||(t.n=e)}function Yn(e={}){const t=e.locale,n=Hn(e),a=M(e.pluralRules)&&C(t)&&$(e.pluralRules[t])?e.pluralRules[t]:it,l=M(e.pluralRules)&&C(t)&&$(e.pluralRules[t])?it:void 0,s=u=>u[a(n,u.length,l)],o=e.list||[],m=u=>o[u],i=e.named||{};x(e.pluralIndex)&&Kn(n,i);const E=u=>i[u];function O(u){const L=$(e.messages)?e.messages(u):M(e.messages)?e.messages[u]:!1;return L||(e.parent?e.parent.message(u):$n)}const b=u=>e.modifiers?e.modifiers[u]:xn,N=R(e.processor)&&$(e.processor.normalize)?e.processor.normalize:Gn,y=R(e.processor)&&$(e.processor.interpolate)?e.processor.interpolate:jn,P=R(e.processor)&&C(e.processor.type)?e.processor.type:Xn,S={list:m,named:E,plural:s,linked:(u,...L)=>{const[T,g]=L;let d="text",I="";L.length===1?M(T)?(I=T.modifier||I,d=T.type||d):C(T)&&(I=T||I):L.length===2&&(C(T)&&(I=T||I),C(g)&&(d=g||d));let k=O(u)(S);return d==="vnode"&&W(k)&&I&&(k=k[0]),I?b(I)(k,d):k},message:O,type:P,interpolate:y,normalize:N};return S}let Bn=null;Dn.FunctionTranslate;function Jn(e){return t=>Bn}const Qn={NOT_FOUND_KEY:1,FALLBACK_TO_TRANSLATE:2,CANNOT_FORMAT_NUMBER:3,FALLBACK_TO_NUMBER_FORMAT:4,CANNOT_FORMAT_DATE:5,FALLBACK_TO_DATE_FORMAT:6,__EXTEND_POINT__:7};function qn(e,t,n){return[...new Set([n,...W(t)?t:M(t)?Object.keys(t):C(t)?[t]:[n]])]}function Ft(e,t,n){const a=C(n)?n:tt,l=e;l.__localeChainCache||(l.__localeChainCache=new Map);let s=l.__localeChainCache.get(a);if(!s){s=[];let o=[n];for(;W(o);)o=ft(s,o,t);const m=W(t)||!R(t)?t:t.default?t.default:null;o=C(m)?[m]:m,W(o)&&ft(s,o,!1),l.__localeChainCache.set(a,s)}return s}function ft(e,t,n){let a=!0;for(let l=0;l<t.length&&U(a);l++){const s=t[l];C(s)&&(a=Zn(e,t[l],n))}return a}function Zn(e,t,n){let a;const l=t.split("-");do{const s=l.join("-");a=zn(e,s,n),l.splice(-1,1)}while(l.length&&a===!0);return a}function zn(e,t,n){let a=!1;if(!e.includes(t)&&(a=!0,t)){a=t[t.length-1]!=="!";const l=t.replace(/!/g,"");e.push(l),(W(n)||R(n))&&n[l]&&(a=n[l])}return a}const er="9.2.2",De=-1,tt="en-US",mt="",_t=e=>`${e.charAt(0).toLocaleUpperCase()}${e.substr(1)}`;function tr(){return{upper:(e,t)=>t==="text"&&C(e)?e.toUpperCase():t==="vnode"&&M(e)&&"__v_isVNode"in e?e.children.toUpperCase():e,lower:(e,t)=>t==="text"&&C(e)?e.toLowerCase():t==="vnode"&&M(e)&&"__v_isVNode"in e?e.children.toLowerCase():e,capitalize:(e,t)=>t==="text"&&C(e)?_t(e):t==="vnode"&&M(e)&&"__v_isVNode"in e?_t(e.children):e}}let vt;function nr(e){vt=e}let Mt;function rr(e){Mt=e}let wt;function ar(e){wt=e}let dt=0;function sr(e={}){const t=C(e.version)?e.version:er,n=C(e.locale)?e.locale:tt,a=W(e.fallbackLocale)||R(e.fallbackLocale)||C(e.fallbackLocale)||e.fallbackLocale===!1?e.fallbackLocale:n,l=R(e.messages)?e.messages:{[n]:{}},s=R(e.datetimeFormats)?e.datetimeFormats:{[n]:{}},o=R(e.numberFormats)?e.numberFormats:{[n]:{}},m=X({},e.modifiers||{},tr()),i=e.pluralRules||{},E=$(e.missing)?e.missing:null,O=U(e.missingWarn)||ye(e.missingWarn)?e.missingWarn:!0,b=U(e.fallbackWarn)||ye(e.fallbackWarn)?e.fallbackWarn:!0,N=!!e.fallbackFormat,y=!!e.unresolving,P=$(e.postTranslation)?e.postTranslation:null,A=R(e.processor)?e.processor:null,S=U(e.warnHtmlMessage)?e.warnHtmlMessage:!0,u=!!e.escapeParameter,L=$(e.messageCompiler)?e.messageCompiler:vt,T=$(e.messageResolver)?e.messageResolver:Mt||Wn,g=$(e.localeFallbacker)?e.localeFallbacker:wt||qn,d=M(e.fallbackContext)?e.fallbackContext:void 0,I=$(e.onWarn)?e.onWarn:ln,k=e,H=M(k.__datetimeFormatters)?k.__datetimeFormatters:new Map,w=M(k.__numberFormatters)?k.__numberFormatters:new Map,Y=M(k.__meta)?k.__meta:{};dt++;const J={version:t,cid:dt,locale:n,fallbackLocale:a,messages:l,modifiers:m,pluralRules:i,missing:E,missingWarn:O,fallbackWarn:b,fallbackFormat:N,unresolving:y,postTranslation:P,processor:A,warnHtmlMessage:S,escapeParameter:u,messageCompiler:L,messageResolver:T,localeFallbacker:g,fallbackContext:d,onWarn:I,__meta:Y};return J.datetimeFormats=s,J.numberFormats=o,J.__datetimeFormatters=H,J.__numberFormatters=w,J}function nt(e,t,n,a,l){const{missing:s,onWarn:o}=e;if(s!==null){const m=s(e,n,t,l);return C(m)?m:t}else return t}function pe(e,t,n){const a=e;a.__localeChainCache=new Map,e.localeFallbacker(e,n,t)}const lr=e=>e;let Et=Object.create(null);function cr(e,t={}){{const a=(t.onCacheKey||lr)(e),l=Et[a];if(l)return l;let s=!1;const o=t.onError||un;t.onError=E=>{s=!0,o(E)};const{code:m}=Pn(e,t),i=new Function(`return ${m}`)();return s?i:Et[a]=i}}let Ut=F.__EXTEND_POINT__;const $e=()=>++Ut,ie={INVALID_ARGUMENT:Ut,INVALID_DATE_ARGUMENT:$e(),INVALID_ISO_DATE_ARGUMENT:$e(),__EXTEND_POINT__:$e()};function fe(e){return Pe(e,null,void 0)}const gt=()=>"",ce=e=>$(e);function Lt(e,...t){const{fallbackFormat:n,postTranslation:a,unresolving:l,messageCompiler:s,fallbackLocale:o,messages:m}=e,[i,E]=He(...t),O=U(E.missingWarn)?E.missingWarn:e.missingWarn,b=U(E.fallbackWarn)?E.fallbackWarn:e.fallbackWarn,N=U(E.escapeParameter)?E.escapeParameter:e.escapeParameter,y=!!E.resolvedMessage,P=C(E.default)||U(E.default)?U(E.default)?s?i:()=>i:E.default:n?s?i:()=>i:"",A=n||P!=="",S=C(E.locale)?E.locale:e.locale;N&&or(E);let[u,L,T]=y?[i,S,m[S]||{}]:Wt(e,i,S,o,b,O),g=u,d=i;if(!y&&!(C(g)||ce(g))&&A&&(g=P,d=g),!y&&(!(C(g)||ce(g))||!C(L)))return l?De:i;let I=!1;const k=()=>{I=!0},H=ce(g)?g:Vt(e,i,L,g,d,k);if(I)return g;const w=fr(e,L,T,E),Y=Yn(w),J=ur(e,H,Y);return a?a(J,i):J}function or(e){W(e.list)?e.list=e.list.map(t=>C(t)?lt(t):t):M(e.named)&&Object.keys(e.named).forEach(t=>{C(e.named[t])&&(e.named[t]=lt(e.named[t]))})}function Wt(e,t,n,a,l,s){const{messages:o,onWarn:m,messageResolver:i,localeFallbacker:E}=e,O=E(e,a,n);let b={},N,y=null;const P="translate";for(let A=0;A<O.length&&(N=O[A],b=o[N]||{},(y=i(b,t))===null&&(y=b[t]),!(C(y)||$(y)));A++){const S=nt(e,t,N,s,P);S!==t&&(y=S)}return[y,N,b]}function Vt(e,t,n,a,l,s){const{messageCompiler:o,warnHtmlMessage:m}=e;if(ce(a)){const E=a;return E.locale=E.locale||n,E.key=E.key||t,E}if(o==null){const E=()=>a;return E.locale=n,E.key=t,E}const i=o(a,ir(e,n,l,a,m,s));return i.locale=n,i.key=t,i.source=a,i}function ur(e,t,n){return t(n)}function He(...e){const[t,n,a]=e,l={};if(!C(t)&&!x(t)&&!ce(t))throw fe(ie.INVALID_ARGUMENT);const s=x(t)?String(t):(ce(t),t);return x(n)?l.plural=n:C(n)?l.default=n:R(n)&&!ke(n)?l.named=n:W(n)&&(l.list=n),x(a)?l.plural=a:C(a)?l.default=a:R(a)&&X(l,a),[s,l]}function ir(e,t,n,a,l,s){return{warnHtmlMessage:l,onError:o=>{throw s&&s(o),o},onCacheKey:o=>rn(t,n,o)}}function fr(e,t,n,a){const{modifiers:l,pluralRules:s,messageResolver:o,fallbackLocale:m,fallbackWarn:i,missingWarn:E,fallbackContext:O}=e,N={locale:t,modifiers:l,pluralRules:s,messages:y=>{let P=o(n,y);if(P==null&&O){const[,,A]=Wt(O,y,t,m,i,E);P=o(A,y)}if(C(P)){let A=!1;const u=Vt(e,y,t,P,y,()=>{A=!0});return A?gt:u}else return ce(P)?P:gt}};return e.processor&&(N.processor=e.processor),a.list&&(N.list=a.list),a.named&&(N.named=a.named),x(a.plural)&&(N.pluralIndex=a.plural),N}function Nt(e,...t){const{datetimeFormats:n,unresolving:a,fallbackLocale:l,onWarn:s,localeFallbacker:o}=e,{__datetimeFormatters:m}=e,[i,E,O,b]=Ke(...t),N=U(O.missingWarn)?O.missingWarn:e.missingWarn;U(O.fallbackWarn)?O.fallbackWarn:e.fallbackWarn;const y=!!O.part,P=C(O.locale)?O.locale:e.locale,A=o(e,l,P);if(!C(i)||i==="")return new Intl.DateTimeFormat(P,b).format(E);let S={},u,L=null;const T="datetime format";for(let I=0;I<A.length&&(u=A[I],S=n[u]||{},L=S[i],!R(L));I++)nt(e,i,u,N,T);if(!R(L)||!C(u))return a?De:i;let g=`${u}__${i}`;ke(b)||(g=`${g}__${JSON.stringify(b)}`);let d=m.get(g);return d||(d=new Intl.DateTimeFormat(u,X({},L,b)),m.set(g,d)),y?d.formatToParts(E):d.format(E)}const xt=["localeMatcher","weekday","era","year","month","day","hour","minute","second","timeZoneName","formatMatcher","hour12","timeZone","dateStyle","timeStyle","calendar","dayPeriod","numberingSystem","hourCycle","fractionalSecondDigits"];function Ke(...e){const[t,n,a,l]=e,s={};let o={},m;if(C(t)){const i=t.match(/(\d{4}-\d{2}-\d{2})(T|\s)?(.*)/);if(!i)throw fe(ie.INVALID_ISO_DATE_ARGUMENT);const E=i[3]?i[3].trim().startsWith("T")?`${i[1].trim()}${i[3].trim()}`:`${i[1].trim()}T${i[3].trim()}`:i[1].trim();m=new Date(E);try{m.toISOString()}catch{throw fe(ie.INVALID_ISO_DATE_ARGUMENT)}}else if(sn(t)){if(isNaN(t.getTime()))throw fe(ie.INVALID_DATE_ARGUMENT);m=t}else if(x(t))m=t;else throw fe(ie.INVALID_ARGUMENT);return C(n)?s.key=n:R(n)&&Object.keys(n).forEach(i=>{xt.includes(i)?o[i]=n[i]:s[i]=n[i]}),C(a)?s.locale=a:R(a)&&(o=a),R(l)&&(o=l),[s.key||"",m,s,o]}function pt(e,t,n){const a=e;for(const l in n){const s=`${t}__${l}`;!a.__datetimeFormatters.has(s)||a.__datetimeFormatters.delete(s)}}function bt(e,...t){const{numberFormats:n,unresolving:a,fallbackLocale:l,onWarn:s,localeFallbacker:o}=e,{__numberFormatters:m}=e,[i,E,O,b]=Ye(...t),N=U(O.missingWarn)?O.missingWarn:e.missingWarn;U(O.fallbackWarn)?O.fallbackWarn:e.fallbackWarn;const y=!!O.part,P=C(O.locale)?O.locale:e.locale,A=o(e,l,P);if(!C(i)||i==="")return new Intl.NumberFormat(P,b).format(E);let S={},u,L=null;const T="number format";for(let I=0;I<A.length&&(u=A[I],S=n[u]||{},L=S[i],!R(L));I++)nt(e,i,u,N,T);if(!R(L)||!C(u))return a?De:i;let g=`${u}__${i}`;ke(b)||(g=`${g}__${JSON.stringify(b)}`);let d=m.get(g);return d||(d=new Intl.NumberFormat(u,X({},L,b)),m.set(g,d)),y?d.formatToParts(E):d.format(E)}const $t=["localeMatcher","style","currency","currencyDisplay","currencySign","useGrouping","minimumIntegerDigits","minimumFractionDigits","maximumFractionDigits","minimumSignificantDigits","maximumSignificantDigits","compactDisplay","notation","signDisplay","unit","unitDisplay","roundingMode","roundingPriority","roundingIncrement","trailingZeroDisplay"];function Ye(...e){const[t,n,a,l]=e,s={};let o={};if(!x(t))throw fe(ie.INVALID_ARGUMENT);const m=t;return C(n)?s.key=n:R(n)&&Object.keys(n).forEach(i=>{$t.includes(i)?o[i]=n[i]:s[i]=n[i]}),C(a)?s.locale=a:R(a)&&(o=a),R(l)&&(o=l),[s.key||"",m,s,o]}function Tt(e,t,n){const a=e;for(const l in n){const s=`${t}__${l}`;!a.__numberFormatters.has(s)||a.__numberFormatters.delete(s)}}/*!
  * vue-i18n v9.2.2
  * (c) 2022 kazuya kawaguchi
  * Released under the MIT License.
  */const mr="9.2.2";Qn.__EXTEND_POINT__;let Xt=F.__EXTEND_POINT__;const K=()=>++Xt,B={UNEXPECTED_RETURN_TYPE:Xt,INVALID_ARGUMENT:K(),MUST_BE_CALL_SETUP_TOP:K(),NOT_INSLALLED:K(),NOT_AVAILABLE_IN_LEGACY_MODE:K(),REQUIRED_VALUE:K(),INVALID_VALUE:K(),CANNOT_SETUP_VUE_DEVTOOLS_PLUGIN:K(),NOT_INSLALLED_WITH_PROVIDE:K(),UNEXPECTED_ERROR:K(),NOT_COMPATIBLE_LEGACY_VUE_I18N:K(),BRIDGE_SUPPORT_VUE_2_ONLY:K(),MUST_DEFINE_I18N_OPTION_IN_ALLOW_COMPOSITION:K(),NOT_AVAILABLE_COMPOSITION_IN_LEGACY:K(),__EXTEND_POINT__:K()};function Q(e,...t){return Pe(e,null,void 0)}const Be=ae("__transrateVNode"),Je=ae("__datetimeParts"),Qe=ae("__numberParts"),_r=ae("__setPluralRules");ae("__intlifyMeta");const dr=ae("__injectWithOption");function qe(e){if(!M(e))return e;for(const t in e)if(!!Ze(e,t))if(!t.includes("."))M(e[t])&&qe(e[t]);else{const n=t.split("."),a=n.length-1;let l=e;for(let s=0;s<a;s++)n[s]in l||(l[n[s]]={}),l=l[n[s]];l[n[a]]=e[t],delete e[t],M(l[n[a]])&&qe(l[n[a]])}return e}function Gt(e,t){const{messages:n,__i18n:a,messageResolver:l,flatJson:s}=t,o=R(n)?n:W(a)?{}:{[e]:{}};if(W(a)&&a.forEach(m=>{if("locale"in m&&"resource"in m){const{locale:i,resource:E}=m;i?(o[i]=o[i]||{},be(E,o[i])):be(E,o)}else C(m)&&be(JSON.parse(m),o)}),l==null&&s)for(const m in o)Ze(o,m)&&qe(o[m]);return o}const Se=e=>!M(e)||W(e);function be(e,t){if(Se(e)||Se(t))throw Q(B.INVALID_VALUE);for(const n in e)Ze(e,n)&&(Se(e[n])||Se(t[n])?t[n]=e[n]:be(e[n],t[n]))}function Er(e){return e.type}function gr(e,t,n){let a=M(t.messages)?t.messages:{};"__i18nGlobal"in n&&(a=Gt(e.locale.value,{messages:a,__i18n:n.__i18nGlobal}));const l=Object.keys(a);l.length&&l.forEach(s=>{e.mergeLocaleMessage(s,a[s])});{if(M(t.datetimeFormats)){const s=Object.keys(t.datetimeFormats);s.length&&s.forEach(o=>{e.mergeDateTimeFormat(o,t.datetimeFormats[o])})}if(M(t.numberFormats)){const s=Object.keys(t.numberFormats);s.length&&s.forEach(o=>{e.mergeNumberFormat(o,t.numberFormats[o])})}}}function ht(e){return en(tn,null,e,0)}let It=0;function Ct(e){return(t,n,a,l)=>e(n,a,Dt()||void 0,l)}function jt(e={},t){const{__root:n}=e,a=n===void 0;let l=U(e.inheritLocale)?e.inheritLocale:!0;const s=Le(n&&l?n.locale.value:C(e.locale)?e.locale:tt),o=Le(n&&l?n.fallbackLocale.value:C(e.fallbackLocale)||W(e.fallbackLocale)||R(e.fallbackLocale)||e.fallbackLocale===!1?e.fallbackLocale:s.value),m=Le(Gt(s.value,e)),i=Le(R(e.datetimeFormats)?e.datetimeFormats:{[s.value]:{}}),E=Le(R(e.numberFormats)?e.numberFormats:{[s.value]:{}});let O=n?n.missingWarn:U(e.missingWarn)||ye(e.missingWarn)?e.missingWarn:!0,b=n?n.fallbackWarn:U(e.fallbackWarn)||ye(e.fallbackWarn)?e.fallbackWarn:!0,N=n?n.fallbackRoot:U(e.fallbackRoot)?e.fallbackRoot:!0,y=!!e.fallbackFormat,P=$(e.missing)?e.missing:null,A=$(e.missing)?Ct(e.missing):null,S=$(e.postTranslation)?e.postTranslation:null,u=n?n.warnHtmlMessage:U(e.warnHtmlMessage)?e.warnHtmlMessage:!0,L=!!e.escapeParameter;const T=n?n.modifiers:R(e.modifiers)?e.modifiers:{};let g=e.pluralRules||n&&n.pluralRules,d;d=(()=>{const f={version:mr,locale:s.value,fallbackLocale:o.value,messages:m.value,modifiers:T,pluralRules:g,missing:A===null?void 0:A,missingWarn:O,fallbackWarn:b,fallbackFormat:y,unresolving:!0,postTranslation:S===null?void 0:S,warnHtmlMessage:u,escapeParameter:L,messageResolver:e.messageResolver,__meta:{framework:"vue"}};return f.datetimeFormats=i.value,f.numberFormats=E.value,f.__datetimeFormatters=R(d)?d.__datetimeFormatters:void 0,f.__numberFormatters=R(d)?d.__numberFormatters:void 0,sr(f)})(),pe(d,s.value,o.value);function k(){return[s.value,o.value,m.value,i.value,E.value]}const H=Ne({get:()=>s.value,set:f=>{s.value=f,d.locale=s.value}}),w=Ne({get:()=>o.value,set:f=>{o.value=f,d.fallbackLocale=o.value,pe(d,s.value,f)}}),Y=Ne(()=>m.value),J=Ne(()=>i.value),oe=Ne(()=>E.value);function le(){return $(S)?S:null}function Te(f){S=f,d.postTranslation=f}function Re(){return P}function Fe(f){f!==null&&(A=Ct(f)),P=f,d.missing=A}const ee=(f,p,Z,G,xe,Ae)=>{k();let ge;if(ge=f(d),x(ge)&&ge===De){const[Yt,Rr]=p();return n&&N?G(n):xe(Yt)}else{if(Ae(ge))return ge;throw Q(B.UNEXPECTED_RETURN_TYPE)}};function he(...f){return ee(p=>Reflect.apply(Lt,null,[p,...f]),()=>He(...f),"translate",p=>Reflect.apply(p.t,p,[...f]),p=>p,p=>C(p))}function Ie(...f){const[p,Z,G]=f;if(G&&!M(G))throw Q(B.INVALID_ARGUMENT);return he(p,Z,X({resolvedMessage:!0},G||{}))}function ve(...f){return ee(p=>Reflect.apply(Nt,null,[p,...f]),()=>Ke(...f),"datetime format",p=>Reflect.apply(p.d,p,[...f]),()=>mt,p=>C(p))}function Me(...f){return ee(p=>Reflect.apply(bt,null,[p,...f]),()=>Ye(...f),"number format",p=>Reflect.apply(p.n,p,[...f]),()=>mt,p=>C(p))}function we(f){return f.map(p=>C(p)||x(p)||U(p)?ht(String(p)):p)}const Ce={normalize:we,interpolate:f=>f,type:"vnode"};function Ue(...f){return ee(p=>{let Z;const G=p;try{G.processor=Ce,Z=Reflect.apply(Lt,null,[G,...f])}finally{G.processor=null}return Z},()=>He(...f),"translate",p=>p[Be](...f),p=>[ht(p)],p=>W(p))}function We(...f){return ee(p=>Reflect.apply(bt,null,[p,...f]),()=>Ye(...f),"number format",p=>p[Qe](...f),()=>[],p=>C(p)||W(p))}function Ve(...f){return ee(p=>Reflect.apply(Nt,null,[p,...f]),()=>Ke(...f),"datetime format",p=>p[Je](...f),()=>[],p=>C(p)||W(p))}function _e(f){g=f,d.pluralRules=g}function de(f,p){const Z=C(p)?p:s.value,G=Oe(Z);return d.messageResolver(G,f)!==null}function ue(f){let p=null;const Z=Ft(d,o.value,s.value);for(let G=0;G<Z.length;G++){const xe=m.value[Z[G]]||{},Ae=d.messageResolver(xe,f);if(Ae!=null){p=Ae;break}}return p}function Ee(f){const p=ue(f);return p!=null?p:n?n.tm(f)||{}:{}}function Oe(f){return m.value[f]||{}}function r(f,p){m.value[f]=p,d.messages=m.value}function c(f,p){m.value[f]=m.value[f]||{},be(p,m.value[f]),d.messages=m.value}function _(f){return i.value[f]||{}}function h(f,p){i.value[f]=p,d.datetimeFormats=i.value,pt(d,f,p)}function D(f,p){i.value[f]=X(i.value[f]||{},p),d.datetimeFormats=i.value,pt(d,f,p)}function v(f){return E.value[f]||{}}function q(f,p){E.value[f]=p,d.numberFormats=E.value,Tt(d,f,p)}function te(f,p){E.value[f]=X(E.value[f]||{},p),d.numberFormats=E.value,Tt(d,f,p)}It++,n&&Ge&&(Xe(n.locale,f=>{l&&(s.value=f,d.locale=f,pe(d,s.value,o.value))}),Xe(n.fallbackLocale,f=>{l&&(o.value=f,d.fallbackLocale=f,pe(d,s.value,o.value))}));const V={id:It,locale:H,fallbackLocale:w,get inheritLocale(){return l},set inheritLocale(f){l=f,f&&n&&(s.value=n.locale.value,o.value=n.fallbackLocale.value,pe(d,s.value,o.value))},get availableLocales(){return Object.keys(m.value).sort()},messages:Y,get modifiers(){return T},get pluralRules(){return g||{}},get isGlobal(){return a},get missingWarn(){return O},set missingWarn(f){O=f,d.missingWarn=O},get fallbackWarn(){return b},set fallbackWarn(f){b=f,d.fallbackWarn=b},get fallbackRoot(){return N},set fallbackRoot(f){N=f},get fallbackFormat(){return y},set fallbackFormat(f){y=f,d.fallbackFormat=y},get warnHtmlMessage(){return u},set warnHtmlMessage(f){u=f,d.warnHtmlMessage=f},get escapeParameter(){return L},set escapeParameter(f){L=f,d.escapeParameter=f},t:he,getLocaleMessage:Oe,setLocaleMessage:r,mergeLocaleMessage:c,getPostTranslationHandler:le,setPostTranslationHandler:Te,getMissingHandler:Re,setMissingHandler:Fe,[_r]:_e};return V.datetimeFormats=J,V.numberFormats=oe,V.rt=Ie,V.te=de,V.tm=Ee,V.d=ve,V.n=Me,V.getDateTimeFormat=_,V.setDateTimeFormat=h,V.mergeDateTimeFormat=D,V.getNumberFormat=v,V.setNumberFormat=q,V.mergeNumberFormat=te,V[dr]=e.__injectWithOption,V[Be]=Ue,V[Je]=Ve,V[Qe]=We,V}const rt={tag:{type:[String,Object]},locale:{type:String},scope:{type:String,validator:e=>e==="parent"||e==="global",default:"parent"},i18n:{type:Object}};function Lr({slots:e},t){return t.length===1&&t[0]==="default"?(e.default?e.default():[]).reduce((a,l)=>a=[...a,...W(l.children)?l.children:[l]],[]):t.reduce((n,a)=>{const l=e[a];return l&&(n[a]=l()),n},{})}function Ht(e){return Zt}const Ot={name:"i18n-t",props:X({keypath:{type:String,required:!0},plural:{type:[Number,String],validator:e=>x(e)||!isNaN(e)}},rt),setup(e,t){const{slots:n,attrs:a}=t,l=e.i18n||at({useScope:e.scope,__useComponent:!0});return()=>{const s=Object.keys(n).filter(b=>b!=="_"),o={};e.locale&&(o.locale=e.locale),e.plural!==void 0&&(o.plural=C(e.plural)?+e.plural:e.plural);const m=Lr(t,s),i=l[Be](e.keypath,m,o),E=X({},a),O=C(e.tag)||M(e.tag)?e.tag:Ht();return Pt(O,E,i)}}};function Nr(e){return W(e)&&!C(e[0])}function Kt(e,t,n,a){const{slots:l,attrs:s}=t;return()=>{const o={part:!0};let m={};e.locale&&(o.locale=e.locale),C(e.format)?o.key=e.format:M(e.format)&&(C(e.format.key)&&(o.key=e.format.key),m=Object.keys(e.format).reduce((N,y)=>n.includes(y)?X({},N,{[y]:e.format[y]}):N,{}));const i=a(e.value,o,m);let E=[o.key];W(i)?E=i.map((N,y)=>{const P=l[N.type],A=P?P({[N.type]:N.value,index:y,parts:i}):[N.value];return Nr(A)&&(A[0].key=`${N.type}-${y}`),A}):C(i)&&(E=[i]);const O=X({},s),b=C(e.tag)||M(e.tag)?e.tag:Ht();return Pt(b,O,E)}}const At={name:"i18n-n",props:X({value:{type:Number,required:!0},format:{type:[String,Object]}},rt),setup(e,t){const n=e.i18n||at({useScope:"parent",__useComponent:!0});return Kt(e,t,$t,(...a)=>n[Qe](...a))}},St={name:"i18n-d",props:X({value:{type:[Number,Date],required:!0},format:{type:[String,Object]}},rt),setup(e,t){const n=e.i18n||at({useScope:"parent",__useComponent:!0});return Kt(e,t,xt,(...a)=>n[Je](...a))}};function pr(e,t){const n=e;if(e.mode==="composition")return n.__getInstance(t)||e.global;{const a=n.__getInstance(t);return a!=null?a.__composer:e.global.__composer}}function br(e){const t=o=>{const{instance:m,modifiers:i,value:E}=o;if(!m||!m.$)throw Q(B.UNEXPECTED_ERROR);const O=pr(e,m.$),b=yt(E);return[Reflect.apply(O.t,O,[...kt(b)]),O]};return{created:(o,m)=>{const[i,E]=t(m);Ge&&e.global===E&&(o.__i18nWatcher=Xe(E.locale,()=>{m.instance&&m.instance.$forceUpdate()})),o.__composer=E,o.textContent=i},unmounted:o=>{Ge&&o.__i18nWatcher&&(o.__i18nWatcher(),o.__i18nWatcher=void 0,delete o.__i18nWatcher),o.__composer&&(o.__composer=void 0,delete o.__composer)},beforeUpdate:(o,{value:m})=>{if(o.__composer){const i=o.__composer,E=yt(m);o.textContent=Reflect.apply(i.t,i,[...kt(E)])}},getSSRProps:o=>{const[m]=t(o);return{textContent:m}}}}function yt(e){if(C(e))return{path:e};if(R(e)){if(!("path"in e))throw Q(B.REQUIRED_VALUE,"path");return e}else throw Q(B.INVALID_VALUE)}function kt(e){const{path:t,locale:n,args:a,choice:l,plural:s}=e,o={},m=a||{};return C(n)&&(o.locale=n),x(l)&&(o.plural=l),x(s)&&(o.plural=s),[t,m,o]}function Tr(e,t,...n){const a=R(n[0])?n[0]:{},l=!!a.useI18nComponentName;(U(a.globalInstall)?a.globalInstall:!0)&&(e.component(l?"i18n":Ot.name,Ot),e.component(At.name,At),e.component(St.name,St)),e.directive("t",br(t))}const hr=ae("global-vue-i18n");function vr(e={},t){const n=U(e.globalInjection)?e.globalInjection:!0,a=!0,l=new Map,[s,o]=Ir(e),m=ae("");function i(b){return l.get(b)||null}function E(b,N){l.set(b,N)}function O(b){l.delete(b)}{const b={get mode(){return"composition"},get allowComposition(){return a},async install(N,...y){N.__VUE_I18N_SYMBOL__=m,N.provide(N.__VUE_I18N_SYMBOL__,b),n&&Dr(N,b.global),Tr(N,b,...y);const P=N.unmount;N.unmount=()=>{b.dispose(),P()}},get global(){return o},dispose(){s.stop()},__instances:l,__getInstance:i,__setInstance:E,__deleteInstance:O};return b}}function at(e={}){const t=Dt();if(t==null)throw Q(B.MUST_BE_CALL_SETUP_TOP);if(!t.isCE&&t.appContext.app!=null&&!t.appContext.app.__VUE_I18N_SYMBOL__)throw Q(B.NOT_INSLALLED);const n=Cr(t),a=Ar(n),l=Er(t),s=Or(e,l);if(s==="global")return gr(a,e,l),a;if(s==="parent"){let i=Sr(n,t,e.__useComponent);return i==null&&(i=a),i}const o=n;let m=o.__getInstance(t);if(m==null){const i=X({},e);"__i18n"in l&&(i.__i18n=l.__i18n),a&&(i.__root=a),m=jt(i),yr(o,t),o.__setInstance(t,m)}return m}function Ir(e,t,n){const a=Bt();{const l=a.run(()=>jt(e));if(l==null)throw Q(B.UNEXPECTED_ERROR);return[a,l]}}function Cr(e){{const t=Jt(e.isCE?hr:e.appContext.app.__VUE_I18N_SYMBOL__);if(!t)throw Q(e.isCE?B.NOT_INSLALLED_WITH_PROVIDE:B.UNEXPECTED_ERROR);return t}}function Or(e,t){return ke(e)?"__i18n"in t?"local":"global":e.useScope?e.useScope:"local"}function Ar(e){return e.mode==="composition"?e.global:e.global.__composer}function Sr(e,t,n=!1){let a=null;const l=t.root;let s=t.parent;for(;s!=null;){const o=e;if(e.mode==="composition"&&(a=o.__getInstance(s)),a!=null||l===s)break;s=s.parent}return a}function yr(e,t,n){Qt(()=>{},t),qt(()=>{e.__deleteInstance(t)},t)}const kr=["locale","fallbackLocale","availableLocales"],Pr=["t","rt","d","n","tm"];function Dr(e,t){const n=Object.create(null);kr.forEach(a=>{const l=Object.getOwnPropertyDescriptor(t,a);if(!l)throw Q(B.UNEXPECTED_ERROR);const s=zt(l.value)?{get(){return l.value.value},set(o){l.value.value=o}}:{get(){return l.get&&l.get()}};Object.defineProperty(n,a,s)}),e.config.globalProperties.$i18n=n,Pr.forEach(a=>{const l=Object.getOwnPropertyDescriptor(t,a);if(!l||!l.value)throw Q(B.UNEXPECTED_ERROR);Object.defineProperty(e.config.globalProperties,`$${a}`,l)})}nr(cr);rr(Vn);ar(Ft);export{vr as c,at as u};
