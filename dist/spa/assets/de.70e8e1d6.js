var n={isoName:"de",nativeName:"<PERSON><PERSON><PERSON>",label:{clear:"<PERSON><PERSON>",ok:"Ok",cancel:"<PERSON><PERSON><PERSON><PERSON>",close:"<PERSON><PERSON><PERSON>\xDFen",set:"<PERSON><PERSON>",select:"Ausw\xE4hlen",reset:"Zur\xFCcksetzen",remove:"L\xF6schen",update:"Aktualisieren",create:"<PERSON><PERSON>elle<PERSON>",search:"Suche",filter:"Filter",refresh:"Aktualisieren",expand:e=>e?`Erweitern Sie "${e}"`:"Erweitern",collapse:e=>e?`"${e}" minimieren`:"<PERSON>usammenbruch"},date:{days:"Sonntag_Montag_Dienstag_Mittwoch_Donnerstag_Freitag_Samstag".split("_"),daysShort:"So_Mo_Di_Mi_Do_Fr_Sa".split("_"),months:"Januar_Februar_M\xE4rz_April_Mai_Juni_Juli_August_September_Oktober_November_Dezember".split("_"),monthsShort:"Jan_Feb_Mar_Apr_Mai_Jun_Jul_Aug_Sep_Okt_Nov_Dez".split("_"),firstDayOfWeek:1,format24h:!0,pluralDay:"Tage"},table:{noData:"Keine Daten vorhanden.",noResults:"Keine Eintr\xE4ge gefunden",loading:"Lade...",selectedRecords:e=>e>1?e+" ausgew\xE4hlte Zeilen":(e===0?"Keine":"1")+" ausgew\xE4hlt.",recordsPerPage:"Zeilen pro Seite",allRows:"Alle",pagination:(e,r,t)=>e+"-"+r+" von "+t,columns:"Spalten"},editor:{url:"URL",bold:"Fett",italic:"Kursiv",strikethrough:"Durchgestrichen",underline:"Unterstrichen",unorderedList:"Ungeordnete Liste",orderedList:"Geordnete Liste",subscript:"tiefgestellt",superscript:"hochgestellt",hyperlink:"Link",toggleFullscreen:"Vollbild umschalten",quote:"Zitat",left:"linksb\xFCndig",center:"zentriert",right:"rechtsb\xFCndig",justify:"Ausrichten",print:"Drucken",outdent:"ausr\xFCcken",indent:"einr\xFCcken",removeFormat:"Entferne Formatierung",formatting:"Formatiere",fontSize:"Schriftgr\xF6\xDFe",align:"Ausrichten",hr:"Horizontale Linie einf\xFCgen",undo:"R\xFCckg\xE4nging",redo:"Wiederherstellen",heading1:"\xDCberschrift 1",heading2:"\xDCberschrift 2",heading3:"\xDCberschrift 3",heading4:"\xDCberschrift 4",heading5:"\xDCberschrift 5",heading6:"\xDCberschrift 6",paragraph:"Absatz",code:"Code",size1:"Sehr klein",size2:"klein",size3:"Normal",size4:"Gro\xDF",size5:"Gr\xF6\xDFer",size6:"Sehr gro\xDF",size7:"Maximum",defaultFont:"Standard Schrift",viewSource:"Quelltext anzeigen"},tree:{noNodes:"Keine Knoten verf\xFCgbar",noResults:"Keine passenden Knoten gefunden"}};export{n as default};
