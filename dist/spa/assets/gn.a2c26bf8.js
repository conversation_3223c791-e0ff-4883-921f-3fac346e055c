var o={isoName:"gn",nativeName:"Ava\xF1e'\u1EBD",label:{clear:"Mbogue",ok:"O\u0128ma",cancel:"<PERSON>celar",close:"<PERSON><PERSON><PERSON>",set:"<PERSON><PERSON><PERSON>",select:"Pora<PERSON>",reset:"<PERSON>\u0128 jey",remove:"Juka",update:"Actualisa",create:"Jap<PERSON>",search:"Heka",filter:"Mbogua",refresh:"Actualisa",expand:e=>e?`O\xF1embotuichave "${e}"`:"Myas\xE3i",collapse:e=>e?`Omboguejy "${e}"`:"\xD1embyaipa"},date:{days:"Domingo_Lunes_Martes_Mi\xE9rcoles_Jueves_Viernes_S\xE1bado".split("_"),daysShort:"Dom_Lun_Mar_Mi\xE9_Jue_Vie_S\xE1b".split("_"),months:"Enero_Febrero_Marzo_Abril_Mayo_Junio_Julio_Agosto_Septiembre_Octubre_Noviembre_Diciembre".split("_"),monthsShort:"Ene_Feb_Mar_Abr_May_Jun_Jul_Ago_Sep_Oct_Nov_Dic".split("_"),firstDayOfWeek:1,format24h:!0,pluralDay:"\xE1ra"},table:{noData:"Ni pete\u0128 mba'e",noResults:"Ni pete\u0128 resultado",loading:"Era'ar\xF5...",selectedRecords:e=>e>1?e+" fila selesionada.":(e===0?"Sin":"1")+" fila selesionada.",recordsPerPage:"Fila por p\xE1hina:",allRows:"Entero",pagination:(e,a,i)=>e+"-"+a+" de "+i,columns:"Columnaku\xE9ra"},editor:{url:"URL",bold:"Negrita",italic:"Kurs\xEDva",strikethrough:"Tachado",underline:"Subrayado",unorderedList:"Lista Desordenada",orderedList:"Lista Ordenada",subscript:"Sub\xEDndice",superscript:"Super\xEDndice",hyperlink:"Hiperv\xEDnculo",toggleFullscreen:"Pantalla kompl\xE9ta",quote:"Cita",left:"Alineaci\xF3n izquierda",center:"Alineaci\xF3n centro",right:"Alineaci\xF3n derecha",justify:"Justificar alineaci\xF3n",print:"Imprimir",outdent:"Disminuir indentaci\xF3n",indent:"Aumentar indentaci\xF3n",removeFormat:"Eliminar formato",formatting:"Formato",fontSize:"Tama\xF1o de Fuente",align:"Alinear",hr:"Insertar l\xEDnea horizontal",undo:"Deshacer",redo:"Rehacer",heading1:"Encabezado 1",heading2:"Encabezado 2",heading3:"Encabezado 3",heading4:"Encabezado 4",heading5:"Encabezado 5",heading6:"Encabezado 6",paragraph:"P\xE1rrafo",code:"C\xF3digo",size1:"Muy peque\xF1o",size2:"Peque\xF1o",size3:"Normal",size4:"Mediano",size5:"Grande",size6:"Muy grande",size7:"M\xE1ximo",defaultFont:"Fuente por defecto",viewSource:"View Source"},tree:{noNodes:"Sin nodos disponibles",noResults:"No se encontraron nodos correspondientes"}};export{o as default};
