import{v as S,c as z,h as C}from"./QBtn.c6cd36c1.js";import{w as g,o as H,g as T,z as h,c as U,h as _}from"./index.c6ba88b2.js";const Q={modelValue:{type:Boolean,default:null},"onUpdate:modelValue":[Function,Array]},w=["beforeShow","show","beforeHide","hide"];function A({showing:o,canShow:r,hideOnRouteChange:s,handleShow:n,handleHide:d,processOnMount:M}){const f=T(),{props:l,emit:t,proxy:c}=f;let a;function y(e){o.value===!0?i(e):p(e)}function p(e){if(l.disable===!0||e!==void 0&&e.qAnchorHandled===!0||r!==void 0&&r(e)!==!0)return;const u=l["onUpdate:modelValue"]!==void 0;u===!0&&(t("update:modelValue",!0),a=e,h(()=>{a===e&&(a=void 0)})),(l.modelValue===null||u===!1)&&m(e)}function m(e){o.value!==!0&&(o.value=!0,t("beforeShow",e),n!==void 0?n(e):t("show",e))}function i(e){if(l.disable===!0)return;const u=l["onUpdate:modelValue"]!==void 0;u===!0&&(t("update:modelValue",!1),a=e,h(()=>{a===e&&(a=void 0)})),(l.modelValue===null||u===!1)&&V(e)}function V(e){o.value!==!1&&(o.value=!1,t("beforeHide",e),d!==void 0?d(e):t("hide",e))}function v(e){l.disable===!0&&e===!0?l["onUpdate:modelValue"]!==void 0&&t("update:modelValue",!1):e===!0!==o.value&&(e===!0?m:V)(a)}g(()=>l.modelValue,v),s!==void 0&&S(f)===!0&&g(()=>c.$route.fullPath,()=>{s.value===!0&&o.value===!0&&i()}),M===!0&&H(()=>{v(l.modelValue)});const b={show:p,hide:i,toggle:y};return Object.assign(c,b),b}var B=z({name:"QCardSection",props:{tag:{type:String,default:"div"},horizontal:Boolean},setup(o,{slots:r}){const s=U(()=>`q-card__section q-card__section--${o.horizontal===!0?"horiz row no-wrap":"vert"}`);return()=>_(o.tag,{class:s.value},C(r.default))}});export{B as Q,w as a,A as b,Q as u};
