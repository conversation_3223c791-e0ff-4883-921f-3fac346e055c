import{r as y,D as z,o as c,k as m,n as w,z as g,h as R,g as O,E as h,aa as E}from"./index.c6ba88b2.js";import{c as T}from"./QBtn.c6cd36c1.js";function L(){const t=y(!z.value);return t.value===!1&&c(()=>{t.value=!0}),t}const b=typeof ResizeObserver!="undefined",p=b===!0?{}:{style:"display:block;position:absolute;top:0;left:0;right:0;bottom:0;height:100%;width:100%;overflow:hidden;pointer-events:none;z-index:-1;",url:"about:blank"};var x=T({name:"QResizeObserver",props:{debounce:{type:[String,Number],default:100}},emits:["resize"],setup(t,{emit:e}){let i=null,r,s={width:-1,height:-1};function u(n){n===!0||t.debounce===0||t.debounce==="0"?l():i===null&&(i=setTimeout(l,t.debounce))}function l(){if(i!==null&&(clearTimeout(i),i=null),r){const{offsetWidth:n,offsetHeight:o}=r;(n!==s.width||o!==s.height)&&(s={width:n,height:o},e("resize",s))}}const{proxy:d}=O();if(b===!0){let n;const o=a=>{r=d.$el.parentNode,r?(n=new ResizeObserver(u),n.observe(r),l()):a!==!0&&g(()=>{o(!0)})};return c(()=>{o()}),m(()=>{i!==null&&clearTimeout(i),n!==void 0&&(n.disconnect!==void 0?n.disconnect():r&&n.unobserve(r))}),w}else{let a=function(){i!==null&&(clearTimeout(i),i=null),o!==void 0&&(o.removeEventListener!==void 0&&o.removeEventListener("resize",u,h.passive),o=void 0)},v=function(){a(),r&&r.contentDocument&&(o=r.contentDocument.defaultView,o.addEventListener("resize",u,h.passive),l())};const n=L();let o;return c(()=>{g(()=>{r=d.$el,r&&v()})}),m(a),d.trigger=u,()=>{if(n.value===!0)return R("object",{style:p.style,tabindex:-1,type:"text/html",data:p.url,"aria-hidden":"true",onLoad:v})}}}});const f={left:!0,right:!0,up:!0,down:!0,horizontal:!0,vertical:!0},k=Object.keys(f);f.all=!0;function A(t){const e={};for(const i of k)t[i]===!0&&(e[i]=!0);return Object.keys(e).length===0?f:(e.horizontal===!0?e.left=e.right=!0:e.left===!0&&e.right===!0&&(e.horizontal=!0),e.vertical===!0?e.up=e.down=!0:e.up===!0&&e.down===!0&&(e.vertical=!0),e.horizontal===!0&&e.vertical===!0&&(e.all=!0),e)}const C=["INPUT","TEXTAREA"];function D(t,e){return e.event===void 0&&t.target!==void 0&&t.target.draggable!==!0&&typeof e.handler=="function"&&C.includes(t.target.nodeName.toUpperCase())===!1&&(t.qClonedBy===void 0||t.qClonedBy.indexOf(e.uid)===-1)}function j(){if(window.getSelection!==void 0){const t=window.getSelection();t.empty!==void 0?t.empty():t.removeAllRanges!==void 0&&(t.removeAllRanges(),E.is.mobile!==!0&&t.addRange(document.createRange()))}else document.selection!==void 0&&document.selection.empty()}export{x as Q,j as c,A as g,D as s};
