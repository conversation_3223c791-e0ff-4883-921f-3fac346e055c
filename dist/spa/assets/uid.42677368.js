let i,n=0;const o=new Array(256);for(let t=0;t<256;t++)o[t]=(t+256).toString(16).substring(1);const u=(()=>{const t=typeof crypto!="undefined"?crypto:typeof window!="undefined"?window.crypto||window.msCrypto:void 0;if(t!==void 0){if(t.randomBytes!==void 0)return t.randomBytes;if(t.getRandomValues!==void 0)return e=>{const r=new Uint8Array(e);return t.getRandomValues(r),r}}return e=>{const r=[];for(let d=e;d>0;d--)r.push(Math.floor(Math.random()*256));return r}})(),s=4096;function f(){(i===void 0||n+16>s)&&(n=0,i=u(s));const t=Array.prototype.slice.call(i,n,n+=16);return t[6]=t[6]&15|64,t[8]=t[8]&63|128,o[t[0]]+o[t[1]]+o[t[2]]+o[t[3]]+"-"+o[t[4]]+o[t[5]]+"-"+o[t[6]]+o[t[7]]+"-"+o[t[8]]+o[t[9]]+"-"+o[t[10]]+o[t[11]]+o[t[12]]+o[t[13]]+o[t[14]]+o[t[15]]}export{f as u};
