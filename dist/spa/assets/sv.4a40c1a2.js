var t={isoName:"sv",nativeName:"<PERSON><PERSON>",label:{clear:"<PERSON><PERSON>",ok:"OK",cancel:"<PERSON><PERSON><PERSON><PERSON><PERSON>",close:"St\xE4ng",set:"S\xE4tt",select:"V\xE4lj",reset:"Nollst\xE4ll",remove:"Ta bort",update:"Uppdatera",create:"Ska<PERSON>",search:"S\xF6k",filter:"<PERSON><PERSON>rera",refresh:"Uppdatera",expand:e=>e?`Ut\xF6ka "${e}"`:"Bygga ut",collapse:e=>e?`Komprimera "${e}"`:"Kollaps"},date:{days:"S\xF6ndag_M\xE5ndag_Tisdag_Onsdag_Torsdag_Fredag_L\xF6rdag".split("_"),daysShort:"S\xF6n_M\xE5n_Tis_Ons_Tor_Fre_L\xF6r".split("_"),months:"<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_<PERSON>_April_Maj_Juni_Juli_Augusti_September_Oktober_November_December".split("_"),monthsShort:"Jan_Feb_Mar_Apr_Maj_Jun_Jul_Aug_Sep_Okt_Nov_Dec".split("_"),firstDayOfWeek:1,format24h:!0,pluralDay:"dagar"},table:{noData:"Ingen data tillg\xE4nglig",noResults:"Inget resultat matchar",loading:"Laddar...",selectedRecords:e=>e===1?"1 vald rad.":(e===0?"Inga":e)+" valda rader.",recordsPerPage:"Rader per sida:",allRows:"Alla",pagination:(e,r,a)=>e+"-"+r+" av "+a,columns:"Kolumner"},editor:{url:"URL",bold:"Fet",italic:"Kursiv",strikethrough:"Genomstruken",underline:"Understruken",unorderedList:"Punktlista",orderedList:"Numrerad lista",subscript:"Neds\xE4nkt",superscript:"Upph\xF6jt",hyperlink:"L\xE4nk",toggleFullscreen:"V\xE4xla helsk\xE4rm",quote:"Citat",left:"V\xE4nsterjustera",center:"Centrera",right:"H\xF6gerjustera",justify:"Justera",print:"Skriv ut",outdent:"Minska indrag",indent:"\xD6ka indrag",removeFormat:"Ta bort formatering",formatting:"Formatering",fontSize:"Teckenstorlek",align:"Justera",hr:"Infoga v\xE5gr\xE4t linje",undo:"\xC5ngra",redo:"G\xF6r om",heading1:"Rubrik 1",heading2:"Rubrik 2",heading3:"Rubrik 3",heading4:"Rubrik 4",heading5:"Rubrik 5",heading6:"Rubrik 6",paragraph:"Stycke",code:"Kod",size1:"V\xE4ldigt liten",size2:"Liten",size3:"Normal",size4:"St\xF6rre \xE4n normal",size5:"Stor",size6:"V\xE4ldigt stor",size7:"Maximalt stor",defaultFont:"Standardteckensnitt",viewSource:"Visa k\xE4lla"},tree:{noNodes:"Inga noder tillg\xE4ngliga",noResults:"Inga noder matchar"}};export{t as default};
