import{c as ve,n as J,o as Fe,p as F,h as De,q as Me,b as ze}from"./QBtn.c6cd36c1.js";import{h as g,k as he,a4 as ee,m as f,E as h,y as Oe,a5 as We,c as w,a6 as te,r as L,a as Ae,a7 as Ke,a8 as Ne,a3 as pe,w as oe,Z as ne,g as Qe}from"./index.c6ba88b2.js";import{u as Ve,a as Re}from"./use-tick.9c8d097a.js";import{u as Ie,a as Xe,b as Ye}from"./QCardSection.d8eae2a5.js";const je=g("div",{class:"q-space"});var Pt=ve({name:"QSpace",setup(){return()=>je}});function Ge(e,t,o){let n;function l(){n!==void 0&&(ee.remove(n),n=void 0)}return he(()=>{e.value===!0&&l()}),{removeFromHistory:l,addToHistory(){n={condition:()=>o.value===!0,handler:t},ee.add(n)}}}const Ue=[null,document,document.body,document.scrollingElement,document.documentElement];function _t(e,t){let o=Fe(t);if(o===void 0){if(e==null)return window;o=e.closest(".scroll,.scroll-y,.overflow-auto")}return Ue.includes(o)?window:o}function Ze(e){return e===window?window.pageYOffset||window.scrollY||document.body.scrollTop||0:e.scrollTop}function Je(e){return e===window?window.pageXOffset||window.scrollX||document.body.scrollLeft||0:e.scrollLeft}let H;function kt(){if(H!==void 0)return H;const e=document.createElement("p"),t=document.createElement("div");J(e,{width:"100%",height:"200px"}),J(t,{position:"absolute",top:"0px",left:"0px",visibility:"hidden",width:"200px",height:"150px",overflow:"hidden"}),t.appendChild(e),document.body.appendChild(t);const o=e.offsetWidth;t.style.overflow="scroll";let n=e.offsetWidth;return o===n&&(n=t.clientWidth),t.remove(),H=o-n,H}function et(e,t=!0){return!e||e.nodeType!==Node.ELEMENT_NODE?!1:t?e.scrollHeight>e.clientHeight&&(e.classList.contains("scroll")||e.classList.contains("overflow-auto")||["auto","scroll"].includes(window.getComputedStyle(e)["overflow-y"])):e.scrollWidth>e.clientWidth&&(e.classList.contains("scroll")||e.classList.contains("overflow-auto")||["auto","scroll"].includes(window.getComputedStyle(e)["overflow-x"]))}let S=0,N,Q,x,V=!1,ie,le,se,p=null;function tt(e){ot(e)&&Oe(e)}function ot(e){if(e.target===document.body||e.target.classList.contains("q-layout__backdrop"))return!0;const t=We(e),o=e.shiftKey&&!e.deltaX,n=!o&&Math.abs(e.deltaX)<=Math.abs(e.deltaY),l=o||n?e.deltaY:e.deltaX;for(let r=0;r<t.length;r++){const s=t[r];if(et(s,n))return n?l<0&&s.scrollTop===0?!0:l>0&&s.scrollTop+s.clientHeight===s.scrollHeight:l<0&&s.scrollLeft===0?!0:l>0&&s.scrollLeft+s.clientWidth===s.scrollWidth}return!0}function re(e){e.target===document&&(document.scrollingElement.scrollTop=document.scrollingElement.scrollTop)}function $(e){V!==!0&&(V=!0,requestAnimationFrame(()=>{V=!1;const{height:t}=e.target,{clientHeight:o,scrollTop:n}=document.scrollingElement;(x===void 0||t!==window.innerHeight)&&(x=o-t,document.scrollingElement.scrollTop=n),n>x&&(document.scrollingElement.scrollTop-=Math.ceil((n-x)/8))}))}function ae(e){const t=document.body,o=window.visualViewport!==void 0;if(e==="add"){const{overflowY:n,overflowX:l}=window.getComputedStyle(t);N=Je(window),Q=Ze(window),ie=t.style.left,le=t.style.top,se=window.location.href,t.style.left=`-${N}px`,t.style.top=`-${Q}px`,l!=="hidden"&&(l==="scroll"||t.scrollWidth>window.innerWidth)&&t.classList.add("q-body--force-scrollbar-x"),n!=="hidden"&&(n==="scroll"||t.scrollHeight>window.innerHeight)&&t.classList.add("q-body--force-scrollbar-y"),t.classList.add("q-body--prevent-scroll"),document.qScrollPrevented=!0,f.is.ios===!0&&(o===!0?(window.scrollTo(0,0),window.visualViewport.addEventListener("resize",$,h.passiveCapture),window.visualViewport.addEventListener("scroll",$,h.passiveCapture),window.scrollTo(0,0)):window.addEventListener("scroll",re,h.passiveCapture))}f.is.desktop===!0&&f.is.mac===!0&&window[`${e}EventListener`]("wheel",tt,h.notPassive),e==="remove"&&(f.is.ios===!0&&(o===!0?(window.visualViewport.removeEventListener("resize",$,h.passiveCapture),window.visualViewport.removeEventListener("scroll",$,h.passiveCapture)):window.removeEventListener("scroll",re,h.passiveCapture)),t.classList.remove("q-body--prevent-scroll"),t.classList.remove("q-body--force-scrollbar-x"),t.classList.remove("q-body--force-scrollbar-y"),document.qScrollPrevented=!1,t.style.left=ie,t.style.top=le,window.location.href===se&&window.scrollTo(N,Q),x=void 0)}function nt(e){let t="add";if(e===!0){if(S++,p!==null){clearTimeout(p),p=null;return}if(S>1)return}else{if(S===0||(S--,S>0))return;if(t="remove",f.is.ios===!0&&f.is.nativeMobile===!0){p!==null&&clearTimeout(p),p=setTimeout(()=>{ae(t),p=null},100);return}}ae(t)}function it(){let e;return{preventBodyScroll(t){t!==e&&(e!==void 0||t===!0)&&(e=t,nt(t))}}}const lt={transitionShow:{type:String,default:"fade"},transitionHide:{type:String,default:"fade"},transitionDuration:{type:[String,Number],default:300}};function st(e,t=()=>{},o=()=>{}){return{transitionProps:w(()=>{const n=`q-transition--${e.transitionShow||t()}`,l=`q-transition--${e.transitionHide||o()}`;return{appear:!0,enterFromClass:`${n}-enter-from`,enterActiveClass:`${n}-enter-active`,enterToClass:`${n}-enter-to`,leaveFromClass:`${l}-leave-from`,leaveActiveClass:`${l}-leave-active`,leaveToClass:`${l}-leave-to`}}),transitionStyle:w(()=>`--q-transition-duration: ${e.transitionDuration}ms`)}}let C=[],P=[];function ge(e){P=P.filter(t=>t!==e)}function rt(e){ge(e),P.push(e)}function ue(e){ge(e),P.length===0&&C.length!==0&&(C[C.length-1](),C=[])}function at(e){P.length===0?e():C.push(e)}let ut=1,ct=document.body;function dt(e,t){const o=document.createElement("div");if(o.id=t!==void 0?`q-portal--${t}--${ut++}`:e,te.globalNodes!==void 0){const n=te.globalNodes.class;n!==void 0&&(o.className=n)}return ct.appendChild(o),o}function ft(e){e.remove()}const D=[];function mt(e){return D.find(t=>t.contentEl!==null&&t.contentEl.contains(e))}function vt(e,t){do{if(e.$options.name==="QMenu"){if(e.hide(t),e.$props.separateClosePopup===!0)return F(e)}else if(e.__qPortal===!0){const o=F(e);return o!==void 0&&o.$options.name==="QPopupProxy"?(e.hide(t),o):e}e=F(e)}while(e!=null)}function ht(e,t,o){for(;o!==0&&e!==void 0&&e!==null;){if(e.__qPortal===!0){if(o--,e.$options.name==="QMenu"){e=vt(e,t);continue}e.hide(t)}e=F(e)}}function pt(e){for(e=e.parent;e!=null;){if(e.type.name==="QGlobalDialog")return!0;if(e.type.name==="QDialog"||e.type.name==="QMenu")return!1;e=e.parent}return!1}function gt(e,t,o,n){const l=L(!1),r=L(!1);let s=null;const c={},u=n==="dialog"&&pt(e);function d(v){if(v===!0){ue(c),r.value=!0;return}r.value=!1,l.value===!1&&(u===!1&&s===null&&(s=dt(!1,n)),l.value=!0,D.push(e.proxy),rt(c))}function m(v){if(r.value=!1,v!==!0)return;ue(c),l.value=!1;const q=D.indexOf(e.proxy);q!==-1&&D.splice(q,1),s!==null&&(ft(s),s=null)}return Ae(()=>{m(!0)}),e.proxy.__qPortal=!0,Ke(e.proxy,"contentEl",()=>t.value),{showPortal:d,hidePortal:m,portalIsActive:l,portalIsAccessible:r,renderPortal:()=>u===!0?o():l.value===!0?[g(Ne,{to:s},o())]:void 0}}const y=[];let E;function wt(e){E=e.keyCode===27}function yt(){E===!0&&(E=!1)}function bt(e){E===!0&&(E=!1,pe(e,27)===!0&&y[y.length-1](e))}function we(e){window[e]("keydown",wt),window[e]("blur",yt),window[e]("keyup",bt),E=!1}function Et(e){f.is.desktop===!0&&(y.push(e),y.length===1&&we("addEventListener"))}function ce(e){const t=y.indexOf(e);t>-1&&(y.splice(t,1),y.length===0&&we("removeEventListener"))}const b=[];function ye(e){b[b.length-1](e)}function qt(e){f.is.desktop===!0&&(b.push(e),b.length===1&&document.body.addEventListener("focusin",ye))}function de(e){const t=b.indexOf(e);t>-1&&(b.splice(t,1),b.length===0&&document.body.removeEventListener("focusin",ye))}let B=0;const Tt={standard:"fixed-full flex-center",top:"fixed-top justify-center",bottom:"fixed-bottom justify-center",right:"fixed-right items-center",left:"fixed-left items-center"},fe={standard:["scale","scale"],top:["slide-down","slide-up"],bottom:["slide-up","slide-down"],right:["slide-left","slide-right"],left:["slide-right","slide-left"]};var Ht=ve({name:"QDialog",inheritAttrs:!1,props:{...Ie,...lt,transitionShow:String,transitionHide:String,persistent:Boolean,autoClose:Boolean,allowFocusOutside:Boolean,noEscDismiss:Boolean,noBackdropDismiss:Boolean,noRouteDismiss:Boolean,noRefocus:Boolean,noFocus:Boolean,noShake:Boolean,seamless:Boolean,maximized:Boolean,fullWidth:Boolean,fullHeight:Boolean,square:Boolean,position:{type:String,default:"standard",validator:e=>e==="standard"||["top","bottom","left","right"].includes(e)}},emits:[...Xe,"shake","click","escapeKey"],setup(e,{slots:t,emit:o,attrs:n}){const l=Qe(),r=L(null),s=L(!1),c=L(!1);let u=null,d=null,m,v;const q=w(()=>e.persistent!==!0&&e.noRouteDismiss!==!0&&e.seamless!==!0),{preventBodyScroll:R}=it(),{registerTimeout:I}=Ve(),{registerTick:be,removeTick:X}=Re(),{transitionProps:Ee,transitionStyle:Y}=st(e,()=>fe[e.position][0],()=>fe[e.position][1]),{showPortal:j,hidePortal:G,portalIsAccessible:qe,renderPortal:Te}=gt(l,r,Be,"dialog"),{hide:_}=Ye({showing:s,hideOnRouteChange:q,handleShow:_e,handleHide:ke,processOnMount:!0}),{addToHistory:Se,removeFromHistory:xe}=Ge(s,_,q),Ce=w(()=>`q-dialog__inner flex no-pointer-events q-dialog__inner--${e.maximized===!0?"maximized":"minimized"} q-dialog__inner--${e.position} ${Tt[e.position]}`+(c.value===!0?" q-dialog__inner--animating":"")+(e.fullWidth===!0?" q-dialog__inner--fullwidth":"")+(e.fullHeight===!0?" q-dialog__inner--fullheight":"")+(e.square===!0?" q-dialog__inner--square":"")),k=w(()=>s.value===!0&&e.seamless!==!0),Le=w(()=>e.autoClose===!0?{onClick:He}:{}),Pe=w(()=>[`q-dialog fullscreen no-pointer-events q-dialog--${k.value===!0?"modal":"seamless"}`,n.class]);oe(()=>e.maximized,i=>{s.value===!0&&O(i)}),oe(k,i=>{R(i),i===!0?(qt(W),Et(z)):(de(W),ce(z))});function _e(i){Se(),d=e.noRefocus===!1&&document.activeElement!==null?document.activeElement:null,O(e.maximized),j(),c.value=!0,e.noFocus!==!0?(document.activeElement!==null&&document.activeElement.blur(),be(T)):X(),I(()=>{if(l.proxy.$q.platform.is.ios===!0){if(e.seamless!==!0&&document.activeElement){const{top:a,bottom:A}=document.activeElement.getBoundingClientRect(),{innerHeight:Z}=window,K=window.visualViewport!==void 0?window.visualViewport.height:Z;a>0&&A>K/2&&(document.scrollingElement.scrollTop=Math.min(document.scrollingElement.scrollHeight-K,A>=Z?1/0:Math.ceil(document.scrollingElement.scrollTop+A-K/2))),document.activeElement.scrollIntoView()}v=!0,r.value.click(),v=!1}j(!0),c.value=!1,o("show",i)},e.transitionDuration)}function ke(i){X(),xe(),U(!0),c.value=!0,G(),d!==null&&(((i&&i.type.indexOf("key")===0?d.closest('[tabindex]:not([tabindex^="-"])'):void 0)||d).focus(),d=null),I(()=>{G(!0),c.value=!1,o("hide",i)},e.transitionDuration)}function T(i){at(()=>{let a=r.value;a===null||a.contains(document.activeElement)===!0||(a=(i!==""?a.querySelector(i):null)||a.querySelector("[autofocus][tabindex], [data-autofocus][tabindex]")||a.querySelector("[autofocus] [tabindex], [data-autofocus] [tabindex]")||a.querySelector("[autofocus], [data-autofocus]")||a,a.focus({preventScroll:!0}))})}function M(i){i&&typeof i.focus=="function"?i.focus({preventScroll:!0}):T(),o("shake");const a=r.value;a!==null&&(a.classList.remove("q-animate--scale"),a.classList.add("q-animate--scale"),u!==null&&clearTimeout(u),u=setTimeout(()=>{u=null,r.value!==null&&(a.classList.remove("q-animate--scale"),T())},170))}function z(){e.seamless!==!0&&(e.persistent===!0||e.noEscDismiss===!0?e.maximized!==!0&&e.noShake!==!0&&M():(o("escapeKey"),_()))}function U(i){u!==null&&(clearTimeout(u),u=null),(i===!0||s.value===!0)&&(O(!1),e.seamless!==!0&&(R(!1),de(W),ce(z))),i!==!0&&(d=null)}function O(i){i===!0?m!==!0&&(B<1&&document.body.classList.add("q-body--dialog"),B++,m=!0):m===!0&&(B<2&&document.body.classList.remove("q-body--dialog"),B--,m=!1)}function He(i){v!==!0&&(_(i),o("click",i))}function $e(i){e.persistent!==!0&&e.noBackdropDismiss!==!0?_(i):e.noShake!==!0&&M()}function W(i){e.allowFocusOutside!==!0&&qe.value===!0&&Me(r.value,i.target)!==!0&&T('[tabindex]:not([tabindex="-1"])')}Object.assign(l.proxy,{focus:T,shake:M,__updateRefocusTarget(i){d=i||null}}),he(U);function Be(){return g("div",{role:"dialog","aria-modal":k.value===!0?"true":"false",...n,class:Pe.value},[g(ne,{name:"q-transition--fade",appear:!0},()=>k.value===!0?g("div",{class:"q-dialog__backdrop fixed-full",style:Y.value,"aria-hidden":"true",tabindex:-1,onClick:$e}):null),g(ne,Ee.value,()=>s.value===!0?g("div",{ref:r,class:Ce.value,style:Y.value,tabindex:-1,...Le.value},De(t.default)):null)])}return Te}});function me(e){if(e===!1)return 0;if(e===!0||e===void 0)return 1;const t=parseInt(e,10);return isNaN(t)?0:t}var $t=ze({name:"close-popup",beforeMount(e,{value:t}){const o={depth:me(t),handler(n){o.depth!==0&&setTimeout(()=>{const l=mt(e);l!==void 0&&ht(l,n,o.depth)})},handlerKey(n){pe(n,13)===!0&&o.handler(n)}};e.__qclosepopup=o,e.addEventListener("click",o.handler),e.addEventListener("keyup",o.handlerKey)},updated(e,{value:t,oldValue:o}){t!==o&&(e.__qclosepopup.depth=me(t))},beforeUnmount(e){const t=e.__qclosepopup;e.removeEventListener("click",t.handler),e.removeEventListener("keyup",t.handlerKey),delete e.__qclosepopup}});export{$t as C,Pt as Q,it as a,Ze as b,Je as c,kt as d,Ht as e,_t as g,Ge as u};
