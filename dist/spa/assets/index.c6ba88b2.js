const Ia=function(){const t=document.createElement("link").relList;return t&&t.supports&&t.supports("modulepreload")?"modulepreload":"preload"}(),ko={},Pa="",ce=function(t,n){return!n||n.length===0?t():Promise.all(n.map(r=>{if(r=`${Pa}${r}`,r in ko)return;ko[r]=!0;const o=r.endsWith(".css"),s=o?'[rel="stylesheet"]':"";if(document.querySelector(`link[href="${r}"]${s}`))return;const i=document.createElement("link");if(i.rel=o?"stylesheet":Ia,o||(i.as="script",i.crossOrigin=""),i.href=r,document.head.appendChild(i),o)return new Promise((a,c)=>{i.addEventListener("load",a),i.addEventListener("error",()=>c(new Error(`Unable to preload CSS for ${r}`)))})})).then(()=>t())};function Gr(e,t){const n=Object.create(null),r=e.split(",");for(let o=0;o<r.length;o++)n[r[o]]=!0;return t?o=>!!n[o.toLowerCase()]:o=>!!n[o]}const ie={},Lt=[],Fe=()=>{},Ra=()=>!1,ka=/^on[^a-z]/,Vn=e=>ka.test(e),Xr=e=>e.startsWith("onUpdate:"),he=Object.assign,Zr=(e,t)=>{const n=e.indexOf(t);n>-1&&e.splice(n,1)},Aa=Object.prototype.hasOwnProperty,X=(e,t)=>Aa.call(e,t),q=Array.isArray,Nt=e=>yn(e)==="[object Map]",Qs=e=>yn(e)==="[object Set]",Ma=e=>yn(e)==="[object RegExp]",W=e=>typeof e=="function",ue=e=>typeof e=="string",eo=e=>typeof e=="symbol",ae=e=>e!==null&&typeof e=="object",Ys=e=>ae(e)&&W(e.then)&&W(e.catch),Js=Object.prototype.toString,yn=e=>Js.call(e),Ta=e=>yn(e).slice(8,-1),Gs=e=>yn(e)==="[object Object]",to=e=>ue(e)&&e!=="NaN"&&e[0]!=="-"&&""+parseInt(e,10)===e,An=Gr(",key,ref,ref_for,ref_key,onVnodeBeforeMount,onVnodeMounted,onVnodeBeforeUpdate,onVnodeUpdated,onVnodeBeforeUnmount,onVnodeUnmounted"),Wn=e=>{const t=Object.create(null);return n=>t[n]||(t[n]=e(n))},La=/-(\w)/g,We=Wn(e=>e.replace(La,(t,n)=>n?n.toUpperCase():"")),Na=/\B([A-Z])/g,Kt=Wn(e=>e.replace(Na,"-$1").toLowerCase()),Qn=Wn(e=>e.charAt(0).toUpperCase()+e.slice(1)),ur=Wn(e=>e?`on${Qn(e)}`:""),un=(e,t)=>!Object.is(e,t),tn=(e,t)=>{for(let n=0;n<e.length;n++)e[n](t)},Nn=(e,t,n)=>{Object.defineProperty(e,t,{configurable:!0,enumerable:!1,value:n})},Da=e=>{const t=parseFloat(e);return isNaN(t)?e:t},ja=e=>{const t=ue(e)?Number(e):NaN;return isNaN(t)?e:t};let Ao;const kr=()=>Ao||(Ao=typeof globalThis!="undefined"?globalThis:typeof self!="undefined"?self:typeof window!="undefined"?window:typeof global!="undefined"?global:{});function no(e){if(q(e)){const t={};for(let n=0;n<e.length;n++){const r=e[n],o=ue(r)?Ba(r):no(r);if(o)for(const s in o)t[s]=o[s]}return t}else{if(ue(e))return e;if(ae(e))return e}}const Fa=/;(?![^(]*\))/g,$a=/:([^]+)/,Ha=/\/\*[^]*?\*\//g;function Ba(e){const t={};return e.replace(Ha,"").split(Fa).forEach(n=>{if(n){const r=n.split($a);r.length>1&&(t[r[0].trim()]=r[1].trim())}}),t}function ro(e){let t="";if(ue(e))t=e;else if(q(e))for(let n=0;n<e.length;n++){const r=ro(e[n]);r&&(t+=r+" ")}else if(ae(e))for(const n in e)e[n]&&(t+=n+" ");return t.trim()}const za="itemscope,allowfullscreen,formnovalidate,ismap,nomodule,novalidate,readonly",Ua=Gr(za);function Xs(e){return!!e||e===""}const fh=e=>ue(e)?e:e==null?"":q(e)||ae(e)&&(e.toString===Js||!W(e.toString))?JSON.stringify(e,Zs,2):String(e),Zs=(e,t)=>t&&t.__v_isRef?Zs(e,t.value):Nt(t)?{[`Map(${t.size})`]:[...t.entries()].reduce((n,[r,o])=>(n[`${r} =>`]=o,n),{})}:Qs(t)?{[`Set(${t.size})`]:[...t.values()]}:ae(t)&&!q(t)&&!Gs(t)?String(t):t;let Pe;class ei{constructor(t=!1){this.detached=t,this._active=!0,this.effects=[],this.cleanups=[],this.parent=Pe,!t&&Pe&&(this.index=(Pe.scopes||(Pe.scopes=[])).push(this)-1)}get active(){return this._active}run(t){if(this._active){const n=Pe;try{return Pe=this,t()}finally{Pe=n}}}on(){Pe=this}off(){Pe=this.parent}stop(t){if(this._active){let n,r;for(n=0,r=this.effects.length;n<r;n++)this.effects[n].stop();for(n=0,r=this.cleanups.length;n<r;n++)this.cleanups[n]();if(this.scopes)for(n=0,r=this.scopes.length;n<r;n++)this.scopes[n].stop(!0);if(!this.detached&&this.parent&&!t){const o=this.parent.scopes.pop();o&&o!==this&&(this.parent.scopes[this.index]=o,o.index=this.index)}this.parent=void 0,this._active=!1}}}function ti(e){return new ei(e)}function qa(e,t=Pe){t&&t.active&&t.effects.push(e)}function ni(){return Pe}function Ka(e){Pe&&Pe.cleanups.push(e)}const oo=e=>{const t=new Set(e);return t.w=0,t.n=0,t},ri=e=>(e.w&mt)>0,oi=e=>(e.n&mt)>0,Va=({deps:e})=>{if(e.length)for(let t=0;t<e.length;t++)e[t].w|=mt},Wa=e=>{const{deps:t}=e;if(t.length){let n=0;for(let r=0;r<t.length;r++){const o=t[r];ri(o)&&!oi(o)?o.delete(e):t[n++]=o,o.w&=~mt,o.n&=~mt}t.length=n}},Dn=new WeakMap;let Xt=0,mt=1;const Ar=30;let De;const Ct=Symbol(""),Mr=Symbol("");class so{constructor(t,n=null,r){this.fn=t,this.scheduler=n,this.active=!0,this.deps=[],this.parent=void 0,qa(this,r)}run(){if(!this.active)return this.fn();let t=De,n=ft;for(;t;){if(t===this)return;t=t.parent}try{return this.parent=De,De=this,ft=!0,mt=1<<++Xt,Xt<=Ar?Va(this):Mo(this),this.fn()}finally{Xt<=Ar&&Wa(this),mt=1<<--Xt,De=this.parent,ft=n,this.parent=void 0,this.deferStop&&this.stop()}}stop(){De===this?this.deferStop=!0:this.active&&(Mo(this),this.onStop&&this.onStop(),this.active=!1)}}function Mo(e){const{deps:t}=e;if(t.length){for(let n=0;n<t.length;n++)t[n].delete(e);t.length=0}}let ft=!0;const si=[];function Vt(){si.push(ft),ft=!1}function Wt(){const e=si.pop();ft=e===void 0?!0:e}function Oe(e,t,n){if(ft&&De){let r=Dn.get(e);r||Dn.set(e,r=new Map);let o=r.get(n);o||r.set(n,o=oo()),ii(o)}}function ii(e,t){let n=!1;Xt<=Ar?oi(e)||(e.n|=mt,n=!ri(e)):n=!e.has(De),n&&(e.add(De),De.deps.push(e))}function Xe(e,t,n,r,o,s){const i=Dn.get(e);if(!i)return;let a=[];if(t==="clear")a=[...i.values()];else if(n==="length"&&q(e)){const c=Number(r);i.forEach((l,u)=>{(u==="length"||u>=c)&&a.push(l)})}else switch(n!==void 0&&a.push(i.get(n)),t){case"add":q(e)?to(n)&&a.push(i.get("length")):(a.push(i.get(Ct)),Nt(e)&&a.push(i.get(Mr)));break;case"delete":q(e)||(a.push(i.get(Ct)),Nt(e)&&a.push(i.get(Mr)));break;case"set":Nt(e)&&a.push(i.get(Ct));break}if(a.length===1)a[0]&&Tr(a[0]);else{const c=[];for(const l of a)l&&c.push(...l);Tr(oo(c))}}function Tr(e,t){const n=q(e)?e:[...e];for(const r of n)r.computed&&To(r);for(const r of n)r.computed||To(r)}function To(e,t){(e!==De||e.allowRecurse)&&(e.scheduler?e.scheduler():e.run())}function Qa(e,t){var n;return(n=Dn.get(e))==null?void 0:n.get(t)}const Ya=Gr("__proto__,__v_isRef,__isVue"),ai=new Set(Object.getOwnPropertyNames(Symbol).filter(e=>e!=="arguments"&&e!=="caller").map(e=>Symbol[e]).filter(eo)),Ja=io(),Ga=io(!1,!0),Xa=io(!0),Lo=Za();function Za(){const e={};return["includes","indexOf","lastIndexOf"].forEach(t=>{e[t]=function(...n){const r=G(this);for(let s=0,i=this.length;s<i;s++)Oe(r,"get",s+"");const o=r[t](...n);return o===-1||o===!1?r[t](...n.map(G)):o}}),["push","pop","shift","unshift","splice"].forEach(t=>{e[t]=function(...n){Vt();const r=G(this)[t].apply(this,n);return Wt(),r}}),e}function ec(e){const t=G(this);return Oe(t,"has",e),t.hasOwnProperty(e)}function io(e=!1,t=!1){return function(r,o,s){if(o==="__v_isReactive")return!e;if(o==="__v_isReadonly")return e;if(o==="__v_isShallow")return t;if(o==="__v_raw"&&s===(e?t?gc:di:t?fi:ui).get(r))return r;const i=q(r);if(!e){if(i&&X(Lo,o))return Reflect.get(Lo,o,s);if(o==="hasOwnProperty")return ec}const a=Reflect.get(r,o,s);return(eo(o)?ai.has(o):Ya(o))||(e||Oe(r,"get",o),t)?a:fe(a)?i&&to(o)?a:a.value:ae(a)?e?pi(a):It(a):a}}const tc=ci(),nc=ci(!0);function ci(e=!1){return function(n,r,o,s){let i=n[r];if($t(i)&&fe(i)&&!fe(o))return!1;if(!e&&(!jn(o)&&!$t(o)&&(i=G(i),o=G(o)),!q(n)&&fe(i)&&!fe(o)))return i.value=o,!0;const a=q(n)&&to(r)?Number(r)<n.length:X(n,r),c=Reflect.set(n,r,o,s);return n===G(s)&&(a?un(o,i)&&Xe(n,"set",r,o):Xe(n,"add",r,o)),c}}function rc(e,t){const n=X(e,t);e[t];const r=Reflect.deleteProperty(e,t);return r&&n&&Xe(e,"delete",t,void 0),r}function oc(e,t){const n=Reflect.has(e,t);return(!eo(t)||!ai.has(t))&&Oe(e,"has",t),n}function sc(e){return Oe(e,"iterate",q(e)?"length":Ct),Reflect.ownKeys(e)}const li={get:Ja,set:tc,deleteProperty:rc,has:oc,ownKeys:sc},ic={get:Xa,set(e,t){return!0},deleteProperty(e,t){return!0}},ac=he({},li,{get:Ga,set:nc}),ao=e=>e,Yn=e=>Reflect.getPrototypeOf(e);function Sn(e,t,n=!1,r=!1){e=e.__v_raw;const o=G(e),s=G(t);n||(t!==s&&Oe(o,"get",t),Oe(o,"get",s));const{has:i}=Yn(o),a=r?ao:n?uo:fn;if(i.call(o,t))return a(e.get(t));if(i.call(o,s))return a(e.get(s));e!==o&&e.get(t)}function Cn(e,t=!1){const n=this.__v_raw,r=G(n),o=G(e);return t||(e!==o&&Oe(r,"has",e),Oe(r,"has",o)),e===o?n.has(e):n.has(e)||n.has(o)}function xn(e,t=!1){return e=e.__v_raw,!t&&Oe(G(e),"iterate",Ct),Reflect.get(e,"size",e)}function No(e){e=G(e);const t=G(this);return Yn(t).has.call(t,e)||(t.add(e),Xe(t,"add",e,e)),this}function Do(e,t){t=G(t);const n=G(this),{has:r,get:o}=Yn(n);let s=r.call(n,e);s||(e=G(e),s=r.call(n,e));const i=o.call(n,e);return n.set(e,t),s?un(t,i)&&Xe(n,"set",e,t):Xe(n,"add",e,t),this}function jo(e){const t=G(this),{has:n,get:r}=Yn(t);let o=n.call(t,e);o||(e=G(e),o=n.call(t,e)),r&&r.call(t,e);const s=t.delete(e);return o&&Xe(t,"delete",e,void 0),s}function Fo(){const e=G(this),t=e.size!==0,n=e.clear();return t&&Xe(e,"clear",void 0,void 0),n}function On(e,t){return function(r,o){const s=this,i=s.__v_raw,a=G(i),c=t?ao:e?uo:fn;return!e&&Oe(a,"iterate",Ct),i.forEach((l,u)=>r.call(o,c(l),c(u),s))}}function In(e,t,n){return function(...r){const o=this.__v_raw,s=G(o),i=Nt(s),a=e==="entries"||e===Symbol.iterator&&i,c=e==="keys"&&i,l=o[e](...r),u=n?ao:t?uo:fn;return!t&&Oe(s,"iterate",c?Mr:Ct),{next(){const{value:h,done:f}=l.next();return f?{value:h,done:f}:{value:a?[u(h[0]),u(h[1])]:u(h),done:f}},[Symbol.iterator](){return this}}}}function nt(e){return function(...t){return e==="delete"?!1:this}}function cc(){const e={get(s){return Sn(this,s)},get size(){return xn(this)},has:Cn,add:No,set:Do,delete:jo,clear:Fo,forEach:On(!1,!1)},t={get(s){return Sn(this,s,!1,!0)},get size(){return xn(this)},has:Cn,add:No,set:Do,delete:jo,clear:Fo,forEach:On(!1,!0)},n={get(s){return Sn(this,s,!0)},get size(){return xn(this,!0)},has(s){return Cn.call(this,s,!0)},add:nt("add"),set:nt("set"),delete:nt("delete"),clear:nt("clear"),forEach:On(!0,!1)},r={get(s){return Sn(this,s,!0,!0)},get size(){return xn(this,!0)},has(s){return Cn.call(this,s,!0)},add:nt("add"),set:nt("set"),delete:nt("delete"),clear:nt("clear"),forEach:On(!0,!0)};return["keys","values","entries",Symbol.iterator].forEach(s=>{e[s]=In(s,!1,!1),n[s]=In(s,!0,!1),t[s]=In(s,!1,!0),r[s]=In(s,!0,!0)}),[e,n,t,r]}const[lc,uc,fc,dc]=cc();function co(e,t){const n=t?e?dc:fc:e?uc:lc;return(r,o,s)=>o==="__v_isReactive"?!e:o==="__v_isReadonly"?e:o==="__v_raw"?r:Reflect.get(X(n,o)&&o in r?n:r,o,s)}const hc={get:co(!1,!1)},pc={get:co(!1,!0)},mc={get:co(!0,!1)},ui=new WeakMap,fi=new WeakMap,di=new WeakMap,gc=new WeakMap;function vc(e){switch(e){case"Object":case"Array":return 1;case"Map":case"Set":case"WeakMap":case"WeakSet":return 2;default:return 0}}function _c(e){return e.__v_skip||!Object.isExtensible(e)?0:vc(Ta(e))}function It(e){return $t(e)?e:lo(e,!1,li,hc,ui)}function hi(e){return lo(e,!1,ac,pc,fi)}function pi(e){return lo(e,!0,ic,mc,di)}function lo(e,t,n,r,o){if(!ae(e)||e.__v_raw&&!(t&&e.__v_isReactive))return e;const s=o.get(e);if(s)return s;const i=_c(e);if(i===0)return e;const a=new Proxy(e,i===2?r:n);return o.set(e,a),a}function dt(e){return $t(e)?dt(e.__v_raw):!!(e&&e.__v_isReactive)}function $t(e){return!!(e&&e.__v_isReadonly)}function jn(e){return!!(e&&e.__v_isShallow)}function mi(e){return dt(e)||$t(e)}function G(e){const t=e&&e.__v_raw;return t?G(t):e}function wn(e){return Nn(e,"__v_skip",!0),e}const fn=e=>ae(e)?It(e):e,uo=e=>ae(e)?pi(e):e;function gi(e){ft&&De&&(e=G(e),ii(e.dep||(e.dep=oo())))}function vi(e,t){e=G(e);const n=e.dep;n&&Tr(n)}function fe(e){return!!(e&&e.__v_isRef===!0)}function Ht(e){return _i(e,!1)}function bc(e){return _i(e,!0)}function _i(e,t){return fe(e)?e:new yc(e,t)}class yc{constructor(t,n){this.__v_isShallow=n,this.dep=void 0,this.__v_isRef=!0,this._rawValue=n?t:G(t),this._value=n?t:fn(t)}get value(){return gi(this),this._value}set value(t){const n=this.__v_isShallow||jn(t)||$t(t);t=n?t:G(t),un(t,this._rawValue)&&(this._rawValue=t,this._value=n?t:fn(t),vi(this))}}function Dt(e){return fe(e)?e.value:e}const wc={get:(e,t,n)=>Dt(Reflect.get(e,t,n)),set:(e,t,n,r)=>{const o=e[t];return fe(o)&&!fe(n)?(o.value=n,!0):Reflect.set(e,t,n,r)}};function bi(e){return dt(e)?e:new Proxy(e,wc)}function Ec(e){const t=q(e)?new Array(e.length):{};for(const n in e)t[n]=Cc(e,n);return t}class Sc{constructor(t,n,r){this._object=t,this._key=n,this._defaultValue=r,this.__v_isRef=!0}get value(){const t=this._object[this._key];return t===void 0?this._defaultValue:t}set value(t){this._object[this._key]=t}get dep(){return Qa(G(this._object),this._key)}}function Cc(e,t,n){const r=e[t];return fe(r)?r:new Sc(e,t,n)}class xc{constructor(t,n,r,o){this._setter=n,this.dep=void 0,this.__v_isRef=!0,this.__v_isReadonly=!1,this._dirty=!0,this.effect=new so(t,()=>{this._dirty||(this._dirty=!0,vi(this))}),this.effect.computed=this,this.effect.active=this._cacheable=!o,this.__v_isReadonly=r}get value(){const t=G(this);return gi(t),(t._dirty||!t._cacheable)&&(t._dirty=!1,t._value=t.effect.run()),t._value}set value(t){this._setter(t)}}function Oc(e,t,n=!1){let r,o;const s=W(e);return s?(r=e,o=Fe):(r=e.get,o=e.set),new xc(r,o,s||!o,n)}function ht(e,t,n,r){let o;try{o=r?e(...r):e()}catch(s){Jn(s,t,n)}return o}function Me(e,t,n,r){if(W(e)){const s=ht(e,t,n,r);return s&&Ys(s)&&s.catch(i=>{Jn(i,t,n)}),s}const o=[];for(let s=0;s<e.length;s++)o.push(Me(e[s],t,n,r));return o}function Jn(e,t,n,r=!0){const o=t?t.vnode:null;if(t){let s=t.parent;const i=t.proxy,a=n;for(;s;){const l=s.ec;if(l){for(let u=0;u<l.length;u++)if(l[u](e,i,a)===!1)return}s=s.parent}const c=t.appContext.config.errorHandler;if(c){ht(c,null,10,[e,i,a]);return}}Ic(e,n,o,r)}function Ic(e,t,n,r=!0){console.error(e)}let dn=!1,Lr=!1;const be=[];let Ve=0;const jt=[];let Je=null,yt=0;const yi=Promise.resolve();let fo=null;function hn(e){const t=fo||yi;return e?t.then(this?e.bind(this):e):t}function Pc(e){let t=Ve+1,n=be.length;for(;t<n;){const r=t+n>>>1;pn(be[r])<e?t=r+1:n=r}return t}function ho(e){(!be.length||!be.includes(e,dn&&e.allowRecurse?Ve+1:Ve))&&(e.id==null?be.push(e):be.splice(Pc(e.id),0,e),wi())}function wi(){!dn&&!Lr&&(Lr=!0,fo=yi.then(Si))}function Rc(e){const t=be.indexOf(e);t>Ve&&be.splice(t,1)}function kc(e){q(e)?jt.push(...e):(!Je||!Je.includes(e,e.allowRecurse?yt+1:yt))&&jt.push(e),wi()}function $o(e,t=dn?Ve+1:0){for(;t<be.length;t++){const n=be[t];n&&n.pre&&(be.splice(t,1),t--,n())}}function Ei(e){if(jt.length){const t=[...new Set(jt)];if(jt.length=0,Je){Je.push(...t);return}for(Je=t,Je.sort((n,r)=>pn(n)-pn(r)),yt=0;yt<Je.length;yt++)Je[yt]();Je=null,yt=0}}const pn=e=>e.id==null?1/0:e.id,Ac=(e,t)=>{const n=pn(e)-pn(t);if(n===0){if(e.pre&&!t.pre)return-1;if(t.pre&&!e.pre)return 1}return n};function Si(e){Lr=!1,dn=!0,be.sort(Ac);const t=Fe;try{for(Ve=0;Ve<be.length;Ve++){const n=be[Ve];n&&n.active!==!1&&ht(n,null,14)}}finally{Ve=0,be.length=0,Ei(),dn=!1,fo=null,(be.length||jt.length)&&Si()}}function Mc(e,t,...n){if(e.isUnmounted)return;const r=e.vnode.props||ie;let o=n;const s=t.startsWith("update:"),i=s&&t.slice(7);if(i&&i in r){const u=`${i==="modelValue"?"model":i}Modifiers`,{number:h,trim:f}=r[u]||ie;f&&(o=n.map(g=>ue(g)?g.trim():g)),h&&(o=n.map(Da))}let a,c=r[a=ur(t)]||r[a=ur(We(t))];!c&&s&&(c=r[a=ur(Kt(t))]),c&&Me(c,e,6,o);const l=r[a+"Once"];if(l){if(!e.emitted)e.emitted={};else if(e.emitted[a])return;e.emitted[a]=!0,Me(l,e,6,o)}}function Ci(e,t,n=!1){const r=t.emitsCache,o=r.get(e);if(o!==void 0)return o;const s=e.emits;let i={},a=!1;if(!W(e)){const c=l=>{const u=Ci(l,t,!0);u&&(a=!0,he(i,u))};!n&&t.mixins.length&&t.mixins.forEach(c),e.extends&&c(e.extends),e.mixins&&e.mixins.forEach(c)}return!s&&!a?(ae(e)&&r.set(e,null),null):(q(s)?s.forEach(c=>i[c]=null):he(i,s),ae(e)&&r.set(e,i),i)}function Gn(e,t){return!e||!Vn(t)?!1:(t=t.slice(2).replace(/Once$/,""),X(e,t[0].toLowerCase()+t.slice(1))||X(e,Kt(t))||X(e,t))}let Ce=null,Xn=null;function Fn(e){const t=Ce;return Ce=e,Xn=e&&e.type.__scopeId||null,t}function dh(e){Xn=e}function hh(){Xn=null}function Tc(e,t=Ce,n){if(!t||e._n)return e;const r=(...o)=>{r._d&&Xo(-1);const s=Fn(t);let i;try{i=e(...o)}finally{Fn(s),r._d&&Xo(1)}return i};return r._n=!0,r._c=!0,r._d=!0,r}function fr(e){const{type:t,vnode:n,proxy:r,withProxy:o,props:s,propsOptions:[i],slots:a,attrs:c,emit:l,render:u,renderCache:h,data:f,setupState:g,ctx:E,inheritAttrs:I}=e;let D,S;const _=Fn(e);try{if(n.shapeFlag&4){const y=o||r;D=Ke(u.call(y,y,h,s,g,f,E)),S=c}else{const y=t;D=Ke(y.length>1?y(s,{attrs:c,slots:a,emit:l}):y(s,null)),S=t.props?c:Lc(c)}}catch(y){sn.length=0,Jn(y,e,1),D=xe($e)}let R=D;if(S&&I!==!1){const y=Object.keys(S),{shapeFlag:T}=R;y.length&&T&7&&(i&&y.some(Xr)&&(S=Nc(S,i)),R=Ze(R,S))}return n.dirs&&(R=Ze(R),R.dirs=R.dirs?R.dirs.concat(n.dirs):n.dirs),n.transition&&(R.transition=n.transition),D=R,Fn(_),D}const Lc=e=>{let t;for(const n in e)(n==="class"||n==="style"||Vn(n))&&((t||(t={}))[n]=e[n]);return t},Nc=(e,t)=>{const n={};for(const r in e)(!Xr(r)||!(r.slice(9)in t))&&(n[r]=e[r]);return n};function Dc(e,t,n){const{props:r,children:o,component:s}=e,{props:i,children:a,patchFlag:c}=t,l=s.emitsOptions;if(t.dirs||t.transition)return!0;if(n&&c>=0){if(c&1024)return!0;if(c&16)return r?Ho(r,i,l):!!i;if(c&8){const u=t.dynamicProps;for(let h=0;h<u.length;h++){const f=u[h];if(i[f]!==r[f]&&!Gn(l,f))return!0}}}else return(o||a)&&(!a||!a.$stable)?!0:r===i?!1:r?i?Ho(r,i,l):!0:!!i;return!1}function Ho(e,t,n){const r=Object.keys(t);if(r.length!==Object.keys(e).length)return!0;for(let o=0;o<r.length;o++){const s=r[o];if(t[s]!==e[s]&&!Gn(n,s))return!0}return!1}function jc({vnode:e,parent:t},n){for(;t&&t.subTree===e;)(e=t.vnode).el=n,t=t.parent}const xi=e=>e.__isSuspense;function Fc(e,t){t&&t.pendingBranch?q(e)?t.effects.push(...e):t.effects.push(e):kc(e)}const Pn={};function Ft(e,t,n){return Oi(e,t,n)}function Oi(e,t,{immediate:n,deep:r,flush:o,onTrack:s,onTrigger:i}=ie){var a;const c=ni()===((a=pe)==null?void 0:a.scope)?pe:null;let l,u=!1,h=!1;if(fe(e)?(l=()=>e.value,u=jn(e)):dt(e)?(l=()=>e,r=!0):q(e)?(h=!0,u=e.some(y=>dt(y)||jn(y)),l=()=>e.map(y=>{if(fe(y))return y.value;if(dt(y))return St(y);if(W(y))return ht(y,c,2)})):W(e)?t?l=()=>ht(e,c,2):l=()=>{if(!(c&&c.isUnmounted))return f&&f(),Me(e,c,3,[g])}:l=Fe,t&&r){const y=l;l=()=>St(y())}let f,g=y=>{f=_.onStop=()=>{ht(y,c,4)}},E;if(vn)if(g=Fe,t?n&&Me(t,c,3,[l(),h?[]:void 0,g]):l(),o==="sync"){const y=Fl();E=y.__watcherHandles||(y.__watcherHandles=[])}else return Fe;let I=h?new Array(e.length).fill(Pn):Pn;const D=()=>{if(!!_.active)if(t){const y=_.run();(r||u||(h?y.some((T,k)=>un(T,I[k])):un(y,I)))&&(f&&f(),Me(t,c,3,[y,I===Pn?void 0:h&&I[0]===Pn?[]:I,g]),I=y)}else _.run()};D.allowRecurse=!!t;let S;o==="sync"?S=D:o==="post"?S=()=>ge(D,c&&c.suspense):(D.pre=!0,c&&(D.id=c.uid),S=()=>ho(D));const _=new so(l,S);t?n?D():I=_.run():o==="post"?ge(_.run.bind(_),c&&c.suspense):_.run();const R=()=>{_.stop(),c&&c.scope&&Zr(c.scope.effects,_)};return E&&E.push(R),R}function $c(e,t,n){const r=this.proxy,o=ue(e)?e.includes(".")?Ii(r,e):()=>r[e]:e.bind(r,r);let s;W(t)?s=t:(s=t.handler,n=t);const i=pe;Bt(this);const a=Oi(o,s.bind(r),n);return i?Bt(i):xt(),a}function Ii(e,t){const n=t.split(".");return()=>{let r=e;for(let o=0;o<n.length&&r;o++)r=r[n[o]];return r}}function St(e,t){if(!ae(e)||e.__v_skip||(t=t||new Set,t.has(e)))return e;if(t.add(e),fe(e))St(e.value,t);else if(q(e))for(let n=0;n<e.length;n++)St(e[n],t);else if(Qs(e)||Nt(e))e.forEach(n=>{St(n,t)});else if(Gs(e))for(const n in e)St(e[n],t);return e}function ph(e,t){const n=Ce;if(n===null)return e;const r=or(n)||n.proxy,o=e.dirs||(e.dirs=[]);for(let s=0;s<t.length;s++){let[i,a,c,l=ie]=t[s];i&&(W(i)&&(i={mounted:i,updated:i}),i.deep&&St(a),o.push({dir:i,instance:r,value:a,oldValue:void 0,arg:c,modifiers:l}))}return e}function gt(e,t,n,r){const o=e.dirs,s=t&&t.dirs;for(let i=0;i<o.length;i++){const a=o[i];s&&(a.oldValue=s[i].value);let c=a.dir[r];c&&(Vt(),Me(c,n,8,[e.el,a,e,t]),Wt())}}function Hc(){const e={isMounted:!1,isLeaving:!1,isUnmounting:!1,leavingVNodes:new Map};return tr(()=>{e.isMounted=!0}),mo(()=>{e.isUnmounting=!0}),e}const Re=[Function,Array],Pi={mode:String,appear:Boolean,persisted:Boolean,onBeforeEnter:Re,onEnter:Re,onAfterEnter:Re,onEnterCancelled:Re,onBeforeLeave:Re,onLeave:Re,onAfterLeave:Re,onLeaveCancelled:Re,onBeforeAppear:Re,onAppear:Re,onAfterAppear:Re,onAppearCancelled:Re},Bc={name:"BaseTransition",props:Pi,setup(e,{slots:t}){const n=Vi(),r=Hc();let o;return()=>{const s=t.default&&ki(t.default(),!0);if(!s||!s.length)return;let i=s[0];if(s.length>1){for(const I of s)if(I.type!==$e){i=I;break}}const a=G(e),{mode:c}=a;if(r.isLeaving)return dr(i);const l=Bo(i);if(!l)return dr(i);const u=Nr(l,a,r,n);$n(l,u);const h=n.subTree,f=h&&Bo(h);let g=!1;const{getTransitionKey:E}=l.type;if(E){const I=E();o===void 0?o=I:I!==o&&(o=I,g=!0)}if(f&&f.type!==$e&&(!lt(l,f)||g)){const I=Nr(f,a,r,n);if($n(f,I),c==="out-in")return r.isLeaving=!0,I.afterLeave=()=>{r.isLeaving=!1,n.update.active!==!1&&n.update()},dr(i);c==="in-out"&&l.type!==$e&&(I.delayLeave=(D,S,_)=>{const R=Ri(r,f);R[String(f.key)]=f,D._leaveCb=()=>{S(),D._leaveCb=void 0,delete u.delayedLeave},u.delayedLeave=_})}return i}}},zc=Bc;function Ri(e,t){const{leavingVNodes:n}=e;let r=n.get(t.type);return r||(r=Object.create(null),n.set(t.type,r)),r}function Nr(e,t,n,r){const{appear:o,mode:s,persisted:i=!1,onBeforeEnter:a,onEnter:c,onAfterEnter:l,onEnterCancelled:u,onBeforeLeave:h,onLeave:f,onAfterLeave:g,onLeaveCancelled:E,onBeforeAppear:I,onAppear:D,onAfterAppear:S,onAppearCancelled:_}=t,R=String(e.key),y=Ri(n,e),T=(O,N)=>{O&&Me(O,r,9,N)},k=(O,N)=>{const K=N[1];T(O,N),q(O)?O.every(Q=>Q.length<=1)&&K():O.length<=1&&K()},H={mode:s,persisted:i,beforeEnter(O){let N=a;if(!n.isMounted)if(o)N=I||a;else return;O._leaveCb&&O._leaveCb(!0);const K=y[R];K&&lt(e,K)&&K.el._leaveCb&&K.el._leaveCb(),T(N,[O])},enter(O){let N=c,K=l,Q=u;if(!n.isMounted)if(o)N=D||c,K=S||l,Q=_||u;else return;let F=!1;const Z=O._enterCb=me=>{F||(F=!0,me?T(Q,[O]):T(K,[O]),H.delayedLeave&&H.delayedLeave(),O._enterCb=void 0)};N?k(N,[O,Z]):Z()},leave(O,N){const K=String(e.key);if(O._enterCb&&O._enterCb(!0),n.isUnmounting)return N();T(h,[O]);let Q=!1;const F=O._leaveCb=Z=>{Q||(Q=!0,N(),Z?T(E,[O]):T(g,[O]),O._leaveCb=void 0,y[K]===e&&delete y[K])};y[K]=e,f?k(f,[O,F]):F()},clone(O){return Nr(O,t,n,r)}};return H}function dr(e){if(Zn(e))return e=Ze(e),e.children=null,e}function Bo(e){return Zn(e)?e.children?e.children[0]:void 0:e}function $n(e,t){e.shapeFlag&6&&e.component?$n(e.component.subTree,t):e.shapeFlag&128?(e.ssContent.transition=t.clone(e.ssContent),e.ssFallback.transition=t.clone(e.ssFallback)):e.transition=t}function ki(e,t=!1,n){let r=[],o=0;for(let s=0;s<e.length;s++){let i=e[s];const a=n==null?i.key:String(n)+String(i.key!=null?i.key:s);i.type===qe?(i.patchFlag&128&&o++,r=r.concat(ki(i.children,t,a))):(t||i.type!==$e)&&r.push(a!=null?Ze(i,{key:a}):i)}if(o>1)for(let s=0;s<r.length;s++)r[s].patchFlag=-2;return r}function po(e,t){return W(e)?(()=>he({name:e.name},t,{setup:e}))():e}const nn=e=>!!e.type.__asyncLoader,Zn=e=>e.type.__isKeepAlive,Uc={name:"KeepAlive",__isKeepAlive:!0,props:{include:[String,RegExp,Array],exclude:[String,RegExp,Array],max:[String,Number]},setup(e,{slots:t}){const n=Vi(),r=n.ctx;if(!r.renderer)return()=>{const _=t.default&&t.default();return _&&_.length===1?_[0]:_};const o=new Map,s=new Set;let i=null;const a=n.suspense,{renderer:{p:c,m:l,um:u,o:{createElement:h}}}=r,f=h("div");r.activate=(_,R,y,T,k)=>{const H=_.component;l(_,R,y,0,a),c(H.vnode,_,R,y,H,a,T,_.slotScopeIds,k),ge(()=>{H.isDeactivated=!1,H.a&&tn(H.a);const O=_.props&&_.props.onVnodeMounted;O&&ke(O,H.parent,_)},a)},r.deactivate=_=>{const R=_.component;l(_,f,null,1,a),ge(()=>{R.da&&tn(R.da);const y=_.props&&_.props.onVnodeUnmounted;y&&ke(y,R.parent,_),R.isDeactivated=!0},a)};function g(_){hr(_),u(_,n,a,!0)}function E(_){o.forEach((R,y)=>{const T=zr(R.type);T&&(!_||!_(T))&&I(y)})}function I(_){const R=o.get(_);!i||!lt(R,i)?g(R):i&&hr(i),o.delete(_),s.delete(_)}Ft(()=>[e.include,e.exclude],([_,R])=>{_&&E(y=>Zt(_,y)),R&&E(y=>!Zt(R,y))},{flush:"post",deep:!0});let D=null;const S=()=>{D!=null&&o.set(D,pr(n.subTree))};return tr(S),Mi(S),mo(()=>{o.forEach(_=>{const{subTree:R,suspense:y}=n,T=pr(R);if(_.type===T.type&&_.key===T.key){hr(T);const k=T.component.da;k&&ge(k,y);return}g(_)})}),()=>{if(D=null,!t.default)return null;const _=t.default(),R=_[0];if(_.length>1)return i=null,_;if(!Bn(R)||!(R.shapeFlag&4)&&!(R.shapeFlag&128))return i=null,R;let y=pr(R);const T=y.type,k=zr(nn(y)?y.type.__asyncResolved||{}:T),{include:H,exclude:O,max:N}=e;if(H&&(!k||!Zt(H,k))||O&&k&&Zt(O,k))return i=y,R;const K=y.key==null?T:y.key,Q=o.get(K);return y.el&&(y=Ze(y),R.shapeFlag&128&&(R.ssContent=y)),D=K,Q?(y.el=Q.el,y.component=Q.component,y.transition&&$n(y,y.transition),y.shapeFlag|=512,s.delete(K),s.add(K)):(s.add(K),N&&s.size>parseInt(N,10)&&I(s.values().next().value)),y.shapeFlag|=256,i=y,xi(R.type)?R:y}}},mh=Uc;function Zt(e,t){return q(e)?e.some(n=>Zt(n,t)):ue(e)?e.split(",").includes(t):Ma(e)?e.test(t):!1}function qc(e,t){Ai(e,"a",t)}function Kc(e,t){Ai(e,"da",t)}function Ai(e,t,n=pe){const r=e.__wdc||(e.__wdc=()=>{let o=n;for(;o;){if(o.isDeactivated)return;o=o.parent}return e()});if(er(t,r,n),n){let o=n.parent;for(;o&&o.parent;)Zn(o.parent.vnode)&&Vc(r,t,n,o),o=o.parent}}function Vc(e,t,n,r){const o=er(t,e,r,!0);Ti(()=>{Zr(r[t],o)},n)}function hr(e){e.shapeFlag&=-257,e.shapeFlag&=-513}function pr(e){return e.shapeFlag&128?e.ssContent:e}function er(e,t,n=pe,r=!1){if(n){const o=n[e]||(n[e]=[]),s=t.__weh||(t.__weh=(...i)=>{if(n.isUnmounted)return;Vt(),Bt(n);const a=Me(t,n,e,i);return xt(),Wt(),a});return r?o.unshift(s):o.push(s),s}}const et=e=>(t,n=pe)=>(!vn||e==="sp")&&er(e,(...r)=>t(...r),n),Wc=et("bm"),tr=et("m"),Qc=et("bu"),Mi=et("u"),mo=et("bum"),Ti=et("um"),Yc=et("sp"),Jc=et("rtg"),Gc=et("rtc");function Xc(e,t=pe){er("ec",e,t)}const Li="components";function Zc(e,t){return tl(Li,e,!0,t)||e}const el=Symbol.for("v-ndc");function tl(e,t,n=!0,r=!1){const o=Ce||pe;if(o){const s=o.type;if(e===Li){const a=zr(s,!1);if(a&&(a===t||a===We(t)||a===Qn(We(t))))return s}const i=zo(o[e]||s[e],t)||zo(o.appContext[e],t);return!i&&r?s:i}}function zo(e,t){return e&&(e[t]||e[We(t)]||e[Qn(We(t))])}function gh(e,t,n,r){let o;const s=n&&n[r];if(q(e)||ue(e)){o=new Array(e.length);for(let i=0,a=e.length;i<a;i++)o[i]=t(e[i],i,void 0,s&&s[i])}else if(typeof e=="number"){o=new Array(e);for(let i=0;i<e;i++)o[i]=t(i+1,i,void 0,s&&s[i])}else if(ae(e))if(e[Symbol.iterator])o=Array.from(e,(i,a)=>t(i,a,void 0,s&&s[a]));else{const i=Object.keys(e);o=new Array(i.length);for(let a=0,c=i.length;a<c;a++){const l=i[a];o[a]=t(e[l],l,a,s&&s[a])}}else o=[];return n&&(n[r]=o),o}const Dr=e=>e?Wi(e)?or(e)||e.proxy:Dr(e.parent):null,rn=he(Object.create(null),{$:e=>e,$el:e=>e.vnode.el,$data:e=>e.data,$props:e=>e.props,$attrs:e=>e.attrs,$slots:e=>e.slots,$refs:e=>e.refs,$parent:e=>Dr(e.parent),$root:e=>Dr(e.root),$emit:e=>e.emit,$options:e=>go(e),$forceUpdate:e=>e.f||(e.f=()=>ho(e.update)),$nextTick:e=>e.n||(e.n=hn.bind(e.proxy)),$watch:e=>$c.bind(e)}),mr=(e,t)=>e!==ie&&!e.__isScriptSetup&&X(e,t),nl={get({_:e},t){const{ctx:n,setupState:r,data:o,props:s,accessCache:i,type:a,appContext:c}=e;let l;if(t[0]!=="$"){const g=i[t];if(g!==void 0)switch(g){case 1:return r[t];case 2:return o[t];case 4:return n[t];case 3:return s[t]}else{if(mr(r,t))return i[t]=1,r[t];if(o!==ie&&X(o,t))return i[t]=2,o[t];if((l=e.propsOptions[0])&&X(l,t))return i[t]=3,s[t];if(n!==ie&&X(n,t))return i[t]=4,n[t];jr&&(i[t]=0)}}const u=rn[t];let h,f;if(u)return t==="$attrs"&&Oe(e,"get",t),u(e);if((h=a.__cssModules)&&(h=h[t]))return h;if(n!==ie&&X(n,t))return i[t]=4,n[t];if(f=c.config.globalProperties,X(f,t))return f[t]},set({_:e},t,n){const{data:r,setupState:o,ctx:s}=e;return mr(o,t)?(o[t]=n,!0):r!==ie&&X(r,t)?(r[t]=n,!0):X(e.props,t)||t[0]==="$"&&t.slice(1)in e?!1:(s[t]=n,!0)},has({_:{data:e,setupState:t,accessCache:n,ctx:r,appContext:o,propsOptions:s}},i){let a;return!!n[i]||e!==ie&&X(e,i)||mr(t,i)||(a=s[0])&&X(a,i)||X(r,i)||X(rn,i)||X(o.config.globalProperties,i)},defineProperty(e,t,n){return n.get!=null?e._.accessCache[t]=0:X(n,"value")&&this.set(e,t,n.value,null),Reflect.defineProperty(e,t,n)}};function Uo(e){return q(e)?e.reduce((t,n)=>(t[n]=null,t),{}):e}let jr=!0;function rl(e){const t=go(e),n=e.proxy,r=e.ctx;jr=!1,t.beforeCreate&&qo(t.beforeCreate,e,"bc");const{data:o,computed:s,methods:i,watch:a,provide:c,inject:l,created:u,beforeMount:h,mounted:f,beforeUpdate:g,updated:E,activated:I,deactivated:D,beforeDestroy:S,beforeUnmount:_,destroyed:R,unmounted:y,render:T,renderTracked:k,renderTriggered:H,errorCaptured:O,serverPrefetch:N,expose:K,inheritAttrs:Q,components:F,directives:Z,filters:me}=t;if(l&&ol(l,r,null),i)for(const oe in i){const ee=i[oe];W(ee)&&(r[oe]=ee.bind(n))}if(o){const oe=o.call(n,n);ae(oe)&&(e.data=It(oe))}if(jr=!0,s)for(const oe in s){const ee=s[oe],Qe=W(ee)?ee.bind(n,n):W(ee.get)?ee.get.bind(n,n):Fe,tt=!W(ee)&&W(ee.set)?ee.set.bind(n):Fe,Be=Se({get:Qe,set:tt});Object.defineProperty(r,oe,{enumerable:!0,configurable:!0,get:()=>Be.value,set:Ee=>Be.value=Ee})}if(a)for(const oe in a)Ni(a[oe],r,n,oe);if(c){const oe=W(c)?c.call(n):c;Reflect.ownKeys(oe).forEach(ee=>{Mn(ee,oe[ee])})}u&&qo(u,e,"c");function J(oe,ee){q(ee)?ee.forEach(Qe=>oe(Qe.bind(n))):ee&&oe(ee.bind(n))}if(J(Wc,h),J(tr,f),J(Qc,g),J(Mi,E),J(qc,I),J(Kc,D),J(Xc,O),J(Gc,k),J(Jc,H),J(mo,_),J(Ti,y),J(Yc,N),q(K))if(K.length){const oe=e.exposed||(e.exposed={});K.forEach(ee=>{Object.defineProperty(oe,ee,{get:()=>n[ee],set:Qe=>n[ee]=Qe})})}else e.exposed||(e.exposed={});T&&e.render===Fe&&(e.render=T),Q!=null&&(e.inheritAttrs=Q),F&&(e.components=F),Z&&(e.directives=Z)}function ol(e,t,n=Fe){q(e)&&(e=Fr(e));for(const r in e){const o=e[r];let s;ae(o)?"default"in o?s=Te(o.from||r,o.default,!0):s=Te(o.from||r):s=Te(o),fe(s)?Object.defineProperty(t,r,{enumerable:!0,configurable:!0,get:()=>s.value,set:i=>s.value=i}):t[r]=s}}function qo(e,t,n){Me(q(e)?e.map(r=>r.bind(t.proxy)):e.bind(t.proxy),t,n)}function Ni(e,t,n,r){const o=r.includes(".")?Ii(n,r):()=>n[r];if(ue(e)){const s=t[e];W(s)&&Ft(o,s)}else if(W(e))Ft(o,e.bind(n));else if(ae(e))if(q(e))e.forEach(s=>Ni(s,t,n,r));else{const s=W(e.handler)?e.handler.bind(n):t[e.handler];W(s)&&Ft(o,s,e)}}function go(e){const t=e.type,{mixins:n,extends:r}=t,{mixins:o,optionsCache:s,config:{optionMergeStrategies:i}}=e.appContext,a=s.get(t);let c;return a?c=a:!o.length&&!n&&!r?c=t:(c={},o.length&&o.forEach(l=>Hn(c,l,i,!0)),Hn(c,t,i)),ae(t)&&s.set(t,c),c}function Hn(e,t,n,r=!1){const{mixins:o,extends:s}=t;s&&Hn(e,s,n,!0),o&&o.forEach(i=>Hn(e,i,n,!0));for(const i in t)if(!(r&&i==="expose")){const a=sl[i]||n&&n[i];e[i]=a?a(e[i],t[i]):t[i]}return e}const sl={data:Ko,props:Vo,emits:Vo,methods:en,computed:en,beforeCreate:we,created:we,beforeMount:we,mounted:we,beforeUpdate:we,updated:we,beforeDestroy:we,beforeUnmount:we,destroyed:we,unmounted:we,activated:we,deactivated:we,errorCaptured:we,serverPrefetch:we,components:en,directives:en,watch:al,provide:Ko,inject:il};function Ko(e,t){return t?e?function(){return he(W(e)?e.call(this,this):e,W(t)?t.call(this,this):t)}:t:e}function il(e,t){return en(Fr(e),Fr(t))}function Fr(e){if(q(e)){const t={};for(let n=0;n<e.length;n++)t[e[n]]=e[n];return t}return e}function we(e,t){return e?[...new Set([].concat(e,t))]:t}function en(e,t){return e?he(Object.create(null),e,t):t}function Vo(e,t){return e?q(e)&&q(t)?[...new Set([...e,...t])]:he(Object.create(null),Uo(e),Uo(t!=null?t:{})):t}function al(e,t){if(!e)return t;if(!t)return e;const n=he(Object.create(null),e);for(const r in t)n[r]=we(e[r],t[r]);return n}function Di(){return{app:null,config:{isNativeTag:Ra,performance:!1,globalProperties:{},optionMergeStrategies:{},errorHandler:void 0,warnHandler:void 0,compilerOptions:{}},mixins:[],components:{},directives:{},provides:Object.create(null),optionsCache:new WeakMap,propsCache:new WeakMap,emitsCache:new WeakMap}}let cl=0;function ll(e,t){return function(r,o=null){W(r)||(r=he({},r)),o!=null&&!ae(o)&&(o=null);const s=Di(),i=new Set;let a=!1;const c=s.app={_uid:cl++,_component:r,_props:o,_container:null,_context:s,_instance:null,version:$l,get config(){return s.config},set config(l){},use(l,...u){return i.has(l)||(l&&W(l.install)?(i.add(l),l.install(c,...u)):W(l)&&(i.add(l),l(c,...u))),c},mixin(l){return s.mixins.includes(l)||s.mixins.push(l),c},component(l,u){return u?(s.components[l]=u,c):s.components[l]},directive(l,u){return u?(s.directives[l]=u,c):s.directives[l]},mount(l,u,h){if(!a){const f=xe(r,o);return f.appContext=s,u&&t?t(f,l):e(f,l,h),a=!0,c._container=l,l.__vue_app__=c,or(f.component)||f.component.proxy}},unmount(){a&&(e(null,c._container),delete c._container.__vue_app__)},provide(l,u){return s.provides[l]=u,c},runWithContext(l){mn=c;try{return l()}finally{mn=null}}};return c}}let mn=null;function Mn(e,t){if(pe){let n=pe.provides;const r=pe.parent&&pe.parent.provides;r===n&&(n=pe.provides=Object.create(r)),n[e]=t}}function Te(e,t,n=!1){const r=pe||Ce;if(r||mn){const o=r?r.parent==null?r.vnode.appContext&&r.vnode.appContext.provides:r.parent.provides:mn._context.provides;if(o&&e in o)return o[e];if(arguments.length>1)return n&&W(t)?t.call(r&&r.proxy):t}}function ul(){return!!(pe||Ce||mn)}function fl(e,t,n,r=!1){const o={},s={};Nn(s,rr,1),e.propsDefaults=Object.create(null),ji(e,t,o,s);for(const i in e.propsOptions[0])i in o||(o[i]=void 0);n?e.props=r?o:hi(o):e.type.props?e.props=o:e.props=s,e.attrs=s}function dl(e,t,n,r){const{props:o,attrs:s,vnode:{patchFlag:i}}=e,a=G(o),[c]=e.propsOptions;let l=!1;if((r||i>0)&&!(i&16)){if(i&8){const u=e.vnode.dynamicProps;for(let h=0;h<u.length;h++){let f=u[h];if(Gn(e.emitsOptions,f))continue;const g=t[f];if(c)if(X(s,f))g!==s[f]&&(s[f]=g,l=!0);else{const E=We(f);o[E]=$r(c,a,E,g,e,!1)}else g!==s[f]&&(s[f]=g,l=!0)}}}else{ji(e,t,o,s)&&(l=!0);let u;for(const h in a)(!t||!X(t,h)&&((u=Kt(h))===h||!X(t,u)))&&(c?n&&(n[h]!==void 0||n[u]!==void 0)&&(o[h]=$r(c,a,h,void 0,e,!0)):delete o[h]);if(s!==a)for(const h in s)(!t||!X(t,h)&&!0)&&(delete s[h],l=!0)}l&&Xe(e,"set","$attrs")}function ji(e,t,n,r){const[o,s]=e.propsOptions;let i=!1,a;if(t)for(let c in t){if(An(c))continue;const l=t[c];let u;o&&X(o,u=We(c))?!s||!s.includes(u)?n[u]=l:(a||(a={}))[u]=l:Gn(e.emitsOptions,c)||(!(c in r)||l!==r[c])&&(r[c]=l,i=!0)}if(s){const c=G(n),l=a||ie;for(let u=0;u<s.length;u++){const h=s[u];n[h]=$r(o,c,h,l[h],e,!X(l,h))}}return i}function $r(e,t,n,r,o,s){const i=e[n];if(i!=null){const a=X(i,"default");if(a&&r===void 0){const c=i.default;if(i.type!==Function&&!i.skipFactory&&W(c)){const{propsDefaults:l}=o;n in l?r=l[n]:(Bt(o),r=l[n]=c.call(null,t),xt())}else r=c}i[0]&&(s&&!a?r=!1:i[1]&&(r===""||r===Kt(n))&&(r=!0))}return r}function Fi(e,t,n=!1){const r=t.propsCache,o=r.get(e);if(o)return o;const s=e.props,i={},a=[];let c=!1;if(!W(e)){const u=h=>{c=!0;const[f,g]=Fi(h,t,!0);he(i,f),g&&a.push(...g)};!n&&t.mixins.length&&t.mixins.forEach(u),e.extends&&u(e.extends),e.mixins&&e.mixins.forEach(u)}if(!s&&!c)return ae(e)&&r.set(e,Lt),Lt;if(q(s))for(let u=0;u<s.length;u++){const h=We(s[u]);Wo(h)&&(i[h]=ie)}else if(s)for(const u in s){const h=We(u);if(Wo(h)){const f=s[u],g=i[h]=q(f)||W(f)?{type:f}:he({},f);if(g){const E=Jo(Boolean,g.type),I=Jo(String,g.type);g[0]=E>-1,g[1]=I<0||E<I,(E>-1||X(g,"default"))&&a.push(h)}}}const l=[i,a];return ae(e)&&r.set(e,l),l}function Wo(e){return e[0]!=="$"}function Qo(e){const t=e&&e.toString().match(/^\s*(function|class) (\w+)/);return t?t[2]:e===null?"null":""}function Yo(e,t){return Qo(e)===Qo(t)}function Jo(e,t){return q(t)?t.findIndex(n=>Yo(n,e)):W(t)&&Yo(t,e)?0:-1}const $i=e=>e[0]==="_"||e==="$stable",vo=e=>q(e)?e.map(Ke):[Ke(e)],hl=(e,t,n)=>{if(t._n)return t;const r=Tc((...o)=>vo(t(...o)),n);return r._c=!1,r},Hi=(e,t,n)=>{const r=e._ctx;for(const o in e){if($i(o))continue;const s=e[o];if(W(s))t[o]=hl(o,s,r);else if(s!=null){const i=vo(s);t[o]=()=>i}}},Bi=(e,t)=>{const n=vo(t);e.slots.default=()=>n},pl=(e,t)=>{if(e.vnode.shapeFlag&32){const n=t._;n?(e.slots=G(t),Nn(t,"_",n)):Hi(t,e.slots={})}else e.slots={},t&&Bi(e,t);Nn(e.slots,rr,1)},ml=(e,t,n)=>{const{vnode:r,slots:o}=e;let s=!0,i=ie;if(r.shapeFlag&32){const a=t._;a?n&&a===1?s=!1:(he(o,t),!n&&a===1&&delete o._):(s=!t.$stable,Hi(t,o)),i=t}else t&&(Bi(e,t),i={default:1});if(s)for(const a in o)!$i(a)&&!(a in i)&&delete o[a]};function Hr(e,t,n,r,o=!1){if(q(e)){e.forEach((f,g)=>Hr(f,t&&(q(t)?t[g]:t),n,r,o));return}if(nn(r)&&!o)return;const s=r.shapeFlag&4?or(r.component)||r.component.proxy:r.el,i=o?null:s,{i:a,r:c}=e,l=t&&t.r,u=a.refs===ie?a.refs={}:a.refs,h=a.setupState;if(l!=null&&l!==c&&(ue(l)?(u[l]=null,X(h,l)&&(h[l]=null)):fe(l)&&(l.value=null)),W(c))ht(c,a,12,[i,u]);else{const f=ue(c),g=fe(c);if(f||g){const E=()=>{if(e.f){const I=f?X(h,c)?h[c]:u[c]:c.value;o?q(I)&&Zr(I,s):q(I)?I.includes(s)||I.push(s):f?(u[c]=[s],X(h,c)&&(h[c]=u[c])):(c.value=[s],e.k&&(u[e.k]=c.value))}else f?(u[c]=i,X(h,c)&&(h[c]=i)):g&&(c.value=i,e.k&&(u[e.k]=i))};i?(E.id=-1,ge(E,n)):E()}}}const ge=Fc;function gl(e){return vl(e)}function vl(e,t){const n=kr();n.__VUE__=!0;const{insert:r,remove:o,patchProp:s,createElement:i,createText:a,createComment:c,setText:l,setElementText:u,parentNode:h,nextSibling:f,setScopeId:g=Fe,insertStaticContent:E}=e,I=(d,p,m,v=null,w=null,C=null,L=!1,P=null,A=!!p.dynamicChildren)=>{if(d===p)return;d&&!lt(d,p)&&(v=b(d),Ee(d,w,C,!0),d=null),p.patchFlag===-2&&(A=!1,p.dynamicChildren=null);const{type:x,ref:z,shapeFlag:$}=p;switch(x){case nr:D(d,p,m,v);break;case $e:S(d,p,m,v);break;case gr:d==null&&_(p,m,v,L);break;case qe:F(d,p,m,v,w,C,L,P,A);break;default:$&1?T(d,p,m,v,w,C,L,P,A):$&6?Z(d,p,m,v,w,C,L,P,A):($&64||$&128)&&x.process(d,p,m,v,w,C,L,P,A,M)}z!=null&&w&&Hr(z,d&&d.ref,C,p||d,!p)},D=(d,p,m,v)=>{if(d==null)r(p.el=a(p.children),m,v);else{const w=p.el=d.el;p.children!==d.children&&l(w,p.children)}},S=(d,p,m,v)=>{d==null?r(p.el=c(p.children||""),m,v):p.el=d.el},_=(d,p,m,v)=>{[d.el,d.anchor]=E(d.children,p,m,v,d.el,d.anchor)},R=({el:d,anchor:p},m,v)=>{let w;for(;d&&d!==p;)w=f(d),r(d,m,v),d=w;r(p,m,v)},y=({el:d,anchor:p})=>{let m;for(;d&&d!==p;)m=f(d),o(d),d=m;o(p)},T=(d,p,m,v,w,C,L,P,A)=>{L=L||p.type==="svg",d==null?k(p,m,v,w,C,L,P,A):N(d,p,w,C,L,P,A)},k=(d,p,m,v,w,C,L,P)=>{let A,x;const{type:z,props:$,shapeFlag:U,transition:V,dirs:Y}=d;if(A=d.el=i(d.type,C,$&&$.is,$),U&8?u(A,d.children):U&16&&O(d.children,A,null,v,w,C&&z!=="foreignObject",L,P),Y&&gt(d,null,v,"created"),H(A,d,d.scopeId,L,v),$){for(const re in $)re!=="value"&&!An(re)&&s(A,re,null,$[re],C,d.children,v,w,ve);"value"in $&&s(A,"value",null,$.value),(x=$.onVnodeBeforeMount)&&ke(x,v,d)}Y&&gt(d,null,v,"beforeMount");const se=(!w||w&&!w.pendingBranch)&&V&&!V.persisted;se&&V.beforeEnter(A),r(A,p,m),((x=$&&$.onVnodeMounted)||se||Y)&&ge(()=>{x&&ke(x,v,d),se&&V.enter(A),Y&&gt(d,null,v,"mounted")},w)},H=(d,p,m,v,w)=>{if(m&&g(d,m),v)for(let C=0;C<v.length;C++)g(d,v[C]);if(w){let C=w.subTree;if(p===C){const L=w.vnode;H(d,L,L.scopeId,L.slotScopeIds,w.parent)}}},O=(d,p,m,v,w,C,L,P,A=0)=>{for(let x=A;x<d.length;x++){const z=d[x]=P?at(d[x]):Ke(d[x]);I(null,z,p,m,v,w,C,L,P)}},N=(d,p,m,v,w,C,L)=>{const P=p.el=d.el;let{patchFlag:A,dynamicChildren:x,dirs:z}=p;A|=d.patchFlag&16;const $=d.props||ie,U=p.props||ie;let V;m&&vt(m,!1),(V=U.onVnodeBeforeUpdate)&&ke(V,m,p,d),z&&gt(p,d,m,"beforeUpdate"),m&&vt(m,!0);const Y=w&&p.type!=="foreignObject";if(x?K(d.dynamicChildren,x,P,m,v,Y,C):L||ee(d,p,P,null,m,v,Y,C,!1),A>0){if(A&16)Q(P,p,$,U,m,v,w);else if(A&2&&$.class!==U.class&&s(P,"class",null,U.class,w),A&4&&s(P,"style",$.style,U.style,w),A&8){const se=p.dynamicProps;for(let re=0;re<se.length;re++){const de=se[re],Le=$[de],kt=U[de];(kt!==Le||de==="value")&&s(P,de,Le,kt,w,d.children,m,v,ve)}}A&1&&d.children!==p.children&&u(P,p.children)}else!L&&x==null&&Q(P,p,$,U,m,v,w);((V=U.onVnodeUpdated)||z)&&ge(()=>{V&&ke(V,m,p,d),z&&gt(p,d,m,"updated")},v)},K=(d,p,m,v,w,C,L)=>{for(let P=0;P<p.length;P++){const A=d[P],x=p[P],z=A.el&&(A.type===qe||!lt(A,x)||A.shapeFlag&70)?h(A.el):m;I(A,x,z,null,v,w,C,L,!0)}},Q=(d,p,m,v,w,C,L)=>{if(m!==v){if(m!==ie)for(const P in m)!An(P)&&!(P in v)&&s(d,P,m[P],null,L,p.children,w,C,ve);for(const P in v){if(An(P))continue;const A=v[P],x=m[P];A!==x&&P!=="value"&&s(d,P,x,A,L,p.children,w,C,ve)}"value"in v&&s(d,"value",m.value,v.value)}},F=(d,p,m,v,w,C,L,P,A)=>{const x=p.el=d?d.el:a(""),z=p.anchor=d?d.anchor:a("");let{patchFlag:$,dynamicChildren:U,slotScopeIds:V}=p;V&&(P=P?P.concat(V):V),d==null?(r(x,m,v),r(z,m,v),O(p.children,m,z,w,C,L,P,A)):$>0&&$&64&&U&&d.dynamicChildren?(K(d.dynamicChildren,U,m,w,C,L,P),(p.key!=null||w&&p===w.subTree)&&_o(d,p,!0)):ee(d,p,m,z,w,C,L,P,A)},Z=(d,p,m,v,w,C,L,P,A)=>{p.slotScopeIds=P,d==null?p.shapeFlag&512?w.ctx.activate(p,m,v,L,A):me(p,m,v,w,C,L,A):ye(d,p,A)},me=(d,p,m,v,w,C,L)=>{const P=d.component=Al(d,v,w);if(Zn(d)&&(P.ctx.renderer=M),Ml(P),P.asyncDep){if(w&&w.registerDep(P,J),!d.el){const A=P.subTree=xe($e);S(null,A,p,m)}return}J(P,d,p,m,w,C,L)},ye=(d,p,m)=>{const v=p.component=d.component;if(Dc(d,p,m))if(v.asyncDep&&!v.asyncResolved){oe(v,p,m);return}else v.next=p,Rc(v.update),v.update();else p.el=d.el,v.vnode=p},J=(d,p,m,v,w,C,L)=>{const P=()=>{if(d.isMounted){let{next:z,bu:$,u:U,parent:V,vnode:Y}=d,se=z,re;vt(d,!1),z?(z.el=Y.el,oe(d,z,L)):z=Y,$&&tn($),(re=z.props&&z.props.onVnodeBeforeUpdate)&&ke(re,V,z,Y),vt(d,!0);const de=fr(d),Le=d.subTree;d.subTree=de,I(Le,de,h(Le.el),b(Le),d,w,C),z.el=de.el,se===null&&jc(d,de.el),U&&ge(U,w),(re=z.props&&z.props.onVnodeUpdated)&&ge(()=>ke(re,V,z,Y),w)}else{let z;const{el:$,props:U}=p,{bm:V,m:Y,parent:se}=d,re=nn(p);if(vt(d,!1),V&&tn(V),!re&&(z=U&&U.onVnodeBeforeMount)&&ke(z,se,p),vt(d,!0),$&&te){const de=()=>{d.subTree=fr(d),te($,d.subTree,d,w,null)};re?p.type.__asyncLoader().then(()=>!d.isUnmounted&&de()):de()}else{const de=d.subTree=fr(d);I(null,de,m,v,d,w,C),p.el=de.el}if(Y&&ge(Y,w),!re&&(z=U&&U.onVnodeMounted)){const de=p;ge(()=>ke(z,se,de),w)}(p.shapeFlag&256||se&&nn(se.vnode)&&se.vnode.shapeFlag&256)&&d.a&&ge(d.a,w),d.isMounted=!0,p=m=v=null}},A=d.effect=new so(P,()=>ho(x),d.scope),x=d.update=()=>A.run();x.id=d.uid,vt(d,!0),x()},oe=(d,p,m)=>{p.component=d;const v=d.vnode.props;d.vnode=p,d.next=null,dl(d,p.props,v,m),ml(d,p.children,m),Vt(),$o(),Wt()},ee=(d,p,m,v,w,C,L,P,A=!1)=>{const x=d&&d.children,z=d?d.shapeFlag:0,$=p.children,{patchFlag:U,shapeFlag:V}=p;if(U>0){if(U&128){tt(x,$,m,v,w,C,L,P,A);return}else if(U&256){Qe(x,$,m,v,w,C,L,P,A);return}}V&8?(z&16&&ve(x,w,C),$!==x&&u(m,$)):z&16?V&16?tt(x,$,m,v,w,C,L,P,A):ve(x,w,C,!0):(z&8&&u(m,""),V&16&&O($,m,v,w,C,L,P,A))},Qe=(d,p,m,v,w,C,L,P,A)=>{d=d||Lt,p=p||Lt;const x=d.length,z=p.length,$=Math.min(x,z);let U;for(U=0;U<$;U++){const V=p[U]=A?at(p[U]):Ke(p[U]);I(d[U],V,m,null,w,C,L,P,A)}x>z?ve(d,w,C,!0,!1,$):O(p,m,v,w,C,L,P,A,$)},tt=(d,p,m,v,w,C,L,P,A)=>{let x=0;const z=p.length;let $=d.length-1,U=z-1;for(;x<=$&&x<=U;){const V=d[x],Y=p[x]=A?at(p[x]):Ke(p[x]);if(lt(V,Y))I(V,Y,m,null,w,C,L,P,A);else break;x++}for(;x<=$&&x<=U;){const V=d[$],Y=p[U]=A?at(p[U]):Ke(p[U]);if(lt(V,Y))I(V,Y,m,null,w,C,L,P,A);else break;$--,U--}if(x>$){if(x<=U){const V=U+1,Y=V<z?p[V].el:v;for(;x<=U;)I(null,p[x]=A?at(p[x]):Ke(p[x]),m,Y,w,C,L,P,A),x++}}else if(x>U)for(;x<=$;)Ee(d[x],w,C,!0),x++;else{const V=x,Y=x,se=new Map;for(x=Y;x<=U;x++){const Ie=p[x]=A?at(p[x]):Ke(p[x]);Ie.key!=null&&se.set(Ie.key,x)}let re,de=0;const Le=U-Y+1;let kt=!1,Io=0;const Qt=new Array(Le);for(x=0;x<Le;x++)Qt[x]=0;for(x=V;x<=$;x++){const Ie=d[x];if(de>=Le){Ee(Ie,w,C,!0);continue}let ze;if(Ie.key!=null)ze=se.get(Ie.key);else for(re=Y;re<=U;re++)if(Qt[re-Y]===0&&lt(Ie,p[re])){ze=re;break}ze===void 0?Ee(Ie,w,C,!0):(Qt[ze-Y]=x+1,ze>=Io?Io=ze:kt=!0,I(Ie,p[ze],m,null,w,C,L,P,A),de++)}const Po=kt?_l(Qt):Lt;for(re=Po.length-1,x=Le-1;x>=0;x--){const Ie=Y+x,ze=p[Ie],Ro=Ie+1<z?p[Ie+1].el:v;Qt[x]===0?I(null,ze,m,Ro,w,C,L,P,A):kt&&(re<0||x!==Po[re]?Be(ze,m,Ro,2):re--)}}},Be=(d,p,m,v,w=null)=>{const{el:C,type:L,transition:P,children:A,shapeFlag:x}=d;if(x&6){Be(d.component.subTree,p,m,v);return}if(x&128){d.suspense.move(p,m,v);return}if(x&64){L.move(d,p,m,M);return}if(L===qe){r(C,p,m);for(let $=0;$<A.length;$++)Be(A[$],p,m,v);r(d.anchor,p,m);return}if(L===gr){R(d,p,m);return}if(v!==2&&x&1&&P)if(v===0)P.beforeEnter(C),r(C,p,m),ge(()=>P.enter(C),w);else{const{leave:$,delayLeave:U,afterLeave:V}=P,Y=()=>r(C,p,m),se=()=>{$(C,()=>{Y(),V&&V()})};U?U(C,Y,se):se()}else r(C,p,m)},Ee=(d,p,m,v=!1,w=!1)=>{const{type:C,props:L,ref:P,children:A,dynamicChildren:x,shapeFlag:z,patchFlag:$,dirs:U}=d;if(P!=null&&Hr(P,null,m,d,!0),z&256){p.ctx.deactivate(d);return}const V=z&1&&U,Y=!nn(d);let se;if(Y&&(se=L&&L.onVnodeBeforeUnmount)&&ke(se,p,d),z&6)En(d.component,m,v);else{if(z&128){d.suspense.unmount(m,v);return}V&&gt(d,null,p,"beforeUnmount"),z&64?d.type.remove(d,p,m,w,M,v):x&&(C!==qe||$>0&&$&64)?ve(x,p,m,!1,!0):(C===qe&&$&384||!w&&z&16)&&ve(A,p,m),v&&Pt(d)}(Y&&(se=L&&L.onVnodeUnmounted)||V)&&ge(()=>{se&&ke(se,p,d),V&&gt(d,null,p,"unmounted")},m)},Pt=d=>{const{type:p,el:m,anchor:v,transition:w}=d;if(p===qe){Rt(m,v);return}if(p===gr){y(d);return}const C=()=>{o(m),w&&!w.persisted&&w.afterLeave&&w.afterLeave()};if(d.shapeFlag&1&&w&&!w.persisted){const{leave:L,delayLeave:P}=w,A=()=>L(m,C);P?P(d.el,C,A):A()}else C()},Rt=(d,p)=>{let m;for(;d!==p;)m=f(d),o(d),d=m;o(p)},En=(d,p,m)=>{const{bum:v,scope:w,update:C,subTree:L,um:P}=d;v&&tn(v),w.stop(),C&&(C.active=!1,Ee(L,d,p,m)),P&&ge(P,p),ge(()=>{d.isUnmounted=!0},p),p&&p.pendingBranch&&!p.isUnmounted&&d.asyncDep&&!d.asyncResolved&&d.suspenseId===p.pendingId&&(p.deps--,p.deps===0&&p.resolve())},ve=(d,p,m,v=!1,w=!1,C=0)=>{for(let L=C;L<d.length;L++)Ee(d[L],p,m,v,w)},b=d=>d.shapeFlag&6?b(d.component.subTree):d.shapeFlag&128?d.suspense.next():f(d.anchor||d.el),j=(d,p,m)=>{d==null?p._vnode&&Ee(p._vnode,null,null,!0):I(p._vnode||null,d,p,null,null,null,m),$o(),Ei(),p._vnode=d},M={p:I,um:Ee,m:Be,r:Pt,mt:me,mc:O,pc:ee,pbc:K,n:b,o:e};let B,te;return t&&([B,te]=t(M)),{render:j,hydrate:B,createApp:ll(j,B)}}function vt({effect:e,update:t},n){e.allowRecurse=t.allowRecurse=n}function _o(e,t,n=!1){const r=e.children,o=t.children;if(q(r)&&q(o))for(let s=0;s<r.length;s++){const i=r[s];let a=o[s];a.shapeFlag&1&&!a.dynamicChildren&&((a.patchFlag<=0||a.patchFlag===32)&&(a=o[s]=at(o[s]),a.el=i.el),n||_o(i,a)),a.type===nr&&(a.el=i.el)}}function _l(e){const t=e.slice(),n=[0];let r,o,s,i,a;const c=e.length;for(r=0;r<c;r++){const l=e[r];if(l!==0){if(o=n[n.length-1],e[o]<l){t[r]=o,n.push(r);continue}for(s=0,i=n.length-1;s<i;)a=s+i>>1,e[n[a]]<l?s=a+1:i=a;l<e[n[s]]&&(s>0&&(t[r]=n[s-1]),n[s]=r)}}for(s=n.length,i=n[s-1];s-- >0;)n[s]=i,i=t[i];return n}const bl=e=>e.__isTeleport,on=e=>e&&(e.disabled||e.disabled===""),Go=e=>typeof SVGElement!="undefined"&&e instanceof SVGElement,Br=(e,t)=>{const n=e&&e.to;return ue(n)?t?t(n):null:n},yl={__isTeleport:!0,process(e,t,n,r,o,s,i,a,c,l){const{mc:u,pc:h,pbc:f,o:{insert:g,querySelector:E,createText:I,createComment:D}}=l,S=on(t.props);let{shapeFlag:_,children:R,dynamicChildren:y}=t;if(e==null){const T=t.el=I(""),k=t.anchor=I("");g(T,n,r),g(k,n,r);const H=t.target=Br(t.props,E),O=t.targetAnchor=I("");H&&(g(O,H),i=i||Go(H));const N=(K,Q)=>{_&16&&u(R,K,Q,o,s,i,a,c)};S?N(n,k):H&&N(H,O)}else{t.el=e.el;const T=t.anchor=e.anchor,k=t.target=e.target,H=t.targetAnchor=e.targetAnchor,O=on(e.props),N=O?n:k,K=O?T:H;if(i=i||Go(k),y?(f(e.dynamicChildren,y,N,o,s,i,a),_o(e,t,!0)):c||h(e,t,N,K,o,s,i,a,!1),S)O||Rn(t,n,T,l,1);else if((t.props&&t.props.to)!==(e.props&&e.props.to)){const Q=t.target=Br(t.props,E);Q&&Rn(t,Q,null,l,0)}else O&&Rn(t,k,H,l,1)}zi(t)},remove(e,t,n,r,{um:o,o:{remove:s}},i){const{shapeFlag:a,children:c,anchor:l,targetAnchor:u,target:h,props:f}=e;if(h&&s(u),(i||!on(f))&&(s(l),a&16))for(let g=0;g<c.length;g++){const E=c[g];o(E,t,n,!0,!!E.dynamicChildren)}},move:Rn,hydrate:wl};function Rn(e,t,n,{o:{insert:r},m:o},s=2){s===0&&r(e.targetAnchor,t,n);const{el:i,anchor:a,shapeFlag:c,children:l,props:u}=e,h=s===2;if(h&&r(i,t,n),(!h||on(u))&&c&16)for(let f=0;f<l.length;f++)o(l[f],t,n,2);h&&r(a,t,n)}function wl(e,t,n,r,o,s,{o:{nextSibling:i,parentNode:a,querySelector:c}},l){const u=t.target=Br(t.props,c);if(u){const h=u._lpa||u.firstChild;if(t.shapeFlag&16)if(on(t.props))t.anchor=l(i(e),t,a(e),n,r,o,s),t.targetAnchor=h;else{t.anchor=i(e);let f=h;for(;f;)if(f=i(f),f&&f.nodeType===8&&f.data==="teleport anchor"){t.targetAnchor=f,u._lpa=t.targetAnchor&&i(t.targetAnchor);break}l(h,t,u,n,r,o,s)}zi(t)}return t.anchor&&i(t.anchor)}const vh=yl;function zi(e){const t=e.ctx;if(t&&t.ut){let n=e.children[0].el;for(;n!==e.targetAnchor;)n.nodeType===1&&n.setAttribute("data-v-owner",t.uid),n=n.nextSibling;t.ut()}}const qe=Symbol.for("v-fgt"),nr=Symbol.for("v-txt"),$e=Symbol.for("v-cmt"),gr=Symbol.for("v-stc"),sn=[];let je=null;function Ui(e=!1){sn.push(je=e?null:[])}function El(){sn.pop(),je=sn[sn.length-1]||null}let gn=1;function Xo(e){gn+=e}function qi(e){return e.dynamicChildren=gn>0?je||Lt:null,El(),gn>0&&je&&je.push(e),e}function Sl(e,t,n,r,o,s){return qi(bo(e,t,n,r,o,s,!0))}function Cl(e,t,n,r,o){return qi(xe(e,t,n,r,o,!0))}function Bn(e){return e?e.__v_isVNode===!0:!1}function lt(e,t){return e.type===t.type&&e.key===t.key}const rr="__vInternal",Ki=({key:e})=>e!=null?e:null,Tn=({ref:e,ref_key:t,ref_for:n})=>(typeof e=="number"&&(e=""+e),e!=null?ue(e)||fe(e)||W(e)?{i:Ce,r:e,k:t,f:!!n}:e:null);function bo(e,t=null,n=null,r=0,o=null,s=e===qe?0:1,i=!1,a=!1){const c={__v_isVNode:!0,__v_skip:!0,type:e,props:t,key:t&&Ki(t),ref:t&&Tn(t),scopeId:Xn,slotScopeIds:null,children:n,component:null,suspense:null,ssContent:null,ssFallback:null,dirs:null,transition:null,el:null,anchor:null,target:null,targetAnchor:null,staticCount:0,shapeFlag:s,patchFlag:r,dynamicProps:o,dynamicChildren:null,appContext:null,ctx:Ce};return a?(yo(c,n),s&128&&e.normalize(c)):n&&(c.shapeFlag|=ue(n)?8:16),gn>0&&!i&&je&&(c.patchFlag>0||s&6)&&c.patchFlag!==32&&je.push(c),c}const xe=xl;function xl(e,t=null,n=null,r=0,o=null,s=!1){if((!e||e===el)&&(e=$e),Bn(e)){const a=Ze(e,t,!0);return n&&yo(a,n),gn>0&&!s&&je&&(a.shapeFlag&6?je[je.indexOf(e)]=a:je.push(a)),a.patchFlag|=-2,a}if(Dl(e)&&(e=e.__vccOpts),t){t=Ol(t);let{class:a,style:c}=t;a&&!ue(a)&&(t.class=ro(a)),ae(c)&&(mi(c)&&!q(c)&&(c=he({},c)),t.style=no(c))}const i=ue(e)?1:xi(e)?128:bl(e)?64:ae(e)?4:W(e)?2:0;return bo(e,t,n,r,o,i,s,!0)}function Ol(e){return e?mi(e)||rr in e?he({},e):e:null}function Ze(e,t,n=!1){const{props:r,ref:o,patchFlag:s,children:i}=e,a=t?Pl(r||{},t):r;return{__v_isVNode:!0,__v_skip:!0,type:e.type,props:a,key:a&&Ki(a),ref:t&&t.ref?n&&o?q(o)?o.concat(Tn(t)):[o,Tn(t)]:Tn(t):o,scopeId:e.scopeId,slotScopeIds:e.slotScopeIds,children:i,target:e.target,targetAnchor:e.targetAnchor,staticCount:e.staticCount,shapeFlag:e.shapeFlag,patchFlag:t&&e.type!==qe?s===-1?16:s|16:s,dynamicProps:e.dynamicProps,dynamicChildren:e.dynamicChildren,appContext:e.appContext,dirs:e.dirs,transition:e.transition,component:e.component,suspense:e.suspense,ssContent:e.ssContent&&Ze(e.ssContent),ssFallback:e.ssFallback&&Ze(e.ssFallback),el:e.el,anchor:e.anchor,ctx:e.ctx,ce:e.ce}}function Il(e=" ",t=0){return xe(nr,null,e,t)}function _h(e="",t=!1){return t?(Ui(),Cl($e,null,e)):xe($e,null,e)}function Ke(e){return e==null||typeof e=="boolean"?xe($e):q(e)?xe(qe,null,e.slice()):typeof e=="object"?at(e):xe(nr,null,String(e))}function at(e){return e.el===null&&e.patchFlag!==-1||e.memo?e:Ze(e)}function yo(e,t){let n=0;const{shapeFlag:r}=e;if(t==null)t=null;else if(q(t))n=16;else if(typeof t=="object")if(r&65){const o=t.default;o&&(o._c&&(o._d=!1),yo(e,o()),o._c&&(o._d=!0));return}else{n=32;const o=t._;!o&&!(rr in t)?t._ctx=Ce:o===3&&Ce&&(Ce.slots._===1?t._=1:(t._=2,e.patchFlag|=1024))}else W(t)?(t={default:t,_ctx:Ce},n=32):(t=String(t),r&64?(n=16,t=[Il(t)]):n=8);e.children=t,e.shapeFlag|=n}function Pl(...e){const t={};for(let n=0;n<e.length;n++){const r=e[n];for(const o in r)if(o==="class")t.class!==r.class&&(t.class=ro([t.class,r.class]));else if(o==="style")t.style=no([t.style,r.style]);else if(Vn(o)){const s=t[o],i=r[o];i&&s!==i&&!(q(s)&&s.includes(i))&&(t[o]=s?[].concat(s,i):i)}else o!==""&&(t[o]=r[o])}return t}function ke(e,t,n,r=null){Me(e,t,7,[n,r])}const Rl=Di();let kl=0;function Al(e,t,n){const r=e.type,o=(t?t.appContext:e.appContext)||Rl,s={uid:kl++,vnode:e,type:r,parent:t,appContext:o,root:null,next:null,subTree:null,effect:null,update:null,scope:new ei(!0),render:null,proxy:null,exposed:null,exposeProxy:null,withProxy:null,provides:t?t.provides:Object.create(o.provides),accessCache:null,renderCache:[],components:null,directives:null,propsOptions:Fi(r,o),emitsOptions:Ci(r,o),emit:null,emitted:null,propsDefaults:ie,inheritAttrs:r.inheritAttrs,ctx:ie,data:ie,props:ie,attrs:ie,slots:ie,refs:ie,setupState:ie,setupContext:null,attrsProxy:null,slotsProxy:null,suspense:n,suspenseId:n?n.pendingId:0,asyncDep:null,asyncResolved:!1,isMounted:!1,isUnmounted:!1,isDeactivated:!1,bc:null,c:null,bm:null,m:null,bu:null,u:null,um:null,bum:null,da:null,a:null,rtg:null,rtc:null,ec:null,sp:null};return s.ctx={_:s},s.root=t?t.root:s,s.emit=Mc.bind(null,s),e.ce&&e.ce(s),s}let pe=null;const Vi=()=>pe||Ce;let wo,At,Zo="__VUE_INSTANCE_SETTERS__";(At=kr()[Zo])||(At=kr()[Zo]=[]),At.push(e=>pe=e),wo=e=>{At.length>1?At.forEach(t=>t(e)):At[0](e)};const Bt=e=>{wo(e),e.scope.on()},xt=()=>{pe&&pe.scope.off(),wo(null)};function Wi(e){return e.vnode.shapeFlag&4}let vn=!1;function Ml(e,t=!1){vn=t;const{props:n,children:r}=e.vnode,o=Wi(e);fl(e,n,o,t),pl(e,r);const s=o?Tl(e,t):void 0;return vn=!1,s}function Tl(e,t){const n=e.type;e.accessCache=Object.create(null),e.proxy=wn(new Proxy(e.ctx,nl));const{setup:r}=n;if(r){const o=e.setupContext=r.length>1?Nl(e):null;Bt(e),Vt();const s=ht(r,e,0,[e.props,o]);if(Wt(),xt(),Ys(s)){if(s.then(xt,xt),t)return s.then(i=>{es(e,i,t)}).catch(i=>{Jn(i,e,0)});e.asyncDep=s}else es(e,s,t)}else Qi(e,t)}function es(e,t,n){W(t)?e.type.__ssrInlineRender?e.ssrRender=t:e.render=t:ae(t)&&(e.setupState=bi(t)),Qi(e,n)}let ts;function Qi(e,t,n){const r=e.type;if(!e.render){if(!t&&ts&&!r.render){const o=r.template||go(e).template;if(o){const{isCustomElement:s,compilerOptions:i}=e.appContext.config,{delimiters:a,compilerOptions:c}=r,l=he(he({isCustomElement:s,delimiters:a},i),c);r.render=ts(o,l)}}e.render=r.render||Fe}Bt(e),Vt(),rl(e),Wt(),xt()}function Ll(e){return e.attrsProxy||(e.attrsProxy=new Proxy(e.attrs,{get(t,n){return Oe(e,"get","$attrs"),t[n]}}))}function Nl(e){const t=n=>{e.exposed=n||{}};return{get attrs(){return Ll(e)},slots:e.slots,emit:e.emit,expose:t}}function or(e){if(e.exposed)return e.exposeProxy||(e.exposeProxy=new Proxy(bi(wn(e.exposed)),{get(t,n){if(n in t)return t[n];if(n in rn)return rn[n](e)},has(t,n){return n in t||n in rn}}))}function zr(e,t=!0){return W(e)?e.displayName||e.name:e.name||t&&e.__name}function Dl(e){return W(e)&&"__vccOpts"in e}const Se=(e,t)=>Oc(e,t,vn);function Eo(e,t,n){const r=arguments.length;return r===2?ae(t)&&!q(t)?Bn(t)?xe(e,null,[t]):xe(e,t):xe(e,null,t):(r>3?n=Array.prototype.slice.call(arguments,2):r===3&&Bn(n)&&(n=[n]),xe(e,t,n))}const jl=Symbol.for("v-scx"),Fl=()=>Te(jl),$l="3.3.4",Hl="http://www.w3.org/2000/svg",wt=typeof document!="undefined"?document:null,ns=wt&&wt.createElement("template"),Bl={insert:(e,t,n)=>{t.insertBefore(e,n||null)},remove:e=>{const t=e.parentNode;t&&t.removeChild(e)},createElement:(e,t,n,r)=>{const o=t?wt.createElementNS(Hl,e):wt.createElement(e,n?{is:n}:void 0);return e==="select"&&r&&r.multiple!=null&&o.setAttribute("multiple",r.multiple),o},createText:e=>wt.createTextNode(e),createComment:e=>wt.createComment(e),setText:(e,t)=>{e.nodeValue=t},setElementText:(e,t)=>{e.textContent=t},parentNode:e=>e.parentNode,nextSibling:e=>e.nextSibling,querySelector:e=>wt.querySelector(e),setScopeId(e,t){e.setAttribute(t,"")},insertStaticContent(e,t,n,r,o,s){const i=n?n.previousSibling:t.lastChild;if(o&&(o===s||o.nextSibling))for(;t.insertBefore(o.cloneNode(!0),n),!(o===s||!(o=o.nextSibling)););else{ns.innerHTML=r?`<svg>${e}</svg>`:e;const a=ns.content;if(r){const c=a.firstChild;for(;c.firstChild;)a.appendChild(c.firstChild);a.removeChild(c)}t.insertBefore(a,n)}return[i?i.nextSibling:t.firstChild,n?n.previousSibling:t.lastChild]}};function zl(e,t,n){const r=e._vtc;r&&(t=(t?[t,...r]:[...r]).join(" ")),t==null?e.removeAttribute("class"):n?e.setAttribute("class",t):e.className=t}function Ul(e,t,n){const r=e.style,o=ue(n);if(n&&!o){if(t&&!ue(t))for(const s in t)n[s]==null&&Ur(r,s,"");for(const s in n)Ur(r,s,n[s])}else{const s=r.display;o?t!==n&&(r.cssText=n):t&&e.removeAttribute("style"),"_vod"in e&&(r.display=s)}}const rs=/\s*!important$/;function Ur(e,t,n){if(q(n))n.forEach(r=>Ur(e,t,r));else if(n==null&&(n=""),t.startsWith("--"))e.setProperty(t,n);else{const r=ql(e,t);rs.test(n)?e.setProperty(Kt(r),n.replace(rs,""),"important"):e[r]=n}}const os=["Webkit","Moz","ms"],vr={};function ql(e,t){const n=vr[t];if(n)return n;let r=We(t);if(r!=="filter"&&r in e)return vr[t]=r;r=Qn(r);for(let o=0;o<os.length;o++){const s=os[o]+r;if(s in e)return vr[t]=s}return t}const ss="http://www.w3.org/1999/xlink";function Kl(e,t,n,r,o){if(r&&t.startsWith("xlink:"))n==null?e.removeAttributeNS(ss,t.slice(6,t.length)):e.setAttributeNS(ss,t,n);else{const s=Ua(t);n==null||s&&!Xs(n)?e.removeAttribute(t):e.setAttribute(t,s?"":n)}}function Vl(e,t,n,r,o,s,i){if(t==="innerHTML"||t==="textContent"){r&&i(r,o,s),e[t]=n==null?"":n;return}const a=e.tagName;if(t==="value"&&a!=="PROGRESS"&&!a.includes("-")){e._value=n;const l=a==="OPTION"?e.getAttribute("value"):e.value,u=n==null?"":n;l!==u&&(e.value=u),n==null&&e.removeAttribute(t);return}let c=!1;if(n===""||n==null){const l=typeof e[t];l==="boolean"?n=Xs(n):n==null&&l==="string"?(n="",c=!0):l==="number"&&(n=0,c=!0)}try{e[t]=n}catch{}c&&e.removeAttribute(t)}function Wl(e,t,n,r){e.addEventListener(t,n,r)}function Ql(e,t,n,r){e.removeEventListener(t,n,r)}function Yl(e,t,n,r,o=null){const s=e._vei||(e._vei={}),i=s[t];if(r&&i)i.value=r;else{const[a,c]=Jl(t);if(r){const l=s[t]=Zl(r,o);Wl(e,a,l,c)}else i&&(Ql(e,a,i,c),s[t]=void 0)}}const is=/(?:Once|Passive|Capture)$/;function Jl(e){let t;if(is.test(e)){t={};let r;for(;r=e.match(is);)e=e.slice(0,e.length-r[0].length),t[r[0].toLowerCase()]=!0}return[e[2]===":"?e.slice(3):Kt(e.slice(2)),t]}let _r=0;const Gl=Promise.resolve(),Xl=()=>_r||(Gl.then(()=>_r=0),_r=Date.now());function Zl(e,t){const n=r=>{if(!r._vts)r._vts=Date.now();else if(r._vts<=n.attached)return;Me(eu(r,n.value),t,5,[r])};return n.value=e,n.attached=Xl(),n}function eu(e,t){if(q(t)){const n=e.stopImmediatePropagation;return e.stopImmediatePropagation=()=>{n.call(e),e._stopped=!0},t.map(r=>o=>!o._stopped&&r&&r(o))}else return t}const as=/^on[a-z]/,tu=(e,t,n,r,o=!1,s,i,a,c)=>{t==="class"?zl(e,r,o):t==="style"?Ul(e,n,r):Vn(t)?Xr(t)||Yl(e,t,n,r,i):(t[0]==="."?(t=t.slice(1),!0):t[0]==="^"?(t=t.slice(1),!1):nu(e,t,r,o))?Vl(e,t,r,s,i,a,c):(t==="true-value"?e._trueValue=r:t==="false-value"&&(e._falseValue=r),Kl(e,t,r,o))};function nu(e,t,n,r){return r?!!(t==="innerHTML"||t==="textContent"||t in e&&as.test(t)&&W(n)):t==="spellcheck"||t==="draggable"||t==="translate"||t==="form"||t==="list"&&e.tagName==="INPUT"||t==="type"&&e.tagName==="TEXTAREA"||as.test(t)&&ue(n)?!1:t in e}const rt="transition",Yt="animation",Yi=(e,{slots:t})=>Eo(zc,ru(e),t);Yi.displayName="Transition";const Ji={name:String,type:String,css:{type:Boolean,default:!0},duration:[String,Number,Object],enterFromClass:String,enterActiveClass:String,enterToClass:String,appearFromClass:String,appearActiveClass:String,appearToClass:String,leaveFromClass:String,leaveActiveClass:String,leaveToClass:String};Yi.props=he({},Pi,Ji);const _t=(e,t=[])=>{q(e)?e.forEach(n=>n(...t)):e&&e(...t)},cs=e=>e?q(e)?e.some(t=>t.length>1):e.length>1:!1;function ru(e){const t={};for(const F in e)F in Ji||(t[F]=e[F]);if(e.css===!1)return t;const{name:n="v",type:r,duration:o,enterFromClass:s=`${n}-enter-from`,enterActiveClass:i=`${n}-enter-active`,enterToClass:a=`${n}-enter-to`,appearFromClass:c=s,appearActiveClass:l=i,appearToClass:u=a,leaveFromClass:h=`${n}-leave-from`,leaveActiveClass:f=`${n}-leave-active`,leaveToClass:g=`${n}-leave-to`}=e,E=ou(o),I=E&&E[0],D=E&&E[1],{onBeforeEnter:S,onEnter:_,onEnterCancelled:R,onLeave:y,onLeaveCancelled:T,onBeforeAppear:k=S,onAppear:H=_,onAppearCancelled:O=R}=t,N=(F,Z,me)=>{bt(F,Z?u:a),bt(F,Z?l:i),me&&me()},K=(F,Z)=>{F._isLeaving=!1,bt(F,h),bt(F,g),bt(F,f),Z&&Z()},Q=F=>(Z,me)=>{const ye=F?H:_,J=()=>N(Z,F,me);_t(ye,[Z,J]),ls(()=>{bt(Z,F?c:s),ot(Z,F?u:a),cs(ye)||us(Z,r,I,J)})};return he(t,{onBeforeEnter(F){_t(S,[F]),ot(F,s),ot(F,i)},onBeforeAppear(F){_t(k,[F]),ot(F,c),ot(F,l)},onEnter:Q(!1),onAppear:Q(!0),onLeave(F,Z){F._isLeaving=!0;const me=()=>K(F,Z);ot(F,h),au(),ot(F,f),ls(()=>{!F._isLeaving||(bt(F,h),ot(F,g),cs(y)||us(F,r,D,me))}),_t(y,[F,me])},onEnterCancelled(F){N(F,!1),_t(R,[F])},onAppearCancelled(F){N(F,!0),_t(O,[F])},onLeaveCancelled(F){K(F),_t(T,[F])}})}function ou(e){if(e==null)return null;if(ae(e))return[br(e.enter),br(e.leave)];{const t=br(e);return[t,t]}}function br(e){return ja(e)}function ot(e,t){t.split(/\s+/).forEach(n=>n&&e.classList.add(n)),(e._vtc||(e._vtc=new Set)).add(t)}function bt(e,t){t.split(/\s+/).forEach(r=>r&&e.classList.remove(r));const{_vtc:n}=e;n&&(n.delete(t),n.size||(e._vtc=void 0))}function ls(e){requestAnimationFrame(()=>{requestAnimationFrame(e)})}let su=0;function us(e,t,n,r){const o=e._endId=++su,s=()=>{o===e._endId&&r()};if(n)return setTimeout(s,n);const{type:i,timeout:a,propCount:c}=iu(e,t);if(!i)return r();const l=i+"end";let u=0;const h=()=>{e.removeEventListener(l,f),s()},f=g=>{g.target===e&&++u>=c&&h()};setTimeout(()=>{u<c&&h()},a+1),e.addEventListener(l,f)}function iu(e,t){const n=window.getComputedStyle(e),r=E=>(n[E]||"").split(", "),o=r(`${rt}Delay`),s=r(`${rt}Duration`),i=fs(o,s),a=r(`${Yt}Delay`),c=r(`${Yt}Duration`),l=fs(a,c);let u=null,h=0,f=0;t===rt?i>0&&(u=rt,h=i,f=s.length):t===Yt?l>0&&(u=Yt,h=l,f=c.length):(h=Math.max(i,l),u=h>0?i>l?rt:Yt:null,f=u?u===rt?s.length:c.length:0);const g=u===rt&&/\b(transform|all)(,|$)/.test(r(`${rt}Property`).toString());return{type:u,timeout:h,propCount:f,hasTransform:g}}function fs(e,t){for(;e.length<t.length;)e=e.concat(e);return Math.max(...t.map((n,r)=>ds(n)+ds(e[r])))}function ds(e){return Number(e.slice(0,-1).replace(",","."))*1e3}function au(){return document.body.offsetHeight}const cu=["ctrl","shift","alt","meta"],lu={stop:e=>e.stopPropagation(),prevent:e=>e.preventDefault(),self:e=>e.target!==e.currentTarget,ctrl:e=>!e.ctrlKey,shift:e=>!e.shiftKey,alt:e=>!e.altKey,meta:e=>!e.metaKey,left:e=>"button"in e&&e.button!==0,middle:e=>"button"in e&&e.button!==1,right:e=>"button"in e&&e.button!==2,exact:(e,t)=>cu.some(n=>e[`${n}Key`]&&!t.includes(n))},bh=(e,t)=>(n,...r)=>{for(let o=0;o<t.length;o++){const s=lu[t[o]];if(s&&s(n,t))return}return e(n,...r)},yh={beforeMount(e,{value:t},{transition:n}){e._vod=e.style.display==="none"?"":e.style.display,n&&t?n.beforeEnter(e):Jt(e,t)},mounted(e,{value:t},{transition:n}){n&&t&&n.enter(e)},updated(e,{value:t,oldValue:n},{transition:r}){!t!=!n&&(r?t?(r.beforeEnter(e),Jt(e,!0),r.enter(e)):r.leave(e,()=>{Jt(e,!1)}):Jt(e,t))},beforeUnmount(e,{value:t}){Jt(e,t)}};function Jt(e,t){e.style.display=t?e._vod:"none"}const uu=he({patchProp:tu},Bl);let hs;function fu(){return hs||(hs=gl(uu))}const du=(...e)=>{const t=fu().createApp(...e),{mount:n}=t;return t.mount=r=>{const o=hu(r);if(!o)return;const s=t._component;!W(s)&&!s.render&&!s.template&&(s.template=o.innerHTML),o.innerHTML="";const i=n(o,!1,o instanceof SVGElement);return o instanceof Element&&(o.removeAttribute("v-cloak"),o.setAttribute("data-v-app","")),i},t};function hu(e){return ue(e)?document.querySelector(e):e}function So(e,t,n,r){return Object.defineProperty(e,t,{get:n,set:r,enumerable:!0}),e}const Ot=Ht(!1);let sr;function pu(e,t){const n=/(edg|edge|edga|edgios)\/([\w.]+)/.exec(e)||/(opr)[\/]([\w.]+)/.exec(e)||/(vivaldi)[\/]([\w.]+)/.exec(e)||/(chrome|crios)[\/]([\w.]+)/.exec(e)||/(version)(applewebkit)[\/]([\w.]+).*(safari)[\/]([\w.]+)/.exec(e)||/(webkit)[\/]([\w.]+).*(version)[\/]([\w.]+).*(safari)[\/]([\w.]+)/.exec(e)||/(firefox|fxios)[\/]([\w.]+)/.exec(e)||/(webkit)[\/]([\w.]+)/.exec(e)||/(opera)(?:.*version|)[\/]([\w.]+)/.exec(e)||[];return{browser:n[5]||n[3]||n[1]||"",version:n[2]||n[4]||"0",versionNumber:n[4]||n[2]||"0",platform:t[0]||""}}function mu(e){return/(ipad)/.exec(e)||/(ipod)/.exec(e)||/(windows phone)/.exec(e)||/(iphone)/.exec(e)||/(kindle)/.exec(e)||/(silk)/.exec(e)||/(android)/.exec(e)||/(win)/.exec(e)||/(mac)/.exec(e)||/(linux)/.exec(e)||/(cros)/.exec(e)||/(playbook)/.exec(e)||/(bb)/.exec(e)||/(blackberry)/.exec(e)||[]}const Gi="ontouchstart"in window||window.navigator.maxTouchPoints>0;function gu(e){sr={is:{...e}},delete e.mac,delete e.desktop;const t=Math.min(window.innerHeight,window.innerWidth)>414?"ipad":"iphone";Object.assign(e,{mobile:!0,ios:!0,platform:t,[t]:!0})}function vu(e){const t=e.toLowerCase(),n=mu(t),r=pu(t,n),o={};r.browser&&(o[r.browser]=!0,o.version=r.version,o.versionNumber=parseInt(r.versionNumber,10)),r.platform&&(o[r.platform]=!0);const s=o.android||o.ios||o.bb||o.blackberry||o.ipad||o.iphone||o.ipod||o.kindle||o.playbook||o.silk||o["windows phone"];return s===!0||t.indexOf("mobile")>-1?(o.mobile=!0,o.edga||o.edgios?(o.edge=!0,r.browser="edge"):o.crios?(o.chrome=!0,r.browser="chrome"):o.fxios&&(o.firefox=!0,r.browser="firefox")):o.desktop=!0,(o.ipod||o.ipad||o.iphone)&&(o.ios=!0),o["windows phone"]&&(o.winphone=!0,delete o["windows phone"]),(o.chrome||o.opr||o.safari||o.vivaldi||o.mobile===!0&&o.ios!==!0&&s!==!0)&&(o.webkit=!0),o.edg&&(r.browser="edgechromium",o.edgeChromium=!0),(o.safari&&o.blackberry||o.bb)&&(r.browser="blackberry",o.blackberry=!0),o.safari&&o.playbook&&(r.browser="playbook",o.playbook=!0),o.opr&&(r.browser="opera",o.opera=!0),o.safari&&o.android&&(r.browser="android",o.android=!0),o.safari&&o.kindle&&(r.browser="kindle",o.kindle=!0),o.safari&&o.silk&&(r.browser="silk",o.silk=!0),o.vivaldi&&(r.browser="vivaldi",o.vivaldi=!0),o.name=r.browser,o.platform=r.platform,t.indexOf("electron")>-1?o.electron=!0:document.location.href.indexOf("-extension://")>-1?o.bex=!0:(window.Capacitor!==void 0?(o.capacitor=!0,o.nativeMobile=!0,o.nativeMobileWrapper="capacitor"):(window._cordovaNative!==void 0||window.cordova!==void 0)&&(o.cordova=!0,o.nativeMobile=!0,o.nativeMobileWrapper="cordova"),Gi===!0&&o.mac===!0&&(o.desktop===!0&&o.safari===!0||o.nativeMobile===!0&&o.android!==!0&&o.ios!==!0&&o.ipad!==!0)&&gu(o)),o}const ps=navigator.userAgent||navigator.vendor||window.opera,_u={has:{touch:!1,webStorage:!1},within:{iframe:!1}},Ae={userAgent:ps,is:vu(ps),has:{touch:Gi},within:{iframe:window.self!==window.top}},qr={install(e){const{$q:t}=e;Ot.value===!0?(e.onSSRHydrated.push(()=>{Object.assign(t.platform,Ae),Ot.value=!1,sr=void 0}),t.platform=It(this)):t.platform=this}};{let e;So(Ae.has,"webStorage",()=>{if(e!==void 0)return e;try{if(window.localStorage)return e=!0,!0}catch{}return e=!1,!1}),Ae.is.ios===!0&&window.navigator.vendor.toLowerCase().indexOf("apple"),Ot.value===!0?Object.assign(qr,Ae,sr,_u):Object.assign(qr,Ae)}var ir=(e,t)=>{const n=It(e);for(const r in e)So(t,r,()=>n[r],o=>{n[r]=o});return t};const zt={hasPassive:!1,passiveCapture:!0,notPassiveCapture:!0};try{const e=Object.defineProperty({},"passive",{get(){Object.assign(zt,{hasPassive:!0,passive:{passive:!0},notPassive:{passive:!1},passiveCapture:{passive:!0,capture:!0},notPassiveCapture:{passive:!1,capture:!0}})}});window.addEventListener("qtest",null,e),window.removeEventListener("qtest",null,e)}catch{}function _n(){}function wh(e){return e.button===0}function Eh(e){return e.touches&&e.touches[0]?e=e.touches[0]:e.changedTouches&&e.changedTouches[0]?e=e.changedTouches[0]:e.targetTouches&&e.targetTouches[0]&&(e=e.targetTouches[0]),{top:e.clientY,left:e.clientX}}function Sh(e){if(e.path)return e.path;if(e.composedPath)return e.composedPath();const t=[];let n=e.target;for(;n;){if(t.push(n),n.tagName==="HTML")return t.push(document),t.push(window),t;n=n.parentElement}}function Ch(e){e.stopPropagation()}function ms(e){e.cancelable!==!1&&e.preventDefault()}function xh(e){e.cancelable!==!1&&e.preventDefault(),e.stopPropagation()}function Oh(e,t){if(e===void 0||t===!0&&e.__dragPrevented===!0)return;const n=t===!0?r=>{r.__dragPrevented=!0,r.addEventListener("dragstart",ms,zt.notPassiveCapture)}:r=>{delete r.__dragPrevented,r.removeEventListener("dragstart",ms,zt.notPassiveCapture)};e.querySelectorAll("a, img").forEach(n)}function Ih(e,t,n){const r=`__q_${t}_evt`;e[r]=e[r]!==void 0?e[r].concat(n):n,n.forEach(o=>{o[0].addEventListener(o[1],e[o[2]],zt[o[3]])})}function Ph(e,t){const n=`__q_${t}_evt`;e[n]!==void 0&&(e[n].forEach(r=>{r[0].removeEventListener(r[1],e[r[2]],zt[r[3]])}),e[n]=void 0)}function bu(e,t=250,n){let r=null;function o(){const s=arguments,i=()=>{r=null,n!==!0&&e.apply(this,s)};r!==null?clearTimeout(r):n===!0&&e.apply(this,s),r=setTimeout(i,t)}return o.cancel=()=>{r!==null&&clearTimeout(r)},o}const yr=["sm","md","lg","xl"],{passive:gs}=zt;var yu=ir({width:0,height:0,name:"xs",sizes:{sm:600,md:1024,lg:1440,xl:1920},lt:{sm:!0,md:!0,lg:!0,xl:!0},gt:{xs:!1,sm:!1,md:!1,lg:!1},xs:!0,sm:!1,md:!1,lg:!1,xl:!1},{setSizes:_n,setDebounce:_n,install({$q:e,onSSRHydrated:t}){if(e.screen=this,this.__installed===!0){e.config.screen!==void 0&&(e.config.screen.bodyClasses===!1?document.body.classList.remove(`screen--${this.name}`):this.__update(!0));return}const{visualViewport:n}=window,r=n||window,o=document.scrollingElement||document.documentElement,s=n===void 0||Ae.is.mobile===!0?()=>[Math.max(window.innerWidth,o.clientWidth),Math.max(window.innerHeight,o.clientHeight)]:()=>[n.width*n.scale+window.innerWidth-o.clientWidth,n.height*n.scale+window.innerHeight-o.clientHeight],i=e.config.screen!==void 0&&e.config.screen.bodyClasses===!0;this.__update=h=>{const[f,g]=s();if(g!==this.height&&(this.height=g),f!==this.width)this.width=f;else if(h!==!0)return;let E=this.sizes;this.gt.xs=f>=E.sm,this.gt.sm=f>=E.md,this.gt.md=f>=E.lg,this.gt.lg=f>=E.xl,this.lt.sm=f<E.sm,this.lt.md=f<E.md,this.lt.lg=f<E.lg,this.lt.xl=f<E.xl,this.xs=this.lt.sm,this.sm=this.gt.xs===!0&&this.lt.md===!0,this.md=this.gt.sm===!0&&this.lt.lg===!0,this.lg=this.gt.md===!0&&this.lt.xl===!0,this.xl=this.gt.lg,E=this.xs===!0&&"xs"||this.sm===!0&&"sm"||this.md===!0&&"md"||this.lg===!0&&"lg"||"xl",E!==this.name&&(i===!0&&(document.body.classList.remove(`screen--${this.name}`),document.body.classList.add(`screen--${E}`)),this.name=E)};let a,c={},l=16;this.setSizes=h=>{yr.forEach(f=>{h[f]!==void 0&&(c[f]=h[f])})},this.setDebounce=h=>{l=h};const u=()=>{const h=getComputedStyle(document.body);h.getPropertyValue("--q-size-sm")&&yr.forEach(f=>{this.sizes[f]=parseInt(h.getPropertyValue(`--q-size-${f}`),10)}),this.setSizes=f=>{yr.forEach(g=>{f[g]&&(this.sizes[g]=f[g])}),this.__update(!0)},this.setDebounce=f=>{a!==void 0&&r.removeEventListener("resize",a,gs),a=f>0?bu(this.__update,f):this.__update,r.addEventListener("resize",a,gs)},this.setDebounce(l),Object.keys(c).length!==0?(this.setSizes(c),c=void 0):this.__update(),i===!0&&this.name==="xs"&&document.body.classList.add("screen--xs")};Ot.value===!0?t.push(u):u()}});const _e=ir({isActive:!1,mode:!1},{__media:void 0,set(e){_e.mode=e,e==="auto"?(_e.__media===void 0&&(_e.__media=window.matchMedia("(prefers-color-scheme: dark)"),_e.__updateMedia=()=>{_e.set("auto")},_e.__media.addListener(_e.__updateMedia)),e=_e.__media.matches):_e.__media!==void 0&&(_e.__media.removeListener(_e.__updateMedia),_e.__media=void 0),_e.isActive=e===!0,document.body.classList.remove(`body--${e===!0?"light":"dark"}`),document.body.classList.add(`body--${e===!0?"dark":"light"}`)},toggle(){_e.set(_e.isActive===!1)},install({$q:e,onSSRHydrated:t,ssrContext:n}){const{dark:r}=e.config;if(e.dark=this,this.__installed===!0&&r===void 0)return;this.isActive=r===!0;const o=r!==void 0?r:!1;if(Ot.value===!0){const s=a=>{this.__fromSSR=a},i=this.set;this.set=s,s(o),t.push(()=>{this.set=i,this.set(this.__fromSSR)})}else this.set(o)}}),Xi=()=>!0;function wu(e){return typeof e=="string"&&e!==""&&e!=="/"&&e!=="#/"}function Eu(e){return e.startsWith("#")===!0&&(e=e.substring(1)),e.startsWith("/")===!1&&(e="/"+e),e.endsWith("/")===!0&&(e=e.substring(0,e.length-1)),"#"+e}function Su(e){if(e.backButtonExit===!1)return()=>!1;if(e.backButtonExit==="*")return Xi;const t=["#/"];return Array.isArray(e.backButtonExit)===!0&&t.push(...e.backButtonExit.filter(wu).map(Eu)),()=>t.includes(window.location.hash)}var Cu={__history:[],add:_n,remove:_n,install({$q:e}){if(this.__installed===!0)return;const{cordova:t,capacitor:n}=Ae.is;if(t!==!0&&n!==!0)return;const r=e.config[t===!0?"cordova":"capacitor"];if(r!==void 0&&r.backButton===!1||n===!0&&(window.Capacitor===void 0||window.Capacitor.Plugins.App===void 0))return;this.add=i=>{i.condition===void 0&&(i.condition=Xi),this.__history.push(i)},this.remove=i=>{const a=this.__history.indexOf(i);a>=0&&this.__history.splice(a,1)};const o=Su(Object.assign({backButtonExit:!0},r)),s=()=>{if(this.__history.length){const i=this.__history[this.__history.length-1];i.condition()===!0&&(this.__history.pop(),i.handler())}else o()===!0?navigator.app.exitApp():window.history.back()};t===!0?document.addEventListener("deviceready",()=>{document.addEventListener("backbutton",s,!1)}):window.Capacitor.Plugins.App.addListener("backButton",s)}},zn={isoName:"en-US",nativeName:"English (US)",label:{clear:"Clear",ok:"OK",cancel:"Cancel",close:"Close",set:"Set",select:"Select",reset:"Reset",remove:"Remove",update:"Update",create:"Create",search:"Search",filter:"Filter",refresh:"Refresh",expand:e=>e?`Expand "${e}"`:"Expand",collapse:e=>e?`Collapse "${e}"`:"Collapse"},date:{days:"Sunday_Monday_Tuesday_Wednesday_Thursday_Friday_Saturday".split("_"),daysShort:"Sun_Mon_Tue_Wed_Thu_Fri_Sat".split("_"),months:"January_February_March_April_May_June_July_August_September_October_November_December".split("_"),monthsShort:"Jan_Feb_Mar_Apr_May_Jun_Jul_Aug_Sep_Oct_Nov_Dec".split("_"),firstDayOfWeek:0,format24h:!1,pluralDay:"days"},table:{noData:"No data available",noResults:"No matching records found",loading:"Loading...",selectedRecords:e=>e===1?"1 record selected.":(e===0?"No":e)+" records selected.",recordsPerPage:"Records per page:",allRows:"All",pagination:(e,t,n)=>e+"-"+t+" of "+n,columns:"Columns"},editor:{url:"URL",bold:"Bold",italic:"Italic",strikethrough:"Strikethrough",underline:"Underline",unorderedList:"Unordered List",orderedList:"Ordered List",subscript:"Subscript",superscript:"Superscript",hyperlink:"Hyperlink",toggleFullscreen:"Toggle Fullscreen",quote:"Quote",left:"Left align",center:"Center align",right:"Right align",justify:"Justify align",print:"Print",outdent:"Decrease indentation",indent:"Increase indentation",removeFormat:"Remove formatting",formatting:"Formatting",fontSize:"Font Size",align:"Align",hr:"Insert Horizontal Rule",undo:"Undo",redo:"Redo",heading1:"Heading 1",heading2:"Heading 2",heading3:"Heading 3",heading4:"Heading 4",heading5:"Heading 5",heading6:"Heading 6",paragraph:"Paragraph",code:"Code",size1:"Very small",size2:"A bit small",size3:"Normal",size4:"Medium-large",size5:"Big",size6:"Very big",size7:"Maximum",defaultFont:"Default Font",viewSource:"View Source"},tree:{noNodes:"No nodes available",noResults:"No matching nodes found"}},Rh=Object.freeze(Object.defineProperty({__proto__:null,default:zn},Symbol.toStringTag,{value:"Module"}));function vs(){const e=Array.isArray(navigator.languages)===!0&&navigator.languages.length!==0?navigator.languages[0]:navigator.language;if(typeof e=="string")return e.split(/[-_]/).map((t,n)=>n===0?t.toLowerCase():n>1||t.length<4?t.toUpperCase():t[0].toUpperCase()+t.slice(1).toLowerCase()).join("-")}const Ne=ir({__langPack:{}},{getLocale:vs,set(e=zn,t){const n={...e,rtl:e.rtl===!0,getLocale:vs};{if(n.set=Ne.set,Ne.__langConfig===void 0||Ne.__langConfig.noHtmlAttrs!==!0){const r=document.documentElement;r.setAttribute("dir",n.rtl===!0?"rtl":"ltr"),r.setAttribute("lang",n.isoName)}Object.assign(Ne.__langPack,n),Ne.props=n,Ne.isoName=n.isoName,Ne.nativeName=n.nativeName}},install({$q:e,lang:t,ssrContext:n}){e.lang=Ne.__langPack,Ne.__langConfig=e.config.lang,this.__installed===!0?t!==void 0&&this.set(t):this.set(t||zn)}});function xu(e,t,n=document.body){if(typeof e!="string")throw new TypeError("Expected a string as propName");if(typeof t!="string")throw new TypeError("Expected a string as value");if(!(n instanceof Element))throw new TypeError("Expected a DOM element");n.style.setProperty(`--q-${e}`,t)}let Zi=!1;function Ou(e){Zi=e.isComposing===!0}function Iu(e){return Zi===!0||e!==Object(e)||e.isComposing===!0||e.qKeyEvent===!0}function kh(e,t){return Iu(e)===!0?!1:[].concat(t).includes(e.keyCode)}function ea(e){if(e.ios===!0)return"ios";if(e.android===!0)return"android"}function Pu({is:e,has:t,within:n},r){const o=[e.desktop===!0?"desktop":"mobile",`${t.touch===!1?"no-":""}touch`];if(e.mobile===!0){const s=ea(e);s!==void 0&&o.push("platform-"+s)}if(e.nativeMobile===!0){const s=e.nativeMobileWrapper;o.push(s),o.push("native-mobile"),e.ios===!0&&(r[s]===void 0||r[s].iosStatusBarPadding!==!1)&&o.push("q-ios-padding")}else e.electron===!0?o.push("electron"):e.bex===!0&&o.push("bex");return n.iframe===!0&&o.push("within-iframe"),o}function Ru(){const{is:e}=Ae,t=document.body.className,n=new Set(t.replace(/ {2}/g," ").split(" "));if(sr!==void 0)n.delete("desktop"),n.add("platform-ios"),n.add("mobile");else if(e.nativeMobile!==!0&&e.electron!==!0&&e.bex!==!0){if(e.desktop===!0)n.delete("mobile"),n.delete("platform-ios"),n.delete("platform-android"),n.add("desktop");else if(e.mobile===!0){n.delete("desktop"),n.add("mobile");const o=ea(e);o!==void 0?(n.add(`platform-${o}`),n.delete(`platform-${o==="ios"?"android":"ios"}`)):(n.delete("platform-ios"),n.delete("platform-android"))}}Ae.has.touch===!0&&(n.delete("no-touch"),n.add("touch")),Ae.within.iframe===!0&&n.add("within-iframe");const r=Array.from(n).join(" ");t!==r&&(document.body.className=r)}function ku(e){for(const t in e)xu(t,e[t])}var Au={install(e){if(this.__installed!==!0){if(Ot.value===!0)Ru();else{const{$q:t}=e;t.config.brand!==void 0&&ku(t.config.brand);const n=Pu(Ae,t.config);document.body.classList.add.apply(document.body.classList,n)}Ae.is.ios===!0&&document.body.addEventListener("touchstart",_n),window.addEventListener("keydown",Ou,!0)}}},Mu={name:"material-icons",type:{positive:"check_circle",negative:"warning",info:"info",warning:"priority_high"},arrow:{up:"arrow_upward",right:"arrow_forward",down:"arrow_downward",left:"arrow_back",dropdown:"arrow_drop_down"},chevron:{left:"chevron_left",right:"chevron_right"},colorPicker:{spectrum:"gradient",tune:"tune",palette:"style"},pullToRefresh:{icon:"refresh"},carousel:{left:"chevron_left",right:"chevron_right",up:"keyboard_arrow_up",down:"keyboard_arrow_down",navigationIcon:"lens"},chip:{remove:"cancel",selected:"check"},datetime:{arrowLeft:"chevron_left",arrowRight:"chevron_right",now:"access_time",today:"today"},editor:{bold:"format_bold",italic:"format_italic",strikethrough:"strikethrough_s",underline:"format_underlined",unorderedList:"format_list_bulleted",orderedList:"format_list_numbered",subscript:"vertical_align_bottom",superscript:"vertical_align_top",hyperlink:"link",toggleFullscreen:"fullscreen",quote:"format_quote",left:"format_align_left",center:"format_align_center",right:"format_align_right",justify:"format_align_justify",print:"print",outdent:"format_indent_decrease",indent:"format_indent_increase",removeFormat:"format_clear",formatting:"text_format",fontSize:"format_size",align:"format_align_left",hr:"remove",undo:"undo",redo:"redo",heading:"format_size",code:"code",size:"format_size",font:"font_download",viewSource:"code"},expansionItem:{icon:"keyboard_arrow_down",denseIcon:"arrow_drop_down"},fab:{icon:"add",activeIcon:"close"},field:{clear:"cancel",error:"error"},pagination:{first:"first_page",prev:"keyboard_arrow_left",next:"keyboard_arrow_right",last:"last_page"},rating:{icon:"grade"},stepper:{done:"check",active:"edit",error:"warning"},tabs:{left:"chevron_left",right:"chevron_right",up:"keyboard_arrow_up",down:"keyboard_arrow_down"},table:{arrowUp:"arrow_upward",warning:"warning",firstPage:"first_page",prevPage:"chevron_left",nextPage:"chevron_right",lastPage:"last_page"},tree:{icon:"play_arrow"},uploader:{done:"done",clear:"clear",add:"add_box",upload:"cloud_upload",removeQueue:"clear_all",removeUploaded:"done_all"}};const Un=ir({iconMapFn:null,__icons:{}},{set(e,t){const n={...e,rtl:e.rtl===!0};n.set=Un.set,Object.assign(Un.__icons,n)},install({$q:e,iconSet:t,ssrContext:n}){e.config.iconMapFn!==void 0&&(this.iconMapFn=e.config.iconMapFn),e.iconSet=this.__icons,So(e,"iconMapFn",()=>this.iconMapFn,r=>{this.iconMapFn=r}),this.__installed===!0?t!==void 0&&this.set(t):this.set(t||Mu)}}),Tu="_q_",Ah="_q_l_",Mh="_q_pc_",Th="_q_tabs_",Lh=()=>{},_s={};let ta=!1;function Lu(){ta=!0}function wr(e,t){if(e===t)return!0;if(e!==null&&t!==null&&typeof e=="object"&&typeof t=="object"){if(e.constructor!==t.constructor)return!1;let n,r;if(e.constructor===Array){if(n=e.length,n!==t.length)return!1;for(r=n;r--!==0;)if(wr(e[r],t[r])!==!0)return!1;return!0}if(e.constructor===Map){if(e.size!==t.size)return!1;let s=e.entries();for(r=s.next();r.done!==!0;){if(t.has(r.value[0])!==!0)return!1;r=s.next()}for(s=e.entries(),r=s.next();r.done!==!0;){if(wr(r.value[1],t.get(r.value[0]))!==!0)return!1;r=s.next()}return!0}if(e.constructor===Set){if(e.size!==t.size)return!1;const s=e.entries();for(r=s.next();r.done!==!0;){if(t.has(r.value[0])!==!0)return!1;r=s.next()}return!0}if(e.buffer!=null&&e.buffer.constructor===ArrayBuffer){if(n=e.length,n!==t.length)return!1;for(r=n;r--!==0;)if(e[r]!==t[r])return!1;return!0}if(e.constructor===RegExp)return e.source===t.source&&e.flags===t.flags;if(e.valueOf!==Object.prototype.valueOf)return e.valueOf()===t.valueOf();if(e.toString!==Object.prototype.toString)return e.toString()===t.toString();const o=Object.keys(e).filter(s=>e[s]!==void 0);if(n=o.length,n!==Object.keys(t).filter(s=>t[s]!==void 0).length)return!1;for(r=n;r--!==0;){const s=o[r];if(wr(e[s],t[s])!==!0)return!1}return!0}return e!==e&&t!==t}function bs(e){return e!==null&&typeof e=="object"&&Array.isArray(e)!==!0}const ys=[qr,Au,_e,yu,Cu,Ne,Un];function ws(e,t){t.forEach(n=>{n.install(e),n.__installed=!0})}function Nu(e,t,n){e.config.globalProperties.$q=n.$q,e.provide(Tu,n.$q),ws(n,ys),t.components!==void 0&&Object.values(t.components).forEach(r=>{bs(r)===!0&&r.name!==void 0&&e.component(r.name,r)}),t.directives!==void 0&&Object.values(t.directives).forEach(r=>{bs(r)===!0&&r.name!==void 0&&e.directive(r.name,r)}),t.plugins!==void 0&&ws(n,Object.values(t.plugins).filter(r=>typeof r.install=="function"&&ys.includes(r)===!1)),Ot.value===!0&&(n.$q.onSSRHydrated=()=>{n.onSSRHydrated.forEach(r=>{r()}),n.$q.onSSRHydrated=()=>{}})}var Du=function(e,t={}){const n={version:"2.12.3"};ta===!1?(t.config!==void 0&&Object.assign(_s,t.config),n.config={..._s},Lu()):n.config=t.config||{},Nu(e,t,{parentApp:e,$q:n,lang:t.lang,iconSet:t.iconSet,onSSRHydrated:[]})},ju={version:"2.12.3",install:Du,lang:Ne,iconSet:Un},Fu=!1;/*!
 * pinia v2.1.6
 * (c) 2023 Eduardo San Martin Morote
 * @license MIT
 */let na;const ar=e=>na=e,ra=Symbol();function Kr(e){return e&&typeof e=="object"&&Object.prototype.toString.call(e)==="[object Object]"&&typeof e.toJSON!="function"}var an;(function(e){e.direct="direct",e.patchObject="patch object",e.patchFunction="patch function"})(an||(an={}));function $u(){const e=ti(!0),t=e.run(()=>Ht({}));let n=[],r=[];const o=wn({install(s){ar(o),o._a=s,s.provide(ra,o),s.config.globalProperties.$pinia=o,r.forEach(i=>n.push(i)),r=[]},use(s){return!this._a&&!Fu?r.push(s):n.push(s),this},_p:n,_a:null,_e:e,_s:new Map,state:t});return o}const oa=()=>{};function Es(e,t,n,r=oa){e.push(t);const o=()=>{const s=e.indexOf(t);s>-1&&(e.splice(s,1),r())};return!n&&ni()&&Ka(o),o}function Mt(e,...t){e.slice().forEach(n=>{n(...t)})}const Hu=e=>e();function Vr(e,t){e instanceof Map&&t instanceof Map&&t.forEach((n,r)=>e.set(r,n)),e instanceof Set&&t instanceof Set&&t.forEach(e.add,e);for(const n in t){if(!t.hasOwnProperty(n))continue;const r=t[n],o=e[n];Kr(o)&&Kr(r)&&e.hasOwnProperty(n)&&!fe(r)&&!dt(r)?e[n]=Vr(o,r):e[n]=r}return e}const Bu=Symbol();function zu(e){return!Kr(e)||!e.hasOwnProperty(Bu)}const{assign:it}=Object;function Uu(e){return!!(fe(e)&&e.effect)}function qu(e,t,n,r){const{state:o,actions:s,getters:i}=t,a=n.state.value[e];let c;function l(){a||(n.state.value[e]=o?o():{});const u=Ec(n.state.value[e]);return it(u,s,Object.keys(i||{}).reduce((h,f)=>(h[f]=wn(Se(()=>{ar(n);const g=n._s.get(e);return i[f].call(g,g)})),h),{}))}return c=sa(e,l,t,n,r,!0),c}function sa(e,t,n={},r,o,s){let i;const a=it({actions:{}},n),c={deep:!0};let l,u,h=[],f=[],g;const E=r.state.value[e];!s&&!E&&(r.state.value[e]={}),Ht({});let I;function D(O){let N;l=u=!1,typeof O=="function"?(O(r.state.value[e]),N={type:an.patchFunction,storeId:e,events:g}):(Vr(r.state.value[e],O),N={type:an.patchObject,payload:O,storeId:e,events:g});const K=I=Symbol();hn().then(()=>{I===K&&(l=!0)}),u=!0,Mt(h,N,r.state.value[e])}const S=s?function(){const{state:N}=n,K=N?N():{};this.$patch(Q=>{it(Q,K)})}:oa;function _(){i.stop(),h=[],f=[],r._s.delete(e)}function R(O,N){return function(){ar(r);const K=Array.from(arguments),Q=[],F=[];function Z(J){Q.push(J)}function me(J){F.push(J)}Mt(f,{args:K,name:O,store:T,after:Z,onError:me});let ye;try{ye=N.apply(this&&this.$id===e?this:T,K)}catch(J){throw Mt(F,J),J}return ye instanceof Promise?ye.then(J=>(Mt(Q,J),J)).catch(J=>(Mt(F,J),Promise.reject(J))):(Mt(Q,ye),ye)}}const y={_p:r,$id:e,$onAction:Es.bind(null,f),$patch:D,$reset:S,$subscribe(O,N={}){const K=Es(h,O,N.detached,()=>Q()),Q=i.run(()=>Ft(()=>r.state.value[e],F=>{(N.flush==="sync"?u:l)&&O({storeId:e,type:an.direct,events:g},F)},it({},c,N)));return K},$dispose:_},T=It(y);r._s.set(e,T);const k=r._a&&r._a.runWithContext||Hu,H=r._e.run(()=>(i=ti(),k(()=>i.run(t))));for(const O in H){const N=H[O];if(fe(N)&&!Uu(N)||dt(N))s||(E&&zu(N)&&(fe(N)?N.value=E[O]:Vr(N,E[O])),r.state.value[e][O]=N);else if(typeof N=="function"){const K=R(O,N);H[O]=K,a.actions[O]=N}}return it(T,H),it(G(T),H),Object.defineProperty(T,"$state",{get:()=>r.state.value[e],set:O=>{D(N=>{it(N,O)})}}),r._p.forEach(O=>{it(T,i.run(()=>O({store:T,app:r._a,pinia:r,options:a})))}),E&&s&&n.hydrate&&n.hydrate(T.$state,E),l=!0,u=!0,T}function Ku(e,t,n){let r,o;const s=typeof t=="function";typeof e=="string"?(r=e,o=s?n:t):(o=e,r=e.id);function i(a,c){const l=ul();return a=a||(l?Te(ra,null):null),a&&ar(a),a=na,a._s.has(r)||(s?sa(r,t,o,a):qu(r,o,a)),a._s.get(r)}return i.$id=r,i}var ut=typeof globalThis!="undefined"?globalThis:typeof window!="undefined"?window:typeof global!="undefined"?global:typeof self!="undefined"?self:{},Ue={};(function(e){(function(t,n){for(var r in n)t[r]=n[r]})(e,function(t){var n={};function r(o){if(n[o])return n[o].exports;var s=n[o]={exports:{},id:o,loaded:!1};return t[o].call(s.exports,s,s.exports,r),s.loaded=!0,s.exports}return r.m=t,r.c=n,r.oe=function(o){throw o},r.p="",r(r.s=1)}([function(t,n){var r={};r.UTILS={},r.debug={isActive:!0},r.SCORM={version:null,handleCompletionStatus:!0,handleExitMode:!0,API:{handle:null,isFound:!1},connection:{isActive:!1},data:{completionStatus:null,exitStatus:null},debug:{}},r.SCORM.isAvailable=function(){return!0},r.SCORM.API.find=function(o){for(var s=null,i=0,a=500,c="SCORM.API.find",l=r.UTILS.trace,u=r.SCORM;!o.API&&!o.API_1484_11&&o.parent&&o.parent!=o&&i<=a;)i++,o=o.parent;if(u.version)switch(u.version){case"2004":o.API_1484_11?s=o.API_1484_11:l(c+": SCORM version 2004 was specified by user, but API_1484_11 cannot be found.");break;case"1.2":o.API?s=o.API:l(c+": SCORM version 1.2 was specified by user, but API cannot be found.");break}else o.API_1484_11?(u.version="2004",s=o.API_1484_11):o.API&&(u.version="1.2",s=o.API);return s?(l(c+": API found. Version: "+u.version),l("API: "+s)):l(c+`: Error finding API. 
Find attempts: `+i+`. 
Find attempt limit: `+a),s},r.SCORM.API.get=function(){var o=null,s=window,i=r.SCORM,a=i.API.find,c=r.UTILS.trace;return o=a(s),!o&&s.parent&&s.parent!=s&&(o=a(s.parent)),!o&&s.top&&s.top.opener&&(o=a(s.top.opener)),!o&&s.top&&s.top.opener&&s.top.opener.document&&(o=a(s.top.opener.document)),o?i.API.isFound=!0:c("API.get failed: Can't find the API!"),o},r.SCORM.API.getHandle=function(){var o=r.SCORM.API;return!o.handle&&!o.isFound&&(o.handle=o.get()),o.handle},r.SCORM.connection.initialize=function(){var o=!1,s=r.SCORM,i=s.data.completionStatus,a=r.UTILS.trace,c=r.UTILS.StringToBoolean,l=s.debug,u="SCORM.connection.initialize ";if(a("connection.initialize called."),s.connection.isActive)a(u+"aborted: Connection already active.");else{var h=s.API.getHandle(),f=0;if(h){switch(s.version){case"1.2":o=c(h.LMSInitialize(""));break;case"2004":o=c(h.Initialize(""));break}if(o)if(f=l.getCode(),f!==null&&f===0){if(s.connection.isActive=!0,s.handleCompletionStatus&&(i=s.status("get"),i)){switch(i){case"not attempted":s.status("set","incomplete");break;case"unknown":s.status("set","incomplete");break}s.save()}}else o=!1,a(u+`failed. 
Error code: `+f+` 
Error info: `+l.getInfo(f));else f=l.getCode(),a(f!==null&&f!==0?u+`failed. 
Error code: `+f+` 
Error info: `+l.getInfo(f):u+"failed: No response from server.")}else a(u+"failed: API is null.")}return o},r.SCORM.connection.terminate=function(){var o=!1,s=r.SCORM,i=s.data.exitStatus,a=s.data.completionStatus,c=r.UTILS.trace,l=r.UTILS.StringToBoolean,u=s.debug,h="SCORM.connection.terminate ";if(s.connection.isActive){var f=s.API.getHandle(),g=0;if(f){if(s.handleExitMode&&!i)if(a!=="completed"&&a!=="passed")switch(s.version){case"1.2":o=s.set("cmi.core.exit","suspend");break;case"2004":o=s.set("cmi.exit","suspend");break}else switch(s.version){case"1.2":o=s.set("cmi.core.exit","logout");break;case"2004":o=s.set("cmi.exit","normal");break}if(o=s.save(),o){switch(s.version){case"1.2":o=l(f.LMSFinish(""));break;case"2004":o=l(f.Terminate(""));break}o?s.connection.isActive=!1:(g=u.getCode(),c(h+`failed. 
Error code: `+g+` 
Error info: `+u.getInfo(g)))}}else c(h+"failed: API is null.")}else c(h+"aborted: Connection already terminated.");return o},r.SCORM.data.get=function(o){var s=null,i=r.SCORM,a=r.UTILS.trace,c=i.debug,l="SCORM.data.get("+o+") ";if(i.connection.isActive){var u=i.API.getHandle(),h=0;if(u){switch(i.version){case"1.2":s=u.LMSGetValue(o);break;case"2004":s=u.GetValue(o);break}if(h=c.getCode(),s!==""||h===0)switch(o){case"cmi.core.lesson_status":case"cmi.completion_status":i.data.completionStatus=s;break;case"cmi.core.exit":case"cmi.exit":i.data.exitStatus=s;break}else a(l+`failed. 
Error code: `+h+`
Error info: `+c.getInfo(h))}else a(l+"failed: API is null.")}else a(l+"failed: API connection is inactive.");return a(l+" value: "+s),String(s)},r.SCORM.data.set=function(o,s){var i=!1,a=r.SCORM,c=r.UTILS.trace,l=r.UTILS.StringToBoolean,u=a.debug,h="SCORM.data.set("+o+") ";if(a.connection.isActive){var f=a.API.getHandle(),g=0;if(f){switch(a.version){case"1.2":i=l(f.LMSSetValue(o,s));break;case"2004":i=l(f.SetValue(o,s));break}i?(o==="cmi.core.lesson_status"||o==="cmi.completion_status")&&(a.data.completionStatus=s):(g=u.getCode(),c(h+`failed. 
Error code: `+g+`. 
Error info: `+u.getInfo(g)))}else c(h+"failed: API is null.")}else c(h+"failed: API connection is inactive.");return i},r.SCORM.data.save=function(){var o=!1,s=r.SCORM,i=r.UTILS.trace,a=r.UTILS.StringToBoolean,c="SCORM.data.save failed";if(s.connection.isActive){var l=s.API.getHandle();if(l)switch(s.version){case"1.2":o=a(l.LMSCommit(""));break;case"2004":o=a(l.Commit(""));break}else i(c+": API is null.")}else i(c+": API connection is inactive.");return o},r.SCORM.status=function(o,s){var i=!1,a=r.SCORM,c=r.UTILS.trace,l="SCORM.getStatus failed",u="";if(o!==null){switch(a.version){case"1.2":u="cmi.core.lesson_status";break;case"2004":u="cmi.completion_status";break}switch(o){case"get":i=a.data.get(u);break;case"set":s!==null?i=a.data.set(u,s):(i=!1,c(l+": status was not specified."));break;default:i=!1,c(l+": no valid action was specified.")}}else c(l+": action was not specified.");return i},r.SCORM.debug.getCode=function(){var o=r.SCORM,s=o.API.getHandle(),i=r.UTILS.trace,a=0;if(s)switch(o.version){case"1.2":a=parseInt(s.LMSGetLastError(),10);break;case"2004":a=parseInt(s.GetLastError(),10);break}else i("SCORM.debug.getCode failed: API is null.");return a},r.SCORM.debug.getInfo=function(o){var s=r.SCORM,i=s.API.getHandle(),a=r.UTILS.trace,c="";if(i)switch(s.version){case"1.2":c=i.LMSGetErrorString(o.toString());break;case"2004":c=i.GetErrorString(o.toString());break}else a("SCORM.debug.getInfo failed: API is null.");return String(c)},r.SCORM.debug.getDiagnosticInfo=function(o){var s=r.SCORM,i=s.API.getHandle(),a=r.UTILS.trace,c="";if(i)switch(s.version){case"1.2":c=i.LMSGetDiagnostic(o);break;case"2004":c=i.GetDiagnostic(o);break}else a("SCORM.debug.getDiagnosticInfo failed: API is null.");return String(c)},r.SCORM.init=r.SCORM.connection.initialize,r.SCORM.get=r.SCORM.data.get,r.SCORM.set=r.SCORM.data.set,r.SCORM.save=r.SCORM.data.save,r.SCORM.quit=r.SCORM.connection.terminate,r.UTILS.StringToBoolean=function(o){var s=typeof o;switch(s){case"object":case"string":return/(true|1)/i.test(o);case"number":return!!o;case"boolean":return o;case"undefined":return null;default:return!1}},r.UTILS.trace=function(o){r.debug.isActive&&window.console&&window.console.log&&window.console.log(o)},t.exports=r},function(t,n,r){t.exports=r(0)}]))})(Ue);function Vu(e,t){const n=a=>Array.isArray(a)?a.join(""):a,r=n(e),o=n(t),s=r.split("").sort().join(""),i=o.split("").sort().join("");return s===i}function Nh(e,t){const n=e.slice();for(let r=n.length-1;r>0;r--){const o=Math.floor(Math.random()*(r+1));[n[r],n[o]]=[n[o],n[r]]}return n.slice(0,t)}var ia="Expected a function",Ss=0/0,Wu="[object Symbol]",Qu=/^\s+|\s+$/g,Yu=/^[-+]0x[0-9a-f]+$/i,Ju=/^0b[01]+$/i,Gu=/^0o[0-7]+$/i,Xu=parseInt,Zu=typeof ut=="object"&&ut&&ut.Object===Object&&ut,ef=typeof self=="object"&&self&&self.Object===Object&&self,tf=Zu||ef||Function("return this")(),nf=Object.prototype,rf=nf.toString,of=Math.max,sf=Math.min,Er=function(){return tf.Date.now()};function af(e,t,n){var r,o,s,i,a,c,l=0,u=!1,h=!1,f=!0;if(typeof e!="function")throw new TypeError(ia);t=Cs(t)||0,qn(n)&&(u=!!n.leading,h="maxWait"in n,s=h?of(Cs(n.maxWait)||0,t):s,f="trailing"in n?!!n.trailing:f);function g(k){var H=r,O=o;return r=o=void 0,l=k,i=e.apply(O,H),i}function E(k){return l=k,a=setTimeout(S,t),u?g(k):i}function I(k){var H=k-c,O=k-l,N=t-H;return h?sf(N,s-O):N}function D(k){var H=k-c,O=k-l;return c===void 0||H>=t||H<0||h&&O>=s}function S(){var k=Er();if(D(k))return _(k);a=setTimeout(S,I(k))}function _(k){return a=void 0,f&&r?g(k):(r=o=void 0,i)}function R(){a!==void 0&&clearTimeout(a),l=0,r=c=o=a=void 0}function y(){return a===void 0?i:_(Er())}function T(){var k=Er(),H=D(k);if(r=arguments,o=this,c=k,H){if(a===void 0)return E(c);if(h)return a=setTimeout(S,t),g(c)}return a===void 0&&(a=setTimeout(S,t)),i}return T.cancel=R,T.flush=y,T}function cf(e,t,n){var r=!0,o=!0;if(typeof e!="function")throw new TypeError(ia);return qn(n)&&(r="leading"in n?!!n.leading:r,o="trailing"in n?!!n.trailing:o),af(e,t,{leading:r,maxWait:t,trailing:o})}function qn(e){var t=typeof e;return!!e&&(t=="object"||t=="function")}function lf(e){return!!e&&typeof e=="object"}function uf(e){return typeof e=="symbol"||lf(e)&&rf.call(e)==Wu}function Cs(e){if(typeof e=="number")return e;if(uf(e))return Ss;if(qn(e)){var t=typeof e.valueOf=="function"?e.valueOf():e;e=qn(t)?t+"":t}if(typeof e!="string")return e===0?e:+e;e=e.replace(Qu,"");var n=Ju.test(e);return n||Gu.test(e)?Xu(e.slice(2),n?2:8):Yu.test(e)?Ss:+e}var ff=cf,df="Expected a function",xs=0/0,hf="[object Symbol]",pf=/^\s+|\s+$/g,mf=/^[-+]0x[0-9a-f]+$/i,gf=/^0b[01]+$/i,vf=/^0o[0-7]+$/i,_f=parseInt,bf=typeof ut=="object"&&ut&&ut.Object===Object&&ut,yf=typeof self=="object"&&self&&self.Object===Object&&self,wf=bf||yf||Function("return this")(),Ef=Object.prototype,Sf=Ef.toString,Cf=Math.max,xf=Math.min,Sr=function(){return wf.Date.now()};function Of(e,t,n){var r,o,s,i,a,c,l=0,u=!1,h=!1,f=!0;if(typeof e!="function")throw new TypeError(df);t=Os(t)||0,Wr(n)&&(u=!!n.leading,h="maxWait"in n,s=h?Cf(Os(n.maxWait)||0,t):s,f="trailing"in n?!!n.trailing:f);function g(k){var H=r,O=o;return r=o=void 0,l=k,i=e.apply(O,H),i}function E(k){return l=k,a=setTimeout(S,t),u?g(k):i}function I(k){var H=k-c,O=k-l,N=t-H;return h?xf(N,s-O):N}function D(k){var H=k-c,O=k-l;return c===void 0||H>=t||H<0||h&&O>=s}function S(){var k=Sr();if(D(k))return _(k);a=setTimeout(S,I(k))}function _(k){return a=void 0,f&&r?g(k):(r=o=void 0,i)}function R(){a!==void 0&&clearTimeout(a),l=0,r=c=o=a=void 0}function y(){return a===void 0?i:_(Sr())}function T(){var k=Sr(),H=D(k);if(r=arguments,o=this,c=k,H){if(a===void 0)return E(c);if(h)return a=setTimeout(S,t),g(c)}return a===void 0&&(a=setTimeout(S,t)),i}return T.cancel=R,T.flush=y,T}function Wr(e){var t=typeof e;return!!e&&(t=="object"||t=="function")}function If(e){return!!e&&typeof e=="object"}function Pf(e){return typeof e=="symbol"||If(e)&&Sf.call(e)==hf}function Os(e){if(typeof e=="number")return e;if(Pf(e))return xs;if(Wr(e)){var t=typeof e.valueOf=="function"?e.valueOf():e;e=Wr(t)?t+"":t}if(typeof e!="string")return e===0?e:+e;e=e.replace(pf,"");var n=gf.test(e);return n||vf.test(e)?_f(e.slice(2),n?2:8):mf.test(e)?xs:+e}var Is=Of,aa=function(){};function ca(e){var t=void 0,n=void 0,r=void 0;for(t=0;t<e.length;t+=1)if(n=e[t],n.dataset&&n.dataset.aos||(r=n.children&&ca(n.children),r))return!0;return!1}function Rf(e){!e||e.forEach(function(t){var n=Array.prototype.slice.call(t.addedNodes),r=Array.prototype.slice.call(t.removedNodes),o=n.concat(r);if(ca(o))return aa()})}function la(){return window.MutationObserver||window.WebKitMutationObserver||window.MozMutationObserver}function kf(){return!!la()}function Af(e,t){var n=window.document,r=la(),o=new r(Rf);aa=t,o.observe(n.documentElement,{childList:!0,subtree:!0,removedNodes:!0})}var Ps={isSupported:kf,ready:Af},Mf=function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")},Tf=function(){function e(t,n){for(var r=0;r<n.length;r++){var o=n[r];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(t,o.key,o)}}return function(t,n,r){return n&&e(t.prototype,n),r&&e(t,r),t}}(),Lf=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},Nf=/(android|bb\d+|meego).+mobile|avantgo|bada\/|blackberry|blazer|compal|elaine|fennec|hiptop|iemobile|ip(hone|od)|iris|kindle|lge |maemo|midp|mmp|mobile.+firefox|netfront|opera m(ob|in)i|palm( os)?|phone|p(ixi|re)\/|plucker|pocket|psp|series(4|6)0|symbian|treo|up\.(browser|link)|vodafone|wap|windows ce|xda|xiino/i,Df=/1207|6310|6590|3gso|4thp|50[1-6]i|770s|802s|a wa|abac|ac(er|oo|s\-)|ai(ko|rn)|al(av|ca|co)|amoi|an(ex|ny|yw)|aptu|ar(ch|go)|as(te|us)|attw|au(di|\-m|r |s )|avan|be(ck|ll|nq)|bi(lb|rd)|bl(ac|az)|br(e|v)w|bumb|bw\-(n|u)|c55\/|capi|ccwa|cdm\-|cell|chtm|cldc|cmd\-|co(mp|nd)|craw|da(it|ll|ng)|dbte|dc\-s|devi|dica|dmob|do(c|p)o|ds(12|\-d)|el(49|ai)|em(l2|ul)|er(ic|k0)|esl8|ez([4-7]0|os|wa|ze)|fetc|fly(\-|_)|g1 u|g560|gene|gf\-5|g\-mo|go(\.w|od)|gr(ad|un)|haie|hcit|hd\-(m|p|t)|hei\-|hi(pt|ta)|hp( i|ip)|hs\-c|ht(c(\-| |_|a|g|p|s|t)|tp)|hu(aw|tc)|i\-(20|go|ma)|i230|iac( |\-|\/)|ibro|idea|ig01|ikom|im1k|inno|ipaq|iris|ja(t|v)a|jbro|jemu|jigs|kddi|keji|kgt( |\/)|klon|kpt |kwc\-|kyo(c|k)|le(no|xi)|lg( g|\/(k|l|u)|50|54|\-[a-w])|libw|lynx|m1\-w|m3ga|m50\/|ma(te|ui|xo)|mc(01|21|ca)|m\-cr|me(rc|ri)|mi(o8|oa|ts)|mmef|mo(01|02|bi|de|do|t(\-| |o|v)|zz)|mt(50|p1|v )|mwbp|mywa|n10[0-2]|n20[2-3]|n30(0|2)|n50(0|2|5)|n7(0(0|1)|10)|ne((c|m)\-|on|tf|wf|wg|wt)|nok(6|i)|nzph|o2im|op(ti|wv)|oran|owg1|p800|pan(a|d|t)|pdxg|pg(13|\-([1-8]|c))|phil|pire|pl(ay|uc)|pn\-2|po(ck|rt|se)|prox|psio|pt\-g|qa\-a|qc(07|12|21|32|60|\-[2-7]|i\-)|qtek|r380|r600|raks|rim9|ro(ve|zo)|s55\/|sa(ge|ma|mm|ms|ny|va)|sc(01|h\-|oo|p\-)|sdk\/|se(c(\-|0|1)|47|mc|nd|ri)|sgh\-|shar|sie(\-|m)|sk\-0|sl(45|id)|sm(al|ar|b3|it|t5)|so(ft|ny)|sp(01|h\-|v\-|v )|sy(01|mb)|t2(18|50)|t6(00|10|18)|ta(gt|lk)|tcl\-|tdg\-|tel(i|m)|tim\-|t\-mo|to(pl|sh)|ts(70|m\-|m3|m5)|tx\-9|up(\.b|g1|si)|utst|v400|v750|veri|vi(rg|te)|vk(40|5[0-3]|\-v)|vm40|voda|vulc|vx(52|53|60|61|70|80|81|83|85|98)|w3c(\-| )|webc|whit|wi(g |nc|nw)|wmlb|wonu|x700|yas\-|your|zeto|zte\-/i,jf=/(android|bb\d+|meego).+mobile|avantgo|bada\/|blackberry|blazer|compal|elaine|fennec|hiptop|iemobile|ip(hone|od)|iris|kindle|lge |maemo|midp|mmp|mobile.+firefox|netfront|opera m(ob|in)i|palm( os)?|phone|p(ixi|re)\/|plucker|pocket|psp|series(4|6)0|symbian|treo|up\.(browser|link)|vodafone|wap|windows ce|xda|xiino|android|ipad|playbook|silk/i,Ff=/1207|6310|6590|3gso|4thp|50[1-6]i|770s|802s|a wa|abac|ac(er|oo|s\-)|ai(ko|rn)|al(av|ca|co)|amoi|an(ex|ny|yw)|aptu|ar(ch|go)|as(te|us)|attw|au(di|\-m|r |s )|avan|be(ck|ll|nq)|bi(lb|rd)|bl(ac|az)|br(e|v)w|bumb|bw\-(n|u)|c55\/|capi|ccwa|cdm\-|cell|chtm|cldc|cmd\-|co(mp|nd)|craw|da(it|ll|ng)|dbte|dc\-s|devi|dica|dmob|do(c|p)o|ds(12|\-d)|el(49|ai)|em(l2|ul)|er(ic|k0)|esl8|ez([4-7]0|os|wa|ze)|fetc|fly(\-|_)|g1 u|g560|gene|gf\-5|g\-mo|go(\.w|od)|gr(ad|un)|haie|hcit|hd\-(m|p|t)|hei\-|hi(pt|ta)|hp( i|ip)|hs\-c|ht(c(\-| |_|a|g|p|s|t)|tp)|hu(aw|tc)|i\-(20|go|ma)|i230|iac( |\-|\/)|ibro|idea|ig01|ikom|im1k|inno|ipaq|iris|ja(t|v)a|jbro|jemu|jigs|kddi|keji|kgt( |\/)|klon|kpt |kwc\-|kyo(c|k)|le(no|xi)|lg( g|\/(k|l|u)|50|54|\-[a-w])|libw|lynx|m1\-w|m3ga|m50\/|ma(te|ui|xo)|mc(01|21|ca)|m\-cr|me(rc|ri)|mi(o8|oa|ts)|mmef|mo(01|02|bi|de|do|t(\-| |o|v)|zz)|mt(50|p1|v )|mwbp|mywa|n10[0-2]|n20[2-3]|n30(0|2)|n50(0|2|5)|n7(0(0|1)|10)|ne((c|m)\-|on|tf|wf|wg|wt)|nok(6|i)|nzph|o2im|op(ti|wv)|oran|owg1|p800|pan(a|d|t)|pdxg|pg(13|\-([1-8]|c))|phil|pire|pl(ay|uc)|pn\-2|po(ck|rt|se)|prox|psio|pt\-g|qa\-a|qc(07|12|21|32|60|\-[2-7]|i\-)|qtek|r380|r600|raks|rim9|ro(ve|zo)|s55\/|sa(ge|ma|mm|ms|ny|va)|sc(01|h\-|oo|p\-)|sdk\/|se(c(\-|0|1)|47|mc|nd|ri)|sgh\-|shar|sie(\-|m)|sk\-0|sl(45|id)|sm(al|ar|b3|it|t5)|so(ft|ny)|sp(01|h\-|v\-|v )|sy(01|mb)|t2(18|50)|t6(00|10|18)|ta(gt|lk)|tcl\-|tdg\-|tel(i|m)|tim\-|t\-mo|to(pl|sh)|ts(70|m\-|m3|m5)|tx\-9|up(\.b|g1|si)|utst|v400|v750|veri|vi(rg|te)|vk(40|5[0-3]|\-v)|vm40|voda|vulc|vx(52|53|60|61|70|80|81|83|85|98)|w3c(\-| )|webc|whit|wi(g |nc|nw)|wmlb|wonu|x700|yas\-|your|zeto|zte\-/i;function Rs(){return navigator.userAgent||navigator.vendor||window.opera||""}var $f=function(){function e(){Mf(this,e)}return Tf(e,[{key:"phone",value:function(){var n=Rs();return!!(Nf.test(n)||Df.test(n.substr(0,4)))}},{key:"mobile",value:function(){var n=Rs();return!!(jf.test(n)||Ff.test(n.substr(0,4)))}},{key:"tablet",value:function(){return this.mobile()&&!this.phone()}},{key:"ie11",value:function(){return"-ms-scroll-limit"in document.documentElement.style&&"-ms-ime-align"in document.documentElement.style}}]),e}(),Ln=new $f,Hf=function(t,n){return n&&n.forEach(function(r){return t.classList.add(r)})},Bf=function(t,n){return n&&n.forEach(function(r){return t.classList.remove(r)})},kn=function(t,n){var r=void 0;return Ln.ie11()?(r=document.createEvent("CustomEvent"),r.initCustomEvent(t,!0,!0,{detail:n})):r=new CustomEvent(t,{detail:n}),document.dispatchEvent(r)},zf=function(t,n){var r=t.options,o=t.position,s=t.node;t.data;var i=function(){!t.animated||(Bf(s,r.animatedClassNames),kn("aos:out",s),t.options.id&&kn("aos:in:"+t.options.id,s),t.animated=!1)},a=function(){t.animated||(Hf(s,r.animatedClassNames),kn("aos:in",s),t.options.id&&kn("aos:in:"+t.options.id,s),t.animated=!0)};r.mirror&&n>=o.out&&!r.once?i():n>=o.in?a():t.animated&&!r.once&&i()},ks=function(t){return t.forEach(function(n,r){return zf(n,window.pageYOffset)})},ua=function(t){for(var n=0,r=0;t&&!isNaN(t.offsetLeft)&&!isNaN(t.offsetTop);)n+=t.offsetLeft-(t.tagName!="BODY"?t.scrollLeft:0),r+=t.offsetTop-(t.tagName!="BODY"?t.scrollTop:0),t=t.offsetParent;return{top:r,left:n}},pt=function(e,t,n){var r=e.getAttribute("data-aos-"+t);if(typeof r!="undefined"){if(r==="true")return!0;if(r==="false")return!1}return r||n},Uf=function(t,n,r){var o=window.innerHeight,s=pt(t,"anchor"),i=pt(t,"anchor-placement"),a=Number(pt(t,"offset",i?0:n)),c=i||r,l=t;s&&document.querySelectorAll(s)&&(l=document.querySelectorAll(s)[0]);var u=ua(l).top-o;switch(c){case"top-bottom":break;case"center-bottom":u+=l.offsetHeight/2;break;case"bottom-bottom":u+=l.offsetHeight;break;case"top-center":u+=o/2;break;case"center-center":u+=o/2+l.offsetHeight/2;break;case"bottom-center":u+=o/2+l.offsetHeight;break;case"top-top":u+=o;break;case"bottom-top":u+=o+l.offsetHeight;break;case"center-top":u+=o+l.offsetHeight/2;break}return u+a},qf=function(t,n){var r=pt(t,"anchor"),o=pt(t,"offset",n),s=t;r&&document.querySelectorAll(r)&&(s=document.querySelectorAll(r)[0]);var i=ua(s).top;return i+s.offsetHeight-o},Kf=function(t,n){return t.forEach(function(r,o){var s=pt(r.node,"mirror",n.mirror),i=pt(r.node,"once",n.once),a=pt(r.node,"id"),c=n.useClassNames&&r.node.getAttribute("data-aos"),l=[n.animatedClassName].concat(c?c.split(" "):[]).filter(function(u){return typeof u=="string"});n.initClassName&&r.node.classList.add(n.initClassName),r.position={in:Uf(r.node,n.offset,n.anchorPlacement),out:s&&qf(r.node,n.offset)},r.options={once:i,mirror:s,animatedClassNames:l,id:a}}),t},fa=function(){var e=document.querySelectorAll("[data-aos]");return Array.prototype.map.call(e,function(t){return{node:t}})},Ge=[],As=!1,le={offset:120,delay:0,easing:"ease",duration:400,disable:!1,once:!1,mirror:!1,anchorPlacement:"top-bottom",startEvent:"DOMContentLoaded",animatedClassName:"aos-animate",initClassName:"aos-init",useClassNames:!1,disableMutationObserver:!1,throttleDelay:99,debounceDelay:50},da=function(){return document.all&&!window.atob},Vf=function(){return Ge=Kf(Ge,le),ks(Ge),window.addEventListener("scroll",ff(function(){ks(Ge,le.once)},le.throttleDelay)),Ge},Et=function(){var t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:!1;t&&(As=!0),As&&Vf()},ha=function(){if(Ge=fa(),ma(le.disable)||da())return pa();Et()},pa=function(){Ge.forEach(function(t,n){t.node.removeAttribute("data-aos"),t.node.removeAttribute("data-aos-easing"),t.node.removeAttribute("data-aos-duration"),t.node.removeAttribute("data-aos-delay"),le.initClassName&&t.node.classList.remove(le.initClassName),le.animatedClassName&&t.node.classList.remove(le.animatedClassName)})},ma=function(t){return t===!0||t==="mobile"&&Ln.mobile()||t==="phone"&&Ln.phone()||t==="tablet"&&Ln.tablet()||typeof t=="function"&&t()===!0},Wf=function(t){return le=Lf(le,t),Ge=fa(),!le.disableMutationObserver&&!Ps.isSupported()&&(console.info(`
      aos: MutationObserver is not supported on this browser,
      code mutations observing has been disabled.
      You may have to call "refreshHard()" by yourself.
    `),le.disableMutationObserver=!0),le.disableMutationObserver||Ps.ready("[data-aos]",ha),ma(le.disable)||da()?pa():(document.querySelector("body").setAttribute("data-aos-easing",le.easing),document.querySelector("body").setAttribute("data-aos-duration",le.duration),document.querySelector("body").setAttribute("data-aos-delay",le.delay),["DOMContentLoaded","load"].indexOf(le.startEvent)===-1?document.addEventListener(le.startEvent,function(){Et(!0)}):window.addEventListener("load",function(){Et(!0)}),le.startEvent==="DOMContentLoaded"&&["complete","interactive"].indexOf(document.readyState)>-1&&Et(!0),window.addEventListener("resize",Is(Et,le.debounceDelay,!0)),window.addEventListener("orientationchange",Is(Et,le.debounceDelay,!0)),Ge)},Cr={init:Wf,refresh:Et,refreshHard:ha};function ga(){const e=Co(),t=Se(()=>e.aosEnable);function n(){console.log("***------ checkAOS ------***")}function r(){setTimeout(()=>{Cr.refreshHard(),console.log("***------ refreshHardAOS ------***")},800)}function o(){setTimeout(()=>{Cr.refresh(),console.log("***------ refreshAOS ------***")},800)}function s(){console.log("***------ initAOS ------***"),Cr.init({disable:()=>!t.value,offset:115,delay:100,duration:850,easing:"ease",once:!0})}return{checkAOS:n,initAOS:s,refreshAOS:o,refreshHardAOS:r,aosEnable:t}}const Co=Ku("store",{state:()=>({aosEnable:!0,showBadgeComplete:!1,audioEnable:!0,gated:!1,com:{willCheck_Connection:!0,willCheck_SCORMApi:!1,lmsError:{callSucceeded:!0,lastErrorCode:0,lastErrorMsg:""},checkingConnection:!1,settings:{active:!1,suspendData:{},location:null,interactions:[]}},rte:{settings:{return:!1,location:null}},manifest:{settings:{language:"en",tracking:{version:"2004",on:!0},nonEmployee:!1},content:[{id:"page1",status:0},{id:"page2",status:0},{id:"page3",status:0},{id:"page4",status:0},{id:"page5",status:0},{id:"page6",status:0},{id:"page7",status:0},{id:"page8",status:0},{id:"page11",status:0}]},timeUtility:{timeInitialized:null}}),getters:{getPagesCompleted:e=>{let t=0;return e.manifest.content.forEach(n=>{n.id!=="pageintro"&&n.id!=="pagefinal"&&n.status==1&&(t=t+1)}),t},getPageStatusById:e=>t=>{const n=e.manifest.content.find(r=>r.id===t);return n?n.status:null},getNonEmployee:e=>e.manifest.settings.nonEmployee,getActivePage:e=>e.com.settings.location,rte_getReturn:e=>e.rte.settings.return,com_get:()=>e=>{console.log("COM","Getting: '"+e+"'");var t=Ue.SCORM.get(e);return t},com_getLocation:e=>e.com.settings.location,com_getLanguage:e=>e.com_get("cmi.learner_preference.language"),com_getAllInteractions:e=>{var t=["id","type","learner_response","description","result"],n=[],r=parseInt(e.com_get("cmi.interactions._count"),10);if(r>0)for(var o=0;o<r;o++){for(var s={cmiIndex:o.toString()},i=0;i<t.length;i++){var a=t[i],c="cmi.interactions."+o+"."+a;s[a]=e.com_get(c)}s.submitted=!0,n.push(s)}return n},getInteraction:e=>t=>e.com.settings.interactions.slice().reverse().find(r=>r.id===t),getInteractionSubmitted:e=>t=>{const n=e.getInteraction(t);return n&&n.submitted?n:{id:t,submitted:!1,learner_response:null,result:null,cmiIndex:null}},getInteractionsCompleted:e=>(t,n="")=>{let r=!0;return t.forEach(o=>{const s=e.getInteraction(n+o);s&&s.submitted||(r=!1)}),r},getInteractionsPassed:e=>t=>{let n=!0;return t.forEach(r=>{const o=e.getInteraction(r);o&&o.submitted&&o.result=="correct"||(n=!1)}),n},getInteractionsFailed:e=>t=>{let n=!1;return t.forEach(r=>{const o=e.getInteraction(r);o&&o.submitted&&o.result=="incorrect"&&(n=!0)}),n},getInteractionsScore:e=>(t,n)=>{let r=0;return t.forEach(s=>{const i=e.getInteraction(n+s);i&&i.submitted&&i.result=="correct"&&r++}),Math.round(r/t.length*100)},getInteractionsFailedCount:e=>t=>{let n=0;return t.forEach(r=>{const o=e.getInteraction(r);o&&o.submitted&&o.result=="incorrect"&&(n++,console.log("counting ***********",n))}),n},getTestoutScore:e=>t=>{let n=0;return t.forEach(o=>{const s=e.getInteraction(o);s&&s.submitted&&s.result=="correct"&&n++}),Math.round(n/t.length*100)}},actions:{com_init(e){var t=e.version,n=e.callback;Ue.SCORM.version=t,console.log("COM","Initializing course.");var r=!1;this.com.settings.active?r=!0:r=Ue.SCORM.init(),console.log("COM","Call succeeded? "+r),r?(this.com.settings.active=!0,this.com_set({key:"cmi.exit",value:"suspend"}),this.sco_start(),this.com_return_suspendData(this.com_get("cmi.suspend_data")),this.rte.settings.location=this.com_get("cmi.location"),this.com.settings.interactions=this.com_getAllInteractions,console.log("*** this.com.settings.interactions ***",this.com.settings.interactions)):console.log("COM","API Error: API not found."),n&&typeof n!="undefined"&&n()},rte_load(e){this.com_setLocation(e),this.rte_updateContentStatus({arg:e,status:.5})},rte_init(e){this.manifest.settings.tracking.on?this.com_init({version:this.manifest.settings.tracking.version,callback:()=>{if(this.rte.settings.return)for(var t=0;t<this.com.settings.suspendData.loStatus.length;t++)for(var n=0;n<this.manifest.content.length;n++)this.com.settings.suspendData.loStatus[t].id==this.manifest.content[n].id&&(this.manifest.content[n].status=this.com.settings.suspendData.loStatus[t].status);e&&typeof e!="undefined"&&e()}}):e&&typeof e!="undefined"&&e()},rte_updateContentStatus(e){var t=e.arg,n=e.status;console.log("rte_updateContentStatus",t,n);var r=this.manifest.content.find(function(o){return o.id===t});r.status!=1&&(r.status=n,this.com_update_suspendData(t))},com_set(e){var t=e.key,n=e.value;console.log("COM:","Sending: '"+n+"'");var r=Ue.SCORM.set(t,n),o=Ue.SCORM.debug.getCode(),s=Ue.SCORM.debug.getInfo(o)+" --- "+Ue.SCORM.debug.getDiagnosticInfo(o);this.com.willCheck_SCORMApi?r||(this.com.lmsError={callSucceeded:r,lastErrorCode:o,lastErrorMsg:s}):this.com.lmsError={callSucceeded:!0,lastErrorCode:0,lastErrorMsg:""},console.log("COM:","callSucceeded: "+r),console.log("COM:","lastErrorCode: "+o),console.log("COM:","lastErrorMsg: "+s),Ue.SCORM.save()},com_update_suspendData(e){var t=0;"loStatus"in this.com.settings.suspendData||(this.com.settings.suspendData.loStatus=[]);for(var n=0;n<this.manifest.content.length;n++)if(e===this.manifest.content[n].id){for(var r=!1,o=0;o<this.com.settings.suspendData.loStatus.length;o++)this.com.settings.suspendData.loStatus[o].id===e&&(this.com.settings.suspendData.loStatus[o].status=this.manifest.content[n].status,console.log("update_suspendData","pageId: "+e),r=!0);if(!r){var s={id:this.manifest.content[n].id,status:this.manifest.content[n].status};this.com.settings.suspendData.loStatus.push(s)}}for(var i=0;i<this.manifest.content.length;i++)this.manifest.content[i].status==1&&t++;this.com.settings.suspendData.progressMeasure=Math.round(t/this.manifest.content.length*100)/100,this.com.settings.suspendData.pagescompleted=t,this.com_set({key:"cmi.suspend_data",value:JSON.stringify(this.com.settings.suspendData)}),this.sco_set_session_time(),this.com.settings.suspendData.progressMeasure==1&&(this.com_setCompletion("completed"),this.com_setSuccessStatus({value:"passed",set12Status:!0}))},com_return_suspendData(e){if(console.log("return_suspendData",e),e.length>1&&e!=null&&e!=""){this.rte.settings.return=!0;try{var t=JSON.parse(e);typeof t.loStatus!="undefined"&&(this.com.settings.suspendData=t)}catch(n){console.log("Found cmi.suspendData, but encountered error parsing JSON, will reset."),console.warn(n)}}},com_setLocation(e){this.com_set({key:"cmi.location",value:e}),this.com.settings.location=e},com_setLanguage(e){this.com_set({key:"cmi.learner_preference.language",value:e})},com_setCompletion(e){this.com_set({key:"cmi.completion_status",value:e})},com_setSuccessStatus(e){var t=e.value;this.com_set({key:"cmi.success_status",value:t})},com_exit(){this.sco_set_session_time(),console.log("COM","Terminating connection.");var e=Ue.SCORM.quit();console.log("COM","Call succeeded? "+e),e&&top.window.close()},setInteraction(e,t,n,r){if(this.getInteraction(e)&&!r)return;const s={};s.id=e,s.submitted=!0,s.type="choice",t?s.learner_response=t:s.learner_response="",n?s.result=Vu(t,n)?"correct":"incorrect":s.result="correct";const i=this.com.settings.interactions.length.toString();s.cmiIndex=i,this.com.lmsError={callSucceeded:!0,lastErrorCode:0,lastErrorMsg:""};var a="cmi.interactions."+i+".";this.com_set({key:a+"id",value:s.id}),s.type&&this.com_set({key:a+"type",value:s.type}),s.learner_response&&this.com_set({key:a+"learner_response",value:s.learner_response}),s.result&&this.com_set({key:a+"result",value:s.result}),s.description&&this.com_set({key:a+"description",value:s.description}),this.com.lmsError.callSucceeded?this.com.settings.interactions.push(s):console.log("***------ Error inside - setInteraction ------***"),ga().refreshAOS()},sco_start(){this.timeUtility.timeInitialized=new Date},sco_set_session_time(){const e={MillisecondsToCMIDuration:function(r){let o="",s=new Date;s.setTime(r);let i="000"+Math.floor(r/36e5),a="0"+s.getMinutes(),c="0"+s.getSeconds(),l="0"+Math.round(s.getMilliseconds()/10);return o=i.slice(-4)+":"+a.slice(-2)+":",o+=c.slice(-2)+"."+l.slice(-2),o},CentisecsToISODuration:function(r){r=Math.max(r,0);var o="P",s=r,i=Math.floor(s/315576e4);s-=i*315576e4;var a=Math.floor(s/26298e4);s-=a*26298e4;var c=Math.floor(s/864e4);s-=c*864e4;var l=Math.floor(s/36e4);s-=l*36e4;var u=Math.floor(s/6e3);return s-=u*6e3,i>0&&(o+=i+"Y"),a>0&&(o+=a+"M"),c>0&&(o+=c+"D"),(l>0||u>0||s>0)&&(o+="T",l>0&&(o+=l+"H"),u>0&&(o+=u+"M"),s>0&&(o+=s/100+"S")),o=="P"&&(o="PT0H0M0S"),o}};if(this.timeUtility.timeInitialized){var t=new Date,n=t.getTime()-this.timeUtility.timeInitialized.getTime();this.com_set({key:"cmi.session_time",value:e.CentisecsToISODuration(Math.floor(n/10))})}}}});/*!
  * vue-router v4.2.4
  * (c) 2023 Eduardo San Martin Morote
  * @license MIT
  */const Tt=typeof window!="undefined";function Qf(e){return e.__esModule||e[Symbol.toStringTag]==="Module"}const ne=Object.assign;function xr(e,t){const n={};for(const r in t){const o=t[r];n[r]=He(o)?o.map(e):e(o)}return n}const cn=()=>{},He=Array.isArray,Yf=/\/$/,Jf=e=>e.replace(Yf,"");function Or(e,t,n="/"){let r,o={},s="",i="";const a=t.indexOf("#");let c=t.indexOf("?");return a<c&&a>=0&&(c=-1),c>-1&&(r=t.slice(0,c),s=t.slice(c+1,a>-1?a:t.length),o=e(s)),a>-1&&(r=r||t.slice(0,a),i=t.slice(a,t.length)),r=ed(r!=null?r:t,n),{fullPath:r+(s&&"?")+s+i,path:r,query:o,hash:i}}function Gf(e,t){const n=t.query?e(t.query):"";return t.path+(n&&"?")+n+(t.hash||"")}function Ms(e,t){return!t||!e.toLowerCase().startsWith(t.toLowerCase())?e:e.slice(t.length)||"/"}function Xf(e,t,n){const r=t.matched.length-1,o=n.matched.length-1;return r>-1&&r===o&&Ut(t.matched[r],n.matched[o])&&va(t.params,n.params)&&e(t.query)===e(n.query)&&t.hash===n.hash}function Ut(e,t){return(e.aliasOf||e)===(t.aliasOf||t)}function va(e,t){if(Object.keys(e).length!==Object.keys(t).length)return!1;for(const n in e)if(!Zf(e[n],t[n]))return!1;return!0}function Zf(e,t){return He(e)?Ts(e,t):He(t)?Ts(t,e):e===t}function Ts(e,t){return He(t)?e.length===t.length&&e.every((n,r)=>n===t[r]):e.length===1&&e[0]===t}function ed(e,t){if(e.startsWith("/"))return e;if(!e)return t;const n=t.split("/"),r=e.split("/"),o=r[r.length-1];(o===".."||o===".")&&r.push("");let s=n.length-1,i,a;for(i=0;i<r.length;i++)if(a=r[i],a!==".")if(a==="..")s>1&&s--;else break;return n.slice(0,s).join("/")+"/"+r.slice(i-(i===r.length?1:0)).join("/")}var bn;(function(e){e.pop="pop",e.push="push"})(bn||(bn={}));var ln;(function(e){e.back="back",e.forward="forward",e.unknown=""})(ln||(ln={}));function td(e){if(!e)if(Tt){const t=document.querySelector("base");e=t&&t.getAttribute("href")||"/",e=e.replace(/^\w+:\/\/[^\/]+/,"")}else e="/";return e[0]!=="/"&&e[0]!=="#"&&(e="/"+e),Jf(e)}const nd=/^[^#]+#/;function rd(e,t){return e.replace(nd,"#")+t}function od(e,t){const n=document.documentElement.getBoundingClientRect(),r=e.getBoundingClientRect();return{behavior:t.behavior,left:r.left-n.left-(t.left||0),top:r.top-n.top-(t.top||0)}}const cr=()=>({left:window.pageXOffset,top:window.pageYOffset});function sd(e){let t;if("el"in e){const n=e.el,r=typeof n=="string"&&n.startsWith("#"),o=typeof n=="string"?r?document.getElementById(n.slice(1)):document.querySelector(n):n;if(!o)return;t=od(o,e)}else t=e;"scrollBehavior"in document.documentElement.style?window.scrollTo(t):window.scrollTo(t.left!=null?t.left:window.pageXOffset,t.top!=null?t.top:window.pageYOffset)}function Ls(e,t){return(history.state?history.state.position-t:-1)+e}const Qr=new Map;function id(e,t){Qr.set(e,t)}function ad(e){const t=Qr.get(e);return Qr.delete(e),t}let cd=()=>location.protocol+"//"+location.host;function _a(e,t){const{pathname:n,search:r,hash:o}=t,s=e.indexOf("#");if(s>-1){let a=o.includes(e.slice(s))?e.slice(s).length:1,c=o.slice(a);return c[0]!=="/"&&(c="/"+c),Ms(c,"")}return Ms(n,e)+r+o}function ld(e,t,n,r){let o=[],s=[],i=null;const a=({state:f})=>{const g=_a(e,location),E=n.value,I=t.value;let D=0;if(f){if(n.value=g,t.value=f,i&&i===E){i=null;return}D=I?f.position-I.position:0}else r(g);o.forEach(S=>{S(n.value,E,{delta:D,type:bn.pop,direction:D?D>0?ln.forward:ln.back:ln.unknown})})};function c(){i=n.value}function l(f){o.push(f);const g=()=>{const E=o.indexOf(f);E>-1&&o.splice(E,1)};return s.push(g),g}function u(){const{history:f}=window;!f.state||f.replaceState(ne({},f.state,{scroll:cr()}),"")}function h(){for(const f of s)f();s=[],window.removeEventListener("popstate",a),window.removeEventListener("beforeunload",u)}return window.addEventListener("popstate",a),window.addEventListener("beforeunload",u,{passive:!0}),{pauseListeners:c,listen:l,destroy:h}}function Ns(e,t,n,r=!1,o=!1){return{back:e,current:t,forward:n,replaced:r,position:window.history.length,scroll:o?cr():null}}function ud(e){const{history:t,location:n}=window,r={value:_a(e,n)},o={value:t.state};o.value||s(r.value,{back:null,current:r.value,forward:null,position:t.length-1,replaced:!0,scroll:null},!0);function s(c,l,u){const h=e.indexOf("#"),f=h>-1?(n.host&&document.querySelector("base")?e:e.slice(h))+c:cd()+e+c;try{t[u?"replaceState":"pushState"](l,"",f),o.value=l}catch(g){console.error(g),n[u?"replace":"assign"](f)}}function i(c,l){const u=ne({},t.state,Ns(o.value.back,c,o.value.forward,!0),l,{position:o.value.position});s(c,u,!0),r.value=c}function a(c,l){const u=ne({},o.value,t.state,{forward:c,scroll:cr()});s(u.current,u,!0);const h=ne({},Ns(r.value,c,null),{position:u.position+1},l);s(c,h,!1),r.value=c}return{location:r,state:o,push:a,replace:i}}function fd(e){e=td(e);const t=ud(e),n=ld(e,t.state,t.location,t.replace);function r(s,i=!0){i||n.pauseListeners(),history.go(s)}const o=ne({location:"",base:e,go:r,createHref:rd.bind(null,e)},t,n);return Object.defineProperty(o,"location",{enumerable:!0,get:()=>t.location.value}),Object.defineProperty(o,"state",{enumerable:!0,get:()=>t.state.value}),o}function dd(e){return e=location.host?e||location.pathname+location.search:"",e.includes("#")||(e+="#"),fd(e)}function hd(e){return typeof e=="string"||e&&typeof e=="object"}function ba(e){return typeof e=="string"||typeof e=="symbol"}const st={path:"/",name:void 0,params:{},query:{},hash:"",fullPath:"/",matched:[],meta:{},redirectedFrom:void 0},ya=Symbol("");var Ds;(function(e){e[e.aborted=4]="aborted",e[e.cancelled=8]="cancelled",e[e.duplicated=16]="duplicated"})(Ds||(Ds={}));function qt(e,t){return ne(new Error,{type:e,[ya]:!0},t)}function Ye(e,t){return e instanceof Error&&ya in e&&(t==null||!!(e.type&t))}const js="[^/]+?",pd={sensitive:!1,strict:!1,start:!0,end:!0},md=/[.+*?^${}()[\]/\\]/g;function gd(e,t){const n=ne({},pd,t),r=[];let o=n.start?"^":"";const s=[];for(const l of e){const u=l.length?[]:[90];n.strict&&!l.length&&(o+="/");for(let h=0;h<l.length;h++){const f=l[h];let g=40+(n.sensitive?.25:0);if(f.type===0)h||(o+="/"),o+=f.value.replace(md,"\\$&"),g+=40;else if(f.type===1){const{value:E,repeatable:I,optional:D,regexp:S}=f;s.push({name:E,repeatable:I,optional:D});const _=S||js;if(_!==js){g+=10;try{new RegExp(`(${_})`)}catch(y){throw new Error(`Invalid custom RegExp for param "${E}" (${_}): `+y.message)}}let R=I?`((?:${_})(?:/(?:${_}))*)`:`(${_})`;h||(R=D&&l.length<2?`(?:/${R})`:"/"+R),D&&(R+="?"),o+=R,g+=20,D&&(g+=-8),I&&(g+=-20),_===".*"&&(g+=-50)}u.push(g)}r.push(u)}if(n.strict&&n.end){const l=r.length-1;r[l][r[l].length-1]+=.7000000000000001}n.strict||(o+="/?"),n.end?o+="$":n.strict&&(o+="(?:/|$)");const i=new RegExp(o,n.sensitive?"":"i");function a(l){const u=l.match(i),h={};if(!u)return null;for(let f=1;f<u.length;f++){const g=u[f]||"",E=s[f-1];h[E.name]=g&&E.repeatable?g.split("/"):g}return h}function c(l){let u="",h=!1;for(const f of e){(!h||!u.endsWith("/"))&&(u+="/"),h=!1;for(const g of f)if(g.type===0)u+=g.value;else if(g.type===1){const{value:E,repeatable:I,optional:D}=g,S=E in l?l[E]:"";if(He(S)&&!I)throw new Error(`Provided param "${E}" is an array but it is not repeatable (* or + modifiers)`);const _=He(S)?S.join("/"):S;if(!_)if(D)f.length<2&&(u.endsWith("/")?u=u.slice(0,-1):h=!0);else throw new Error(`Missing required param "${E}"`);u+=_}}return u||"/"}return{re:i,score:r,keys:s,parse:a,stringify:c}}function vd(e,t){let n=0;for(;n<e.length&&n<t.length;){const r=t[n]-e[n];if(r)return r;n++}return e.length<t.length?e.length===1&&e[0]===40+40?-1:1:e.length>t.length?t.length===1&&t[0]===40+40?1:-1:0}function _d(e,t){let n=0;const r=e.score,o=t.score;for(;n<r.length&&n<o.length;){const s=vd(r[n],o[n]);if(s)return s;n++}if(Math.abs(o.length-r.length)===1){if(Fs(r))return 1;if(Fs(o))return-1}return o.length-r.length}function Fs(e){const t=e[e.length-1];return e.length>0&&t[t.length-1]<0}const bd={type:0,value:""},yd=/[a-zA-Z0-9_]/;function wd(e){if(!e)return[[]];if(e==="/")return[[bd]];if(!e.startsWith("/"))throw new Error(`Invalid path "${e}"`);function t(g){throw new Error(`ERR (${n})/"${l}": ${g}`)}let n=0,r=n;const o=[];let s;function i(){s&&o.push(s),s=[]}let a=0,c,l="",u="";function h(){!l||(n===0?s.push({type:0,value:l}):n===1||n===2||n===3?(s.length>1&&(c==="*"||c==="+")&&t(`A repeatable param (${l}) must be alone in its segment. eg: '/:ids+.`),s.push({type:1,value:l,regexp:u,repeatable:c==="*"||c==="+",optional:c==="*"||c==="?"})):t("Invalid state to consume buffer"),l="")}function f(){l+=c}for(;a<e.length;){if(c=e[a++],c==="\\"&&n!==2){r=n,n=4;continue}switch(n){case 0:c==="/"?(l&&h(),i()):c===":"?(h(),n=1):f();break;case 4:f(),n=r;break;case 1:c==="("?n=2:yd.test(c)?f():(h(),n=0,c!=="*"&&c!=="?"&&c!=="+"&&a--);break;case 2:c===")"?u[u.length-1]=="\\"?u=u.slice(0,-1)+c:n=3:u+=c;break;case 3:h(),n=0,c!=="*"&&c!=="?"&&c!=="+"&&a--,u="";break;default:t("Unknown state");break}}return n===2&&t(`Unfinished custom RegExp for param "${l}"`),h(),i(),o}function Ed(e,t,n){const r=gd(wd(e.path),n),o=ne(r,{record:e,parent:t,children:[],alias:[]});return t&&!o.record.aliasOf==!t.record.aliasOf&&t.children.push(o),o}function Sd(e,t){const n=[],r=new Map;t=Bs({strict:!1,end:!0,sensitive:!1},t);function o(u){return r.get(u)}function s(u,h,f){const g=!f,E=Cd(u);E.aliasOf=f&&f.record;const I=Bs(t,u),D=[E];if("alias"in u){const R=typeof u.alias=="string"?[u.alias]:u.alias;for(const y of R)D.push(ne({},E,{components:f?f.record.components:E.components,path:y,aliasOf:f?f.record:E}))}let S,_;for(const R of D){const{path:y}=R;if(h&&y[0]!=="/"){const T=h.record.path,k=T[T.length-1]==="/"?"":"/";R.path=h.record.path+(y&&k+y)}if(S=Ed(R,h,I),f?f.alias.push(S):(_=_||S,_!==S&&_.alias.push(S),g&&u.name&&!Hs(S)&&i(u.name)),E.children){const T=E.children;for(let k=0;k<T.length;k++)s(T[k],S,f&&f.children[k])}f=f||S,(S.record.components&&Object.keys(S.record.components).length||S.record.name||S.record.redirect)&&c(S)}return _?()=>{i(_)}:cn}function i(u){if(ba(u)){const h=r.get(u);h&&(r.delete(u),n.splice(n.indexOf(h),1),h.children.forEach(i),h.alias.forEach(i))}else{const h=n.indexOf(u);h>-1&&(n.splice(h,1),u.record.name&&r.delete(u.record.name),u.children.forEach(i),u.alias.forEach(i))}}function a(){return n}function c(u){let h=0;for(;h<n.length&&_d(u,n[h])>=0&&(u.record.path!==n[h].record.path||!wa(u,n[h]));)h++;n.splice(h,0,u),u.record.name&&!Hs(u)&&r.set(u.record.name,u)}function l(u,h){let f,g={},E,I;if("name"in u&&u.name){if(f=r.get(u.name),!f)throw qt(1,{location:u});I=f.record.name,g=ne($s(h.params,f.keys.filter(_=>!_.optional).map(_=>_.name)),u.params&&$s(u.params,f.keys.map(_=>_.name))),E=f.stringify(g)}else if("path"in u)E=u.path,f=n.find(_=>_.re.test(E)),f&&(g=f.parse(E),I=f.record.name);else{if(f=h.name?r.get(h.name):n.find(_=>_.re.test(h.path)),!f)throw qt(1,{location:u,currentLocation:h});I=f.record.name,g=ne({},h.params,u.params),E=f.stringify(g)}const D=[];let S=f;for(;S;)D.unshift(S.record),S=S.parent;return{name:I,path:E,params:g,matched:D,meta:Od(D)}}return e.forEach(u=>s(u)),{addRoute:s,resolve:l,removeRoute:i,getRoutes:a,getRecordMatcher:o}}function $s(e,t){const n={};for(const r of t)r in e&&(n[r]=e[r]);return n}function Cd(e){return{path:e.path,redirect:e.redirect,name:e.name,meta:e.meta||{},aliasOf:void 0,beforeEnter:e.beforeEnter,props:xd(e),children:e.children||[],instances:{},leaveGuards:new Set,updateGuards:new Set,enterCallbacks:{},components:"components"in e?e.components||null:e.component&&{default:e.component}}}function xd(e){const t={},n=e.props||!1;if("component"in e)t.default=n;else for(const r in e.components)t[r]=typeof n=="object"?n[r]:n;return t}function Hs(e){for(;e;){if(e.record.aliasOf)return!0;e=e.parent}return!1}function Od(e){return e.reduce((t,n)=>ne(t,n.meta),{})}function Bs(e,t){const n={};for(const r in e)n[r]=r in t?t[r]:e[r];return n}function wa(e,t){return t.children.some(n=>n===e||wa(e,n))}const Ea=/#/g,Id=/&/g,Pd=/\//g,Rd=/=/g,kd=/\?/g,Sa=/\+/g,Ad=/%5B/g,Md=/%5D/g,Ca=/%5E/g,Td=/%60/g,xa=/%7B/g,Ld=/%7C/g,Oa=/%7D/g,Nd=/%20/g;function xo(e){return encodeURI(""+e).replace(Ld,"|").replace(Ad,"[").replace(Md,"]")}function Dd(e){return xo(e).replace(xa,"{").replace(Oa,"}").replace(Ca,"^")}function Yr(e){return xo(e).replace(Sa,"%2B").replace(Nd,"+").replace(Ea,"%23").replace(Id,"%26").replace(Td,"`").replace(xa,"{").replace(Oa,"}").replace(Ca,"^")}function jd(e){return Yr(e).replace(Rd,"%3D")}function Fd(e){return xo(e).replace(Ea,"%23").replace(kd,"%3F")}function $d(e){return e==null?"":Fd(e).replace(Pd,"%2F")}function Kn(e){try{return decodeURIComponent(""+e)}catch{}return""+e}function Hd(e){const t={};if(e===""||e==="?")return t;const r=(e[0]==="?"?e.slice(1):e).split("&");for(let o=0;o<r.length;++o){const s=r[o].replace(Sa," "),i=s.indexOf("="),a=Kn(i<0?s:s.slice(0,i)),c=i<0?null:Kn(s.slice(i+1));if(a in t){let l=t[a];He(l)||(l=t[a]=[l]),l.push(c)}else t[a]=c}return t}function zs(e){let t="";for(let n in e){const r=e[n];if(n=jd(n),r==null){r!==void 0&&(t+=(t.length?"&":"")+n);continue}(He(r)?r.map(s=>s&&Yr(s)):[r&&Yr(r)]).forEach(s=>{s!==void 0&&(t+=(t.length?"&":"")+n,s!=null&&(t+="="+s))})}return t}function Bd(e){const t={};for(const n in e){const r=e[n];r!==void 0&&(t[n]=He(r)?r.map(o=>o==null?null:""+o):r==null?r:""+r)}return t}const zd=Symbol(""),Us=Symbol(""),lr=Symbol(""),Oo=Symbol(""),Jr=Symbol("");function Gt(){let e=[];function t(r){return e.push(r),()=>{const o=e.indexOf(r);o>-1&&e.splice(o,1)}}function n(){e=[]}return{add:t,list:()=>e.slice(),reset:n}}function ct(e,t,n,r,o){const s=r&&(r.enterCallbacks[o]=r.enterCallbacks[o]||[]);return()=>new Promise((i,a)=>{const c=h=>{h===!1?a(qt(4,{from:n,to:t})):h instanceof Error?a(h):hd(h)?a(qt(2,{from:t,to:h})):(s&&r.enterCallbacks[o]===s&&typeof h=="function"&&s.push(h),i())},l=e.call(r&&r.instances[o],t,n,c);let u=Promise.resolve(l);e.length<3&&(u=u.then(c)),u.catch(h=>a(h))})}function Ir(e,t,n,r){const o=[];for(const s of e)for(const i in s.components){let a=s.components[i];if(!(t!=="beforeRouteEnter"&&!s.instances[i]))if(Ud(a)){const l=(a.__vccOpts||a)[t];l&&o.push(ct(l,n,r,s,i))}else{let c=a();o.push(()=>c.then(l=>{if(!l)return Promise.reject(new Error(`Couldn't resolve component "${i}" at "${s.path}"`));const u=Qf(l)?l.default:l;s.components[i]=u;const f=(u.__vccOpts||u)[t];return f&&ct(f,n,r,s,i)()}))}}return o}function Ud(e){return typeof e=="object"||"displayName"in e||"props"in e||"__vccOpts"in e}function qs(e){const t=Te(lr),n=Te(Oo),r=Se(()=>t.resolve(Dt(e.to))),o=Se(()=>{const{matched:c}=r.value,{length:l}=c,u=c[l-1],h=n.matched;if(!u||!h.length)return-1;const f=h.findIndex(Ut.bind(null,u));if(f>-1)return f;const g=Ks(c[l-2]);return l>1&&Ks(u)===g&&h[h.length-1].path!==g?h.findIndex(Ut.bind(null,c[l-2])):f}),s=Se(()=>o.value>-1&&Wd(n.params,r.value.params)),i=Se(()=>o.value>-1&&o.value===n.matched.length-1&&va(n.params,r.value.params));function a(c={}){return Vd(c)?t[Dt(e.replace)?"replace":"push"](Dt(e.to)).catch(cn):Promise.resolve()}return{route:r,href:Se(()=>r.value.href),isActive:s,isExactActive:i,navigate:a}}const qd=po({name:"RouterLink",compatConfig:{MODE:3},props:{to:{type:[String,Object],required:!0},replace:Boolean,activeClass:String,exactActiveClass:String,custom:Boolean,ariaCurrentValue:{type:String,default:"page"}},useLink:qs,setup(e,{slots:t}){const n=It(qs(e)),{options:r}=Te(lr),o=Se(()=>({[Vs(e.activeClass,r.linkActiveClass,"router-link-active")]:n.isActive,[Vs(e.exactActiveClass,r.linkExactActiveClass,"router-link-exact-active")]:n.isExactActive}));return()=>{const s=t.default&&t.default(n);return e.custom?s:Eo("a",{"aria-current":n.isExactActive?e.ariaCurrentValue:null,href:n.href,onClick:n.navigate,class:o.value},s)}}}),Kd=qd;function Vd(e){if(!(e.metaKey||e.altKey||e.ctrlKey||e.shiftKey)&&!e.defaultPrevented&&!(e.button!==void 0&&e.button!==0)){if(e.currentTarget&&e.currentTarget.getAttribute){const t=e.currentTarget.getAttribute("target");if(/\b_blank\b/i.test(t))return}return e.preventDefault&&e.preventDefault(),!0}}function Wd(e,t){for(const n in t){const r=t[n],o=e[n];if(typeof r=="string"){if(r!==o)return!1}else if(!He(o)||o.length!==r.length||r.some((s,i)=>s!==o[i]))return!1}return!0}function Ks(e){return e?e.aliasOf?e.aliasOf.path:e.path:""}const Vs=(e,t,n)=>e!=null?e:t!=null?t:n,Qd=po({name:"RouterView",inheritAttrs:!1,props:{name:{type:String,default:"default"},route:Object},compatConfig:{MODE:3},setup(e,{attrs:t,slots:n}){const r=Te(Jr),o=Se(()=>e.route||r.value),s=Te(Us,0),i=Se(()=>{let l=Dt(s);const{matched:u}=o.value;let h;for(;(h=u[l])&&!h.components;)l++;return l}),a=Se(()=>o.value.matched[i.value]);Mn(Us,Se(()=>i.value+1)),Mn(zd,a),Mn(Jr,o);const c=Ht();return Ft(()=>[c.value,a.value,e.name],([l,u,h],[f,g,E])=>{u&&(u.instances[h]=l,g&&g!==u&&l&&l===f&&(u.leaveGuards.size||(u.leaveGuards=g.leaveGuards),u.updateGuards.size||(u.updateGuards=g.updateGuards))),l&&u&&(!g||!Ut(u,g)||!f)&&(u.enterCallbacks[h]||[]).forEach(I=>I(l))},{flush:"post"}),()=>{const l=o.value,u=e.name,h=a.value,f=h&&h.components[u];if(!f)return Ws(n.default,{Component:f,route:l});const g=h.props[u],E=g?g===!0?l.params:typeof g=="function"?g(l):g:null,D=Eo(f,ne({},E,t,{onVnodeUnmounted:S=>{S.component.isUnmounted&&(h.instances[u]=null)},ref:c}));return Ws(n.default,{Component:D,route:l})||D}}});function Ws(e,t){if(!e)return null;const n=e(t);return n.length===1?n[0]:n}const Yd=Qd;function Jd(e){const t=Sd(e.routes,e),n=e.parseQuery||Hd,r=e.stringifyQuery||zs,o=e.history,s=Gt(),i=Gt(),a=Gt(),c=bc(st);let l=st;Tt&&e.scrollBehavior&&"scrollRestoration"in history&&(history.scrollRestoration="manual");const u=xr.bind(null,b=>""+b),h=xr.bind(null,$d),f=xr.bind(null,Kn);function g(b,j){let M,B;return ba(b)?(M=t.getRecordMatcher(b),B=j):B=b,t.addRoute(B,M)}function E(b){const j=t.getRecordMatcher(b);j&&t.removeRoute(j)}function I(){return t.getRoutes().map(b=>b.record)}function D(b){return!!t.getRecordMatcher(b)}function S(b,j){if(j=ne({},j||c.value),typeof b=="string"){const m=Or(n,b,j.path),v=t.resolve({path:m.path},j),w=o.createHref(m.fullPath);return ne(m,v,{params:f(v.params),hash:Kn(m.hash),redirectedFrom:void 0,href:w})}let M;if("path"in b)M=ne({},b,{path:Or(n,b.path,j.path).path});else{const m=ne({},b.params);for(const v in m)m[v]==null&&delete m[v];M=ne({},b,{params:h(m)}),j.params=h(j.params)}const B=t.resolve(M,j),te=b.hash||"";B.params=u(f(B.params));const d=Gf(r,ne({},b,{hash:Dd(te),path:B.path})),p=o.createHref(d);return ne({fullPath:d,hash:te,query:r===zs?Bd(b.query):b.query||{}},B,{redirectedFrom:void 0,href:p})}function _(b){return typeof b=="string"?Or(n,b,c.value.path):ne({},b)}function R(b,j){if(l!==b)return qt(8,{from:j,to:b})}function y(b){return H(b)}function T(b){return y(ne(_(b),{replace:!0}))}function k(b){const j=b.matched[b.matched.length-1];if(j&&j.redirect){const{redirect:M}=j;let B=typeof M=="function"?M(b):M;return typeof B=="string"&&(B=B.includes("?")||B.includes("#")?B=_(B):{path:B},B.params={}),ne({query:b.query,hash:b.hash,params:"path"in B?{}:b.params},B)}}function H(b,j){const M=l=S(b),B=c.value,te=b.state,d=b.force,p=b.replace===!0,m=k(M);if(m)return H(ne(_(m),{state:typeof m=="object"?ne({},te,m.state):te,force:d,replace:p}),j||M);const v=M;v.redirectedFrom=j;let w;return!d&&Xf(r,B,M)&&(w=qt(16,{to:v,from:B}),Be(B,B,!0,!1)),(w?Promise.resolve(w):K(v,B)).catch(C=>Ye(C)?Ye(C,2)?C:tt(C):ee(C,v,B)).then(C=>{if(C){if(Ye(C,2))return H(ne({replace:p},_(C.to),{state:typeof C.to=="object"?ne({},te,C.to.state):te,force:d}),j||v)}else C=F(v,B,!0,p,te);return Q(v,B,C),C})}function O(b,j){const M=R(b,j);return M?Promise.reject(M):Promise.resolve()}function N(b){const j=Rt.values().next().value;return j&&typeof j.runWithContext=="function"?j.runWithContext(b):b()}function K(b,j){let M;const[B,te,d]=Gd(b,j);M=Ir(B.reverse(),"beforeRouteLeave",b,j);for(const m of B)m.leaveGuards.forEach(v=>{M.push(ct(v,b,j))});const p=O.bind(null,b,j);return M.push(p),ve(M).then(()=>{M=[];for(const m of s.list())M.push(ct(m,b,j));return M.push(p),ve(M)}).then(()=>{M=Ir(te,"beforeRouteUpdate",b,j);for(const m of te)m.updateGuards.forEach(v=>{M.push(ct(v,b,j))});return M.push(p),ve(M)}).then(()=>{M=[];for(const m of d)if(m.beforeEnter)if(He(m.beforeEnter))for(const v of m.beforeEnter)M.push(ct(v,b,j));else M.push(ct(m.beforeEnter,b,j));return M.push(p),ve(M)}).then(()=>(b.matched.forEach(m=>m.enterCallbacks={}),M=Ir(d,"beforeRouteEnter",b,j),M.push(p),ve(M))).then(()=>{M=[];for(const m of i.list())M.push(ct(m,b,j));return M.push(p),ve(M)}).catch(m=>Ye(m,8)?m:Promise.reject(m))}function Q(b,j,M){a.list().forEach(B=>N(()=>B(b,j,M)))}function F(b,j,M,B,te){const d=R(b,j);if(d)return d;const p=j===st,m=Tt?history.state:{};M&&(B||p?o.replace(b.fullPath,ne({scroll:p&&m&&m.scroll},te)):o.push(b.fullPath,te)),c.value=b,Be(b,j,M,p),tt()}let Z;function me(){Z||(Z=o.listen((b,j,M)=>{if(!En.listening)return;const B=S(b),te=k(B);if(te){H(ne(te,{replace:!0}),B).catch(cn);return}l=B;const d=c.value;Tt&&id(Ls(d.fullPath,M.delta),cr()),K(B,d).catch(p=>Ye(p,12)?p:Ye(p,2)?(H(p.to,B).then(m=>{Ye(m,20)&&!M.delta&&M.type===bn.pop&&o.go(-1,!1)}).catch(cn),Promise.reject()):(M.delta&&o.go(-M.delta,!1),ee(p,B,d))).then(p=>{p=p||F(B,d,!1),p&&(M.delta&&!Ye(p,8)?o.go(-M.delta,!1):M.type===bn.pop&&Ye(p,20)&&o.go(-1,!1)),Q(B,d,p)}).catch(cn)}))}let ye=Gt(),J=Gt(),oe;function ee(b,j,M){tt(b);const B=J.list();return B.length?B.forEach(te=>te(b,j,M)):console.error(b),Promise.reject(b)}function Qe(){return oe&&c.value!==st?Promise.resolve():new Promise((b,j)=>{ye.add([b,j])})}function tt(b){return oe||(oe=!b,me(),ye.list().forEach(([j,M])=>b?M(b):j()),ye.reset()),b}function Be(b,j,M,B){const{scrollBehavior:te}=e;if(!Tt||!te)return Promise.resolve();const d=!M&&ad(Ls(b.fullPath,0))||(B||!M)&&history.state&&history.state.scroll||null;return hn().then(()=>te(b,j,d)).then(p=>p&&sd(p)).catch(p=>ee(p,b,j))}const Ee=b=>o.go(b);let Pt;const Rt=new Set,En={currentRoute:c,listening:!0,addRoute:g,removeRoute:E,hasRoute:D,getRoutes:I,resolve:S,options:e,push:y,replace:T,go:Ee,back:()=>Ee(-1),forward:()=>Ee(1),beforeEach:s.add,beforeResolve:i.add,afterEach:a.add,onError:J.add,isReady:Qe,install(b){const j=this;b.component("RouterLink",Kd),b.component("RouterView",Yd),b.config.globalProperties.$router=j,Object.defineProperty(b.config.globalProperties,"$route",{enumerable:!0,get:()=>Dt(c)}),Tt&&!Pt&&c.value===st&&(Pt=!0,y(o.location).catch(te=>{}));const M={};for(const te in st)Object.defineProperty(M,te,{get:()=>c.value[te],enumerable:!0});b.provide(lr,j),b.provide(Oo,hi(M)),b.provide(Jr,c);const B=b.unmount;Rt.add(b),b.unmount=function(){Rt.delete(b),Rt.size<1&&(l=st,Z&&Z(),Z=null,c.value=st,Pt=!1,oe=!1),B()}}};function ve(b){return b.reduce((j,M)=>j.then(()=>N(M)),Promise.resolve())}return En}function Gd(e,t){const n=[],r=[],o=[],s=Math.max(t.matched.length,e.matched.length);for(let i=0;i<s;i++){const a=t.matched[i];a&&(e.matched.find(l=>Ut(l,a))?r.push(a):n.push(a));const c=e.matched[i];c&&(t.matched.find(l=>Ut(l,c))||o.push(c))}return[n,r,o]}function Xd(){return Te(lr)}function Zd(){return Te(Oo)}function eh(){const e=Co(),t=Xd(),n=Zd(),r=Se(()=>document.querySelector(".q-layout"));function o(){return n.params.isStarted==="true"}const s=async S=>{for(let y=1;y<=4;y++)try{e.com.checkingConnection=!0;const T=await i();if(console.log(T?"Could read PING file":"Could NOT read PING file"),T){e.com.checkingConnection=!1,S();break}}catch(T){console.error(`Error checking file existence (attempt ${y}):`,T)}finally{y<4?await new Promise(T=>setTimeout(T,8e3)):(e.com.checkingConnection=!1,e.com.lmsError={callSucceeded:!1,lastErrorCode:0,lastErrorMsg:"Connection issues. Check connection and try again"})}},i=async()=>{try{return(await fetch("./media/ping.txt",{method:"GET",cache:"no-store"})).ok}catch(S){return console.error("Error checking file existence:",S),!1}},a=async S=>{await hn(),window.scrollBy({top:S,left:0,behavior:"smooth"})},c=async()=>{await hn(),window.scrollTo({top:0,left:0,behavior:"smooth"})};function l(){e.rte_updateContentStatus({arg:e.getActivePage,status:1})}function u(S){e.rte_updateContentStatus({arg:S,status:1})}function h(S,_){u(S),f(_)}function f(S){t.push({path:S})}function g(S,_){return _.find(y=>y.id===S)}function E(){setTimeout(()=>{const S=document.getElementsByTagName("h1")[0];S&&(S.setAttribute("tabindex",0),S.focus())},500)}function I(S,_){setTimeout(()=>{S.$el&&S.$el.focus({preventScroll:!0})},_||0)}function D(S,_){setTimeout(()=>{const y=document.getElementById(S);y&&y.focus({preventScroll:!0})},_||0)}return{scrollDown:a,scrollTop:c,complete:l,routerTo:f,completePage:u,focusH1:E,findQuestionById:g,rootElement:r,completePageAndRoute:h,routeParamsIsStarted:o,setFocusByRef:I,setFocusById:D,checkNetwork:s}}var th=(e,t)=>{const n=e.__vccOpts||e;for(const[r,o]of t)n[r]=o;return n};const nh=po({name:"App",setup(){const e=Ht(!1),t=Ht(0),n=Co();eh();const r=ga();tr(()=>{r.initAOS()});function o(){t.value=t.value+1,t.value==40&&(e.value=!0,console.log("***------ imgLoaded ------***",t.value))}return{store:n,imagesAllLoaded:e,imagesLoaded:t,imgLoaded:o}}}),rh=bo("div",{style:{display:"none"}},null,-1);function oh(e,t,n,r,o,s){const i=Zc("router-view");return Ui(),Sl("div",null,[rh,xe(i)])}var sh=th(nh,[["render",oh]]);function Dh(e){return e}var Pr=()=>$u();const ih=[{path:"/",component:()=>ce(()=>import("./MainLayout.ae57c711.js"),["assets/MainLayout.ae57c711.js","assets/QSeparator.3707aaf2.js","assets/use-dark.0de4478a.js","assets/QBtn.c6cd36c1.js","assets/QImg.f55ff82e.js","assets/ClosePopup.b5270395.js","assets/use-tick.9c8d097a.js","assets/QCardSection.d8eae2a5.js","assets/selection.3c67842b.js","assets/QItem.cc743337.js","assets/QCard.8d7aab57.js","assets/use-quasar.de58b715.js","assets/vue-i18n.esm-bundler.5dea4c24.js","assets/logo-blue-red.20d53d90.js"]),children:[{path:"",component:()=>ce(()=>import("./Page1.731b16d9.js"),["assets/Page1.731b16d9.js","assets/Page1.795356cf.css","assets/QImg.f55ff82e.js","assets/QBtn.c6cd36c1.js","assets/QSeparator.3707aaf2.js","assets/use-dark.0de4478a.js","assets/QuestionCard.9699d84c.js","assets/vue-i18n.esm-bundler.5dea4c24.js","assets/QPage.75be44cb.js","assets/use-quasar.de58b715.js","assets/graphic_globe.e2a7f483.js","assets/graphic_callout1.59e8f0e2.js","assets/logo-blue-red.20d53d90.js"])}]},{path:"/page1",component:()=>ce(()=>import("./MainLayout.ae57c711.js"),["assets/MainLayout.ae57c711.js","assets/QSeparator.3707aaf2.js","assets/use-dark.0de4478a.js","assets/QBtn.c6cd36c1.js","assets/QImg.f55ff82e.js","assets/ClosePopup.b5270395.js","assets/use-tick.9c8d097a.js","assets/QCardSection.d8eae2a5.js","assets/selection.3c67842b.js","assets/QItem.cc743337.js","assets/QCard.8d7aab57.js","assets/use-quasar.de58b715.js","assets/vue-i18n.esm-bundler.5dea4c24.js","assets/logo-blue-red.20d53d90.js"]),children:[{path:"",component:()=>ce(()=>import("./Page1.731b16d9.js"),["assets/Page1.731b16d9.js","assets/Page1.795356cf.css","assets/QImg.f55ff82e.js","assets/QBtn.c6cd36c1.js","assets/QSeparator.3707aaf2.js","assets/use-dark.0de4478a.js","assets/QuestionCard.9699d84c.js","assets/vue-i18n.esm-bundler.5dea4c24.js","assets/QPage.75be44cb.js","assets/use-quasar.de58b715.js","assets/graphic_globe.e2a7f483.js","assets/graphic_callout1.59e8f0e2.js","assets/logo-blue-red.20d53d90.js"])}]},{path:"/page2",component:()=>ce(()=>import("./MainLayout.ae57c711.js"),["assets/MainLayout.ae57c711.js","assets/QSeparator.3707aaf2.js","assets/use-dark.0de4478a.js","assets/QBtn.c6cd36c1.js","assets/QImg.f55ff82e.js","assets/ClosePopup.b5270395.js","assets/use-tick.9c8d097a.js","assets/QCardSection.d8eae2a5.js","assets/selection.3c67842b.js","assets/QItem.cc743337.js","assets/QCard.8d7aab57.js","assets/use-quasar.de58b715.js","assets/vue-i18n.esm-bundler.5dea4c24.js","assets/logo-blue-red.20d53d90.js"]),children:[{path:"",component:()=>ce(()=>import("./Page2.70e1ebf8.js"),["assets/Page2.70e1ebf8.js","assets/Page2.4d051b67.css","assets/QSeparator.3707aaf2.js","assets/use-dark.0de4478a.js","assets/QBtn.c6cd36c1.js","assets/QPage.75be44cb.js","assets/use-quasar.de58b715.js","assets/vue-i18n.esm-bundler.5dea4c24.js","assets/graphic_globe.e2a7f483.js"])}]},{path:"/page3",component:()=>ce(()=>import("./MainLayout.ae57c711.js"),["assets/MainLayout.ae57c711.js","assets/QSeparator.3707aaf2.js","assets/use-dark.0de4478a.js","assets/QBtn.c6cd36c1.js","assets/QImg.f55ff82e.js","assets/ClosePopup.b5270395.js","assets/use-tick.9c8d097a.js","assets/QCardSection.d8eae2a5.js","assets/selection.3c67842b.js","assets/QItem.cc743337.js","assets/QCard.8d7aab57.js","assets/use-quasar.de58b715.js","assets/vue-i18n.esm-bundler.5dea4c24.js","assets/logo-blue-red.20d53d90.js"]),children:[{path:"",component:()=>ce(()=>import("./Page3.e7d6460c.js"),["assets/Page3.e7d6460c.js","assets/Page3.54964240.css","assets/QSeparator.3707aaf2.js","assets/use-dark.0de4478a.js","assets/QBtn.c6cd36c1.js","assets/QImg.f55ff82e.js","assets/QPage.75be44cb.js","assets/use-quasar.de58b715.js","assets/vue-i18n.esm-bundler.5dea4c24.js","assets/graphic_globe.e2a7f483.js","assets/QItem.cc743337.js","assets/QCardSection.d8eae2a5.js","assets/QCard.8d7aab57.js","assets/uid.42677368.js","assets/modalcheckciti.cc5533a7.js","assets/graphic_callout1.59e8f0e2.js"])}]},{path:"/page4",component:()=>ce(()=>import("./MainLayout.ae57c711.js"),["assets/MainLayout.ae57c711.js","assets/QSeparator.3707aaf2.js","assets/use-dark.0de4478a.js","assets/QBtn.c6cd36c1.js","assets/QImg.f55ff82e.js","assets/ClosePopup.b5270395.js","assets/use-tick.9c8d097a.js","assets/QCardSection.d8eae2a5.js","assets/selection.3c67842b.js","assets/QItem.cc743337.js","assets/QCard.8d7aab57.js","assets/use-quasar.de58b715.js","assets/vue-i18n.esm-bundler.5dea4c24.js","assets/logo-blue-red.20d53d90.js"]),children:[{path:"",component:()=>ce(()=>import("./Page4.d13f5709.js"),["assets/Page4.d13f5709.js","assets/Page4.4bf29d84.css","assets/QImg.f55ff82e.js","assets/QBtn.c6cd36c1.js","assets/QSeparator.3707aaf2.js","assets/use-dark.0de4478a.js","assets/QPage.75be44cb.js","assets/use-quasar.de58b715.js","assets/vue-i18n.esm-bundler.5dea4c24.js","assets/graphic_globe.e2a7f483.js","assets/graphic_callout1.59e8f0e2.js"])}]},{path:"/page5",component:()=>ce(()=>import("./MainLayout.ae57c711.js"),["assets/MainLayout.ae57c711.js","assets/QSeparator.3707aaf2.js","assets/use-dark.0de4478a.js","assets/QBtn.c6cd36c1.js","assets/QImg.f55ff82e.js","assets/ClosePopup.b5270395.js","assets/use-tick.9c8d097a.js","assets/QCardSection.d8eae2a5.js","assets/selection.3c67842b.js","assets/QItem.cc743337.js","assets/QCard.8d7aab57.js","assets/use-quasar.de58b715.js","assets/vue-i18n.esm-bundler.5dea4c24.js","assets/logo-blue-red.20d53d90.js"]),children:[{path:"",component:()=>ce(()=>import("./Page5.8a4d63fa.js"),["assets/Page5.8a4d63fa.js","assets/Page5.a0adf233.css","assets/QSeparator.3707aaf2.js","assets/use-dark.0de4478a.js","assets/QBtn.c6cd36c1.js","assets/QImg.f55ff82e.js","assets/QPage.75be44cb.js","assets/use-quasar.de58b715.js","assets/uid.42677368.js","assets/selection.3c67842b.js","assets/use-tick.9c8d097a.js","assets/QCard.8d7aab57.js","assets/graphic_globe.e2a7f483.js","assets/modalcheckciti.cc5533a7.js","assets/graphic_callout1.59e8f0e2.js","assets/vue-i18n.esm-bundler.5dea4c24.js"])}]},{path:"/page6",component:()=>ce(()=>import("./MainLayout.ae57c711.js"),["assets/MainLayout.ae57c711.js","assets/QSeparator.3707aaf2.js","assets/use-dark.0de4478a.js","assets/QBtn.c6cd36c1.js","assets/QImg.f55ff82e.js","assets/ClosePopup.b5270395.js","assets/use-tick.9c8d097a.js","assets/QCardSection.d8eae2a5.js","assets/selection.3c67842b.js","assets/QItem.cc743337.js","assets/QCard.8d7aab57.js","assets/use-quasar.de58b715.js","assets/vue-i18n.esm-bundler.5dea4c24.js","assets/logo-blue-red.20d53d90.js"]),children:[{path:"",component:()=>ce(()=>import("./Page6.746c04d7.js"),["assets/Page6.746c04d7.js","assets/Page6.fa0f30e3.css","assets/QImg.f55ff82e.js","assets/QBtn.c6cd36c1.js","assets/QSeparator.3707aaf2.js","assets/use-dark.0de4478a.js","assets/QPage.75be44cb.js","assets/use-quasar.de58b715.js","assets/vue-i18n.esm-bundler.5dea4c24.js","assets/graphic_globe.e2a7f483.js","assets/graphic_callout1.59e8f0e2.js","assets/scroll.ee85abe8.js"])}]},{path:"/page7",component:()=>ce(()=>import("./MainLayout.ae57c711.js"),["assets/MainLayout.ae57c711.js","assets/QSeparator.3707aaf2.js","assets/use-dark.0de4478a.js","assets/QBtn.c6cd36c1.js","assets/QImg.f55ff82e.js","assets/ClosePopup.b5270395.js","assets/use-tick.9c8d097a.js","assets/QCardSection.d8eae2a5.js","assets/selection.3c67842b.js","assets/QItem.cc743337.js","assets/QCard.8d7aab57.js","assets/use-quasar.de58b715.js","assets/vue-i18n.esm-bundler.5dea4c24.js","assets/logo-blue-red.20d53d90.js"]),children:[{path:"",component:()=>ce(()=>import("./Page7.32beb13c.js"),["assets/Page7.32beb13c.js","assets/Page7.68f1659a.css","assets/QImg.f55ff82e.js","assets/QBtn.c6cd36c1.js","assets/ClosePopup.b5270395.js","assets/use-tick.9c8d097a.js","assets/QCardSection.d8eae2a5.js","assets/QCard.8d7aab57.js","assets/use-dark.0de4478a.js","assets/QPage.75be44cb.js","assets/use-quasar.de58b715.js","assets/vue-i18n.esm-bundler.5dea4c24.js","assets/graphic_globe.e2a7f483.js","assets/graphic_callout1.59e8f0e2.js","assets/modalcheckciti.cc5533a7.js","assets/scroll.ee85abe8.js"])}]},{path:"/page8",component:()=>ce(()=>import("./MainLayout.ae57c711.js"),["assets/MainLayout.ae57c711.js","assets/QSeparator.3707aaf2.js","assets/use-dark.0de4478a.js","assets/QBtn.c6cd36c1.js","assets/QImg.f55ff82e.js","assets/ClosePopup.b5270395.js","assets/use-tick.9c8d097a.js","assets/QCardSection.d8eae2a5.js","assets/selection.3c67842b.js","assets/QItem.cc743337.js","assets/QCard.8d7aab57.js","assets/use-quasar.de58b715.js","assets/vue-i18n.esm-bundler.5dea4c24.js","assets/logo-blue-red.20d53d90.js"]),children:[{path:"",component:()=>ce(()=>import("./Page8.f2b818a9.js"),["assets/Page8.f2b818a9.js","assets/Page8.46e178bb.css","assets/QBtn.c6cd36c1.js","assets/QPage.75be44cb.js","assets/use-quasar.de58b715.js","assets/vue-i18n.esm-bundler.5dea4c24.js","assets/graphic_globe.e2a7f483.js","assets/graphic_callout1.59e8f0e2.js","assets/modalcheckciti.cc5533a7.js"])}]},{path:"/page11",component:()=>ce(()=>import("./MainLayout.ae57c711.js"),["assets/MainLayout.ae57c711.js","assets/QSeparator.3707aaf2.js","assets/use-dark.0de4478a.js","assets/QBtn.c6cd36c1.js","assets/QImg.f55ff82e.js","assets/ClosePopup.b5270395.js","assets/use-tick.9c8d097a.js","assets/QCardSection.d8eae2a5.js","assets/selection.3c67842b.js","assets/QItem.cc743337.js","assets/QCard.8d7aab57.js","assets/use-quasar.de58b715.js","assets/vue-i18n.esm-bundler.5dea4c24.js","assets/logo-blue-red.20d53d90.js"]),children:[{path:"",component:()=>ce(()=>import("./Page11.b0a7bb58.js"),["assets/Page11.b0a7bb58.js","assets/Page11.102239aa.css","assets/QPage.75be44cb.js","assets/QBtn.c6cd36c1.js","assets/use-quasar.de58b715.js","assets/vue-i18n.esm-bundler.5dea4c24.js","assets/QSeparator.3707aaf2.js","assets/use-dark.0de4478a.js","assets/QImg.f55ff82e.js","assets/QuestionCard.9699d84c.js"])}]},{path:"/:catchAll(.*)*",component:()=>ce(()=>import("./ErrorNotFound.d71c7e60.js"),["assets/ErrorNotFound.d71c7e60.js","assets/QBtn.c6cd36c1.js"])}];var Rr=function(){return Jd({scrollBehavior:()=>({left:0,top:0}),routes:ih,history:dd("/")})};async function ah(e,t){const n=e(sh);n.use(ju,t);const r=typeof Pr=="function"?await Pr({}):Pr;n.use(r);const o=wn(typeof Rr=="function"?await Rr({store:r}):Rr);return r.use(({store:s})=>{s.router=o}),{app:n,store:r,router:o}}var ch={config:{screen:{bodyClasses:!0}},lang:zn};const lh="/";async function uh({app:e,router:t,store:n},r){let o=!1;const s=c=>{try{return t.resolve(c).href}catch{}return Object(c)===c?null:c},i=c=>{if(o=!0,typeof c=="string"&&/^https?:\/\//.test(c)){window.location.href=c;return}const l=s(c);l!==null&&(window.location.href=l,window.location.reload())},a=window.location.href.replace(window.location.origin,"");for(let c=0;o===!1&&c<r.length;c++)try{await r[c]({app:e,router:t,store:n,ssrContext:null,redirect:i,urlPath:a,publicPath:lh})}catch(l){if(l&&l.url){i(l.url);return}console.error("[Quasar] boot error:",l);return}o!==!0&&(e.use(t),e.mount("#q-app"))}ah(du,ch).then(e=>{const[t,n]=Promise.allSettled!==void 0?["allSettled",r=>r.map(o=>{if(o.status==="rejected"){console.error("[Quasar] boot error:",o.reason);return}return o.value.default})]:["all",r=>r.map(o=>o.default)];return Promise[t]([ce(()=>import("./i18n.62f6830c.js"),["assets/i18n.62f6830c.js","assets/vue-i18n.esm-bundler.5dea4c24.js"]),ce(()=>import("./quasar-lang-pack.f3955cbd.js"),[])]).then(r=>{const o=n(r).filter(s=>typeof s=="function");uh(e,o)})});export{gh as $,ph as A,Mn as B,Mh as C,Ot as D,zt as E,qe as F,It as G,po as H,th as I,Ui as J,Cl as K,Tc as L,ro as M,_h as N,bo as O,Il as P,ju as Q,fh as R,Co as S,nr as T,eh as U,ga as V,Zc as W,Sl as X,bh as Y,Yi as Z,ce as _,Ti as a,yh as a0,wn as a1,Dt as a2,kh as a3,Cu as a4,Sh as a5,_s as a6,So as a7,vh as a8,Tu as a9,qr as aa,Kc as ab,no as ac,Nh as ad,dh as ae,hh as af,G as ag,ut as ah,hi as ai,Th as aj,Iu as ak,wr as al,qc as am,mh as an,Rh as ao,Dh as b,Se as c,fe as d,ti as e,xe as f,Vi as g,Eo as h,Te as i,Lh as j,mo as k,Ah as l,Ae as m,_n as n,tr as o,wh as p,Ih as q,Ht as r,Oh as s,ms as t,Ch as u,Eh as v,Ft as w,Ph as x,xh as y,hn as z};
