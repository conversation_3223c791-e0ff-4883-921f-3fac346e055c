var o={isoName:"eo",nativeName:"Esperanto",label:{clear:"<PERSON><PERSON><PERSON>",ok:"<PERSON><PERSON>",cancel:"<PERSON><PERSON><PERSON>",close:"<PERSON><PERSON><PERSON>",set:"<PERSON><PERSON><PERSON>",select:"<PERSON><PERSON><PERSON>",reset:"<PERSON><PERSON><PERSON>",remove:"<PERSON><PERSON>",update:"\u011Cisdatigi",create:"<PERSON><PERSON><PERSON>",search:"Ser\u0109i",filter:"<PERSON><PERSON><PERSON>",refresh:"Re\u015Dargi",expand:i=>i?`<PERSON><PERSON><PERSON> "${i}"`:"<PERSON><PERSON><PERSON>",collapse:i=>i?`<PERSON><PERSON><PERSON> "${i}"`:"<PERSON><PERSON><PERSON>"},date:{days:"Diman\u0109o_Lundo_Mardo_Merkredo_Ja\u016Ddo_Vendredo_Sabato".split("_"),daysShort:"Dim_Lun_Mar_Mer_Ja\u016D_Ven_Sab".split("_"),months:"<PERSON><PERSON><PERSON>_Februaro_Marto_Aprilo_Majo_Junio_Julio_A\u016Dgusto_Septembro_Oktobro_Novembro_Decembro".split("_"),monthsShort:"Jan_Feb_Mar_Apr_Maj_Jun_Jul_A\u016Dg_Sep_Okt_Nov_Dec".split("_"),firstDayOfWeek:1,format24h:!0,pluralDay:"tagoj"},table:{noData:"Neniu datumo afi\u015Denda",noResults:"Neniu datumo trovita",loading:"\u015Car\u011Dado...",selectedRecords:i=>i>0?i+" "+(i===1?"elektita linio":"elektitaj linioj")+".":"Neniu elektita linio.",recordsPerPage:"Linioj po pa\u011Doj:",allRows:"\u0108iuj",pagination:(i,e,a)=>i+"-"+e+" el "+a,columns:"Kolumnoj"},editor:{url:"URL",bold:"Grasa",italic:"Kursiva",strikethrough:"Trastreka",underline:"Substreka",unorderedList:"Neordigita listo",orderedList:"Ordigita listo",subscript:"Indico",superscript:"Supra indico",hyperlink:"Ligilo",toggleFullscreen:"\u015Calti plenekranon",quote:"Cita\u0135o",left:"\u011Cisrandigi maldekstren",center:"Centrigi",right:"\u011Cisrandigi dekstren",justify:"\u011Cisrandigi amba\u016Dflanke",print:"Printi",outdent:"Malkrommar\u011Denigi",indent:"Krommar\u011Denigi",removeFormat:"Forigi prezenton",formatting:"Prezento",fontSize:"Tipara grando",align:"\u011Cisrandigi",hr:"Enmeti horizontalan strekon",undo:"Malfari",redo:"Refari",heading1:"Titolo 1",heading2:"Titolo 2",heading3:"Titolo 3",heading4:"Titolo 4",heading5:"Titolo 5",heading6:"Titolo 6",paragraph:"Paragrafo",code:"Kodo",size1:"Tre malgranda",size2:"Malgranda",size3:"Normala",size4:"Meza",size5:"Granda",size6:"Tre granda",size7:"Maksimuma",defaultFont:"Implicita tiparo",viewSource:"Vida Fonto"},tree:{noData:"Neniu nodo afi\u015Denda",noResults:"Neniu nodo trovita"}};export{o as default};
