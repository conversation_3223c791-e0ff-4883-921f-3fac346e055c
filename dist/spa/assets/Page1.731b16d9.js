import{Q as Ut}from"./QImg.f55ff82e.js";import{Q as Pt}from"./QSeparator.3707aaf2.js";import{l as Xr,Q as Ps}from"./QBtn.c6cd36c1.js";import{L as ss,Q as Fl,a as zl}from"./QuestionCard.9699d84c.js";import{Q as $l}from"./QPage.75be44cb.js";import{u as Bl}from"./use-quasar.de58b715.js";import{u as Xn}from"./vue-i18n.esm-bundler.5dea4c24.js";import{H as eo,I as os,W as ur,J as H,X as re,F as Kt,$ as Or,M as wr,O as x,ac as yi,R as Hl,f as F,K as xr,S as ca,U as Vn,V as Wn,r as Gi,c as Po,w as Nl,o as fa,ad as Yl,N as Yt,L as Xl,ae as Vl,af as Wl}from"./index.c6ba88b2.js";import{V as Ql,g as Ul}from"./graphic_globe.e2a7f483.js";import{c as Gl,g as jl}from"./graphic_callout1.59e8f0e2.js";import{_ as Kl}from"./logo-blue-red.20d53d90.js";import"./use-dark.0de4478a.js";function ar(s){if(s===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return s}function da(s,e){s.prototype=Object.create(e.prototype),s.prototype.constructor=s,s.__proto__=e}/*!
 * GSAP 3.12.5
 * https://gsap.com
 *
 * @license Copyright 2008-2024, GreenSock. All rights reserved.
 * Subject to the terms at https://gsap.com/standard-license or for
 * Club GSAP members, the agreement issued with that membership.
 * @author: Jack Doyle, <EMAIL>
*/var St={autoSleep:120,force3D:"auto",nullTargetWarn:1,units:{lineHeight:""}},Pi={duration:.5,overwrite:!1,delay:0},to,Ue,ce,Lt=1e8,se=1/Lt,Os=Math.PI*2,Zl=Os/4,Jl=0,ha=Math.sqrt,eu=Math.cos,tu=Math.sin,Ie=function(e){return typeof e=="string"},me=function(e){return typeof e=="function"},hr=function(e){return typeof e=="number"},ro=function(e){return typeof e=="undefined"},ir=function(e){return typeof e=="object"},ct=function(e){return e!==!1},io=function(){return typeof window!="undefined"},xn=function(e){return me(e)||Ie(e)},_a=typeof ArrayBuffer=="function"&&ArrayBuffer.isView||function(){},Ge=Array.isArray,Ds=/(?:-?\.?\d|\.)+/gi,pa=/[-+=.]*\d+[.e\-+]*\d*[e\-+]*\d*/g,vi=/[-+=.]*\d+[.e-]*\d*[a-z%]*/g,ds=/[-+=.]*\d+\.?\d*(?:e-|e\+)?\d*/gi,ga=/[+-]=-?[.\d]+/,ma=/[^,'"\[\]\s]+/gi,ru=/^[+\-=e\s\d]*\d+[.\d]*([a-z]*|%)\s*$/i,he,Gt,Ls,no,Ct={},Qn={},ya,va=function(e){return(Qn=ii(e,Ct))&&_t},so=function(e,r){return console.warn("Invalid property",e,"set to",r,"Missing plugin? gsap.registerPlugin()")},un=function(e,r){return!r&&console.warn(e)},ba=function(e,r){return e&&(Ct[e]=r)&&Qn&&(Qn[e]=r)||Ct},cn=function(){return 0},iu={suppressEvents:!0,isStart:!0,kill:!1},qn={suppressEvents:!0,kill:!1},nu={suppressEvents:!0},oo={},Dr=[],As={},Ta,bt={},hs={},Oo=30,In=[],ao="",lo=function(e){var r=e[0],t,i;if(ir(r)||me(r)||(e=[e]),!(t=(r._gsap||{}).harness)){for(i=In.length;i--&&!In[i].targetTest(r););t=In[i]}for(i=e.length;i--;)e[i]&&(e[i]._gsap||(e[i]._gsap=new Va(e[i],t)))||e.splice(i,1);return e},jr=function(e){return e._gsap||lo(At(e))[0]._gsap},xa=function(e,r,t){return(t=e[r])&&me(t)?e[r]():ro(t)&&e.getAttribute&&e.getAttribute(r)||t},ft=function(e,r){return(e=e.split(",")).forEach(r)||e},Te=function(e){return Math.round(e*1e5)/1e5||0},qe=function(e){return Math.round(e*1e7)/1e7||0},xi=function(e,r){var t=r.charAt(0),i=parseFloat(r.substr(2));return e=parseFloat(e),t==="+"?e+i:t==="-"?e-i:t==="*"?e*i:e/i},su=function(e,r){for(var t=r.length,i=0;e.indexOf(r[i])<0&&++i<t;);return i<t},Un=function(){var e=Dr.length,r=Dr.slice(0),t,i;for(As={},Dr.length=0,t=0;t<e;t++)i=r[t],i&&i._lazy&&(i.render(i._lazy[0],i._lazy[1],!0)._lazy=0)},wa=function(e,r,t,i){Dr.length&&!Ue&&Un(),e.render(r,t,i||Ue&&r<0&&(e._initted||e._startAt)),Dr.length&&!Ue&&Un()},Sa=function(e){var r=parseFloat(e);return(r||r===0)&&(e+"").match(ma).length<2?r:Ie(e)?e.trim():e},Ca=function(e){return e},Et=function(e,r){for(var t in r)t in e||(e[t]=r[t]);return e},ou=function(e){return function(r,t){for(var i in t)i in r||i==="duration"&&e||i==="ease"||(r[i]=t[i])}},ii=function(e,r){for(var t in r)e[t]=r[t];return e},Do=function s(e,r){for(var t in r)t!=="__proto__"&&t!=="constructor"&&t!=="prototype"&&(e[t]=ir(r[t])?s(e[t]||(e[t]={}),r[t]):r[t]);return e},Gn=function(e,r){var t={},i;for(i in e)i in r||(t[i]=e[i]);return t},ji=function(e){var r=e.parent||he,t=e.keyframes?ou(Ge(e.keyframes)):Et;if(ct(e.inherit))for(;r;)t(e,r.vars.defaults),r=r.parent||r._dp;return e},au=function(e,r){for(var t=e.length,i=t===r.length;i&&t--&&e[t]===r[t];);return t<0},ka=function(e,r,t,i,n){t===void 0&&(t="_first"),i===void 0&&(i="_last");var o=e[i],a;if(n)for(a=r[n];o&&o[n]>a;)o=o._prev;return o?(r._next=o._next,o._next=r):(r._next=e[t],e[t]=r),r._next?r._next._prev=r:e[i]=r,r._prev=o,r.parent=r._dp=e,r},as=function(e,r,t,i){t===void 0&&(t="_first"),i===void 0&&(i="_last");var n=r._prev,o=r._next;n?n._next=o:e[t]===r&&(e[t]=o),o?o._prev=n:e[i]===r&&(e[i]=n),r._next=r._prev=r.parent=null},Rr=function(e,r){e.parent&&(!r||e.parent.autoRemoveChildren)&&e.parent.remove&&e.parent.remove(e),e._act=0},Kr=function(e,r){if(e&&(!r||r._end>e._dur||r._start<0))for(var t=e;t;)t._dirty=1,t=t.parent;return e},lu=function(e){for(var r=e.parent;r&&r.parent;)r._dirty=1,r.totalDuration(),r=r.parent;return e},Rs=function(e,r,t,i){return e._startAt&&(Ue?e._startAt.revert(qn):e.vars.immediateRender&&!e.vars.autoRevert||e._startAt.render(r,!0,i))},uu=function s(e){return!e||e._ts&&s(e.parent)},Lo=function(e){return e._repeat?Oi(e._tTime,e=e.duration()+e._rDelay)*e:0},Oi=function(e,r){var t=Math.floor(e/=r);return e&&t===e?t-1:t},jn=function(e,r){return(e-r._start)*r._ts+(r._ts>=0?0:r._dirty?r.totalDuration():r._tDur)},ls=function(e){return e._end=qe(e._start+(e._tDur/Math.abs(e._ts||e._rts||se)||0))},us=function(e,r){var t=e._dp;return t&&t.smoothChildTiming&&e._ts&&(e._start=qe(t._time-(e._ts>0?r/e._ts:((e._dirty?e.totalDuration():e._tDur)-r)/-e._ts)),ls(e),t._dirty||Kr(t,e)),e},Ma=function(e,r){var t;if((r._time||!r._dur&&r._initted||r._start<e._time&&(r._dur||!r.add))&&(t=jn(e.rawTime(),r),(!r._dur||yn(0,r.totalDuration(),t)-r._tTime>se)&&r.render(t,!0)),Kr(e,r)._dp&&e._initted&&e._time>=e._dur&&e._ts){if(e._dur<e.duration())for(t=e;t._dp;)t.rawTime()>=0&&t.totalTime(t._tTime),t=t._dp;e._zTime=-se}},Zt=function(e,r,t,i){return r.parent&&Rr(r),r._start=qe((hr(t)?t:t||e!==he?Mt(e,t,r):e._time)+r._delay),r._end=qe(r._start+(r.totalDuration()/Math.abs(r.timeScale())||0)),ka(e,r,"_first","_last",e._sort?"_start":0),Es(r)||(e._recent=r),i||Ma(e,r),e._ts<0&&us(e,e._tTime),e},Pa=function(e,r){return(Ct.ScrollTrigger||so("scrollTrigger",r))&&Ct.ScrollTrigger.create(r,e)},Oa=function(e,r,t,i,n){if(co(e,r,n),!e._initted)return 1;if(!t&&e._pt&&!Ue&&(e._dur&&e.vars.lazy!==!1||!e._dur&&e.vars.lazy)&&Ta!==Tt.frame)return Dr.push(e),e._lazy=[n,i],1},cu=function s(e){var r=e.parent;return r&&r._ts&&r._initted&&!r._lock&&(r.rawTime()<0||s(r))},Es=function(e){var r=e.data;return r==="isFromStart"||r==="isStart"},fu=function(e,r,t,i){var n=e.ratio,o=r<0||!r&&(!e._start&&cu(e)&&!(!e._initted&&Es(e))||(e._ts<0||e._dp._ts<0)&&!Es(e))?0:1,a=e._rDelay,l=0,u,c,d;if(a&&e._repeat&&(l=yn(0,e._tDur,r),c=Oi(l,a),e._yoyo&&c&1&&(o=1-o),c!==Oi(e._tTime,a)&&(n=1-o,e.vars.repeatRefresh&&e._initted&&e.invalidate())),o!==n||Ue||i||e._zTime===se||!r&&e._zTime){if(!e._initted&&Oa(e,r,i,t,l))return;for(d=e._zTime,e._zTime=r||(t?se:0),t||(t=r&&!d),e.ratio=o,e._from&&(o=1-o),e._time=0,e._tTime=l,u=e._pt;u;)u.r(o,u.d),u=u._next;r<0&&Rs(e,r,t,!0),e._onUpdate&&!t&&wt(e,"onUpdate"),l&&e._repeat&&!t&&e.parent&&wt(e,"onRepeat"),(r>=e._tDur||r<0)&&e.ratio===o&&(o&&Rr(e,1),!t&&!Ue&&(wt(e,o?"onComplete":"onReverseComplete",!0),e._prom&&e._prom()))}else e._zTime||(e._zTime=r)},du=function(e,r,t){var i;if(t>r)for(i=e._first;i&&i._start<=t;){if(i.data==="isPause"&&i._start>r)return i;i=i._next}else for(i=e._last;i&&i._start>=t;){if(i.data==="isPause"&&i._start<r)return i;i=i._prev}},Di=function(e,r,t,i){var n=e._repeat,o=qe(r)||0,a=e._tTime/e._tDur;return a&&!i&&(e._time*=o/e._dur),e._dur=o,e._tDur=n?n<0?1e10:qe(o*(n+1)+e._rDelay*n):o,a>0&&!i&&us(e,e._tTime=e._tDur*a),e.parent&&ls(e),t||Kr(e.parent,e),e},Ao=function(e){return e instanceof st?Kr(e):Di(e,e._dur)},hu={_start:0,endTime:cn,totalDuration:cn},Mt=function s(e,r,t){var i=e.labels,n=e._recent||hu,o=e.duration()>=Lt?n.endTime(!1):e._dur,a,l,u;return Ie(r)&&(isNaN(r)||r in i)?(l=r.charAt(0),u=r.substr(-1)==="%",a=r.indexOf("="),l==="<"||l===">"?(a>=0&&(r=r.replace(/=/,"")),(l==="<"?n._start:n.endTime(n._repeat>=0))+(parseFloat(r.substr(1))||0)*(u?(a<0?n:t).totalDuration()/100:1)):a<0?(r in i||(i[r]=o),i[r]):(l=parseFloat(r.charAt(a-1)+r.substr(a+1)),u&&t&&(l=l/100*(Ge(t)?t[0]:t).totalDuration()),a>1?s(e,r.substr(0,a-1),t)+l:o+l)):r==null?o:+r},Ki=function(e,r,t){var i=hr(r[1]),n=(i?2:1)+(e<2?0:1),o=r[n],a,l;if(i&&(o.duration=r[1]),o.parent=t,e){for(a=o,l=t;l&&!("immediateRender"in a);)a=l.vars.defaults||{},l=ct(l.vars.inherit)&&l.parent;o.immediateRender=ct(a.immediateRender),e<2?o.runBackwards=1:o.startAt=r[n-1]}return new Me(r[0],o,r[n+1])},Ir=function(e,r){return e||e===0?r(e):r},yn=function(e,r,t){return t<e?e:t>r?r:t},Qe=function(e,r){return!Ie(e)||!(r=ru.exec(e))?"":r[1]},_u=function(e,r,t){return Ir(t,function(i){return yn(e,r,i)})},qs=[].slice,Da=function(e,r){return e&&ir(e)&&"length"in e&&(!r&&!e.length||e.length-1 in e&&ir(e[0]))&&!e.nodeType&&e!==Gt},pu=function(e,r,t){return t===void 0&&(t=[]),e.forEach(function(i){var n;return Ie(i)&&!r||Da(i,1)?(n=t).push.apply(n,At(i)):t.push(i)})||t},At=function(e,r,t){return ce&&!r&&ce.selector?ce.selector(e):Ie(e)&&!t&&(Ls||!Li())?qs.call((r||no).querySelectorAll(e),0):Ge(e)?pu(e,t):Da(e)?qs.call(e,0):e?[e]:[]},Is=function(e){return e=At(e)[0]||un("Invalid scope")||{},function(r){var t=e.current||e.nativeElement||e;return At(r,t.querySelectorAll?t:t===e?un("Invalid scope")||no.createElement("div"):e)}},La=function(e){return e.sort(function(){return .5-Math.random()})},Aa=function(e){if(me(e))return e;var r=ir(e)?e:{each:e},t=Zr(r.ease),i=r.from||0,n=parseFloat(r.base)||0,o={},a=i>0&&i<1,l=isNaN(i)||a,u=r.axis,c=i,d=i;return Ie(i)?c=d={center:.5,edges:.5,end:1}[i]||0:!a&&l&&(c=i[0],d=i[1]),function(_,f,p){var h=(p||r).length,m=o[h],w,T,C,v,S,M,b,O,k;if(!m){if(k=r.grid==="auto"?0:(r.grid||[1,Lt])[1],!k){for(b=-Lt;b<(b=p[k++].getBoundingClientRect().left)&&k<h;);k<h&&k--}for(m=o[h]=[],w=l?Math.min(k,h)*c-.5:i%k,T=k===Lt?0:l?h*d/k-.5:i/k|0,b=0,O=Lt,M=0;M<h;M++)C=M%k-w,v=T-(M/k|0),m[M]=S=u?Math.abs(u==="y"?v:C):ha(C*C+v*v),S>b&&(b=S),S<O&&(O=S);i==="random"&&La(m),m.max=b-O,m.min=O,m.v=h=(parseFloat(r.amount)||parseFloat(r.each)*(k>h?h-1:u?u==="y"?h/k:k:Math.max(k,h/k))||0)*(i==="edges"?-1:1),m.b=h<0?n-h:n,m.u=Qe(r.amount||r.each)||0,t=t&&h<0?Na(t):t}return h=(m[_]-m.min)/m.max||0,qe(m.b+(t?t(h):h)*m.v)+m.u}},Fs=function(e){var r=Math.pow(10,((e+"").split(".")[1]||"").length);return function(t){var i=qe(Math.round(parseFloat(t)/e)*e*r);return(i-i%1)/r+(hr(t)?0:Qe(t))}},Ra=function(e,r){var t=Ge(e),i,n;return!t&&ir(e)&&(i=t=e.radius||Lt,e.values?(e=At(e.values),(n=!hr(e[0]))&&(i*=i)):e=Fs(e.increment)),Ir(r,t?me(e)?function(o){return n=e(o),Math.abs(n-o)<=i?n:o}:function(o){for(var a=parseFloat(n?o.x:o),l=parseFloat(n?o.y:0),u=Lt,c=0,d=e.length,_,f;d--;)n?(_=e[d].x-a,f=e[d].y-l,_=_*_+f*f):_=Math.abs(e[d]-a),_<u&&(u=_,c=d);return c=!i||u<=i?e[c]:o,n||c===o||hr(o)?c:c+Qe(o)}:Fs(e))},Ea=function(e,r,t,i){return Ir(Ge(e)?!r:t===!0?!!(t=0):!i,function(){return Ge(e)?e[~~(Math.random()*e.length)]:(t=t||1e-5)&&(i=t<1?Math.pow(10,(t+"").length-2):1)&&Math.floor(Math.round((e-t/2+Math.random()*(r-e+t*.99))/t)*t*i)/i})},gu=function(){for(var e=arguments.length,r=new Array(e),t=0;t<e;t++)r[t]=arguments[t];return function(i){return r.reduce(function(n,o){return o(n)},i)}},mu=function(e,r){return function(t){return e(parseFloat(t))+(r||Qe(t))}},yu=function(e,r,t){return Ia(e,r,0,1,t)},qa=function(e,r,t){return Ir(t,function(i){return e[~~r(i)]})},vu=function s(e,r,t){var i=r-e;return Ge(e)?qa(e,s(0,e.length),r):Ir(t,function(n){return(i+(n-e)%i)%i+e})},bu=function s(e,r,t){var i=r-e,n=i*2;return Ge(e)?qa(e,s(0,e.length-1),r):Ir(t,function(o){return o=(n+(o-e)%n)%n||0,e+(o>i?n-o:o)})},fn=function(e){for(var r=0,t="",i,n,o,a;~(i=e.indexOf("random(",r));)o=e.indexOf(")",i),a=e.charAt(i+7)==="[",n=e.substr(i+7,o-i-7).match(a?ma:Ds),t+=e.substr(r,i-r)+Ea(a?n:+n[0],a?0:+n[1],+n[2]||1e-5),r=o+1;return t+e.substr(r,e.length-r)},Ia=function(e,r,t,i,n){var o=r-e,a=i-t;return Ir(n,function(l){return t+((l-e)/o*a||0)})},Tu=function s(e,r,t,i){var n=isNaN(e+r)?0:function(f){return(1-f)*e+f*r};if(!n){var o=Ie(e),a={},l,u,c,d,_;if(t===!0&&(i=1)&&(t=null),o)e={p:e},r={p:r};else if(Ge(e)&&!Ge(r)){for(c=[],d=e.length,_=d-2,u=1;u<d;u++)c.push(s(e[u-1],e[u]));d--,n=function(p){p*=d;var h=Math.min(_,~~p);return c[h](p-h)},t=r}else i||(e=ii(Ge(e)?[]:{},e));if(!c){for(l in r)uo.call(a,e,l,"get",r[l]);n=function(p){return _o(p,a)||(o?e.p:e)}}}return Ir(t,n)},Ro=function(e,r,t){var i=e.labels,n=Lt,o,a,l;for(o in i)a=i[o]-r,a<0==!!t&&a&&n>(a=Math.abs(a))&&(l=o,n=a);return l},wt=function(e,r,t){var i=e.vars,n=i[r],o=ce,a=e._ctx,l,u,c;if(!!n)return l=i[r+"Params"],u=i.callbackScope||e,t&&Dr.length&&Un(),a&&(ce=a),c=l?n.apply(u,l):n.call(u),ce=o,c},Ni=function(e){return Rr(e),e.scrollTrigger&&e.scrollTrigger.kill(!!Ue),e.progress()<1&&wt(e,"onInterrupt"),e},bi,Fa=[],za=function(e){if(!!e)if(e=!e.name&&e.default||e,io()||e.headless){var r=e.name,t=me(e),i=r&&!t&&e.init?function(){this._props=[]}:e,n={init:cn,render:_o,add:uo,kill:Fu,modifier:Iu,rawVars:0},o={targetTest:0,get:0,getSetter:ho,aliases:{},register:0};if(Li(),e!==i){if(bt[r])return;Et(i,Et(Gn(e,n),o)),ii(i.prototype,ii(n,Gn(e,o))),bt[i.prop=r]=i,e.targetTest&&(In.push(i),oo[r]=1),r=(r==="css"?"CSS":r.charAt(0).toUpperCase()+r.substr(1))+"Plugin"}ba(r,i),e.register&&e.register(_t,i,dt)}else Fa.push(e)},ne=255,Yi={aqua:[0,ne,ne],lime:[0,ne,0],silver:[192,192,192],black:[0,0,0],maroon:[128,0,0],teal:[0,128,128],blue:[0,0,ne],navy:[0,0,128],white:[ne,ne,ne],olive:[128,128,0],yellow:[ne,ne,0],orange:[ne,165,0],gray:[128,128,128],purple:[128,0,128],green:[0,128,0],red:[ne,0,0],pink:[ne,192,203],cyan:[0,ne,ne],transparent:[ne,ne,ne,0]},_s=function(e,r,t){return e+=e<0?1:e>1?-1:0,(e*6<1?r+(t-r)*e*6:e<.5?t:e*3<2?r+(t-r)*(2/3-e)*6:r)*ne+.5|0},$a=function(e,r,t){var i=e?hr(e)?[e>>16,e>>8&ne,e&ne]:0:Yi.black,n,o,a,l,u,c,d,_,f,p;if(!i){if(e.substr(-1)===","&&(e=e.substr(0,e.length-1)),Yi[e])i=Yi[e];else if(e.charAt(0)==="#"){if(e.length<6&&(n=e.charAt(1),o=e.charAt(2),a=e.charAt(3),e="#"+n+n+o+o+a+a+(e.length===5?e.charAt(4)+e.charAt(4):"")),e.length===9)return i=parseInt(e.substr(1,6),16),[i>>16,i>>8&ne,i&ne,parseInt(e.substr(7),16)/255];e=parseInt(e.substr(1),16),i=[e>>16,e>>8&ne,e&ne]}else if(e.substr(0,3)==="hsl"){if(i=p=e.match(Ds),!r)l=+i[0]%360/360,u=+i[1]/100,c=+i[2]/100,o=c<=.5?c*(u+1):c+u-c*u,n=c*2-o,i.length>3&&(i[3]*=1),i[0]=_s(l+1/3,n,o),i[1]=_s(l,n,o),i[2]=_s(l-1/3,n,o);else if(~e.indexOf("="))return i=e.match(pa),t&&i.length<4&&(i[3]=1),i}else i=e.match(Ds)||Yi.transparent;i=i.map(Number)}return r&&!p&&(n=i[0]/ne,o=i[1]/ne,a=i[2]/ne,d=Math.max(n,o,a),_=Math.min(n,o,a),c=(d+_)/2,d===_?l=u=0:(f=d-_,u=c>.5?f/(2-d-_):f/(d+_),l=d===n?(o-a)/f+(o<a?6:0):d===o?(a-n)/f+2:(n-o)/f+4,l*=60),i[0]=~~(l+.5),i[1]=~~(u*100+.5),i[2]=~~(c*100+.5)),t&&i.length<4&&(i[3]=1),i},Ba=function(e){var r=[],t=[],i=-1;return e.split(Lr).forEach(function(n){var o=n.match(vi)||[];r.push.apply(r,o),t.push(i+=o.length+1)}),r.c=t,r},Eo=function(e,r,t){var i="",n=(e+i).match(Lr),o=r?"hsla(":"rgba(",a=0,l,u,c,d;if(!n)return e;if(n=n.map(function(_){return(_=$a(_,r,1))&&o+(r?_[0]+","+_[1]+"%,"+_[2]+"%,"+_[3]:_.join(","))+")"}),t&&(c=Ba(e),l=t.c,l.join(i)!==c.c.join(i)))for(u=e.replace(Lr,"1").split(vi),d=u.length-1;a<d;a++)i+=u[a]+(~l.indexOf(a)?n.shift()||o+"0,0,0,0)":(c.length?c:n.length?n:t).shift());if(!u)for(u=e.split(Lr),d=u.length-1;a<d;a++)i+=u[a]+n[a];return i+u[d]},Lr=function(){var s="(?:\\b(?:(?:rgb|rgba|hsl|hsla)\\(.+?\\))|\\B#(?:[0-9a-f]{3,4}){1,2}\\b",e;for(e in Yi)s+="|"+e+"\\b";return new RegExp(s+")","gi")}(),xu=/hsl[a]?\(/,Ha=function(e){var r=e.join(" "),t;if(Lr.lastIndex=0,Lr.test(r))return t=xu.test(r),e[1]=Eo(e[1],t),e[0]=Eo(e[0],t,Ba(e[1])),!0},dn,Tt=function(){var s=Date.now,e=500,r=33,t=s(),i=t,n=1e3/240,o=n,a=[],l,u,c,d,_,f,p=function h(m){var w=s()-i,T=m===!0,C,v,S,M;if((w>e||w<0)&&(t+=w-r),i+=w,S=i-t,C=S-o,(C>0||T)&&(M=++d.frame,_=S-d.time*1e3,d.time=S=S/1e3,o+=C+(C>=n?4:n-C),v=1),T||(l=u(h)),v)for(f=0;f<a.length;f++)a[f](S,_,M,m)};return d={time:0,frame:0,tick:function(){p(!0)},deltaRatio:function(m){return _/(1e3/(m||60))},wake:function(){ya&&(!Ls&&io()&&(Gt=Ls=window,no=Gt.document||{},Ct.gsap=_t,(Gt.gsapVersions||(Gt.gsapVersions=[])).push(_t.version),va(Qn||Gt.GreenSockGlobals||!Gt.gsap&&Gt||{}),Fa.forEach(za)),c=typeof requestAnimationFrame!="undefined"&&requestAnimationFrame,l&&d.sleep(),u=c||function(m){return setTimeout(m,o-d.time*1e3+1|0)},dn=1,p(2))},sleep:function(){(c?cancelAnimationFrame:clearTimeout)(l),dn=0,u=cn},lagSmoothing:function(m,w){e=m||1/0,r=Math.min(w||33,e)},fps:function(m){n=1e3/(m||240),o=d.time*1e3+n},add:function(m,w,T){var C=w?function(v,S,M,b){m(v,S,M,b),d.remove(C)}:m;return d.remove(m),a[T?"unshift":"push"](C),Li(),C},remove:function(m,w){~(w=a.indexOf(m))&&a.splice(w,1)&&f>=w&&f--},_listeners:a},d}(),Li=function(){return!dn&&Tt.wake()},K={},wu=/^[\d.\-M][\d.\-,\s]/,Su=/["']/g,Cu=function(e){for(var r={},t=e.substr(1,e.length-3).split(":"),i=t[0],n=1,o=t.length,a,l,u;n<o;n++)l=t[n],a=n!==o-1?l.lastIndexOf(","):l.length,u=l.substr(0,a),r[i]=isNaN(u)?u.replace(Su,"").trim():+u,i=l.substr(a+1).trim();return r},ku=function(e){var r=e.indexOf("(")+1,t=e.indexOf(")"),i=e.indexOf("(",r);return e.substring(r,~i&&i<t?e.indexOf(")",t+1):t)},Mu=function(e){var r=(e+"").split("("),t=K[r[0]];return t&&r.length>1&&t.config?t.config.apply(null,~e.indexOf("{")?[Cu(r[1])]:ku(e).split(",").map(Sa)):K._CE&&wu.test(e)?K._CE("",e):t},Na=function(e){return function(r){return 1-e(1-r)}},Ya=function s(e,r){for(var t=e._first,i;t;)t instanceof st?s(t,r):t.vars.yoyoEase&&(!t._yoyo||!t._repeat)&&t._yoyo!==r&&(t.timeline?s(t.timeline,r):(i=t._ease,t._ease=t._yEase,t._yEase=i,t._yoyo=r)),t=t._next},Zr=function(e,r){return e&&(me(e)?e:K[e]||Mu(e))||r},li=function(e,r,t,i){t===void 0&&(t=function(l){return 1-r(1-l)}),i===void 0&&(i=function(l){return l<.5?r(l*2)/2:1-r((1-l)*2)/2});var n={easeIn:r,easeOut:t,easeInOut:i},o;return ft(e,function(a){K[a]=Ct[a]=n,K[o=a.toLowerCase()]=t;for(var l in n)K[o+(l==="easeIn"?".in":l==="easeOut"?".out":".inOut")]=K[a+"."+l]=n[l]}),n},Xa=function(e){return function(r){return r<.5?(1-e(1-r*2))/2:.5+e((r-.5)*2)/2}},ps=function s(e,r,t){var i=r>=1?r:1,n=(t||(e?.3:.45))/(r<1?r:1),o=n/Os*(Math.asin(1/i)||0),a=function(c){return c===1?1:i*Math.pow(2,-10*c)*tu((c-o)*n)+1},l=e==="out"?a:e==="in"?function(u){return 1-a(1-u)}:Xa(a);return n=Os/n,l.config=function(u,c){return s(e,u,c)},l},gs=function s(e,r){r===void 0&&(r=1.70158);var t=function(o){return o?--o*o*((r+1)*o+r)+1:0},i=e==="out"?t:e==="in"?function(n){return 1-t(1-n)}:Xa(t);return i.config=function(n){return s(e,n)},i};ft("Linear,Quad,Cubic,Quart,Quint,Strong",function(s,e){var r=e<5?e+1:e;li(s+",Power"+(r-1),e?function(t){return Math.pow(t,r)}:function(t){return t},function(t){return 1-Math.pow(1-t,r)},function(t){return t<.5?Math.pow(t*2,r)/2:1-Math.pow((1-t)*2,r)/2})});K.Linear.easeNone=K.none=K.Linear.easeIn;li("Elastic",ps("in"),ps("out"),ps());(function(s,e){var r=1/e,t=2*r,i=2.5*r,n=function(a){return a<r?s*a*a:a<t?s*Math.pow(a-1.5/e,2)+.75:a<i?s*(a-=2.25/e)*a+.9375:s*Math.pow(a-2.625/e,2)+.984375};li("Bounce",function(o){return 1-n(1-o)},n)})(7.5625,2.75);li("Expo",function(s){return s?Math.pow(2,10*(s-1)):0});li("Circ",function(s){return-(ha(1-s*s)-1)});li("Sine",function(s){return s===1?1:-eu(s*Zl)+1});li("Back",gs("in"),gs("out"),gs());K.SteppedEase=K.steps=Ct.SteppedEase={config:function(e,r){e===void 0&&(e=1);var t=1/e,i=e+(r?0:1),n=r?1:0,o=1-se;return function(a){return((i*yn(0,o,a)|0)+n)*t}}};Pi.ease=K["quad.out"];ft("onComplete,onUpdate,onStart,onRepeat,onReverseComplete,onInterrupt",function(s){return ao+=s+","+s+"Params,"});var Va=function(e,r){this.id=Jl++,e._gsap=this,this.target=e,this.harness=r,this.get=r?r.get:xa,this.set=r?r.getSetter:ho},hn=function(){function s(r){this.vars=r,this._delay=+r.delay||0,(this._repeat=r.repeat===1/0?-2:r.repeat||0)&&(this._rDelay=r.repeatDelay||0,this._yoyo=!!r.yoyo||!!r.yoyoEase),this._ts=1,Di(this,+r.duration,1,1),this.data=r.data,ce&&(this._ctx=ce,ce.data.push(this)),dn||Tt.wake()}var e=s.prototype;return e.delay=function(t){return t||t===0?(this.parent&&this.parent.smoothChildTiming&&this.startTime(this._start+t-this._delay),this._delay=t,this):this._delay},e.duration=function(t){return arguments.length?this.totalDuration(this._repeat>0?t+(t+this._rDelay)*this._repeat:t):this.totalDuration()&&this._dur},e.totalDuration=function(t){return arguments.length?(this._dirty=0,Di(this,this._repeat<0?t:(t-this._repeat*this._rDelay)/(this._repeat+1))):this._tDur},e.totalTime=function(t,i){if(Li(),!arguments.length)return this._tTime;var n=this._dp;if(n&&n.smoothChildTiming&&this._ts){for(us(this,t),!n._dp||n.parent||Ma(n,this);n&&n.parent;)n.parent._time!==n._start+(n._ts>=0?n._tTime/n._ts:(n.totalDuration()-n._tTime)/-n._ts)&&n.totalTime(n._tTime,!0),n=n.parent;!this.parent&&this._dp.autoRemoveChildren&&(this._ts>0&&t<this._tDur||this._ts<0&&t>0||!this._tDur&&!t)&&Zt(this._dp,this,this._start-this._delay)}return(this._tTime!==t||!this._dur&&!i||this._initted&&Math.abs(this._zTime)===se||!t&&!this._initted&&(this.add||this._ptLookup))&&(this._ts||(this._pTime=t),wa(this,t,i)),this},e.time=function(t,i){return arguments.length?this.totalTime(Math.min(this.totalDuration(),t+Lo(this))%(this._dur+this._rDelay)||(t?this._dur:0),i):this._time},e.totalProgress=function(t,i){return arguments.length?this.totalTime(this.totalDuration()*t,i):this.totalDuration()?Math.min(1,this._tTime/this._tDur):this.rawTime()>0?1:0},e.progress=function(t,i){return arguments.length?this.totalTime(this.duration()*(this._yoyo&&!(this.iteration()&1)?1-t:t)+Lo(this),i):this.duration()?Math.min(1,this._time/this._dur):this.rawTime()>0?1:0},e.iteration=function(t,i){var n=this.duration()+this._rDelay;return arguments.length?this.totalTime(this._time+(t-1)*n,i):this._repeat?Oi(this._tTime,n)+1:1},e.timeScale=function(t,i){if(!arguments.length)return this._rts===-se?0:this._rts;if(this._rts===t)return this;var n=this.parent&&this._ts?jn(this.parent._time,this):this._tTime;return this._rts=+t||0,this._ts=this._ps||t===-se?0:this._rts,this.totalTime(yn(-Math.abs(this._delay),this._tDur,n),i!==!1),ls(this),lu(this)},e.paused=function(t){return arguments.length?(this._ps!==t&&(this._ps=t,t?(this._pTime=this._tTime||Math.max(-this._delay,this.rawTime()),this._ts=this._act=0):(Li(),this._ts=this._rts,this.totalTime(this.parent&&!this.parent.smoothChildTiming?this.rawTime():this._tTime||this._pTime,this.progress()===1&&Math.abs(this._zTime)!==se&&(this._tTime-=se)))),this):this._ps},e.startTime=function(t){if(arguments.length){this._start=t;var i=this.parent||this._dp;return i&&(i._sort||!this.parent)&&Zt(i,this,t-this._delay),this}return this._start},e.endTime=function(t){return this._start+(ct(t)?this.totalDuration():this.duration())/Math.abs(this._ts||1)},e.rawTime=function(t){var i=this.parent||this._dp;return i?t&&(!this._ts||this._repeat&&this._time&&this.totalProgress()<1)?this._tTime%(this._dur+this._rDelay):this._ts?jn(i.rawTime(t),this):this._tTime:this._tTime},e.revert=function(t){t===void 0&&(t=nu);var i=Ue;return Ue=t,(this._initted||this._startAt)&&(this.timeline&&this.timeline.revert(t),this.totalTime(-.01,t.suppressEvents)),this.data!=="nested"&&t.kill!==!1&&this.kill(),Ue=i,this},e.globalTime=function(t){for(var i=this,n=arguments.length?t:i.rawTime();i;)n=i._start+n/(Math.abs(i._ts)||1),i=i._dp;return!this.parent&&this._sat?this._sat.globalTime(t):n},e.repeat=function(t){return arguments.length?(this._repeat=t===1/0?-2:t,Ao(this)):this._repeat===-2?1/0:this._repeat},e.repeatDelay=function(t){if(arguments.length){var i=this._time;return this._rDelay=t,Ao(this),i?this.time(i):this}return this._rDelay},e.yoyo=function(t){return arguments.length?(this._yoyo=t,this):this._yoyo},e.seek=function(t,i){return this.totalTime(Mt(this,t),ct(i))},e.restart=function(t,i){return this.play().totalTime(t?-this._delay:0,ct(i))},e.play=function(t,i){return t!=null&&this.seek(t,i),this.reversed(!1).paused(!1)},e.reverse=function(t,i){return t!=null&&this.seek(t||this.totalDuration(),i),this.reversed(!0).paused(!1)},e.pause=function(t,i){return t!=null&&this.seek(t,i),this.paused(!0)},e.resume=function(){return this.paused(!1)},e.reversed=function(t){return arguments.length?(!!t!==this.reversed()&&this.timeScale(-this._rts||(t?-se:0)),this):this._rts<0},e.invalidate=function(){return this._initted=this._act=0,this._zTime=-se,this},e.isActive=function(){var t=this.parent||this._dp,i=this._start,n;return!!(!t||this._ts&&this._initted&&t.isActive()&&(n=t.rawTime(!0))>=i&&n<this.endTime(!0)-se)},e.eventCallback=function(t,i,n){var o=this.vars;return arguments.length>1?(i?(o[t]=i,n&&(o[t+"Params"]=n),t==="onUpdate"&&(this._onUpdate=i)):delete o[t],this):o[t]},e.then=function(t){var i=this;return new Promise(function(n){var o=me(t)?t:Ca,a=function(){var u=i.then;i.then=null,me(o)&&(o=o(i))&&(o.then||o===i)&&(i.then=u),n(o),i.then=u};i._initted&&i.totalProgress()===1&&i._ts>=0||!i._tTime&&i._ts<0?a():i._prom=a})},e.kill=function(){Ni(this)},s}();Et(hn.prototype,{_time:0,_start:0,_end:0,_tTime:0,_tDur:0,_dirty:0,_repeat:0,_yoyo:!1,parent:null,_initted:!1,_rDelay:0,_ts:1,_dp:0,ratio:0,_zTime:-se,_prom:0,_ps:!1,_rts:1});var st=function(s){da(e,s);function e(t,i){var n;return t===void 0&&(t={}),n=s.call(this,t)||this,n.labels={},n.smoothChildTiming=!!t.smoothChildTiming,n.autoRemoveChildren=!!t.autoRemoveChildren,n._sort=ct(t.sortChildren),he&&Zt(t.parent||he,ar(n),i),t.reversed&&n.reverse(),t.paused&&n.paused(!0),t.scrollTrigger&&Pa(ar(n),t.scrollTrigger),n}var r=e.prototype;return r.to=function(i,n,o){return Ki(0,arguments,this),this},r.from=function(i,n,o){return Ki(1,arguments,this),this},r.fromTo=function(i,n,o,a){return Ki(2,arguments,this),this},r.set=function(i,n,o){return n.duration=0,n.parent=this,ji(n).repeatDelay||(n.repeat=0),n.immediateRender=!!n.immediateRender,new Me(i,n,Mt(this,o),1),this},r.call=function(i,n,o){return Zt(this,Me.delayedCall(0,i,n),o)},r.staggerTo=function(i,n,o,a,l,u,c){return o.duration=n,o.stagger=o.stagger||a,o.onComplete=u,o.onCompleteParams=c,o.parent=this,new Me(i,o,Mt(this,l)),this},r.staggerFrom=function(i,n,o,a,l,u,c){return o.runBackwards=1,ji(o).immediateRender=ct(o.immediateRender),this.staggerTo(i,n,o,a,l,u,c)},r.staggerFromTo=function(i,n,o,a,l,u,c,d){return a.startAt=o,ji(a).immediateRender=ct(a.immediateRender),this.staggerTo(i,n,a,l,u,c,d)},r.render=function(i,n,o){var a=this._time,l=this._dirty?this.totalDuration():this._tDur,u=this._dur,c=i<=0?0:qe(i),d=this._zTime<0!=i<0&&(this._initted||!u),_,f,p,h,m,w,T,C,v,S,M,b;if(this!==he&&c>l&&i>=0&&(c=l),c!==this._tTime||o||d){if(a!==this._time&&u&&(c+=this._time-a,i+=this._time-a),_=c,v=this._start,C=this._ts,w=!C,d&&(u||(a=this._zTime),(i||!n)&&(this._zTime=i)),this._repeat){if(M=this._yoyo,m=u+this._rDelay,this._repeat<-1&&i<0)return this.totalTime(m*100+i,n,o);if(_=qe(c%m),c===l?(h=this._repeat,_=u):(h=~~(c/m),h&&h===c/m&&(_=u,h--),_>u&&(_=u)),S=Oi(this._tTime,m),!a&&this._tTime&&S!==h&&this._tTime-S*m-this._dur<=0&&(S=h),M&&h&1&&(_=u-_,b=1),h!==S&&!this._lock){var O=M&&S&1,k=O===(M&&h&1);if(h<S&&(O=!O),a=O?0:c%u?u:c,this._lock=1,this.render(a||(b?0:qe(h*m)),n,!u)._lock=0,this._tTime=c,!n&&this.parent&&wt(this,"onRepeat"),this.vars.repeatRefresh&&!b&&(this.invalidate()._lock=1),a&&a!==this._time||w!==!this._ts||this.vars.onRepeat&&!this.parent&&!this._act)return this;if(u=this._dur,l=this._tDur,k&&(this._lock=2,a=O?u:-1e-4,this.render(a,!0),this.vars.repeatRefresh&&!b&&this.invalidate()),this._lock=0,!this._ts&&!w)return this;Ya(this,b)}}if(this._hasPause&&!this._forcing&&this._lock<2&&(T=du(this,qe(a),qe(_)),T&&(c-=_-(_=T._start))),this._tTime=c,this._time=_,this._act=!C,this._initted||(this._onUpdate=this.vars.onUpdate,this._initted=1,this._zTime=i,a=0),!a&&_&&!n&&!h&&(wt(this,"onStart"),this._tTime!==c))return this;if(_>=a&&i>=0)for(f=this._first;f;){if(p=f._next,(f._act||_>=f._start)&&f._ts&&T!==f){if(f.parent!==this)return this.render(i,n,o);if(f.render(f._ts>0?(_-f._start)*f._ts:(f._dirty?f.totalDuration():f._tDur)+(_-f._start)*f._ts,n,o),_!==this._time||!this._ts&&!w){T=0,p&&(c+=this._zTime=-se);break}}f=p}else{f=this._last;for(var P=i<0?i:_;f;){if(p=f._prev,(f._act||P<=f._end)&&f._ts&&T!==f){if(f.parent!==this)return this.render(i,n,o);if(f.render(f._ts>0?(P-f._start)*f._ts:(f._dirty?f.totalDuration():f._tDur)+(P-f._start)*f._ts,n,o||Ue&&(f._initted||f._startAt)),_!==this._time||!this._ts&&!w){T=0,p&&(c+=this._zTime=P?-se:se);break}}f=p}}if(T&&!n&&(this.pause(),T.render(_>=a?0:-se)._zTime=_>=a?1:-1,this._ts))return this._start=v,ls(this),this.render(i,n,o);this._onUpdate&&!n&&wt(this,"onUpdate",!0),(c===l&&this._tTime>=this.totalDuration()||!c&&a)&&(v===this._start||Math.abs(C)!==Math.abs(this._ts))&&(this._lock||((i||!u)&&(c===l&&this._ts>0||!c&&this._ts<0)&&Rr(this,1),!n&&!(i<0&&!a)&&(c||a||!l)&&(wt(this,c===l&&i>=0?"onComplete":"onReverseComplete",!0),this._prom&&!(c<l&&this.timeScale()>0)&&this._prom())))}return this},r.add=function(i,n){var o=this;if(hr(n)||(n=Mt(this,n,i)),!(i instanceof hn)){if(Ge(i))return i.forEach(function(a){return o.add(a,n)}),this;if(Ie(i))return this.addLabel(i,n);if(me(i))i=Me.delayedCall(0,i);else return this}return this!==i?Zt(this,i,n):this},r.getChildren=function(i,n,o,a){i===void 0&&(i=!0),n===void 0&&(n=!0),o===void 0&&(o=!0),a===void 0&&(a=-Lt);for(var l=[],u=this._first;u;)u._start>=a&&(u instanceof Me?n&&l.push(u):(o&&l.push(u),i&&l.push.apply(l,u.getChildren(!0,n,o)))),u=u._next;return l},r.getById=function(i){for(var n=this.getChildren(1,1,1),o=n.length;o--;)if(n[o].vars.id===i)return n[o]},r.remove=function(i){return Ie(i)?this.removeLabel(i):me(i)?this.killTweensOf(i):(as(this,i),i===this._recent&&(this._recent=this._last),Kr(this))},r.totalTime=function(i,n){return arguments.length?(this._forcing=1,!this._dp&&this._ts&&(this._start=qe(Tt.time-(this._ts>0?i/this._ts:(this.totalDuration()-i)/-this._ts))),s.prototype.totalTime.call(this,i,n),this._forcing=0,this):this._tTime},r.addLabel=function(i,n){return this.labels[i]=Mt(this,n),this},r.removeLabel=function(i){return delete this.labels[i],this},r.addPause=function(i,n,o){var a=Me.delayedCall(0,n||cn,o);return a.data="isPause",this._hasPause=1,Zt(this,a,Mt(this,i))},r.removePause=function(i){var n=this._first;for(i=Mt(this,i);n;)n._start===i&&n.data==="isPause"&&Rr(n),n=n._next},r.killTweensOf=function(i,n,o){for(var a=this.getTweensOf(i,o),l=a.length;l--;)Sr!==a[l]&&a[l].kill(i,n);return this},r.getTweensOf=function(i,n){for(var o=[],a=At(i),l=this._first,u=hr(n),c;l;)l instanceof Me?su(l._targets,a)&&(u?(!Sr||l._initted&&l._ts)&&l.globalTime(0)<=n&&l.globalTime(l.totalDuration())>n:!n||l.isActive())&&o.push(l):(c=l.getTweensOf(a,n)).length&&o.push.apply(o,c),l=l._next;return o},r.tweenTo=function(i,n){n=n||{};var o=this,a=Mt(o,i),l=n,u=l.startAt,c=l.onStart,d=l.onStartParams,_=l.immediateRender,f,p=Me.to(o,Et({ease:n.ease||"none",lazy:!1,immediateRender:!1,time:a,overwrite:"auto",duration:n.duration||Math.abs((a-(u&&"time"in u?u.time:o._time))/o.timeScale())||se,onStart:function(){if(o.pause(),!f){var m=n.duration||Math.abs((a-(u&&"time"in u?u.time:o._time))/o.timeScale());p._dur!==m&&Di(p,m,0,1).render(p._time,!0,!0),f=1}c&&c.apply(p,d||[])}},n));return _?p.render(0):p},r.tweenFromTo=function(i,n,o){return this.tweenTo(n,Et({startAt:{time:Mt(this,i)}},o))},r.recent=function(){return this._recent},r.nextLabel=function(i){return i===void 0&&(i=this._time),Ro(this,Mt(this,i))},r.previousLabel=function(i){return i===void 0&&(i=this._time),Ro(this,Mt(this,i),1)},r.currentLabel=function(i){return arguments.length?this.seek(i,!0):this.previousLabel(this._time+se)},r.shiftChildren=function(i,n,o){o===void 0&&(o=0);for(var a=this._first,l=this.labels,u;a;)a._start>=o&&(a._start+=i,a._end+=i),a=a._next;if(n)for(u in l)l[u]>=o&&(l[u]+=i);return Kr(this)},r.invalidate=function(i){var n=this._first;for(this._lock=0;n;)n.invalidate(i),n=n._next;return s.prototype.invalidate.call(this,i)},r.clear=function(i){i===void 0&&(i=!0);for(var n=this._first,o;n;)o=n._next,this.remove(n),n=o;return this._dp&&(this._time=this._tTime=this._pTime=0),i&&(this.labels={}),Kr(this)},r.totalDuration=function(i){var n=0,o=this,a=o._last,l=Lt,u,c,d;if(arguments.length)return o.timeScale((o._repeat<0?o.duration():o.totalDuration())/(o.reversed()?-i:i));if(o._dirty){for(d=o.parent;a;)u=a._prev,a._dirty&&a.totalDuration(),c=a._start,c>l&&o._sort&&a._ts&&!o._lock?(o._lock=1,Zt(o,a,c-a._delay,1)._lock=0):l=c,c<0&&a._ts&&(n-=c,(!d&&!o._dp||d&&d.smoothChildTiming)&&(o._start+=c/o._ts,o._time-=c,o._tTime-=c),o.shiftChildren(-c,!1,-1/0),l=0),a._end>n&&a._ts&&(n=a._end),a=u;Di(o,o===he&&o._time>n?o._time:n,1,1),o._dirty=0}return o._tDur},e.updateRoot=function(i){if(he._ts&&(wa(he,jn(i,he)),Ta=Tt.frame),Tt.frame>=Oo){Oo+=St.autoSleep||120;var n=he._first;if((!n||!n._ts)&&St.autoSleep&&Tt._listeners.length<2){for(;n&&!n._ts;)n=n._next;n||Tt.sleep()}}},e}(hn);Et(st.prototype,{_lock:0,_hasPause:0,_forcing:0});var Pu=function(e,r,t,i,n,o,a){var l=new dt(this._pt,e,r,0,1,Ka,null,n),u=0,c=0,d,_,f,p,h,m,w,T;for(l.b=t,l.e=i,t+="",i+="",(w=~i.indexOf("random("))&&(i=fn(i)),o&&(T=[t,i],o(T,e,r),t=T[0],i=T[1]),_=t.match(ds)||[];d=ds.exec(i);)p=d[0],h=i.substring(u,d.index),f?f=(f+1)%5:h.substr(-5)==="rgba("&&(f=1),p!==_[c++]&&(m=parseFloat(_[c-1])||0,l._pt={_next:l._pt,p:h||c===1?h:",",s:m,c:p.charAt(1)==="="?xi(m,p)-m:parseFloat(p)-m,m:f&&f<4?Math.round:0},u=ds.lastIndex);return l.c=u<i.length?i.substring(u,i.length):"",l.fp=a,(ga.test(i)||w)&&(l.e=0),this._pt=l,l},uo=function(e,r,t,i,n,o,a,l,u,c){me(i)&&(i=i(n||0,e,o));var d=e[r],_=t!=="get"?t:me(d)?u?e[r.indexOf("set")||!me(e["get"+r.substr(3)])?r:"get"+r.substr(3)](u):e[r]():d,f=me(d)?u?Ru:Ga:fo,p;if(Ie(i)&&(~i.indexOf("random(")&&(i=fn(i)),i.charAt(1)==="="&&(p=xi(_,i)+(Qe(_)||0),(p||p===0)&&(i=p))),!c||_!==i||zs)return!isNaN(_*i)&&i!==""?(p=new dt(this._pt,e,r,+_||0,i-(_||0),typeof d=="boolean"?qu:ja,0,f),u&&(p.fp=u),a&&p.modifier(a,this,e),this._pt=p):(!d&&!(r in e)&&so(r,i),Pu.call(this,e,r,_,i,f,l||St.stringFilter,u))},Ou=function(e,r,t,i,n){if(me(e)&&(e=Zi(e,n,r,t,i)),!ir(e)||e.style&&e.nodeType||Ge(e)||_a(e))return Ie(e)?Zi(e,n,r,t,i):e;var o={},a;for(a in e)o[a]=Zi(e[a],n,r,t,i);return o},Wa=function(e,r,t,i,n,o){var a,l,u,c;if(bt[e]&&(a=new bt[e]).init(n,a.rawVars?r[e]:Ou(r[e],i,n,o,t),t,i,o)!==!1&&(t._pt=l=new dt(t._pt,n,e,0,1,a.render,a,0,a.priority),t!==bi))for(u=t._ptLookup[t._targets.indexOf(n)],c=a._props.length;c--;)u[a._props[c]]=l;return a},Sr,zs,co=function s(e,r,t){var i=e.vars,n=i.ease,o=i.startAt,a=i.immediateRender,l=i.lazy,u=i.onUpdate,c=i.runBackwards,d=i.yoyoEase,_=i.keyframes,f=i.autoRevert,p=e._dur,h=e._startAt,m=e._targets,w=e.parent,T=w&&w.data==="nested"?w.vars.targets:m,C=e._overwrite==="auto"&&!to,v=e.timeline,S,M,b,O,k,P,z,D,W,Y,Z,Q,q;if(v&&(!_||!n)&&(n="none"),e._ease=Zr(n,Pi.ease),e._yEase=d?Na(Zr(d===!0?n:d,Pi.ease)):0,d&&e._yoyo&&!e._repeat&&(d=e._yEase,e._yEase=e._ease,e._ease=d),e._from=!v&&!!i.runBackwards,!v||_&&!i.stagger){if(D=m[0]?jr(m[0]).harness:0,Q=D&&i[D.prop],S=Gn(i,oo),h&&(h._zTime<0&&h.progress(1),r<0&&c&&a&&!f?h.render(-1,!0):h.revert(c&&p?qn:iu),h._lazy=0),o){if(Rr(e._startAt=Me.set(m,Et({data:"isStart",overwrite:!1,parent:w,immediateRender:!0,lazy:!h&&ct(l),startAt:null,delay:0,onUpdate:u&&function(){return wt(e,"onUpdate")},stagger:0},o))),e._startAt._dp=0,e._startAt._sat=e,r<0&&(Ue||!a&&!f)&&e._startAt.revert(qn),a&&p&&r<=0&&t<=0){r&&(e._zTime=r);return}}else if(c&&p&&!h){if(r&&(a=!1),b=Et({overwrite:!1,data:"isFromStart",lazy:a&&!h&&ct(l),immediateRender:a,stagger:0,parent:w},S),Q&&(b[D.prop]=Q),Rr(e._startAt=Me.set(m,b)),e._startAt._dp=0,e._startAt._sat=e,r<0&&(Ue?e._startAt.revert(qn):e._startAt.render(-1,!0)),e._zTime=r,!a)s(e._startAt,se,se);else if(!r)return}for(e._pt=e._ptCache=0,l=p&&ct(l)||l&&!p,M=0;M<m.length;M++){if(k=m[M],z=k._gsap||lo(m)[M]._gsap,e._ptLookup[M]=Y={},As[z.id]&&Dr.length&&Un(),Z=T===m?M:T.indexOf(k),D&&(W=new D).init(k,Q||S,e,Z,T)!==!1&&(e._pt=O=new dt(e._pt,k,W.name,0,1,W.render,W,0,W.priority),W._props.forEach(function(J){Y[J]=O}),W.priority&&(P=1)),!D||Q)for(b in S)bt[b]&&(W=Wa(b,S,e,Z,k,T))?W.priority&&(P=1):Y[b]=O=uo.call(e,k,b,"get",S[b],Z,T,0,i.stringFilter);e._op&&e._op[M]&&e.kill(k,e._op[M]),C&&e._pt&&(Sr=e,he.killTweensOf(k,Y,e.globalTime(r)),q=!e.parent,Sr=0),e._pt&&l&&(As[z.id]=1)}P&&Za(e),e._onInit&&e._onInit(e)}e._onUpdate=u,e._initted=(!e._op||e._pt)&&!q,_&&r<=0&&v.render(Lt,!0,!0)},Du=function(e,r,t,i,n,o,a,l){var u=(e._pt&&e._ptCache||(e._ptCache={}))[r],c,d,_,f;if(!u)for(u=e._ptCache[r]=[],_=e._ptLookup,f=e._targets.length;f--;){if(c=_[f][r],c&&c.d&&c.d._pt)for(c=c.d._pt;c&&c.p!==r&&c.fp!==r;)c=c._next;if(!c)return zs=1,e.vars[r]="+=0",co(e,a),zs=0,l?un(r+" not eligible for reset"):1;u.push(c)}for(f=u.length;f--;)d=u[f],c=d._pt||d,c.s=(i||i===0)&&!n?i:c.s+(i||0)+o*c.c,c.c=t-c.s,d.e&&(d.e=Te(t)+Qe(d.e)),d.b&&(d.b=c.s+Qe(d.b))},Lu=function(e,r){var t=e[0]?jr(e[0]).harness:0,i=t&&t.aliases,n,o,a,l;if(!i)return r;n=ii({},r);for(o in i)if(o in n)for(l=i[o].split(","),a=l.length;a--;)n[l[a]]=n[o];return n},Au=function(e,r,t,i){var n=r.ease||i||"power1.inOut",o,a;if(Ge(r))a=t[e]||(t[e]=[]),r.forEach(function(l,u){return a.push({t:u/(r.length-1)*100,v:l,e:n})});else for(o in r)a=t[o]||(t[o]=[]),o==="ease"||a.push({t:parseFloat(e),v:r[o],e:n})},Zi=function(e,r,t,i,n){return me(e)?e.call(r,t,i,n):Ie(e)&&~e.indexOf("random(")?fn(e):e},Qa=ao+"repeat,repeatDelay,yoyo,repeatRefresh,yoyoEase,autoRevert",Ua={};ft(Qa+",id,stagger,delay,duration,paused,scrollTrigger",function(s){return Ua[s]=1});var Me=function(s){da(e,s);function e(t,i,n,o){var a;typeof i=="number"&&(n.duration=i,i=n,n=null),a=s.call(this,o?i:ji(i))||this;var l=a.vars,u=l.duration,c=l.delay,d=l.immediateRender,_=l.stagger,f=l.overwrite,p=l.keyframes,h=l.defaults,m=l.scrollTrigger,w=l.yoyoEase,T=i.parent||he,C=(Ge(t)||_a(t)?hr(t[0]):"length"in i)?[t]:At(t),v,S,M,b,O,k,P,z;if(a._targets=C.length?lo(C):un("GSAP target "+t+" not found. https://gsap.com",!St.nullTargetWarn)||[],a._ptLookup=[],a._overwrite=f,p||_||xn(u)||xn(c)){if(i=a.vars,v=a.timeline=new st({data:"nested",defaults:h||{},targets:T&&T.data==="nested"?T.vars.targets:C}),v.kill(),v.parent=v._dp=ar(a),v._start=0,_||xn(u)||xn(c)){if(b=C.length,P=_&&Aa(_),ir(_))for(O in _)~Qa.indexOf(O)&&(z||(z={}),z[O]=_[O]);for(S=0;S<b;S++)M=Gn(i,Ua),M.stagger=0,w&&(M.yoyoEase=w),z&&ii(M,z),k=C[S],M.duration=+Zi(u,ar(a),S,k,C),M.delay=(+Zi(c,ar(a),S,k,C)||0)-a._delay,!_&&b===1&&M.delay&&(a._delay=c=M.delay,a._start+=c,M.delay=0),v.to(k,M,P?P(S,k,C):0),v._ease=K.none;v.duration()?u=c=0:a.timeline=0}else if(p){ji(Et(v.vars.defaults,{ease:"none"})),v._ease=Zr(p.ease||i.ease||"none");var D=0,W,Y,Z;if(Ge(p))p.forEach(function(Q){return v.to(C,Q,">")}),v.duration();else{M={};for(O in p)O==="ease"||O==="easeEach"||Au(O,p[O],M,p.easeEach);for(O in M)for(W=M[O].sort(function(Q,q){return Q.t-q.t}),D=0,S=0;S<W.length;S++)Y=W[S],Z={ease:Y.e,duration:(Y.t-(S?W[S-1].t:0))/100*u},Z[O]=Y.v,v.to(C,Z,D),D+=Z.duration;v.duration()<u&&v.to({},{duration:u-v.duration()})}}u||a.duration(u=v.duration())}else a.timeline=0;return f===!0&&!to&&(Sr=ar(a),he.killTweensOf(C),Sr=0),Zt(T,ar(a),n),i.reversed&&a.reverse(),i.paused&&a.paused(!0),(d||!u&&!p&&a._start===qe(T._time)&&ct(d)&&uu(ar(a))&&T.data!=="nested")&&(a._tTime=-se,a.render(Math.max(0,-c)||0)),m&&Pa(ar(a),m),a}var r=e.prototype;return r.render=function(i,n,o){var a=this._time,l=this._tDur,u=this._dur,c=i<0,d=i>l-se&&!c?l:i<se?0:i,_,f,p,h,m,w,T,C,v;if(!u)fu(this,i,n,o);else if(d!==this._tTime||!i||o||!this._initted&&this._tTime||this._startAt&&this._zTime<0!==c){if(_=d,C=this.timeline,this._repeat){if(h=u+this._rDelay,this._repeat<-1&&c)return this.totalTime(h*100+i,n,o);if(_=qe(d%h),d===l?(p=this._repeat,_=u):(p=~~(d/h),p&&p===qe(d/h)&&(_=u,p--),_>u&&(_=u)),w=this._yoyo&&p&1,w&&(v=this._yEase,_=u-_),m=Oi(this._tTime,h),_===a&&!o&&this._initted&&p===m)return this._tTime=d,this;p!==m&&(C&&this._yEase&&Ya(C,w),this.vars.repeatRefresh&&!w&&!this._lock&&this._time!==h&&this._initted&&(this._lock=o=1,this.render(qe(h*p),!0).invalidate()._lock=0))}if(!this._initted){if(Oa(this,c?i:_,o,n,d))return this._tTime=0,this;if(a!==this._time&&!(o&&this.vars.repeatRefresh&&p!==m))return this;if(u!==this._dur)return this.render(i,n,o)}if(this._tTime=d,this._time=_,!this._act&&this._ts&&(this._act=1,this._lazy=0),this.ratio=T=(v||this._ease)(_/u),this._from&&(this.ratio=T=1-T),_&&!a&&!n&&!p&&(wt(this,"onStart"),this._tTime!==d))return this;for(f=this._pt;f;)f.r(T,f.d),f=f._next;C&&C.render(i<0?i:C._dur*C._ease(_/this._dur),n,o)||this._startAt&&(this._zTime=i),this._onUpdate&&!n&&(c&&Rs(this,i,n,o),wt(this,"onUpdate")),this._repeat&&p!==m&&this.vars.onRepeat&&!n&&this.parent&&wt(this,"onRepeat"),(d===this._tDur||!d)&&this._tTime===d&&(c&&!this._onUpdate&&Rs(this,i,!0,!0),(i||!u)&&(d===this._tDur&&this._ts>0||!d&&this._ts<0)&&Rr(this,1),!n&&!(c&&!a)&&(d||a||w)&&(wt(this,d===l?"onComplete":"onReverseComplete",!0),this._prom&&!(d<l&&this.timeScale()>0)&&this._prom()))}return this},r.targets=function(){return this._targets},r.invalidate=function(i){return(!i||!this.vars.runBackwards)&&(this._startAt=0),this._pt=this._op=this._onUpdate=this._lazy=this.ratio=0,this._ptLookup=[],this.timeline&&this.timeline.invalidate(i),s.prototype.invalidate.call(this,i)},r.resetTo=function(i,n,o,a,l){dn||Tt.wake(),this._ts||this.play();var u=Math.min(this._dur,(this._dp._time-this._start)*this._ts),c;return this._initted||co(this,u),c=this._ease(u/this._dur),Du(this,i,n,o,a,c,u,l)?this.resetTo(i,n,o,a,1):(us(this,0),this.parent||ka(this._dp,this,"_first","_last",this._dp._sort?"_start":0),this.render(0))},r.kill=function(i,n){if(n===void 0&&(n="all"),!i&&(!n||n==="all"))return this._lazy=this._pt=0,this.parent?Ni(this):this;if(this.timeline){var o=this.timeline.totalDuration();return this.timeline.killTweensOf(i,n,Sr&&Sr.vars.overwrite!==!0)._first||Ni(this),this.parent&&o!==this.timeline.totalDuration()&&Di(this,this._dur*this.timeline._tDur/o,0,1),this}var a=this._targets,l=i?At(i):a,u=this._ptLookup,c=this._pt,d,_,f,p,h,m,w;if((!n||n==="all")&&au(a,l))return n==="all"&&(this._pt=0),Ni(this);for(d=this._op=this._op||[],n!=="all"&&(Ie(n)&&(h={},ft(n,function(T){return h[T]=1}),n=h),n=Lu(a,n)),w=a.length;w--;)if(~l.indexOf(a[w])){_=u[w],n==="all"?(d[w]=n,p=_,f={}):(f=d[w]=d[w]||{},p=n);for(h in p)m=_&&_[h],m&&((!("kill"in m.d)||m.d.kill(h)===!0)&&as(this,m,"_pt"),delete _[h]),f!=="all"&&(f[h]=1)}return this._initted&&!this._pt&&c&&Ni(this),this},e.to=function(i,n){return new e(i,n,arguments[2])},e.from=function(i,n){return Ki(1,arguments)},e.delayedCall=function(i,n,o,a){return new e(n,0,{immediateRender:!1,lazy:!1,overwrite:!1,delay:i,onComplete:n,onReverseComplete:n,onCompleteParams:o,onReverseCompleteParams:o,callbackScope:a})},e.fromTo=function(i,n,o){return Ki(2,arguments)},e.set=function(i,n){return n.duration=0,n.repeatDelay||(n.repeat=0),new e(i,n)},e.killTweensOf=function(i,n,o){return he.killTweensOf(i,n,o)},e}(hn);Et(Me.prototype,{_targets:[],_lazy:0,_startAt:0,_op:0,_onInit:0});ft("staggerTo,staggerFrom,staggerFromTo",function(s){Me[s]=function(){var e=new st,r=qs.call(arguments,0);return r.splice(s==="staggerFromTo"?5:4,0,0),e[s].apply(e,r)}});var fo=function(e,r,t){return e[r]=t},Ga=function(e,r,t){return e[r](t)},Ru=function(e,r,t,i){return e[r](i.fp,t)},Eu=function(e,r,t){return e.setAttribute(r,t)},ho=function(e,r){return me(e[r])?Ga:ro(e[r])&&e.setAttribute?Eu:fo},ja=function(e,r){return r.set(r.t,r.p,Math.round((r.s+r.c*e)*1e6)/1e6,r)},qu=function(e,r){return r.set(r.t,r.p,!!(r.s+r.c*e),r)},Ka=function(e,r){var t=r._pt,i="";if(!e&&r.b)i=r.b;else if(e===1&&r.e)i=r.e;else{for(;t;)i=t.p+(t.m?t.m(t.s+t.c*e):Math.round((t.s+t.c*e)*1e4)/1e4)+i,t=t._next;i+=r.c}r.set(r.t,r.p,i,r)},_o=function(e,r){for(var t=r._pt;t;)t.r(e,t.d),t=t._next},Iu=function(e,r,t,i){for(var n=this._pt,o;n;)o=n._next,n.p===i&&n.modifier(e,r,t),n=o},Fu=function(e){for(var r=this._pt,t,i;r;)i=r._next,r.p===e&&!r.op||r.op===e?as(this,r,"_pt"):r.dep||(t=1),r=i;return!t},zu=function(e,r,t,i){i.mSet(e,r,i.m.call(i.tween,t,i.mt),i)},Za=function(e){for(var r=e._pt,t,i,n,o;r;){for(t=r._next,i=n;i&&i.pr>r.pr;)i=i._next;(r._prev=i?i._prev:o)?r._prev._next=r:n=r,(r._next=i)?i._prev=r:o=r,r=t}e._pt=n},dt=function(){function s(r,t,i,n,o,a,l,u,c){this.t=t,this.s=n,this.c=o,this.p=i,this.r=a||ja,this.d=l||this,this.set=u||fo,this.pr=c||0,this._next=r,r&&(r._prev=this)}var e=s.prototype;return e.modifier=function(t,i,n){this.mSet=this.mSet||this.set,this.set=zu,this.m=t,this.mt=n,this.tween=i},s}();ft(ao+"parent,duration,ease,delay,overwrite,runBackwards,startAt,yoyo,immediateRender,repeat,repeatDelay,data,paused,reversed,lazy,callbackScope,stringFilter,id,yoyoEase,stagger,inherit,repeatRefresh,keyframes,autoRevert,scrollTrigger",function(s){return oo[s]=1});Ct.TweenMax=Ct.TweenLite=Me;Ct.TimelineLite=Ct.TimelineMax=st;he=new st({sortChildren:!1,defaults:Pi,autoRemoveChildren:!0,id:"root",smoothChildTiming:!0});St.stringFilter=Ha;var Jr=[],Fn={},$u=[],qo=0,Bu=0,ms=function(e){return(Fn[e]||$u).map(function(r){return r()})},$s=function(){var e=Date.now(),r=[];e-qo>2&&(ms("matchMediaInit"),Jr.forEach(function(t){var i=t.queries,n=t.conditions,o,a,l,u;for(a in i)o=Gt.matchMedia(i[a]).matches,o&&(l=1),o!==n[a]&&(n[a]=o,u=1);u&&(t.revert(),l&&r.push(t))}),ms("matchMediaRevert"),r.forEach(function(t){return t.onMatch(t,function(i){return t.add(null,i)})}),qo=e,ms("matchMedia"))},Ja=function(){function s(r,t){this.selector=t&&Is(t),this.data=[],this._r=[],this.isReverted=!1,this.id=Bu++,r&&this.add(r)}var e=s.prototype;return e.add=function(t,i,n){me(t)&&(n=i,i=t,t=me);var o=this,a=function(){var u=ce,c=o.selector,d;return u&&u!==o&&u.data.push(o),n&&(o.selector=Is(n)),ce=o,d=i.apply(o,arguments),me(d)&&o._r.push(d),ce=u,o.selector=c,o.isReverted=!1,d};return o.last=a,t===me?a(o,function(l){return o.add(null,l)}):t?o[t]=a:a},e.ignore=function(t){var i=ce;ce=null,t(this),ce=i},e.getTweens=function(){var t=[];return this.data.forEach(function(i){return i instanceof s?t.push.apply(t,i.getTweens()):i instanceof Me&&!(i.parent&&i.parent.data==="nested")&&t.push(i)}),t},e.clear=function(){this._r.length=this.data.length=0},e.kill=function(t,i){var n=this;if(t?function(){for(var a=n.getTweens(),l=n.data.length,u;l--;)u=n.data[l],u.data==="isFlip"&&(u.revert(),u.getChildren(!0,!0,!1).forEach(function(c){return a.splice(a.indexOf(c),1)}));for(a.map(function(c){return{g:c._dur||c._delay||c._sat&&!c._sat.vars.immediateRender?c.globalTime(0):-1/0,t:c}}).sort(function(c,d){return d.g-c.g||-1/0}).forEach(function(c){return c.t.revert(t)}),l=n.data.length;l--;)u=n.data[l],u instanceof st?u.data!=="nested"&&(u.scrollTrigger&&u.scrollTrigger.revert(),u.kill()):!(u instanceof Me)&&u.revert&&u.revert(t);n._r.forEach(function(c){return c(t,n)}),n.isReverted=!0}():this.data.forEach(function(a){return a.kill&&a.kill()}),this.clear(),i)for(var o=Jr.length;o--;)Jr[o].id===this.id&&Jr.splice(o,1)},e.revert=function(t){this.kill(t||{})},s}(),Hu=function(){function s(r){this.contexts=[],this.scope=r,ce&&ce.data.push(this)}var e=s.prototype;return e.add=function(t,i,n){ir(t)||(t={matches:t});var o=new Ja(0,n||this.scope),a=o.conditions={},l,u,c;ce&&!o.selector&&(o.selector=ce.selector),this.contexts.push(o),i=o.add("onMatch",i),o.queries=t;for(u in t)u==="all"?c=1:(l=Gt.matchMedia(t[u]),l&&(Jr.indexOf(o)<0&&Jr.push(o),(a[u]=l.matches)&&(c=1),l.addListener?l.addListener($s):l.addEventListener("change",$s)));return c&&i(o,function(d){return o.add(null,d)}),this},e.revert=function(t){this.kill(t||{})},e.kill=function(t){this.contexts.forEach(function(i){return i.kill(t,!0)})},s}(),Kn={registerPlugin:function(){for(var e=arguments.length,r=new Array(e),t=0;t<e;t++)r[t]=arguments[t];r.forEach(function(i){return za(i)})},timeline:function(e){return new st(e)},getTweensOf:function(e,r){return he.getTweensOf(e,r)},getProperty:function(e,r,t,i){Ie(e)&&(e=At(e)[0]);var n=jr(e||{}).get,o=t?Ca:Sa;return t==="native"&&(t=""),e&&(r?o((bt[r]&&bt[r].get||n)(e,r,t,i)):function(a,l,u){return o((bt[a]&&bt[a].get||n)(e,a,l,u))})},quickSetter:function(e,r,t){if(e=At(e),e.length>1){var i=e.map(function(c){return _t.quickSetter(c,r,t)}),n=i.length;return function(c){for(var d=n;d--;)i[d](c)}}e=e[0]||{};var o=bt[r],a=jr(e),l=a.harness&&(a.harness.aliases||{})[r]||r,u=o?function(c){var d=new o;bi._pt=0,d.init(e,t?c+t:c,bi,0,[e]),d.render(1,d),bi._pt&&_o(1,bi)}:a.set(e,l);return o?u:function(c){return u(e,l,t?c+t:c,a,1)}},quickTo:function(e,r,t){var i,n=_t.to(e,ii((i={},i[r]="+=0.1",i.paused=!0,i),t||{})),o=function(l,u,c){return n.resetTo(r,l,u,c)};return o.tween=n,o},isTweening:function(e){return he.getTweensOf(e,!0).length>0},defaults:function(e){return e&&e.ease&&(e.ease=Zr(e.ease,Pi.ease)),Do(Pi,e||{})},config:function(e){return Do(St,e||{})},registerEffect:function(e){var r=e.name,t=e.effect,i=e.plugins,n=e.defaults,o=e.extendTimeline;(i||"").split(",").forEach(function(a){return a&&!bt[a]&&!Ct[a]&&un(r+" effect requires "+a+" plugin.")}),hs[r]=function(a,l,u){return t(At(a),Et(l||{},n),u)},o&&(st.prototype[r]=function(a,l,u){return this.add(hs[r](a,ir(l)?l:(u=l)&&{},this),u)})},registerEase:function(e,r){K[e]=Zr(r)},parseEase:function(e,r){return arguments.length?Zr(e,r):K},getById:function(e){return he.getById(e)},exportRoot:function(e,r){e===void 0&&(e={});var t=new st(e),i,n;for(t.smoothChildTiming=ct(e.smoothChildTiming),he.remove(t),t._dp=0,t._time=t._tTime=he._time,i=he._first;i;)n=i._next,(r||!(!i._dur&&i instanceof Me&&i.vars.onComplete===i._targets[0]))&&Zt(t,i,i._start-i._delay),i=n;return Zt(he,t,0),t},context:function(e,r){return e?new Ja(e,r):ce},matchMedia:function(e){return new Hu(e)},matchMediaRefresh:function(){return Jr.forEach(function(e){var r=e.conditions,t,i;for(i in r)r[i]&&(r[i]=!1,t=1);t&&e.revert()})||$s()},addEventListener:function(e,r){var t=Fn[e]||(Fn[e]=[]);~t.indexOf(r)||t.push(r)},removeEventListener:function(e,r){var t=Fn[e],i=t&&t.indexOf(r);i>=0&&t.splice(i,1)},utils:{wrap:vu,wrapYoyo:bu,distribute:Aa,random:Ea,snap:Ra,normalize:yu,getUnit:Qe,clamp:_u,splitColor:$a,toArray:At,selector:Is,mapRange:Ia,pipe:gu,unitize:mu,interpolate:Tu,shuffle:La},install:va,effects:hs,ticker:Tt,updateRoot:st.updateRoot,plugins:bt,globalTimeline:he,core:{PropTween:dt,globals:ba,Tween:Me,Timeline:st,Animation:hn,getCache:jr,_removeLinkedListItem:as,reverting:function(){return Ue},context:function(e){return e&&ce&&(ce.data.push(e),e._ctx=ce),ce},suppressOverwrites:function(e){return to=e}}};ft("to,from,fromTo,delayedCall,set,killTweensOf",function(s){return Kn[s]=Me[s]});Tt.add(st.updateRoot);bi=Kn.to({},{duration:0});var Nu=function(e,r){for(var t=e._pt;t&&t.p!==r&&t.op!==r&&t.fp!==r;)t=t._next;return t},Yu=function(e,r){var t=e._targets,i,n,o;for(i in r)for(n=t.length;n--;)o=e._ptLookup[n][i],o&&(o=o.d)&&(o._pt&&(o=Nu(o,i)),o&&o.modifier&&o.modifier(r[i],e,t[n],i))},ys=function(e,r){return{name:e,rawVars:1,init:function(i,n,o){o._onInit=function(a){var l,u;if(Ie(n)&&(l={},ft(n,function(c){return l[c]=1}),n=l),r){l={};for(u in n)l[u]=r(n[u]);n=l}Yu(a,n)}}}},_t=Kn.registerPlugin({name:"attr",init:function(e,r,t,i,n){var o,a,l;this.tween=t;for(o in r)l=e.getAttribute(o)||"",a=this.add(e,"setAttribute",(l||0)+"",r[o],i,n,0,0,o),a.op=o,a.b=l,this._props.push(o)},render:function(e,r){for(var t=r._pt;t;)Ue?t.set(t.t,t.p,t.b,t):t.r(e,t.d),t=t._next}},{name:"endArray",init:function(e,r){for(var t=r.length;t--;)this.add(e,t,e[t]||0,r[t],0,0,0,0,0,1)}},ys("roundProps",Fs),ys("modifiers"),ys("snap",Ra))||Kn;Me.version=st.version=_t.version="3.12.5";ya=1;io()&&Li();K.Power0;K.Power1;K.Power2;K.Power3;K.Power4;K.Linear;K.Quad;K.Cubic;K.Quart;K.Quint;K.Strong;K.Elastic;K.Back;K.SteppedEase;K.Bounce;K.Sine;K.Expo;K.Circ;/*!
 * CSSPlugin 3.12.5
 * https://gsap.com
 *
 * Copyright 2008-2024, GreenSock. All rights reserved.
 * Subject to the terms at https://gsap.com/standard-license or for
 * Club GSAP members, the agreement issued with that membership.
 * @author: Jack Doyle, <EMAIL>
*/var Io,Cr,wi,po,Ur,Fo,go,Xu=function(){return typeof window!="undefined"},_r={},Vr=180/Math.PI,Si=Math.PI/180,hi=Math.atan2,zo=1e8,mo=/([A-Z])/g,Vu=/(left|right|width|margin|padding|x)/i,Wu=/[\s,\(]\S/,Jt={autoAlpha:"opacity,visibility",scale:"scaleX,scaleY",alpha:"opacity"},Bs=function(e,r){return r.set(r.t,r.p,Math.round((r.s+r.c*e)*1e4)/1e4+r.u,r)},Qu=function(e,r){return r.set(r.t,r.p,e===1?r.e:Math.round((r.s+r.c*e)*1e4)/1e4+r.u,r)},Uu=function(e,r){return r.set(r.t,r.p,e?Math.round((r.s+r.c*e)*1e4)/1e4+r.u:r.b,r)},Gu=function(e,r){var t=r.s+r.c*e;r.set(r.t,r.p,~~(t+(t<0?-.5:.5))+r.u,r)},el=function(e,r){return r.set(r.t,r.p,e?r.e:r.b,r)},tl=function(e,r){return r.set(r.t,r.p,e!==1?r.b:r.e,r)},ju=function(e,r,t){return e.style[r]=t},Ku=function(e,r,t){return e.style.setProperty(r,t)},Zu=function(e,r,t){return e._gsap[r]=t},Ju=function(e,r,t){return e._gsap.scaleX=e._gsap.scaleY=t},ec=function(e,r,t,i,n){var o=e._gsap;o.scaleX=o.scaleY=t,o.renderTransform(n,o)},tc=function(e,r,t,i,n){var o=e._gsap;o[r]=t,o.renderTransform(n,o)},_e="transform",ht=_e+"Origin",rc=function s(e,r){var t=this,i=this.target,n=i.style,o=i._gsap;if(e in _r&&n){if(this.tfm=this.tfm||{},e!=="transform")e=Jt[e]||e,~e.indexOf(",")?e.split(",").forEach(function(a){return t.tfm[a]=lr(i,a)}):this.tfm[e]=o.x?o[e]:lr(i,e),e===ht&&(this.tfm.zOrigin=o.zOrigin);else return Jt.transform.split(",").forEach(function(a){return s.call(t,a,r)});if(this.props.indexOf(_e)>=0)return;o.svg&&(this.svgo=i.getAttribute("data-svg-origin"),this.props.push(ht,r,"")),e=_e}(n||r)&&this.props.push(e,r,n[e])},rl=function(e){e.translate&&(e.removeProperty("translate"),e.removeProperty("scale"),e.removeProperty("rotate"))},ic=function(){var e=this.props,r=this.target,t=r.style,i=r._gsap,n,o;for(n=0;n<e.length;n+=3)e[n+1]?r[e[n]]=e[n+2]:e[n+2]?t[e[n]]=e[n+2]:t.removeProperty(e[n].substr(0,2)==="--"?e[n]:e[n].replace(mo,"-$1").toLowerCase());if(this.tfm){for(o in this.tfm)i[o]=this.tfm[o];i.svg&&(i.renderTransform(),r.setAttribute("data-svg-origin",this.svgo||"")),n=go(),(!n||!n.isStart)&&!t[_e]&&(rl(t),i.zOrigin&&t[ht]&&(t[ht]+=" "+i.zOrigin+"px",i.zOrigin=0,i.renderTransform()),i.uncache=1)}},il=function(e,r){var t={target:e,props:[],revert:ic,save:rc};return e._gsap||_t.core.getCache(e),r&&r.split(",").forEach(function(i){return t.save(i)}),t},nl,Hs=function(e,r){var t=Cr.createElementNS?Cr.createElementNS((r||"http://www.w3.org/1999/xhtml").replace(/^https/,"http"),e):Cr.createElement(e);return t&&t.style?t:Cr.createElement(e)},tr=function s(e,r,t){var i=getComputedStyle(e);return i[r]||i.getPropertyValue(r.replace(mo,"-$1").toLowerCase())||i.getPropertyValue(r)||!t&&s(e,Ai(r)||r,1)||""},$o="O,Moz,ms,Ms,Webkit".split(","),Ai=function(e,r,t){var i=r||Ur,n=i.style,o=5;if(e in n&&!t)return e;for(e=e.charAt(0).toUpperCase()+e.substr(1);o--&&!($o[o]+e in n););return o<0?null:(o===3?"ms":o>=0?$o[o]:"")+e},Ns=function(){Xu()&&window.document&&(Io=window,Cr=Io.document,wi=Cr.documentElement,Ur=Hs("div")||{style:{}},Hs("div"),_e=Ai(_e),ht=_e+"Origin",Ur.style.cssText="border-width:0;line-height:0;position:absolute;padding:0",nl=!!Ai("perspective"),go=_t.core.reverting,po=1)},vs=function s(e){var r=Hs("svg",this.ownerSVGElement&&this.ownerSVGElement.getAttribute("xmlns")||"http://www.w3.org/2000/svg"),t=this.parentNode,i=this.nextSibling,n=this.style.cssText,o;if(wi.appendChild(r),r.appendChild(this),this.style.display="block",e)try{o=this.getBBox(),this._gsapBBox=this.getBBox,this.getBBox=s}catch{}else this._gsapBBox&&(o=this._gsapBBox());return t&&(i?t.insertBefore(this,i):t.appendChild(this)),wi.removeChild(r),this.style.cssText=n,o},Bo=function(e,r){for(var t=r.length;t--;)if(e.hasAttribute(r[t]))return e.getAttribute(r[t])},sl=function(e){var r;try{r=e.getBBox()}catch{r=vs.call(e,!0)}return r&&(r.width||r.height)||e.getBBox===vs||(r=vs.call(e,!0)),r&&!r.width&&!r.x&&!r.y?{x:+Bo(e,["x","cx","x1"])||0,y:+Bo(e,["y","cy","y1"])||0,width:0,height:0}:r},ol=function(e){return!!(e.getCTM&&(!e.parentNode||e.ownerSVGElement)&&sl(e))},ni=function(e,r){if(r){var t=e.style,i;r in _r&&r!==ht&&(r=_e),t.removeProperty?(i=r.substr(0,2),(i==="ms"||r.substr(0,6)==="webkit")&&(r="-"+r),t.removeProperty(i==="--"?r:r.replace(mo,"-$1").toLowerCase())):t.removeAttribute(r)}},kr=function(e,r,t,i,n,o){var a=new dt(e._pt,r,t,0,1,o?tl:el);return e._pt=a,a.b=i,a.e=n,e._props.push(t),a},Ho={deg:1,rad:1,turn:1},nc={grid:1,flex:1},Er=function s(e,r,t,i){var n=parseFloat(t)||0,o=(t+"").trim().substr((n+"").length)||"px",a=Ur.style,l=Vu.test(r),u=e.tagName.toLowerCase()==="svg",c=(u?"client":"offset")+(l?"Width":"Height"),d=100,_=i==="px",f=i==="%",p,h,m,w;if(i===o||!n||Ho[i]||Ho[o])return n;if(o!=="px"&&!_&&(n=s(e,r,t,"px")),w=e.getCTM&&ol(e),(f||o==="%")&&(_r[r]||~r.indexOf("adius")))return p=w?e.getBBox()[l?"width":"height"]:e[c],Te(f?n/p*d:n/100*p);if(a[l?"width":"height"]=d+(_?o:i),h=~r.indexOf("adius")||i==="em"&&e.appendChild&&!u?e:e.parentNode,w&&(h=(e.ownerSVGElement||{}).parentNode),(!h||h===Cr||!h.appendChild)&&(h=Cr.body),m=h._gsap,m&&f&&m.width&&l&&m.time===Tt.time&&!m.uncache)return Te(n/m.width*d);if(f&&(r==="height"||r==="width")){var T=e.style[r];e.style[r]=d+i,p=e[c],T?e.style[r]=T:ni(e,r)}else(f||o==="%")&&!nc[tr(h,"display")]&&(a.position=tr(e,"position")),h===e&&(a.position="static"),h.appendChild(Ur),p=Ur[c],h.removeChild(Ur),a.position="absolute";return l&&f&&(m=jr(h),m.time=Tt.time,m.width=h[c]),Te(_?p*n/d:p&&n?d/p*n:0)},lr=function(e,r,t,i){var n;return po||Ns(),r in Jt&&r!=="transform"&&(r=Jt[r],~r.indexOf(",")&&(r=r.split(",")[0])),_r[r]&&r!=="transform"?(n=pn(e,i),n=r!=="transformOrigin"?n[r]:n.svg?n.origin:Jn(tr(e,ht))+" "+n.zOrigin+"px"):(n=e.style[r],(!n||n==="auto"||i||~(n+"").indexOf("calc("))&&(n=Zn[r]&&Zn[r](e,r,t)||tr(e,r)||xa(e,r)||(r==="opacity"?1:0))),t&&!~(n+"").trim().indexOf(" ")?Er(e,r,n,t)+t:n},sc=function(e,r,t,i){if(!t||t==="none"){var n=Ai(r,e,1),o=n&&tr(e,n,1);o&&o!==t?(r=n,t=o):r==="borderColor"&&(t=tr(e,"borderTopColor"))}var a=new dt(this._pt,e.style,r,0,1,Ka),l=0,u=0,c,d,_,f,p,h,m,w,T,C,v,S;if(a.b=t,a.e=i,t+="",i+="",i==="auto"&&(h=e.style[r],e.style[r]=i,i=tr(e,r)||i,h?e.style[r]=h:ni(e,r)),c=[t,i],Ha(c),t=c[0],i=c[1],_=t.match(vi)||[],S=i.match(vi)||[],S.length){for(;d=vi.exec(i);)m=d[0],T=i.substring(l,d.index),p?p=(p+1)%5:(T.substr(-5)==="rgba("||T.substr(-5)==="hsla(")&&(p=1),m!==(h=_[u++]||"")&&(f=parseFloat(h)||0,v=h.substr((f+"").length),m.charAt(1)==="="&&(m=xi(f,m)+v),w=parseFloat(m),C=m.substr((w+"").length),l=vi.lastIndex-C.length,C||(C=C||St.units[r]||v,l===i.length&&(i+=C,a.e+=C)),v!==C&&(f=Er(e,r,h,C)||0),a._pt={_next:a._pt,p:T||u===1?T:",",s:f,c:w-f,m:p&&p<4||r==="zIndex"?Math.round:0});a.c=l<i.length?i.substring(l,i.length):""}else a.r=r==="display"&&i==="none"?tl:el;return ga.test(i)&&(a.e=0),this._pt=a,a},No={top:"0%",bottom:"100%",left:"0%",right:"100%",center:"50%"},oc=function(e){var r=e.split(" "),t=r[0],i=r[1]||"50%";return(t==="top"||t==="bottom"||i==="left"||i==="right")&&(e=t,t=i,i=e),r[0]=No[t]||t,r[1]=No[i]||i,r.join(" ")},ac=function(e,r){if(r.tween&&r.tween._time===r.tween._dur){var t=r.t,i=t.style,n=r.u,o=t._gsap,a,l,u;if(n==="all"||n===!0)i.cssText="",l=1;else for(n=n.split(","),u=n.length;--u>-1;)a=n[u],_r[a]&&(l=1,a=a==="transformOrigin"?ht:_e),ni(t,a);l&&(ni(t,_e),o&&(o.svg&&t.removeAttribute("transform"),pn(t,1),o.uncache=1,rl(i)))}},Zn={clearProps:function(e,r,t,i,n){if(n.data!=="isFromStart"){var o=e._pt=new dt(e._pt,r,t,0,0,ac);return o.u=i,o.pr=-10,o.tween=n,e._props.push(t),1}}},_n=[1,0,0,1,0,0],al={},ll=function(e){return e==="matrix(1, 0, 0, 1, 0, 0)"||e==="none"||!e},Yo=function(e){var r=tr(e,_e);return ll(r)?_n:r.substr(7).match(pa).map(Te)},yo=function(e,r){var t=e._gsap||jr(e),i=e.style,n=Yo(e),o,a,l,u;return t.svg&&e.getAttribute("transform")?(l=e.transform.baseVal.consolidate().matrix,n=[l.a,l.b,l.c,l.d,l.e,l.f],n.join(",")==="1,0,0,1,0,0"?_n:n):(n===_n&&!e.offsetParent&&e!==wi&&!t.svg&&(l=i.display,i.display="block",o=e.parentNode,(!o||!e.offsetParent)&&(u=1,a=e.nextElementSibling,wi.appendChild(e)),n=Yo(e),l?i.display=l:ni(e,"display"),u&&(a?o.insertBefore(e,a):o?o.appendChild(e):wi.removeChild(e))),r&&n.length>6?[n[0],n[1],n[4],n[5],n[12],n[13]]:n)},Ys=function(e,r,t,i,n,o){var a=e._gsap,l=n||yo(e,!0),u=a.xOrigin||0,c=a.yOrigin||0,d=a.xOffset||0,_=a.yOffset||0,f=l[0],p=l[1],h=l[2],m=l[3],w=l[4],T=l[5],C=r.split(" "),v=parseFloat(C[0])||0,S=parseFloat(C[1])||0,M,b,O,k;t?l!==_n&&(b=f*m-p*h)&&(O=v*(m/b)+S*(-h/b)+(h*T-m*w)/b,k=v*(-p/b)+S*(f/b)-(f*T-p*w)/b,v=O,S=k):(M=sl(e),v=M.x+(~C[0].indexOf("%")?v/100*M.width:v),S=M.y+(~(C[1]||C[0]).indexOf("%")?S/100*M.height:S)),i||i!==!1&&a.smooth?(w=v-u,T=S-c,a.xOffset=d+(w*f+T*h)-w,a.yOffset=_+(w*p+T*m)-T):a.xOffset=a.yOffset=0,a.xOrigin=v,a.yOrigin=S,a.smooth=!!i,a.origin=r,a.originIsAbsolute=!!t,e.style[ht]="0px 0px",o&&(kr(o,a,"xOrigin",u,v),kr(o,a,"yOrigin",c,S),kr(o,a,"xOffset",d,a.xOffset),kr(o,a,"yOffset",_,a.yOffset)),e.setAttribute("data-svg-origin",v+" "+S)},pn=function(e,r){var t=e._gsap||new Va(e);if("x"in t&&!r&&!t.uncache)return t;var i=e.style,n=t.scaleX<0,o="px",a="deg",l=getComputedStyle(e),u=tr(e,ht)||"0",c,d,_,f,p,h,m,w,T,C,v,S,M,b,O,k,P,z,D,W,Y,Z,Q,q,J,oe,g,ae,je,qt,pe,Fe;return c=d=_=h=m=w=T=C=v=0,f=p=1,t.svg=!!(e.getCTM&&ol(e)),l.translate&&((l.translate!=="none"||l.scale!=="none"||l.rotate!=="none")&&(i[_e]=(l.translate!=="none"?"translate3d("+(l.translate+" 0 0").split(" ").slice(0,3).join(", ")+") ":"")+(l.rotate!=="none"?"rotate("+l.rotate+") ":"")+(l.scale!=="none"?"scale("+l.scale.split(" ").join(",")+") ":"")+(l[_e]!=="none"?l[_e]:"")),i.scale=i.rotate=i.translate="none"),b=yo(e,t.svg),t.svg&&(t.uncache?(J=e.getBBox(),u=t.xOrigin-J.x+"px "+(t.yOrigin-J.y)+"px",q=""):q=!r&&e.getAttribute("data-svg-origin"),Ys(e,q||u,!!q||t.originIsAbsolute,t.smooth!==!1,b)),S=t.xOrigin||0,M=t.yOrigin||0,b!==_n&&(z=b[0],D=b[1],W=b[2],Y=b[3],c=Z=b[4],d=Q=b[5],b.length===6?(f=Math.sqrt(z*z+D*D),p=Math.sqrt(Y*Y+W*W),h=z||D?hi(D,z)*Vr:0,T=W||Y?hi(W,Y)*Vr+h:0,T&&(p*=Math.abs(Math.cos(T*Si))),t.svg&&(c-=S-(S*z+M*W),d-=M-(S*D+M*Y))):(Fe=b[6],qt=b[7],g=b[8],ae=b[9],je=b[10],pe=b[11],c=b[12],d=b[13],_=b[14],O=hi(Fe,je),m=O*Vr,O&&(k=Math.cos(-O),P=Math.sin(-O),q=Z*k+g*P,J=Q*k+ae*P,oe=Fe*k+je*P,g=Z*-P+g*k,ae=Q*-P+ae*k,je=Fe*-P+je*k,pe=qt*-P+pe*k,Z=q,Q=J,Fe=oe),O=hi(-W,je),w=O*Vr,O&&(k=Math.cos(-O),P=Math.sin(-O),q=z*k-g*P,J=D*k-ae*P,oe=W*k-je*P,pe=Y*P+pe*k,z=q,D=J,W=oe),O=hi(D,z),h=O*Vr,O&&(k=Math.cos(O),P=Math.sin(O),q=z*k+D*P,J=Z*k+Q*P,D=D*k-z*P,Q=Q*k-Z*P,z=q,Z=J),m&&Math.abs(m)+Math.abs(h)>359.9&&(m=h=0,w=180-w),f=Te(Math.sqrt(z*z+D*D+W*W)),p=Te(Math.sqrt(Q*Q+Fe*Fe)),O=hi(Z,Q),T=Math.abs(O)>2e-4?O*Vr:0,v=pe?1/(pe<0?-pe:pe):0),t.svg&&(q=e.getAttribute("transform"),t.forceCSS=e.setAttribute("transform","")||!ll(tr(e,_e)),q&&e.setAttribute("transform",q))),Math.abs(T)>90&&Math.abs(T)<270&&(n?(f*=-1,T+=h<=0?180:-180,h+=h<=0?180:-180):(p*=-1,T+=T<=0?180:-180)),r=r||t.uncache,t.x=c-((t.xPercent=c&&(!r&&t.xPercent||(Math.round(e.offsetWidth/2)===Math.round(-c)?-50:0)))?e.offsetWidth*t.xPercent/100:0)+o,t.y=d-((t.yPercent=d&&(!r&&t.yPercent||(Math.round(e.offsetHeight/2)===Math.round(-d)?-50:0)))?e.offsetHeight*t.yPercent/100:0)+o,t.z=_+o,t.scaleX=Te(f),t.scaleY=Te(p),t.rotation=Te(h)+a,t.rotationX=Te(m)+a,t.rotationY=Te(w)+a,t.skewX=T+a,t.skewY=C+a,t.transformPerspective=v+o,(t.zOrigin=parseFloat(u.split(" ")[2])||!r&&t.zOrigin||0)&&(i[ht]=Jn(u)),t.xOffset=t.yOffset=0,t.force3D=St.force3D,t.renderTransform=t.svg?uc:nl?ul:lc,t.uncache=0,t},Jn=function(e){return(e=e.split(" "))[0]+" "+e[1]},bs=function(e,r,t){var i=Qe(r);return Te(parseFloat(r)+parseFloat(Er(e,"x",t+"px",i)))+i},lc=function(e,r){r.z="0px",r.rotationY=r.rotationX="0deg",r.force3D=0,ul(e,r)},Nr="0deg",$i="0px",Yr=") ",ul=function(e,r){var t=r||this,i=t.xPercent,n=t.yPercent,o=t.x,a=t.y,l=t.z,u=t.rotation,c=t.rotationY,d=t.rotationX,_=t.skewX,f=t.skewY,p=t.scaleX,h=t.scaleY,m=t.transformPerspective,w=t.force3D,T=t.target,C=t.zOrigin,v="",S=w==="auto"&&e&&e!==1||w===!0;if(C&&(d!==Nr||c!==Nr)){var M=parseFloat(c)*Si,b=Math.sin(M),O=Math.cos(M),k;M=parseFloat(d)*Si,k=Math.cos(M),o=bs(T,o,b*k*-C),a=bs(T,a,-Math.sin(M)*-C),l=bs(T,l,O*k*-C+C)}m!==$i&&(v+="perspective("+m+Yr),(i||n)&&(v+="translate("+i+"%, "+n+"%) "),(S||o!==$i||a!==$i||l!==$i)&&(v+=l!==$i||S?"translate3d("+o+", "+a+", "+l+") ":"translate("+o+", "+a+Yr),u!==Nr&&(v+="rotate("+u+Yr),c!==Nr&&(v+="rotateY("+c+Yr),d!==Nr&&(v+="rotateX("+d+Yr),(_!==Nr||f!==Nr)&&(v+="skew("+_+", "+f+Yr),(p!==1||h!==1)&&(v+="scale("+p+", "+h+Yr),T.style[_e]=v||"translate(0, 0)"},uc=function(e,r){var t=r||this,i=t.xPercent,n=t.yPercent,o=t.x,a=t.y,l=t.rotation,u=t.skewX,c=t.skewY,d=t.scaleX,_=t.scaleY,f=t.target,p=t.xOrigin,h=t.yOrigin,m=t.xOffset,w=t.yOffset,T=t.forceCSS,C=parseFloat(o),v=parseFloat(a),S,M,b,O,k;l=parseFloat(l),u=parseFloat(u),c=parseFloat(c),c&&(c=parseFloat(c),u+=c,l+=c),l||u?(l*=Si,u*=Si,S=Math.cos(l)*d,M=Math.sin(l)*d,b=Math.sin(l-u)*-_,O=Math.cos(l-u)*_,u&&(c*=Si,k=Math.tan(u-c),k=Math.sqrt(1+k*k),b*=k,O*=k,c&&(k=Math.tan(c),k=Math.sqrt(1+k*k),S*=k,M*=k)),S=Te(S),M=Te(M),b=Te(b),O=Te(O)):(S=d,O=_,M=b=0),(C&&!~(o+"").indexOf("px")||v&&!~(a+"").indexOf("px"))&&(C=Er(f,"x",o,"px"),v=Er(f,"y",a,"px")),(p||h||m||w)&&(C=Te(C+p-(p*S+h*b)+m),v=Te(v+h-(p*M+h*O)+w)),(i||n)&&(k=f.getBBox(),C=Te(C+i/100*k.width),v=Te(v+n/100*k.height)),k="matrix("+S+","+M+","+b+","+O+","+C+","+v+")",f.setAttribute("transform",k),T&&(f.style[_e]=k)},cc=function(e,r,t,i,n){var o=360,a=Ie(n),l=parseFloat(n)*(a&&~n.indexOf("rad")?Vr:1),u=l-i,c=i+u+"deg",d,_;return a&&(d=n.split("_")[1],d==="short"&&(u%=o,u!==u%(o/2)&&(u+=u<0?o:-o)),d==="cw"&&u<0?u=(u+o*zo)%o-~~(u/o)*o:d==="ccw"&&u>0&&(u=(u-o*zo)%o-~~(u/o)*o)),e._pt=_=new dt(e._pt,r,t,i,u,Qu),_.e=c,_.u="deg",e._props.push(t),_},Xo=function(e,r){for(var t in r)e[t]=r[t];return e},fc=function(e,r,t){var i=Xo({},t._gsap),n="perspective,force3D,transformOrigin,svgOrigin",o=t.style,a,l,u,c,d,_,f,p;i.svg?(u=t.getAttribute("transform"),t.setAttribute("transform",""),o[_e]=r,a=pn(t,1),ni(t,_e),t.setAttribute("transform",u)):(u=getComputedStyle(t)[_e],o[_e]=r,a=pn(t,1),o[_e]=u);for(l in _r)u=i[l],c=a[l],u!==c&&n.indexOf(l)<0&&(f=Qe(u),p=Qe(c),d=f!==p?Er(t,l,u,p):parseFloat(u),_=parseFloat(c),e._pt=new dt(e._pt,a,l,d,_-d,Bs),e._pt.u=p||0,e._props.push(l));Xo(a,i)};ft("padding,margin,Width,Radius",function(s,e){var r="Top",t="Right",i="Bottom",n="Left",o=(e<3?[r,t,i,n]:[r+n,r+t,i+t,i+n]).map(function(a){return e<2?s+a:"border"+a+s});Zn[e>1?"border"+s:s]=function(a,l,u,c,d){var _,f;if(arguments.length<4)return _=o.map(function(p){return lr(a,p,u)}),f=_.join(" "),f.split(_[0]).length===5?_[0]:f;_=(c+"").split(" "),f={},o.forEach(function(p,h){return f[p]=_[h]=_[h]||_[(h-1)/2|0]}),a.init(l,f,d)}});var cl={name:"css",register:Ns,targetTest:function(e){return e.style&&e.nodeType},init:function(e,r,t,i,n){var o=this._props,a=e.style,l=t.vars.startAt,u,c,d,_,f,p,h,m,w,T,C,v,S,M,b,O;po||Ns(),this.styles=this.styles||il(e),O=this.styles.props,this.tween=t;for(h in r)if(h!=="autoRound"&&(c=r[h],!(bt[h]&&Wa(h,r,t,i,e,n)))){if(f=typeof c,p=Zn[h],f==="function"&&(c=c.call(t,i,e,n),f=typeof c),f==="string"&&~c.indexOf("random(")&&(c=fn(c)),p)p(this,e,h,c,t)&&(b=1);else if(h.substr(0,2)==="--")u=(getComputedStyle(e).getPropertyValue(h)+"").trim(),c+="",Lr.lastIndex=0,Lr.test(u)||(m=Qe(u),w=Qe(c)),w?m!==w&&(u=Er(e,h,u,w)+w):m&&(c+=m),this.add(a,"setProperty",u,c,i,n,0,0,h),o.push(h),O.push(h,0,a[h]);else if(f!=="undefined"){if(l&&h in l?(u=typeof l[h]=="function"?l[h].call(t,i,e,n):l[h],Ie(u)&&~u.indexOf("random(")&&(u=fn(u)),Qe(u+"")||u==="auto"||(u+=St.units[h]||Qe(lr(e,h))||""),(u+"").charAt(1)==="="&&(u=lr(e,h))):u=lr(e,h),_=parseFloat(u),T=f==="string"&&c.charAt(1)==="="&&c.substr(0,2),T&&(c=c.substr(2)),d=parseFloat(c),h in Jt&&(h==="autoAlpha"&&(_===1&&lr(e,"visibility")==="hidden"&&d&&(_=0),O.push("visibility",0,a.visibility),kr(this,a,"visibility",_?"inherit":"hidden",d?"inherit":"hidden",!d)),h!=="scale"&&h!=="transform"&&(h=Jt[h],~h.indexOf(",")&&(h=h.split(",")[0]))),C=h in _r,C){if(this.styles.save(h),v||(S=e._gsap,S.renderTransform&&!r.parseTransform||pn(e,r.parseTransform),M=r.smoothOrigin!==!1&&S.smooth,v=this._pt=new dt(this._pt,a,_e,0,1,S.renderTransform,S,0,-1),v.dep=1),h==="scale")this._pt=new dt(this._pt,S,"scaleY",S.scaleY,(T?xi(S.scaleY,T+d):d)-S.scaleY||0,Bs),this._pt.u=0,o.push("scaleY",h),h+="X";else if(h==="transformOrigin"){O.push(ht,0,a[ht]),c=oc(c),S.svg?Ys(e,c,0,M,0,this):(w=parseFloat(c.split(" ")[2])||0,w!==S.zOrigin&&kr(this,S,"zOrigin",S.zOrigin,w),kr(this,a,h,Jn(u),Jn(c)));continue}else if(h==="svgOrigin"){Ys(e,c,1,M,0,this);continue}else if(h in al){cc(this,S,h,_,T?xi(_,T+c):c);continue}else if(h==="smoothOrigin"){kr(this,S,"smooth",S.smooth,c);continue}else if(h==="force3D"){S[h]=c;continue}else if(h==="transform"){fc(this,c,e);continue}}else h in a||(h=Ai(h)||h);if(C||(d||d===0)&&(_||_===0)&&!Wu.test(c)&&h in a)m=(u+"").substr((_+"").length),d||(d=0),w=Qe(c)||(h in St.units?St.units[h]:m),m!==w&&(_=Er(e,h,u,w)),this._pt=new dt(this._pt,C?S:a,h,_,(T?xi(_,T+d):d)-_,!C&&(w==="px"||h==="zIndex")&&r.autoRound!==!1?Gu:Bs),this._pt.u=w||0,m!==w&&w!=="%"&&(this._pt.b=u,this._pt.r=Uu);else if(h in a)sc.call(this,e,h,u,T?T+c:c);else if(h in e)this.add(e,h,u||e[h],T?T+c:c,i,n);else if(h!=="parseTransform"){so(h,c);continue}C||(h in a?O.push(h,0,a[h]):O.push(h,1,u||e[h])),o.push(h)}}b&&Za(this)},render:function(e,r){if(r.tween._time||!go())for(var t=r._pt;t;)t.r(e,t.d),t=t._next;else r.styles.revert()},get:lr,aliases:Jt,getSetter:function(e,r,t){var i=Jt[r];return i&&i.indexOf(",")<0&&(r=i),r in _r&&r!==ht&&(e._gsap.x||lr(e,"x"))?t&&Fo===t?r==="scale"?Ju:Zu:(Fo=t||{})&&(r==="scale"?ec:tc):e.style&&!ro(e.style[r])?ju:~r.indexOf("-")?Ku:ho(e,r)},core:{_removeProperty:ni,_getMatrix:yo}};_t.utils.checkPrefix=Ai;_t.core.getStyleSaver=il;(function(s,e,r,t){var i=ft(s+","+e+","+r,function(n){_r[n]=1});ft(e,function(n){St.units[n]="deg",al[n]=1}),Jt[i[13]]=s+","+e,ft(t,function(n){var o=n.split(":");Jt[o[1]]=i[o[0]]})})("x,y,z,scale,scaleX,scaleY,xPercent,yPercent","rotation,rotationX,rotationY,skewX,skewY","transform,transformOrigin,svgOrigin,force3D,smoothOrigin,transformPerspective","0:translateX,1:translateY,2:translateZ,8:rotate,8:rotationZ,8:rotateZ,9:rotateX,10:rotateY");ft("x,y,z,top,right,bottom,left,width,height,fontSize,padding,margin,perspective",function(s){St.units[s]="px"});_t.registerPlugin(cl);var vo=_t.registerPlugin(cl)||_t;vo.core.Tween;function Vo(s,e){for(var r=0;r<e.length;r++){var t=e[r];t.enumerable=t.enumerable||!1,t.configurable=!0,"value"in t&&(t.writable=!0),Object.defineProperty(s,t.key,t)}}function dc(s,e,r){return e&&Vo(s.prototype,e),r&&Vo(s,r),s}/*!
 * Observer 3.12.5
 * https://gsap.com
 *
 * @license Copyright 2008-2024, GreenSock. All rights reserved.
 * Subject to the terms at https://gsap.com/standard-license or for
 * Club GSAP members, the agreement issued with that membership.
 * @author: Jack Doyle, <EMAIL>
*/var Be,zn,xt,Mr,Pr,Ci,fl,Wr,Ji,dl,fr,Nt,hl,_l=function(){return Be||typeof window!="undefined"&&(Be=window.gsap)&&Be.registerPlugin&&Be},pl=1,Ti=[],V=[],rr=[],en=Date.now,Xs=function(e,r){return r},hc=function(){var e=Ji.core,r=e.bridge||{},t=e._scrollers,i=e._proxies;t.push.apply(t,V),i.push.apply(i,rr),V=t,rr=i,Xs=function(o,a){return r[o](a)}},Ar=function(e,r){return~rr.indexOf(e)&&rr[rr.indexOf(e)+1][r]},tn=function(e){return!!~dl.indexOf(e)},tt=function(e,r,t,i,n){return e.addEventListener(r,t,{passive:i!==!1,capture:!!n})},et=function(e,r,t,i){return e.removeEventListener(r,t,!!i)},wn="scrollLeft",Sn="scrollTop",Vs=function(){return fr&&fr.isPressed||V.cache++},es=function(e,r){var t=function i(n){if(n||n===0){pl&&(xt.history.scrollRestoration="manual");var o=fr&&fr.isPressed;n=i.v=Math.round(n)||(fr&&fr.iOS?1:0),e(n),i.cacheID=V.cache,o&&Xs("ss",n)}else(r||V.cache!==i.cacheID||Xs("ref"))&&(i.cacheID=V.cache,i.v=e());return i.v+i.offset};return t.offset=0,e&&t},ot={s:wn,p:"left",p2:"Left",os:"right",os2:"Right",d:"width",d2:"Width",a:"x",sc:es(function(s){return arguments.length?xt.scrollTo(s,De.sc()):xt.pageXOffset||Mr[wn]||Pr[wn]||Ci[wn]||0})},De={s:Sn,p:"top",p2:"Top",os:"bottom",os2:"Bottom",d:"height",d2:"Height",a:"y",op:ot,sc:es(function(s){return arguments.length?xt.scrollTo(ot.sc(),s):xt.pageYOffset||Mr[Sn]||Pr[Sn]||Ci[Sn]||0})},ut=function(e,r){return(r&&r._ctx&&r._ctx.selector||Be.utils.toArray)(e)[0]||(typeof e=="string"&&Be.config().nullTargetWarn!==!1?console.warn("Element not found:",e):null)},qr=function(e,r){var t=r.s,i=r.sc;tn(e)&&(e=Mr.scrollingElement||Pr);var n=V.indexOf(e),o=i===De.sc?1:2;!~n&&(n=V.push(e)-1),V[n+o]||tt(e,"scroll",Vs);var a=V[n+o],l=a||(V[n+o]=es(Ar(e,t),!0)||(tn(e)?i:es(function(u){return arguments.length?e[t]=u:e[t]})));return l.target=e,a||(l.smooth=Be.getProperty(e,"scrollBehavior")==="smooth"),l},Ws=function(e,r,t){var i=e,n=e,o=en(),a=o,l=r||50,u=Math.max(500,l*3),c=function(p,h){var m=en();h||m-o>l?(n=i,i=p,a=o,o=m):t?i+=p:i=n+(p-n)/(m-a)*(o-a)},d=function(){n=i=t?0:i,a=o=0},_=function(p){var h=a,m=n,w=en();return(p||p===0)&&p!==i&&c(p),o===a||w-a>u?0:(i+(t?m:-m))/((t?w:o)-h)*1e3};return{update:c,reset:d,getVelocity:_}},Bi=function(e,r){return r&&!e._gsapAllow&&e.preventDefault(),e.changedTouches?e.changedTouches[0]:e},Wo=function(e){var r=Math.max.apply(Math,e),t=Math.min.apply(Math,e);return Math.abs(r)>=Math.abs(t)?r:t},gl=function(){Ji=Be.core.globals().ScrollTrigger,Ji&&Ji.core&&hc()},ml=function(e){return Be=e||_l(),!zn&&Be&&typeof document!="undefined"&&document.body&&(xt=window,Mr=document,Pr=Mr.documentElement,Ci=Mr.body,dl=[xt,Mr,Pr,Ci],Be.utils.clamp,hl=Be.core.context||function(){},Wr="onpointerenter"in Ci?"pointer":"mouse",fl=xe.isTouch=xt.matchMedia&&xt.matchMedia("(hover: none), (pointer: coarse)").matches?1:"ontouchstart"in xt||navigator.maxTouchPoints>0||navigator.msMaxTouchPoints>0?2:0,Nt=xe.eventTypes=("ontouchstart"in Pr?"touchstart,touchmove,touchcancel,touchend":"onpointerdown"in Pr?"pointerdown,pointermove,pointercancel,pointerup":"mousedown,mousemove,mouseup,mouseup").split(","),setTimeout(function(){return pl=0},500),gl(),zn=1),zn};ot.op=De;V.cache=0;var xe=function(){function s(r){this.init(r)}var e=s.prototype;return e.init=function(t){zn||ml(Be)||console.warn("Please gsap.registerPlugin(Observer)"),Ji||gl();var i=t.tolerance,n=t.dragMinimum,o=t.type,a=t.target,l=t.lineHeight,u=t.debounce,c=t.preventDefault,d=t.onStop,_=t.onStopDelay,f=t.ignore,p=t.wheelSpeed,h=t.event,m=t.onDragStart,w=t.onDragEnd,T=t.onDrag,C=t.onPress,v=t.onRelease,S=t.onRight,M=t.onLeft,b=t.onUp,O=t.onDown,k=t.onChangeX,P=t.onChangeY,z=t.onChange,D=t.onToggleX,W=t.onToggleY,Y=t.onHover,Z=t.onHoverEnd,Q=t.onMove,q=t.ignoreCheck,J=t.isNormalizer,oe=t.onGestureStart,g=t.onGestureEnd,ae=t.onWheel,je=t.onEnable,qt=t.onDisable,pe=t.onClick,Fe=t.scrollSpeed,Ke=t.capture,we=t.allowClicks,Ze=t.lockAxis,He=t.onLockAxis;this.target=a=ut(a)||Pr,this.vars=t,f&&(f=Be.utils.toArray(f)),i=i||1e-9,n=n||0,p=p||1,Fe=Fe||1,o=o||"wheel,touch,pointer",u=u!==!1,l||(l=parseFloat(xt.getComputedStyle(Ci).lineHeight)||22);var pr,Je,It,ee,ye,lt,pt,y=this,gt=0,nr=0,gr=t.passive||!c,Se=qr(a,ot),mr=qr(a,De),Fr=Se(),ui=mr(),Le=~o.indexOf("touch")&&!~o.indexOf("pointer")&&Nt[0]==="pointerdown",yr=tn(a),ve=a.ownerDocument||Mr,Ft=[0,0,0],kt=[0,0,0],sr=0,Ei=function(){return sr=en()},Ce=function(E,te){return(y.event=E)&&f&&~f.indexOf(E.target)||te&&Le&&E.pointerType!=="touch"||q&&q(E,te)},vn=function(){y._vx.reset(),y._vy.reset(),Je.pause(),d&&d(y)},vr=function(){var E=y.deltaX=Wo(Ft),te=y.deltaY=Wo(kt),L=Math.abs(E)>=i,B=Math.abs(te)>=i;z&&(L||B)&&z(y,E,te,Ft,kt),L&&(S&&y.deltaX>0&&S(y),M&&y.deltaX<0&&M(y),k&&k(y),D&&y.deltaX<0!=gt<0&&D(y),gt=y.deltaX,Ft[0]=Ft[1]=Ft[2]=0),B&&(O&&y.deltaY>0&&O(y),b&&y.deltaY<0&&b(y),P&&P(y),W&&y.deltaY<0!=nr<0&&W(y),nr=y.deltaY,kt[0]=kt[1]=kt[2]=0),(ee||It)&&(Q&&Q(y),It&&(T(y),It=!1),ee=!1),lt&&!(lt=!1)&&He&&He(y),ye&&(ae(y),ye=!1),pr=0},ci=function(E,te,L){Ft[L]+=E,kt[L]+=te,y._vx.update(E),y._vy.update(te),u?pr||(pr=requestAnimationFrame(vr)):vr()},fi=function(E,te){Ze&&!pt&&(y.axis=pt=Math.abs(E)>Math.abs(te)?"x":"y",lt=!0),pt!=="y"&&(Ft[2]+=E,y._vx.update(E,!0)),pt!=="x"&&(kt[2]+=te,y._vy.update(te,!0)),u?pr||(pr=requestAnimationFrame(vr)):vr()},br=function(E){if(!Ce(E,1)){E=Bi(E,c);var te=E.clientX,L=E.clientY,B=te-y.x,R=L-y.y,I=y.isDragging;y.x=te,y.y=L,(I||Math.abs(y.startX-te)>=n||Math.abs(y.startY-L)>=n)&&(T&&(It=!0),I||(y.isDragging=!0),fi(B,R),I||m&&m(y))}},zr=y.onPress=function($){Ce($,1)||$&&$.button||(y.axis=pt=null,Je.pause(),y.isPressed=!0,$=Bi($),gt=nr=0,y.startX=y.x=$.clientX,y.startY=y.y=$.clientY,y._vx.reset(),y._vy.reset(),tt(J?a:ve,Nt[1],br,gr,!0),y.deltaX=y.deltaY=0,C&&C(y))},X=y.onRelease=function($){if(!Ce($,1)){et(J?a:ve,Nt[1],br,!0);var E=!isNaN(y.y-y.startY),te=y.isDragging,L=te&&(Math.abs(y.x-y.startX)>3||Math.abs(y.y-y.startY)>3),B=Bi($);!L&&E&&(y._vx.reset(),y._vy.reset(),c&&we&&Be.delayedCall(.08,function(){if(en()-sr>300&&!$.defaultPrevented){if($.target.click)$.target.click();else if(ve.createEvent){var R=ve.createEvent("MouseEvents");R.initMouseEvent("click",!0,!0,xt,1,B.screenX,B.screenY,B.clientX,B.clientY,!1,!1,!1,!1,0,null),$.target.dispatchEvent(R)}}})),y.isDragging=y.isGesturing=y.isPressed=!1,d&&te&&!J&&Je.restart(!0),w&&te&&w(y),v&&v(y,L)}},$r=function(E){return E.touches&&E.touches.length>1&&(y.isGesturing=!0)&&oe(E,y.isDragging)},zt=function(){return(y.isGesturing=!1)||g(y)},$t=function(E){if(!Ce(E)){var te=Se(),L=mr();ci((te-Fr)*Fe,(L-ui)*Fe,1),Fr=te,ui=L,d&&Je.restart(!0)}},Bt=function(E){if(!Ce(E)){E=Bi(E,c),ae&&(ye=!0);var te=(E.deltaMode===1?l:E.deltaMode===2?xt.innerHeight:1)*p;ci(E.deltaX*te,E.deltaY*te,0),d&&!J&&Je.restart(!0)}},Br=function(E){if(!Ce(E)){var te=E.clientX,L=E.clientY,B=te-y.x,R=L-y.y;y.x=te,y.y=L,ee=!0,d&&Je.restart(!0),(B||R)&&fi(B,R)}},di=function(E){y.event=E,Y(y)},or=function(E){y.event=E,Z(y)},qi=function(E){return Ce(E)||Bi(E,c)&&pe(y)};Je=y._dc=Be.delayedCall(_||.25,vn).pause(),y.deltaX=y.deltaY=0,y._vx=Ws(0,50,!0),y._vy=Ws(0,50,!0),y.scrollX=Se,y.scrollY=mr,y.isDragging=y.isGesturing=y.isPressed=!1,hl(this),y.enable=function($){return y.isEnabled||(tt(yr?ve:a,"scroll",Vs),o.indexOf("scroll")>=0&&tt(yr?ve:a,"scroll",$t,gr,Ke),o.indexOf("wheel")>=0&&tt(a,"wheel",Bt,gr,Ke),(o.indexOf("touch")>=0&&fl||o.indexOf("pointer")>=0)&&(tt(a,Nt[0],zr,gr,Ke),tt(ve,Nt[2],X),tt(ve,Nt[3],X),we&&tt(a,"click",Ei,!0,!0),pe&&tt(a,"click",qi),oe&&tt(ve,"gesturestart",$r),g&&tt(ve,"gestureend",zt),Y&&tt(a,Wr+"enter",di),Z&&tt(a,Wr+"leave",or),Q&&tt(a,Wr+"move",Br)),y.isEnabled=!0,$&&$.type&&zr($),je&&je(y)),y},y.disable=function(){y.isEnabled&&(Ti.filter(function($){return $!==y&&tn($.target)}).length||et(yr?ve:a,"scroll",Vs),y.isPressed&&(y._vx.reset(),y._vy.reset(),et(J?a:ve,Nt[1],br,!0)),et(yr?ve:a,"scroll",$t,Ke),et(a,"wheel",Bt,Ke),et(a,Nt[0],zr,Ke),et(ve,Nt[2],X),et(ve,Nt[3],X),et(a,"click",Ei,!0),et(a,"click",qi),et(ve,"gesturestart",$r),et(ve,"gestureend",zt),et(a,Wr+"enter",di),et(a,Wr+"leave",or),et(a,Wr+"move",Br),y.isEnabled=y.isPressed=y.isDragging=!1,qt&&qt(y))},y.kill=y.revert=function(){y.disable();var $=Ti.indexOf(y);$>=0&&Ti.splice($,1),fr===y&&(fr=0)},Ti.push(y),J&&tn(a)&&(fr=y),y.enable(h)},dc(s,[{key:"velocityX",get:function(){return this._vx.getVelocity()}},{key:"velocityY",get:function(){return this._vy.getVelocity()}}]),s}();xe.version="3.12.5";xe.create=function(s){return new xe(s)};xe.register=ml;xe.getAll=function(){return Ti.slice()};xe.getById=function(s){return Ti.filter(function(e){return e.vars.id===s})[0]};_l()&&Be.registerPlugin(xe);/*!
 * ScrollTrigger 3.12.5
 * https://gsap.com
 *
 * @license Copyright 2008-2024, GreenSock. All rights reserved.
 * Subject to the terms at https://gsap.com/standard-license or for
 * Club GSAP members, the agreement issued with that membership.
 * @author: Jack Doyle, <EMAIL>
*/var A,gi,j,de,Xt,le,yl,ts,gn,rn,Xi,Cn,Ve,cs,Qs,it,Qo,Uo,mi,vl,Ts,bl,rt,Us,Tl,xl,Tr,Gs,bo,ki,To,rs,js,xs,kn=1,We=Date.now,ws=We(),Rt=0,Vi=0,Go=function(e,r,t){var i=vt(e)&&(e.substr(0,6)==="clamp("||e.indexOf("max")>-1);return t["_"+r+"Clamp"]=i,i?e.substr(6,e.length-7):e},jo=function(e,r){return r&&(!vt(e)||e.substr(0,6)!=="clamp(")?"clamp("+e+")":e},_c=function s(){return Vi&&requestAnimationFrame(s)},Ko=function(){return cs=1},Zo=function(){return cs=0},jt=function(e){return e},Wi=function(e){return Math.round(e*1e5)/1e5||0},wl=function(){return typeof window!="undefined"},Sl=function(){return A||wl()&&(A=window.gsap)&&A.registerPlugin&&A},si=function(e){return!!~yl.indexOf(e)},Cl=function(e){return(e==="Height"?To:j["inner"+e])||Xt["client"+e]||le["client"+e]},kl=function(e){return Ar(e,"getBoundingClientRect")||(si(e)?function(){return Yn.width=j.innerWidth,Yn.height=To,Yn}:function(){return cr(e)})},pc=function(e,r,t){var i=t.d,n=t.d2,o=t.a;return(o=Ar(e,"getBoundingClientRect"))?function(){return o()[i]}:function(){return(r?Cl(n):e["client"+n])||0}},gc=function(e,r){return!r||~rr.indexOf(e)?kl(e):function(){return Yn}},er=function(e,r){var t=r.s,i=r.d2,n=r.d,o=r.a;return Math.max(0,(t="scroll"+i)&&(o=Ar(e,t))?o()-kl(e)()[n]:si(e)?(Xt[t]||le[t])-Cl(i):e[t]-e["offset"+i])},Mn=function(e,r){for(var t=0;t<mi.length;t+=3)(!r||~r.indexOf(mi[t+1]))&&e(mi[t],mi[t+1],mi[t+2])},vt=function(e){return typeof e=="string"},at=function(e){return typeof e=="function"},Qi=function(e){return typeof e=="number"},Qr=function(e){return typeof e=="object"},Hi=function(e,r,t){return e&&e.progress(r?0:1)&&t&&e.pause()},Ss=function(e,r){if(e.enabled){var t=e._ctx?e._ctx.add(function(){return r(e)}):r(e);t&&t.totalTime&&(e.callbackAnimation=t)}},_i=Math.abs,Ml="left",Pl="top",xo="right",wo="bottom",ei="width",ti="height",nn="Right",sn="Left",on="Top",an="Bottom",ke="padding",Ot="margin",Ri="Width",So="Height",Oe="px",Dt=function(e){return j.getComputedStyle(e)},mc=function(e){var r=Dt(e).position;e.style.position=r==="absolute"||r==="fixed"?r:"relative"},Jo=function(e,r){for(var t in r)t in e||(e[t]=r[t]);return e},cr=function(e,r){var t=r&&Dt(e)[Qs]!=="matrix(1, 0, 0, 1, 0, 0)"&&A.to(e,{x:0,y:0,xPercent:0,yPercent:0,rotation:0,rotationX:0,rotationY:0,scale:1,skewX:0,skewY:0}).progress(1),i=e.getBoundingClientRect();return t&&t.progress(0).kill(),i},is=function(e,r){var t=r.d2;return e["offset"+t]||e["client"+t]||0},Ol=function(e){var r=[],t=e.labels,i=e.duration(),n;for(n in t)r.push(t[n]/i);return r},yc=function(e){return function(r){return A.utils.snap(Ol(e),r)}},Co=function(e){var r=A.utils.snap(e),t=Array.isArray(e)&&e.slice(0).sort(function(i,n){return i-n});return t?function(i,n,o){o===void 0&&(o=.001);var a;if(!n)return r(i);if(n>0){for(i-=o,a=0;a<t.length;a++)if(t[a]>=i)return t[a];return t[a-1]}else for(a=t.length,i+=o;a--;)if(t[a]<=i)return t[a];return t[0]}:function(i,n,o){o===void 0&&(o=.001);var a=r(i);return!n||Math.abs(a-i)<o||a-i<0==n<0?a:r(n<0?i-e:i+e)}},vc=function(e){return function(r,t){return Co(Ol(e))(r,t.direction)}},Pn=function(e,r,t,i){return t.split(",").forEach(function(n){return e(r,n,i)})},Ee=function(e,r,t,i,n){return e.addEventListener(r,t,{passive:!i,capture:!!n})},Re=function(e,r,t,i){return e.removeEventListener(r,t,!!i)},On=function(e,r,t){t=t&&t.wheelHandler,t&&(e(r,"wheel",t),e(r,"touchmove",t))},ea={startColor:"green",endColor:"red",indent:0,fontSize:"16px",fontWeight:"normal"},Dn={toggleActions:"play",anticipatePin:0},ns={top:0,left:0,center:.5,bottom:1,right:1},$n=function(e,r){if(vt(e)){var t=e.indexOf("="),i=~t?+(e.charAt(t-1)+1)*parseFloat(e.substr(t+1)):0;~t&&(e.indexOf("%")>t&&(i*=r/100),e=e.substr(0,t-1)),e=i+(e in ns?ns[e]*r:~e.indexOf("%")?parseFloat(e)*r/100:parseFloat(e)||0)}return e},Ln=function(e,r,t,i,n,o,a,l){var u=n.startColor,c=n.endColor,d=n.fontSize,_=n.indent,f=n.fontWeight,p=de.createElement("div"),h=si(t)||Ar(t,"pinType")==="fixed",m=e.indexOf("scroller")!==-1,w=h?le:t,T=e.indexOf("start")!==-1,C=T?u:c,v="border-color:"+C+";font-size:"+d+";color:"+C+";font-weight:"+f+";pointer-events:none;white-space:nowrap;font-family:sans-serif,Arial;z-index:1000;padding:4px 8px;border-width:0;border-style:solid;";return v+="position:"+((m||l)&&h?"fixed;":"absolute;"),(m||l||!h)&&(v+=(i===De?xo:wo)+":"+(o+parseFloat(_))+"px;"),a&&(v+="box-sizing:border-box;text-align:left;width:"+a.offsetWidth+"px;"),p._isStart=T,p.setAttribute("class","gsap-marker-"+e+(r?" marker-"+r:"")),p.style.cssText=v,p.innerText=r||r===0?e+"-"+r:e,w.children[0]?w.insertBefore(p,w.children[0]):w.appendChild(p),p._offset=p["offset"+i.op.d2],Bn(p,0,i,T),p},Bn=function(e,r,t,i){var n={display:"block"},o=t[i?"os2":"p2"],a=t[i?"p2":"os2"];e._isFlipped=i,n[t.a+"Percent"]=i?-100:0,n[t.a]=i?"1px":0,n["border"+o+Ri]=1,n["border"+a+Ri]=0,n[t.p]=r+"px",A.set(e,n)},N=[],Ks={},mn,ta=function(){return We()-Rt>34&&(mn||(mn=requestAnimationFrame(dr)))},pi=function(){(!rt||!rt.isPressed||rt.startX>le.clientWidth)&&(V.cache++,rt?mn||(mn=requestAnimationFrame(dr)):dr(),Rt||ai("scrollStart"),Rt=We())},Cs=function(){xl=j.innerWidth,Tl=j.innerHeight},Ui=function(){V.cache++,!Ve&&!bl&&!de.fullscreenElement&&!de.webkitFullscreenElement&&(!Us||xl!==j.innerWidth||Math.abs(j.innerHeight-Tl)>j.innerHeight*.25)&&ts.restart(!0)},oi={},bc=[],Dl=function s(){return Re(U,"scrollEnd",s)||Gr(!0)},ai=function(e){return oi[e]&&oi[e].map(function(r){return r()})||bc},yt=[],Ll=function(e){for(var r=0;r<yt.length;r+=5)(!e||yt[r+4]&&yt[r+4].query===e)&&(yt[r].style.cssText=yt[r+1],yt[r].getBBox&&yt[r].setAttribute("transform",yt[r+2]||""),yt[r+3].uncache=1)},ko=function(e,r){var t;for(it=0;it<N.length;it++)t=N[it],t&&(!r||t._ctx===r)&&(e?t.kill(1):t.revert(!0,!0));rs=!0,r&&Ll(r),r||ai("revert")},Al=function(e,r){V.cache++,(r||!nt)&&V.forEach(function(t){return at(t)&&t.cacheID++&&(t.rec=0)}),vt(e)&&(j.history.scrollRestoration=bo=e)},nt,ri=0,ra,Tc=function(){if(ra!==ri){var e=ra=ri;requestAnimationFrame(function(){return e===ri&&Gr(!0)})}},Rl=function(){le.appendChild(ki),To=!rt&&ki.offsetHeight||j.innerHeight,le.removeChild(ki)},ia=function(e){return gn(".gsap-marker-start, .gsap-marker-end, .gsap-marker-scroller-start, .gsap-marker-scroller-end").forEach(function(r){return r.style.display=e?"none":"block"})},Gr=function(e,r){if(Rt&&!e&&!rs){Ee(U,"scrollEnd",Dl);return}Rl(),nt=U.isRefreshing=!0,V.forEach(function(i){return at(i)&&++i.cacheID&&(i.rec=i())});var t=ai("refreshInit");vl&&U.sort(),r||ko(),V.forEach(function(i){at(i)&&(i.smooth&&(i.target.style.scrollBehavior="auto"),i(0))}),N.slice(0).forEach(function(i){return i.refresh()}),rs=!1,N.forEach(function(i){if(i._subPinOffset&&i.pin){var n=i.vars.horizontal?"offsetWidth":"offsetHeight",o=i.pin[n];i.revert(!0,1),i.adjustPinSpacing(i.pin[n]-o),i.refresh()}}),js=1,ia(!0),N.forEach(function(i){var n=er(i.scroller,i._dir),o=i.vars.end==="max"||i._endClamp&&i.end>n,a=i._startClamp&&i.start>=n;(o||a)&&i.setPositions(a?n-1:i.start,o?Math.max(a?n:i.start+1,n):i.end,!0)}),ia(!1),js=0,t.forEach(function(i){return i&&i.render&&i.render(-1)}),V.forEach(function(i){at(i)&&(i.smooth&&requestAnimationFrame(function(){return i.target.style.scrollBehavior="smooth"}),i.rec&&i(i.rec))}),Al(bo,1),ts.pause(),ri++,nt=2,dr(2),N.forEach(function(i){return at(i.vars.onRefresh)&&i.vars.onRefresh(i)}),nt=U.isRefreshing=!1,ai("refresh")},Zs=0,Hn=1,ln,dr=function(e){if(e===2||!nt&&!rs){U.isUpdating=!0,ln&&ln.update(0);var r=N.length,t=We(),i=t-ws>=50,n=r&&N[0].scroll();if(Hn=Zs>n?-1:1,nt||(Zs=n),i&&(Rt&&!cs&&t-Rt>200&&(Rt=0,ai("scrollEnd")),Xi=ws,ws=t),Hn<0){for(it=r;it-- >0;)N[it]&&N[it].update(0,i);Hn=1}else for(it=0;it<r;it++)N[it]&&N[it].update(0,i);U.isUpdating=!1}mn=0},Js=[Ml,Pl,wo,xo,Ot+an,Ot+nn,Ot+on,Ot+sn,"display","flexShrink","float","zIndex","gridColumnStart","gridColumnEnd","gridRowStart","gridRowEnd","gridArea","justifySelf","alignSelf","placeSelf","order"],Nn=Js.concat([ei,ti,"boxSizing","max"+Ri,"max"+So,"position",Ot,ke,ke+on,ke+nn,ke+an,ke+sn]),xc=function(e,r,t){Mi(t);var i=e._gsap;if(i.spacerIsNative)Mi(i.spacerState);else if(e._gsap.swappedIn){var n=r.parentNode;n&&(n.insertBefore(e,r),n.removeChild(r))}e._gsap.swappedIn=!1},ks=function(e,r,t,i){if(!e._gsap.swappedIn){for(var n=Js.length,o=r.style,a=e.style,l;n--;)l=Js[n],o[l]=t[l];o.position=t.position==="absolute"?"absolute":"relative",t.display==="inline"&&(o.display="inline-block"),a[wo]=a[xo]="auto",o.flexBasis=t.flexBasis||"auto",o.overflow="visible",o.boxSizing="border-box",o[ei]=is(e,ot)+Oe,o[ti]=is(e,De)+Oe,o[ke]=a[Ot]=a[Pl]=a[Ml]="0",Mi(i),a[ei]=a["max"+Ri]=t[ei],a[ti]=a["max"+So]=t[ti],a[ke]=t[ke],e.parentNode!==r&&(e.parentNode.insertBefore(r,e),r.appendChild(e)),e._gsap.swappedIn=!0}},wc=/([A-Z])/g,Mi=function(e){if(e){var r=e.t.style,t=e.length,i=0,n,o;for((e.t._gsap||A.core.getCache(e.t)).uncache=1;i<t;i+=2)o=e[i+1],n=e[i],o?r[n]=o:r[n]&&r.removeProperty(n.replace(wc,"-$1").toLowerCase())}},An=function(e){for(var r=Nn.length,t=e.style,i=[],n=0;n<r;n++)i.push(Nn[n],t[Nn[n]]);return i.t=e,i},Sc=function(e,r,t){for(var i=[],n=e.length,o=t?8:0,a;o<n;o+=2)a=e[o],i.push(a,a in r?r[a]:e[o+1]);return i.t=e.t,i},Yn={left:0,top:0},na=function(e,r,t,i,n,o,a,l,u,c,d,_,f,p){at(e)&&(e=e(l)),vt(e)&&e.substr(0,3)==="max"&&(e=_+(e.charAt(4)==="="?$n("0"+e.substr(3),t):0));var h=f?f.time():0,m,w,T;if(f&&f.seek(0),isNaN(e)||(e=+e),Qi(e))f&&(e=A.utils.mapRange(f.scrollTrigger.start,f.scrollTrigger.end,0,_,e)),a&&Bn(a,t,i,!0);else{at(r)&&(r=r(l));var C=(e||"0").split(" "),v,S,M,b;T=ut(r,l)||le,v=cr(T)||{},(!v||!v.left&&!v.top)&&Dt(T).display==="none"&&(b=T.style.display,T.style.display="block",v=cr(T),b?T.style.display=b:T.style.removeProperty("display")),S=$n(C[0],v[i.d]),M=$n(C[1]||"0",t),e=v[i.p]-u[i.p]-c+S+n-M,a&&Bn(a,M,i,t-M<20||a._isStart&&M>20),t-=t-M}if(p&&(l[p]=e||-.001,e<0&&(e=0)),o){var O=e+t,k=o._isStart;m="scroll"+i.d2,Bn(o,O,i,k&&O>20||!k&&(d?Math.max(le[m],Xt[m]):o.parentNode[m])<=O+1),d&&(u=cr(a),d&&(o.style[i.op.p]=u[i.op.p]-i.op.m-o._offset+Oe))}return f&&T&&(m=cr(T),f.seek(_),w=cr(T),f._caScrollDist=m[i.p]-w[i.p],e=e/f._caScrollDist*_),f&&f.seek(h),f?e:Math.round(e)},Cc=/(webkit|moz|length|cssText|inset)/i,sa=function(e,r,t,i){if(e.parentNode!==r){var n=e.style,o,a;if(r===le){e._stOrig=n.cssText,a=Dt(e);for(o in a)!+o&&!Cc.test(o)&&a[o]&&typeof n[o]=="string"&&o!=="0"&&(n[o]=a[o]);n.top=t,n.left=i}else n.cssText=e._stOrig;A.core.getCache(e).uncache=1,r.appendChild(e)}},El=function(e,r,t){var i=r,n=i;return function(o){var a=Math.round(e());return a!==i&&a!==n&&Math.abs(a-i)>3&&Math.abs(a-n)>3&&(o=a,t&&t()),n=i,i=o,o}},Rn=function(e,r,t){var i={};i[r.p]="+="+t,A.set(e,i)},oa=function(e,r){var t=qr(e,r),i="_scroll"+r.p2,n=function o(a,l,u,c,d){var _=o.tween,f=l.onComplete,p={};u=u||t();var h=El(t,u,function(){_.kill(),o.tween=0});return d=c&&d||0,c=c||a-u,_&&_.kill(),l[i]=a,l.inherit=!1,l.modifiers=p,p[i]=function(){return h(u+c*_.ratio+d*_.ratio*_.ratio)},l.onUpdate=function(){V.cache++,o.tween&&dr()},l.onComplete=function(){o.tween=0,f&&f.call(_)},_=o.tween=A.to(e,l),_};return e[i]=t,t.wheelHandler=function(){return n.tween&&n.tween.kill()&&(n.tween=0)},Ee(e,"wheel",t.wheelHandler),U.isTouch&&Ee(e,"touchmove",t.wheelHandler),n},U=function(){function s(r,t){gi||s.register(A)||console.warn("Please gsap.registerPlugin(ScrollTrigger)"),Gs(this),this.init(r,t)}var e=s.prototype;return e.init=function(t,i){if(this.progress=this.start=0,this.vars&&this.kill(!0,!0),!Vi){this.update=this.refresh=this.kill=jt;return}t=Jo(vt(t)||Qi(t)||t.nodeType?{trigger:t}:t,Dn);var n=t,o=n.onUpdate,a=n.toggleClass,l=n.id,u=n.onToggle,c=n.onRefresh,d=n.scrub,_=n.trigger,f=n.pin,p=n.pinSpacing,h=n.invalidateOnRefresh,m=n.anticipatePin,w=n.onScrubComplete,T=n.onSnapComplete,C=n.once,v=n.snap,S=n.pinReparent,M=n.pinSpacer,b=n.containerAnimation,O=n.fastScrollEnd,k=n.preventOverlaps,P=t.horizontal||t.containerAnimation&&t.horizontal!==!1?ot:De,z=!d&&d!==0,D=ut(t.scroller||j),W=A.core.getCache(D),Y=si(D),Z=("pinType"in t?t.pinType:Ar(D,"pinType")||Y&&"fixed")==="fixed",Q=[t.onEnter,t.onLeave,t.onEnterBack,t.onLeaveBack],q=z&&t.toggleActions.split(" "),J="markers"in t?t.markers:Dn.markers,oe=Y?0:parseFloat(Dt(D)["border"+P.p2+Ri])||0,g=this,ae=t.onRefreshInit&&function(){return t.onRefreshInit(g)},je=pc(D,Y,P),qt=gc(D,Y),pe=0,Fe=0,Ke=0,we=qr(D,P),Ze,He,pr,Je,It,ee,ye,lt,pt,y,gt,nr,gr,Se,mr,Fr,ui,Le,yr,ve,Ft,kt,sr,Ei,Ce,vn,vr,ci,fi,br,zr,X,$r,zt,$t,Bt,Br,di,or;if(g._startClamp=g._endClamp=!1,g._dir=P,m*=45,g.scroller=D,g.scroll=b?b.time.bind(b):we,Je=we(),g.vars=t,i=i||t.animation,"refreshPriority"in t&&(vl=1,t.refreshPriority===-9999&&(ln=g)),W.tweenScroll=W.tweenScroll||{top:oa(D,De),left:oa(D,ot)},g.tweenTo=Ze=W.tweenScroll[P.p],g.scrubDuration=function(L){$r=Qi(L)&&L,$r?X?X.duration(L):X=A.to(i,{ease:"expo",totalProgress:"+=0",inherit:!1,duration:$r,paused:!0,onComplete:function(){return w&&w(g)}}):(X&&X.progress(1).kill(),X=0)},i&&(i.vars.lazy=!1,i._initted&&!g.isReverted||i.vars.immediateRender!==!1&&t.immediateRender!==!1&&i.duration()&&i.render(0,!0,!0),g.animation=i.pause(),i.scrollTrigger=g,g.scrubDuration(d),br=0,l||(l=i.vars.id)),v&&((!Qr(v)||v.push)&&(v={snapTo:v}),"scrollBehavior"in le.style&&A.set(Y?[le,Xt]:D,{scrollBehavior:"auto"}),V.forEach(function(L){return at(L)&&L.target===(Y?de.scrollingElement||Xt:D)&&(L.smooth=!1)}),pr=at(v.snapTo)?v.snapTo:v.snapTo==="labels"?yc(i):v.snapTo==="labelsDirectional"?vc(i):v.directional!==!1?function(L,B){return Co(v.snapTo)(L,We()-Fe<500?0:B.direction)}:A.utils.snap(v.snapTo),zt=v.duration||{min:.1,max:2},zt=Qr(zt)?rn(zt.min,zt.max):rn(zt,zt),$t=A.delayedCall(v.delay||$r/2||.1,function(){var L=we(),B=We()-Fe<500,R=Ze.tween;if((B||Math.abs(g.getVelocity())<10)&&!R&&!cs&&pe!==L){var I=(L-ee)/Se,Ae=i&&!z?i.totalProgress():I,G=B?0:(Ae-zr)/(We()-Xi)*1e3||0,be=A.utils.clamp(-I,1-I,_i(G/2)*G/.185),Ne=I+(v.inertia===!1?0:be),ge,ue,ie=v,Ht=ie.onStart,fe=ie.onInterrupt,mt=ie.onComplete;if(ge=pr(Ne,g),Qi(ge)||(ge=Ne),ue=Math.round(ee+ge*Se),L<=ye&&L>=ee&&ue!==L){if(R&&!R._initted&&R.data<=_i(ue-L))return;v.inertia===!1&&(be=ge-I),Ze(ue,{duration:zt(_i(Math.max(_i(Ne-Ae),_i(ge-Ae))*.185/G/.05||0)),ease:v.ease||"power3",data:_i(ue-L),onInterrupt:function(){return $t.restart(!0)&&fe&&fe(g)},onComplete:function(){g.update(),pe=we(),i&&(X?X.resetTo("totalProgress",ge,i._tTime/i._tDur):i.progress(ge)),br=zr=i&&!z?i.totalProgress():g.progress,T&&T(g),mt&&mt(g)}},L,be*Se,ue-L-be*Se),Ht&&Ht(g,Ze.tween)}}else g.isActive&&pe!==L&&$t.restart(!0)}).pause()),l&&(Ks[l]=g),_=g.trigger=ut(_||f!==!0&&f),or=_&&_._gsap&&_._gsap.stRevert,or&&(or=or(g)),f=f===!0?_:ut(f),vt(a)&&(a={targets:_,className:a}),f&&(p===!1||p===Ot||(p=!p&&f.parentNode&&f.parentNode.style&&Dt(f.parentNode).display==="flex"?!1:ke),g.pin=f,He=A.core.getCache(f),He.spacer?mr=He.pinState:(M&&(M=ut(M),M&&!M.nodeType&&(M=M.current||M.nativeElement),He.spacerIsNative=!!M,M&&(He.spacerState=An(M))),He.spacer=Le=M||de.createElement("div"),Le.classList.add("pin-spacer"),l&&Le.classList.add("pin-spacer-"+l),He.pinState=mr=An(f)),t.force3D!==!1&&A.set(f,{force3D:!0}),g.spacer=Le=He.spacer,fi=Dt(f),Ei=fi[p+P.os2],ve=A.getProperty(f),Ft=A.quickSetter(f,P.a,Oe),ks(f,Le,fi),ui=An(f)),J){nr=Qr(J)?Jo(J,ea):ea,y=Ln("scroller-start",l,D,P,nr,0),gt=Ln("scroller-end",l,D,P,nr,0,y),yr=y["offset"+P.op.d2];var qi=ut(Ar(D,"content")||D);lt=this.markerStart=Ln("start",l,qi,P,nr,yr,0,b),pt=this.markerEnd=Ln("end",l,qi,P,nr,yr,0,b),b&&(di=A.quickSetter([lt,pt],P.a,Oe)),!Z&&!(rr.length&&Ar(D,"fixedMarkers")===!0)&&(mc(Y?le:D),A.set([y,gt],{force3D:!0}),vn=A.quickSetter(y,P.a,Oe),ci=A.quickSetter(gt,P.a,Oe))}if(b){var $=b.vars.onUpdate,E=b.vars.onUpdateParams;b.eventCallback("onUpdate",function(){g.update(0,0,1),$&&$.apply(b,E||[])})}if(g.previous=function(){return N[N.indexOf(g)-1]},g.next=function(){return N[N.indexOf(g)+1]},g.revert=function(L,B){if(!B)return g.kill(!0);var R=L!==!1||!g.enabled,I=Ve;R!==g.isReverted&&(R&&(Bt=Math.max(we(),g.scroll.rec||0),Ke=g.progress,Br=i&&i.progress()),lt&&[lt,pt,y,gt].forEach(function(Ae){return Ae.style.display=R?"none":"block"}),R&&(Ve=g,g.update(R)),f&&(!S||!g.isActive)&&(R?xc(f,Le,mr):ks(f,Le,Dt(f),Ce)),R||g.update(R),Ve=I,g.isReverted=R)},g.refresh=function(L,B,R,I){if(!((Ve||!g.enabled)&&!B)){if(f&&L&&Rt){Ee(s,"scrollEnd",Dl);return}!nt&&ae&&ae(g),Ve=g,Ze.tween&&!R&&(Ze.tween.kill(),Ze.tween=0),X&&X.pause(),h&&i&&i.revert({kill:!1}).invalidate(),g.isReverted||g.revert(!0,!0),g._subPinOffset=!1;var Ae=je(),G=qt(),be=b?b.duration():er(D,P),Ne=Se<=.01,ge=0,ue=I||0,ie=Qr(R)?R.end:t.end,Ht=t.endTrigger||_,fe=Qr(R)?R.start:t.start||(t.start===0||!_?0:f?"0 0":"0 100%"),mt=g.pinnedContainer=t.pinnedContainer&&ut(t.pinnedContainer,g),Vt=_&&Math.max(0,N.indexOf(g))||0,ze=Vt,$e,Ye,Hr,bn,Xe,Pe,Wt,fs,Mo,Ii,Qt,Fi,Tn;for(J&&Qr(R)&&(Fi=A.getProperty(y,P.p),Tn=A.getProperty(gt,P.p));ze--;)Pe=N[ze],Pe.end||Pe.refresh(0,1)||(Ve=g),Wt=Pe.pin,Wt&&(Wt===_||Wt===f||Wt===mt)&&!Pe.isReverted&&(Ii||(Ii=[]),Ii.unshift(Pe),Pe.revert(!0,!0)),Pe!==N[ze]&&(Vt--,ze--);for(at(fe)&&(fe=fe(g)),fe=Go(fe,"start",g),ee=na(fe,_,Ae,P,we(),lt,y,g,G,oe,Z,be,b,g._startClamp&&"_startClamp")||(f?-.001:0),at(ie)&&(ie=ie(g)),vt(ie)&&!ie.indexOf("+=")&&(~ie.indexOf(" ")?ie=(vt(fe)?fe.split(" ")[0]:"")+ie:(ge=$n(ie.substr(2),Ae),ie=vt(fe)?fe:(b?A.utils.mapRange(0,b.duration(),b.scrollTrigger.start,b.scrollTrigger.end,ee):ee)+ge,Ht=_)),ie=Go(ie,"end",g),ye=Math.max(ee,na(ie||(Ht?"100% 0":be),Ht,Ae,P,we()+ge,pt,gt,g,G,oe,Z,be,b,g._endClamp&&"_endClamp"))||-.001,ge=0,ze=Vt;ze--;)Pe=N[ze],Wt=Pe.pin,Wt&&Pe.start-Pe._pinPush<=ee&&!b&&Pe.end>0&&($e=Pe.end-(g._startClamp?Math.max(0,Pe.start):Pe.start),(Wt===_&&Pe.start-Pe._pinPush<ee||Wt===mt)&&isNaN(fe)&&(ge+=$e*(1-Pe.progress)),Wt===f&&(ue+=$e));if(ee+=ge,ye+=ge,g._startClamp&&(g._startClamp+=ge),g._endClamp&&!nt&&(g._endClamp=ye||-.001,ye=Math.min(ye,er(D,P))),Se=ye-ee||(ee-=.01)&&.001,Ne&&(Ke=A.utils.clamp(0,1,A.utils.normalize(ee,ye,Bt))),g._pinPush=ue,lt&&ge&&($e={},$e[P.a]="+="+ge,mt&&($e[P.p]="-="+we()),A.set([lt,pt],$e)),f&&!(js&&g.end>=er(D,P)))$e=Dt(f),bn=P===De,Hr=we(),kt=parseFloat(ve(P.a))+ue,!be&&ye>1&&(Qt=(Y?de.scrollingElement||Xt:D).style,Qt={style:Qt,value:Qt["overflow"+P.a.toUpperCase()]},Y&&Dt(le)["overflow"+P.a.toUpperCase()]!=="scroll"&&(Qt.style["overflow"+P.a.toUpperCase()]="scroll")),ks(f,Le,$e),ui=An(f),Ye=cr(f,!0),fs=Z&&qr(D,bn?ot:De)(),p?(Ce=[p+P.os2,Se+ue+Oe],Ce.t=Le,ze=p===ke?is(f,P)+Se+ue:0,ze&&(Ce.push(P.d,ze+Oe),Le.style.flexBasis!=="auto"&&(Le.style.flexBasis=ze+Oe)),Mi(Ce),mt&&N.forEach(function(zi){zi.pin===mt&&zi.vars.pinSpacing!==!1&&(zi._subPinOffset=!0)}),Z&&we(Bt)):(ze=is(f,P),ze&&Le.style.flexBasis!=="auto"&&(Le.style.flexBasis=ze+Oe)),Z&&(Xe={top:Ye.top+(bn?Hr-ee:fs)+Oe,left:Ye.left+(bn?fs:Hr-ee)+Oe,boxSizing:"border-box",position:"fixed"},Xe[ei]=Xe["max"+Ri]=Math.ceil(Ye.width)+Oe,Xe[ti]=Xe["max"+So]=Math.ceil(Ye.height)+Oe,Xe[Ot]=Xe[Ot+on]=Xe[Ot+nn]=Xe[Ot+an]=Xe[Ot+sn]="0",Xe[ke]=$e[ke],Xe[ke+on]=$e[ke+on],Xe[ke+nn]=$e[ke+nn],Xe[ke+an]=$e[ke+an],Xe[ke+sn]=$e[ke+sn],Fr=Sc(mr,Xe,S),nt&&we(0)),i?(Mo=i._initted,Ts(1),i.render(i.duration(),!0,!0),sr=ve(P.a)-kt+Se+ue,vr=Math.abs(Se-sr)>1,Z&&vr&&Fr.splice(Fr.length-2,2),i.render(0,!0,!0),Mo||i.invalidate(!0),i.parent||i.totalTime(i.totalTime()),Ts(0)):sr=Se,Qt&&(Qt.value?Qt.style["overflow"+P.a.toUpperCase()]=Qt.value:Qt.style.removeProperty("overflow-"+P.a));else if(_&&we()&&!b)for(Ye=_.parentNode;Ye&&Ye!==le;)Ye._pinOffset&&(ee-=Ye._pinOffset,ye-=Ye._pinOffset),Ye=Ye.parentNode;Ii&&Ii.forEach(function(zi){return zi.revert(!1,!0)}),g.start=ee,g.end=ye,Je=It=nt?Bt:we(),!b&&!nt&&(Je<Bt&&we(Bt),g.scroll.rec=0),g.revert(!1,!0),Fe=We(),$t&&(pe=-1,$t.restart(!0)),Ve=0,i&&z&&(i._initted||Br)&&i.progress()!==Br&&i.progress(Br||0,!0).render(i.time(),!0,!0),(Ne||Ke!==g.progress||b||h)&&(i&&!z&&i.totalProgress(b&&ee<-.001&&!Ke?A.utils.normalize(ee,ye,0):Ke,!0),g.progress=Ne||(Je-ee)/Se===Ke?0:Ke),f&&p&&(Le._pinOffset=Math.round(g.progress*sr)),X&&X.invalidate(),isNaN(Fi)||(Fi-=A.getProperty(y,P.p),Tn-=A.getProperty(gt,P.p),Rn(y,P,Fi),Rn(lt,P,Fi-(I||0)),Rn(gt,P,Tn),Rn(pt,P,Tn-(I||0))),Ne&&!nt&&g.update(),c&&!nt&&!gr&&(gr=!0,c(g),gr=!1)}},g.getVelocity=function(){return(we()-It)/(We()-Xi)*1e3||0},g.endAnimation=function(){Hi(g.callbackAnimation),i&&(X?X.progress(1):i.paused()?z||Hi(i,g.direction<0,1):Hi(i,i.reversed()))},g.labelToScroll=function(L){return i&&i.labels&&(ee||g.refresh()||ee)+i.labels[L]/i.duration()*Se||0},g.getTrailing=function(L){var B=N.indexOf(g),R=g.direction>0?N.slice(0,B).reverse():N.slice(B+1);return(vt(L)?R.filter(function(I){return I.vars.preventOverlaps===L}):R).filter(function(I){return g.direction>0?I.end<=ee:I.start>=ye})},g.update=function(L,B,R){if(!(b&&!R&&!L)){var I=nt===!0?Bt:g.scroll(),Ae=L?0:(I-ee)/Se,G=Ae<0?0:Ae>1?1:Ae||0,be=g.progress,Ne,ge,ue,ie,Ht,fe,mt,Vt;if(B&&(It=Je,Je=b?we():I,v&&(zr=br,br=i&&!z?i.totalProgress():G)),m&&f&&!Ve&&!kn&&Rt&&(!G&&ee<I+(I-It)/(We()-Xi)*m?G=1e-4:G===1&&ye>I+(I-It)/(We()-Xi)*m&&(G=.9999)),G!==be&&g.enabled){if(Ne=g.isActive=!!G&&G<1,ge=!!be&&be<1,fe=Ne!==ge,Ht=fe||!!G!=!!be,g.direction=G>be?1:-1,g.progress=G,Ht&&!Ve&&(ue=G&&!be?0:G===1?1:be===1?2:3,z&&(ie=!fe&&q[ue+1]!=="none"&&q[ue+1]||q[ue],Vt=i&&(ie==="complete"||ie==="reset"||ie in i))),k&&(fe||Vt)&&(Vt||d||!i)&&(at(k)?k(g):g.getTrailing(k).forEach(function(Hr){return Hr.endAnimation()})),z||(X&&!Ve&&!kn?(X._dp._time-X._start!==X._time&&X.render(X._dp._time-X._start),X.resetTo?X.resetTo("totalProgress",G,i._tTime/i._tDur):(X.vars.totalProgress=G,X.invalidate().restart())):i&&i.totalProgress(G,!!(Ve&&(Fe||L)))),f){if(L&&p&&(Le.style[p+P.os2]=Ei),!Z)Ft(Wi(kt+sr*G));else if(Ht){if(mt=!L&&G>be&&ye+1>I&&I+1>=er(D,P),S)if(!L&&(Ne||mt)){var ze=cr(f,!0),$e=I-ee;sa(f,le,ze.top+(P===De?$e:0)+Oe,ze.left+(P===De?0:$e)+Oe)}else sa(f,Le);Mi(Ne||mt?Fr:ui),vr&&G<1&&Ne||Ft(kt+(G===1&&!mt?sr:0))}}v&&!Ze.tween&&!Ve&&!kn&&$t.restart(!0),a&&(fe||C&&G&&(G<1||!xs))&&gn(a.targets).forEach(function(Hr){return Hr.classList[Ne||C?"add":"remove"](a.className)}),o&&!z&&!L&&o(g),Ht&&!Ve?(z&&(Vt&&(ie==="complete"?i.pause().totalProgress(1):ie==="reset"?i.restart(!0).pause():ie==="restart"?i.restart(!0):i[ie]()),o&&o(g)),(fe||!xs)&&(u&&fe&&Ss(g,u),Q[ue]&&Ss(g,Q[ue]),C&&(G===1?g.kill(!1,1):Q[ue]=0),fe||(ue=G===1?1:3,Q[ue]&&Ss(g,Q[ue]))),O&&!Ne&&Math.abs(g.getVelocity())>(Qi(O)?O:2500)&&(Hi(g.callbackAnimation),X?X.progress(1):Hi(i,ie==="reverse"?1:!G,1))):z&&o&&!Ve&&o(g)}if(ci){var Ye=b?I/b.duration()*(b._caScrollDist||0):I;vn(Ye+(y._isFlipped?1:0)),ci(Ye)}di&&di(-I/b.duration()*(b._caScrollDist||0))}},g.enable=function(L,B){g.enabled||(g.enabled=!0,Ee(D,"resize",Ui),Y||Ee(D,"scroll",pi),ae&&Ee(s,"refreshInit",ae),L!==!1&&(g.progress=Ke=0,Je=It=pe=we()),B!==!1&&g.refresh())},g.getTween=function(L){return L&&Ze?Ze.tween:X},g.setPositions=function(L,B,R,I){if(b){var Ae=b.scrollTrigger,G=b.duration(),be=Ae.end-Ae.start;L=Ae.start+be*L/G,B=Ae.start+be*B/G}g.refresh(!1,!1,{start:jo(L,R&&!!g._startClamp),end:jo(B,R&&!!g._endClamp)},I),g.update()},g.adjustPinSpacing=function(L){if(Ce&&L){var B=Ce.indexOf(P.d)+1;Ce[B]=parseFloat(Ce[B])+L+Oe,Ce[1]=parseFloat(Ce[1])+L+Oe,Mi(Ce)}},g.disable=function(L,B){if(g.enabled&&(L!==!1&&g.revert(!0,!0),g.enabled=g.isActive=!1,B||X&&X.pause(),Bt=0,He&&(He.uncache=1),ae&&Re(s,"refreshInit",ae),$t&&($t.pause(),Ze.tween&&Ze.tween.kill()&&(Ze.tween=0)),!Y)){for(var R=N.length;R--;)if(N[R].scroller===D&&N[R]!==g)return;Re(D,"resize",Ui),Y||Re(D,"scroll",pi)}},g.kill=function(L,B){g.disable(L,B),X&&!B&&X.kill(),l&&delete Ks[l];var R=N.indexOf(g);R>=0&&N.splice(R,1),R===it&&Hn>0&&it--,R=0,N.forEach(function(I){return I.scroller===g.scroller&&(R=1)}),R||nt||(g.scroll.rec=0),i&&(i.scrollTrigger=null,L&&i.revert({kill:!1}),B||i.kill()),lt&&[lt,pt,y,gt].forEach(function(I){return I.parentNode&&I.parentNode.removeChild(I)}),ln===g&&(ln=0),f&&(He&&(He.uncache=1),R=0,N.forEach(function(I){return I.pin===f&&R++}),R||(He.spacer=0)),t.onKill&&t.onKill(g)},N.push(g),g.enable(!1,!1),or&&or(g),i&&i.add&&!Se){var te=g.update;g.update=function(){g.update=te,ee||ye||g.refresh()},A.delayedCall(.01,g.update),Se=.01,ee=ye=0}else g.refresh();f&&Tc()},s.register=function(t){return gi||(A=t||Sl(),wl()&&window.document&&s.enable(),gi=Vi),gi},s.defaults=function(t){if(t)for(var i in t)Dn[i]=t[i];return Dn},s.disable=function(t,i){Vi=0,N.forEach(function(o){return o[i?"kill":"disable"](t)}),Re(j,"wheel",pi),Re(de,"scroll",pi),clearInterval(Cn),Re(de,"touchcancel",jt),Re(le,"touchstart",jt),Pn(Re,de,"pointerdown,touchstart,mousedown",Ko),Pn(Re,de,"pointerup,touchend,mouseup",Zo),ts.kill(),Mn(Re);for(var n=0;n<V.length;n+=3)On(Re,V[n],V[n+1]),On(Re,V[n],V[n+2])},s.enable=function(){if(j=window,de=document,Xt=de.documentElement,le=de.body,A&&(gn=A.utils.toArray,rn=A.utils.clamp,Gs=A.core.context||jt,Ts=A.core.suppressOverwrites||jt,bo=j.history.scrollRestoration||"auto",Zs=j.pageYOffset,A.core.globals("ScrollTrigger",s),le)){Vi=1,ki=document.createElement("div"),ki.style.height="100vh",ki.style.position="absolute",Rl(),_c(),xe.register(A),s.isTouch=xe.isTouch,Tr=xe.isTouch&&/(iPad|iPhone|iPod|Mac)/g.test(navigator.userAgent),Us=xe.isTouch===1,Ee(j,"wheel",pi),yl=[j,de,Xt,le],A.matchMedia?(s.matchMedia=function(l){var u=A.matchMedia(),c;for(c in l)u.add(c,l[c]);return u},A.addEventListener("matchMediaInit",function(){return ko()}),A.addEventListener("matchMediaRevert",function(){return Ll()}),A.addEventListener("matchMedia",function(){Gr(0,1),ai("matchMedia")}),A.matchMedia("(orientation: portrait)",function(){return Cs(),Cs})):console.warn("Requires GSAP 3.11.0 or later"),Cs(),Ee(de,"scroll",pi);var t=le.style,i=t.borderTopStyle,n=A.core.Animation.prototype,o,a;for(n.revert||Object.defineProperty(n,"revert",{value:function(){return this.time(-.01,!0)}}),t.borderTopStyle="solid",o=cr(le),De.m=Math.round(o.top+De.sc())||0,ot.m=Math.round(o.left+ot.sc())||0,i?t.borderTopStyle=i:t.removeProperty("border-top-style"),Cn=setInterval(ta,250),A.delayedCall(.5,function(){return kn=0}),Ee(de,"touchcancel",jt),Ee(le,"touchstart",jt),Pn(Ee,de,"pointerdown,touchstart,mousedown",Ko),Pn(Ee,de,"pointerup,touchend,mouseup",Zo),Qs=A.utils.checkPrefix("transform"),Nn.push(Qs),gi=We(),ts=A.delayedCall(.2,Gr).pause(),mi=[de,"visibilitychange",function(){var l=j.innerWidth,u=j.innerHeight;de.hidden?(Qo=l,Uo=u):(Qo!==l||Uo!==u)&&Ui()},de,"DOMContentLoaded",Gr,j,"load",Gr,j,"resize",Ui],Mn(Ee),N.forEach(function(l){return l.enable(0,1)}),a=0;a<V.length;a+=3)On(Re,V[a],V[a+1]),On(Re,V[a],V[a+2])}},s.config=function(t){"limitCallbacks"in t&&(xs=!!t.limitCallbacks);var i=t.syncInterval;i&&clearInterval(Cn)||(Cn=i)&&setInterval(ta,i),"ignoreMobileResize"in t&&(Us=s.isTouch===1&&t.ignoreMobileResize),"autoRefreshEvents"in t&&(Mn(Re)||Mn(Ee,t.autoRefreshEvents||"none"),bl=(t.autoRefreshEvents+"").indexOf("resize")===-1)},s.scrollerProxy=function(t,i){var n=ut(t),o=V.indexOf(n),a=si(n);~o&&V.splice(o,a?6:2),i&&(a?rr.unshift(j,i,le,i,Xt,i):rr.unshift(n,i))},s.clearMatchMedia=function(t){N.forEach(function(i){return i._ctx&&i._ctx.query===t&&i._ctx.kill(!0,!0)})},s.isInViewport=function(t,i,n){var o=(vt(t)?ut(t):t).getBoundingClientRect(),a=o[n?ei:ti]*i||0;return n?o.right-a>0&&o.left+a<j.innerWidth:o.bottom-a>0&&o.top+a<j.innerHeight},s.positionInViewport=function(t,i,n){vt(t)&&(t=ut(t));var o=t.getBoundingClientRect(),a=o[n?ei:ti],l=i==null?a/2:i in ns?ns[i]*a:~i.indexOf("%")?parseFloat(i)*a/100:parseFloat(i)||0;return n?(o.left+l)/j.innerWidth:(o.top+l)/j.innerHeight},s.killAll=function(t){if(N.slice(0).forEach(function(n){return n.vars.id!=="ScrollSmoother"&&n.kill()}),t!==!0){var i=oi.killAll||[];oi={},i.forEach(function(n){return n()})}},s}();U.version="3.12.5";U.saveStyles=function(s){return s?gn(s).forEach(function(e){if(e&&e.style){var r=yt.indexOf(e);r>=0&&yt.splice(r,5),yt.push(e,e.style.cssText,e.getBBox&&e.getAttribute("transform"),A.core.getCache(e),Gs())}}):yt};U.revert=function(s,e){return ko(!s,e)};U.create=function(s,e){return new U(s,e)};U.refresh=function(s){return s?Ui():(gi||U.register())&&Gr(!0)};U.update=function(s){return++V.cache&&dr(s===!0?2:0)};U.clearScrollMemory=Al;U.maxScroll=function(s,e){return er(s,e?ot:De)};U.getScrollFunc=function(s,e){return qr(ut(s),e?ot:De)};U.getById=function(s){return Ks[s]};U.getAll=function(){return N.filter(function(s){return s.vars.id!=="ScrollSmoother"})};U.isScrolling=function(){return!!Rt};U.snapDirectional=Co;U.addEventListener=function(s,e){var r=oi[s]||(oi[s]=[]);~r.indexOf(e)||r.push(e)};U.removeEventListener=function(s,e){var r=oi[s],t=r&&r.indexOf(e);t>=0&&r.splice(t,1)};U.batch=function(s,e){var r=[],t={},i=e.interval||.016,n=e.batchMax||1e9,o=function(u,c){var d=[],_=[],f=A.delayedCall(i,function(){c(d,_),d=[],_=[]}).pause();return function(p){d.length||f.restart(!0),d.push(p.trigger),_.push(p),n<=d.length&&f.progress(1)}},a;for(a in e)t[a]=a.substr(0,2)==="on"&&at(e[a])&&a!=="onRefreshInit"?o(a,e[a]):e[a];return at(n)&&(n=n(),Ee(U,"refresh",function(){return n=e.batchMax()})),gn(s).forEach(function(l){var u={};for(a in t)u[a]=t[a];u.trigger=l,r.push(U.create(u))}),r};var aa=function(e,r,t,i){return r>i?e(i):r<0&&e(0),t>i?(i-r)/(t-r):t<0?r/(r-t):1},Ms=function s(e,r){r===!0?e.style.removeProperty("touch-action"):e.style.touchAction=r===!0?"auto":r?"pan-"+r+(xe.isTouch?" pinch-zoom":""):"none",e===Xt&&s(le,r)},En={auto:1,scroll:1},kc=function(e){var r=e.event,t=e.target,i=e.axis,n=(r.changedTouches?r.changedTouches[0]:r).target,o=n._gsap||A.core.getCache(n),a=We(),l;if(!o._isScrollT||a-o._isScrollT>2e3){for(;n&&n!==le&&(n.scrollHeight<=n.clientHeight&&n.scrollWidth<=n.clientWidth||!(En[(l=Dt(n)).overflowY]||En[l.overflowX]));)n=n.parentNode;o._isScroll=n&&n!==t&&!si(n)&&(En[(l=Dt(n)).overflowY]||En[l.overflowX]),o._isScrollT=a}(o._isScroll||i==="x")&&(r.stopPropagation(),r._gsapAllow=!0)},ql=function(e,r,t,i){return xe.create({target:e,capture:!0,debounce:!1,lockAxis:!0,type:r,onWheel:i=i&&kc,onPress:i,onDrag:i,onScroll:i,onEnable:function(){return t&&Ee(de,xe.eventTypes[0],ua,!1,!0)},onDisable:function(){return Re(de,xe.eventTypes[0],ua,!0)}})},Mc=/(input|label|select|textarea)/i,la,ua=function(e){var r=Mc.test(e.target.tagName);(r||la)&&(e._gsapAllow=!0,la=r)},Pc=function(e){Qr(e)||(e={}),e.preventDefault=e.isNormalizer=e.allowClicks=!0,e.type||(e.type="wheel,touch"),e.debounce=!!e.debounce,e.id=e.id||"normalizer";var r=e,t=r.normalizeScrollX,i=r.momentum,n=r.allowNestedScroll,o=r.onRelease,a,l,u=ut(e.target)||Xt,c=A.core.globals().ScrollSmoother,d=c&&c.get(),_=Tr&&(e.content&&ut(e.content)||d&&e.content!==!1&&!d.smooth()&&d.content()),f=qr(u,De),p=qr(u,ot),h=1,m=(xe.isTouch&&j.visualViewport?j.visualViewport.scale*j.visualViewport.width:j.outerWidth)/j.innerWidth,w=0,T=at(i)?function(){return i(a)}:function(){return i||2.8},C,v,S=ql(u,e.type,!0,n),M=function(){return v=!1},b=jt,O=jt,k=function(){l=er(u,De),O=rn(Tr?1:0,l),t&&(b=rn(0,er(u,ot))),C=ri},P=function(){_._gsap.y=Wi(parseFloat(_._gsap.y)+f.offset)+"px",_.style.transform="matrix3d(1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1, 0, 0, "+parseFloat(_._gsap.y)+", 0, 1)",f.offset=f.cacheID=0},z=function(){if(v){requestAnimationFrame(M);var J=Wi(a.deltaY/2),oe=O(f.v-J);if(_&&oe!==f.v+f.offset){f.offset=oe-f.v;var g=Wi((parseFloat(_&&_._gsap.y)||0)-f.offset);_.style.transform="matrix3d(1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1, 0, 0, "+g+", 0, 1)",_._gsap.y=g+"px",f.cacheID=V.cache,dr()}return!0}f.offset&&P(),v=!0},D,W,Y,Z,Q=function(){k(),D.isActive()&&D.vars.scrollY>l&&(f()>l?D.progress(1)&&f(l):D.resetTo("scrollY",l))};return _&&A.set(_,{y:"+=0"}),e.ignoreCheck=function(q){return Tr&&q.type==="touchmove"&&z()||h>1.05&&q.type!=="touchstart"||a.isGesturing||q.touches&&q.touches.length>1},e.onPress=function(){v=!1;var q=h;h=Wi((j.visualViewport&&j.visualViewport.scale||1)/m),D.pause(),q!==h&&Ms(u,h>1.01?!0:t?!1:"x"),W=p(),Y=f(),k(),C=ri},e.onRelease=e.onGestureStart=function(q,J){if(f.offset&&P(),!J)Z.restart(!0);else{V.cache++;var oe=T(),g,ae;t&&(g=p(),ae=g+oe*.05*-q.velocityX/.227,oe*=aa(p,g,ae,er(u,ot)),D.vars.scrollX=b(ae)),g=f(),ae=g+oe*.05*-q.velocityY/.227,oe*=aa(f,g,ae,er(u,De)),D.vars.scrollY=O(ae),D.invalidate().duration(oe).play(.01),(Tr&&D.vars.scrollY>=l||g>=l-1)&&A.to({},{onUpdate:Q,duration:oe})}o&&o(q)},e.onWheel=function(){D._ts&&D.pause(),We()-w>1e3&&(C=0,w=We())},e.onChange=function(q,J,oe,g,ae){if(ri!==C&&k(),J&&t&&p(b(g[2]===J?W+(q.startX-q.x):p()+J-g[1])),oe){f.offset&&P();var je=ae[2]===oe,qt=je?Y+q.startY-q.y:f()+oe-ae[1],pe=O(qt);je&&qt!==pe&&(Y+=pe-qt),f(pe)}(oe||J)&&dr()},e.onEnable=function(){Ms(u,t?!1:"x"),U.addEventListener("refresh",Q),Ee(j,"resize",Q),f.smooth&&(f.target.style.scrollBehavior="auto",f.smooth=p.smooth=!1),S.enable()},e.onDisable=function(){Ms(u,!0),Re(j,"resize",Q),U.removeEventListener("refresh",Q),S.kill()},e.lockAxis=e.lockAxis!==!1,a=new xe(e),a.iOS=Tr,Tr&&!f()&&f(1),Tr&&A.ticker.add(jt),Z=a._dc,D=A.to(a,{ease:"power4",paused:!0,inherit:!1,scrollX:t?"+=0.1":"+=0",scrollY:"+=0.1",modifiers:{scrollY:El(f,f(),function(){return D.pause()})},onUpdate:dr,onComplete:Z.vars.onComplete}),a};U.sort=function(s){return N.sort(s||function(e,r){return(e.vars.refreshPriority||0)*-1e6+e.start-(r.start+(r.vars.refreshPriority||0)*-1e6)})};U.observe=function(s){return new xe(s)};U.normalizeScroll=function(s){if(typeof s=="undefined")return rt;if(s===!0&&rt)return rt.enable();if(s===!1){rt&&rt.kill(),rt=s;return}var e=s instanceof xe?s:Pc(s);return rt&&rt.target===e.target&&rt.kill(),si(e.target)&&(rt=e),e};U.core={_getVelocityProp:Ws,_inputObserver:ql,_scrollers:V,_proxies:rr,bridge:{ss:function(){Rt||ai("scrollStart"),Rt=We()},ref:function(){return Ve}}};Sl()&&A.registerPlugin(U);const Oc=eo({name:"BoxListComponent",components:{ListComponent:ss},props:{listItems:{type:Array,required:!0},backgroundClass:{type:String,default:"bg-secondary"},textClass:{type:String,default:"text-white"}}}),Dc={class:"container"},Lc={class:"row q-px-xxl q-py-xl items-center q-col-gutter-lg q-col-gutter-xl-xl"},Ac={class:"col-12 col-md-4 col-lg-3"},Rc={class:"col-12 col-md-8 col-lg-9"};function Ec(s,e,r,t,i,n){const o=ur("ListComponent");return H(),re("div",Dc,[(H(!0),re(Kt,null,Or(s.listItems,(a,l)=>(H(),re("div",{key:l,class:wr([s.backgroundClass,"q-mb-md rounded-borders overflow-hidden"]),"data-aos":"zoom-in"},[x("div",Lc,[x("div",Ac,[x("h4",{class:wr([s.textClass,"Citi-Sans-Display-Bold callout q-mt-sm"]),style:yi({fontSize:s.$q.screen.gt.md?"1.5rem":null})},Hl(a.title),7)]),x("div",Rc,[F(o,{propFirstLevel:"p",propListData:a.paragraphs,propUlClass:"q-mb-none",propMarginFirstLevel:"1rem",propMarginSecondLevel:"",class:wr(s.textClass),"prop-animation":!1},null,8,["propListData","class"])])])],2))),128))])}var qc=os(Oc,[["render",Ec]]);const Ic={name:"ImageBoxTextBox",components:{ListComponent:ss},props:{imagePath:{type:String,required:!0},title:{type:String,required:!0},paragraphs:{type:[Array,String],required:!0},textRight:{type:Boolean,default:!0},imageAlwaysTopOnMobile:{type:Boolean,default:!0},titleClass:{type:String,default:"text-secondary"},backgroundClass:{type:String,default:"bg-primary"},textClass:{type:String,default:"text-white"}},computed:{orderedColumns(){const s=[{type:"image"},{type:"text"}];return this.textRight?s:s.reverse()}}},Fc={class:"container"},zc={class:"row items-stretch q-col-gutter-lg"},$c=["data-aos"],Bc=["innerHTML"];function Hc(s,e,r,t,i,n){const o=ur("ListComponent");return H(),re("div",Fc,[x("div",zc,[(H(!0),re(Kt,null,Or(n.orderedColumns,(a,l)=>(H(),re("div",{key:l,class:wr(["col-12 col-md-6",[a.type==="image"?`order-${r.imageAlwaysTopOnMobile?"first":"none"} order-md-none`:null]]),"data-aos":l===0?"fade-right":"fade-left"},[a.type==="image"?(H(),xr(Ut,{key:0,src:r.imagePath,"spinner-color":"primary",class:"rounded-borders",fit:"cover",position:"50% 50%",height:"100%",style:{"min-height":"300px"}},null,8,["src"])):(H(),re("div",{key:1,class:wr(["rounded-borders q-px-lg q-px-xl-xxl q-py-xl full-height",r.backgroundClass])},[x("h2",{class:wr(["callout Citi-Sans-Display-Bold q-mb-xl",r.titleClass]),innerHTML:r.title},null,10,Bc),F(o,{class:wr(["Citi-Sans-Display-Regular",r.textClass]),propFirstLevel:"p",propListData:r.paragraphs,propMarginFirstLevel:"1rem",propMarginSecondLevel:"0.5rem","prop-animation":!1},null,8,["class","propListData"])],2))],10,$c))),128))])])}var Nc=os(Ic,[["render",Hc],["__scopeId","data-v-7bfc0608"]]);const Yc=eo({name:"AssessmentCourseLevelTestOut",components:{QuestionCard:Fl,ListComponent:ss},props:{pageId:{type:String,default:null},nextRoute:{type:String,default:null},pagesToCompleteWhenPassed:{type:Array},testoutQuestions:{type:Array},numberQuestions:{type:Number}},setup(s){const e=ca(),r={...Vn()};({...Wn()});const{locale:t}=Xn({useScope:"global"}),{t:i,tm:n}=Xn(),o=Gi(),a=Gi(),l=Gi(!1),u=Po(()=>e.getTestoutScore(o.value)),c=Po(()=>!!(o.value&&u.value>=80&&e.getInteractionsCompleted(o.value,a.value)));Nl(c,(f,p)=>{console.log("passedAndCompleted - newValue: ",f),console.log("passedAndCompleted - oldValue: ",p),f&&(s.pagesToCompleteWhenPassed.forEach(h=>{r.completePage(h)}),e.setInteraction(s.pageId+"_testout_end",null,null,!1))});const d=()=>{r.checkNetwork(()=>_())},_=()=>{r.completePageAndRoute(s.pageId,s.nextRoute)};return fa(()=>{if(e.getInteractionSubmitted(s.pageId+"_testout_take").submitted){e.setInteraction(s.pageId+"_testout_complete_skiped",null,null,!0);return}console.log("Test-out option not selected yet");const f=e.getInteractionSubmitted(s.pageId+"_selected_questions");if(e.getInteractionSubmitted(s.pageId+"_testout_take").submitted&&f.submitted)o.value=f.learner_response.split(",");else{const p=s.testoutQuestions.filter(m=>m.bank==="bankA"),h=Yl(p,s.numberQuestions);o.value=h.map(m=>m.id),e.setInteraction(s.pageId+"_selected_questions",o.value.join(","),null,!0)}}),{store:e,...Vn(),...Wn(),locale:t,t:i,tm:n,selectedQuestions:o,assessmentScore:u,continueClicked:d,assessmentAttempts:a,hideFailedFeedback:l}}}),Xc={class:"bg-large_continue"},Vc={class:"container"},Wc={class:"row justify-center items-center"},Qc={class:"col-12 col-md-11 q-pa-md-xl q-pa-lg"},Uc={class:"row justify-center items-center q-mb-lg q-col-gutter-xl"},Gc={class:"col-12 col-lg-auto text-center"},jc={class:"col-12 col-lg-8 q-py-md q-px-lg"},Kc={class:"row justify-center items-center q-col-gutter-md-md q-mb-lg"},Zc={class:"col-12 col-md-6"},Jc={class:"col-12 col-md-6"},ef={key:0},tf={class:"container"},rf={class:"row q-py-xl justify-center"},nf={class:"col-12 col-lg-10"},sf={class:"q-pa-lg q-pa-lg-xxl bg-white","data-aos":"zoom-out"},of=["innerHTML"],af=["innerHTML"],lf=["innerHTML"],uf=["innerHTML"],cf=["innerHTML"],ff={class:"bg-grey"},df={class:"container"},hf={class:"row justify-center"},_f={class:"col-lg-11"},pf={key:0},gf={class:"container q-py-lg"},mf={class:"container q-py-lg"},yf={class:"row items-center"},vf={class:"col-12 col-md-4 q-py-xl"},bf={key:0,class:"q-py-xl q-py-md-none col-12 offset-md-1 col-md-7"},Tf=["innerHTML"],xf=["innerHTML"],wf=["innerHTML"],Sf=["innerHTML"],Cf={class:"row justify-items-between items-center q-col-gutter-xl-md"},kf={class:"col-12 col-xl-6"},Mf={key:1,class:"q-py-xl q-py-md-none col-12 offset-md-1 col-md-7"},Pf=["innerHTML"],Of=["innerHTML"],Df=["innerHTML"],Lf=["innerHTML"],Af={class:"row justify-items-between items-center q-col-gutter-xl-md"},Rf={class:"col-12 col-xl-6"},Ef={class:"col-lg-12 q-py-xxl"},qf={key:0,class:"q-pa-lg q-pa-md-xxl q-mb-xl shadow-3 rounded-borders bg-white","data-aos":"flip-right"},If={class:"row"},Ff={class:"col-12"},zf=["innerHTML"],$f={class:"row q-col-gutter-lg"},Bf={class:"col-lg-6"},Hf={class:"text-h5 Citi-Sans-Display-Regular q-mb-lg"},Nf={class:"col-lg-6"},Yf=["innerHTML"],Xf={key:2,class:"q-py-xl q-py-md-none col-12 offset-md-1 col-md-7"},Vf={key:0},Wf=["innerHTML"],Qf={key:3,class:"q-py-xl q-py-md-none col-12 offset-md-1 col-md-7"},Uf=["innerHTML"];function Gf(s,e,r,t,i,n){const o=ur("ListComponent"),a=ur("QuestionCard");return H(),re("section",null,[x("div",Xc,[F(Pt,{"aria-hidden":"true",size:"60px",color:"transparent"}),x("div",Vc,[x("div",Wc,[x("div",Qc,[x("div",Uc,[x("div",Gc,[x("img",{"aria-hidden":"true",src:"assets/global/hat_callout_assessment.svg",style:yi(s.$q.screen.gt.sm?"width:155px;":"width:105px; padding-top: 1rem;")},null,4)]),x("div",jc,[F(o,{propFirstLevel:"p",propListData:s.tm("assessment.course_level_test_out.this_course_contains_a_test_out_and_a_final_assessment"),propMarginFirstLevel:"1rem",propMarginSecondLevel:"0.5rem",propFirstLevelPClass:""},null,8,["propListData"])])]),F(Pt,{"aria-hidden":"true",size:"100px",color:"transparent"}),x("div",Kc,[x("div",Zc,[F(Xr,{disable:s.store.getInteractionSubmitted(s.pageId+"_testout_take").submitted,class:"q-mb-sm q-mr-md-lg bg-white full-width",padding:"sm xl",unelevated:"","no-caps":"",rounded:"",outline:"",color:"primary",label:s.t("assessment.course_level_test_out.btnTakeTestOut"),onClick:e[0]||(e[0]=l=>{s.store.setInteraction(s.pageId+"_testout_take",null,null,!0),s.scrollDown(600)})},null,8,["disable","label"])]),x("div",Jc,[F(Xr,{disable:s.store.getInteractionSubmitted(s.pageId+"_testout_take").submitted||s.store.getInteractionSubmitted(s.pageId+"_testout_end").submitted,class:"q-mb-sm bg-white full-width",padding:"sm xl",unelevated:"","no-caps":"",rounded:"",outline:"",color:"primary",label:s.t("assessment.course_level_test_out.btnSkipTestOut"),onClick:e[1]||(e[1]=l=>{s.store.setInteraction(s.pageId+"_testout_complete_skiped",null,null,!0),s.scrollTop(),s.setFocusById("a11yIntro",400),s.refreshAOS(),s.continueClicked()})},null,8,["disable","label"])])])])])])]),s.store.getInteractionSubmitted(s.pageId+"_testout_take").submitted&&s.selectedQuestions?(H(),re("section",ef,[x("div",tf,[x("div",rf,[x("div",nf,[x("div",sf,[x("h3",{class:"Citi-Sans-Display-Regular q-mb-xl",innerHTML:s.tm("assessment.course_level_test_out.title1")},null,8,of),x("p",{class:"q-mb-md Citi-Sans-Text-Regular",innerHTML:s.tm("assessment.course_level_test_out.text1")},null,8,af),x("p",{class:"Citi-Sans-Text-Regular q-mb-md text-bold",innerHTML:s.t("assessment.course_level_test_out.text2",{numberOfQuestions:s.selectedQuestions?s.selectedQuestions.length:0})},null,8,lf),x("p",{class:"q-mb-md Citi-Sans-Text-Regular",innerHTML:s.tm("assessment.course_level_test_out.text3")},null,8,uf),F(o,{propFirstLevel:"ul",propListData:s.tm("assessment.course_level_test_out.list1"),propMarginFirstLevel:"1rem",propMarginSecondLevel:"0.5rem",propFirstLevelPClass:"q-mb-md"},null,8,["propListData"]),x("p",{class:"q-mb-md Citi-Sans-Text-Bold",innerHTML:s.tm("assessment.course_level_test_out.text4")},null,8,cf)])])])]),x("div",ff,[F(Pt,{"aria-hidden":"true",size:"60px",color:"transparent"}),x("div",df,[x("div",hf,[x("div",_f,[(H(!0),re(Kt,null,Or(s.selectedQuestions,(l,u)=>(H(),re(Kt,{key:u},[u==0||(s.store.getInteractionsFailedCount(s.selectedQuestions)==2&&!s.hideFailedFeedback?s.store.getInteractionSubmitted(s.selectedQuestions[u]).submitted:s.store.getInteractionSubmitted(s.selectedQuestions[u-1]).submitted)?(H(),xr(a,{class:"q-mb-xl",key:s.locale+"-"+l,questionId:l,questionsArray:s.testoutQuestions,alternativeTitle:s.t("ui.questionTitleTemplate",{a:u+1,b:s.selectedQuestions.length}),submitButtonText:s.t("ui.submit"),defaultColor:"secondary",feedbackType:"correct_incorrect",headerInsideQuestionCard:!1},null,8,["questionId","questionsArray","alternativeTitle","submitButtonText"])):Yt("",!0)],64))),128))])])])]),s.store.getInteractionsCompleted(s.selectedQuestions,s.assessmentAttempts)||s.store.getInteractionsFailedCount(s.selectedQuestions)==2?(H(),re("section",pf,[!s.hideFailedFeedback||s.store.getInteractionsCompleted(s.selectedQuestions,s.assessmentAttempts)?(H(),re("div",{key:0,class:wr(s.assessmentScore>=80?"bg-testout_assessment_pass":"bg-testout_assessment_fail")},[x("div",gf,[x("div",mf,[x("div",yf,[x("div",vf,[s.assessmentScore>=80?(H(),xr(Ut,{key:0,loading:"eager","no-transition":"",src:"assets/global/course_testout_pass.png","spinner-color":"white","data-aos":"fade-right"})):(H(),xr(Ut,{key:1,loading:"eager","no-transition":"",src:"assets/global/course_testout_fail.png","spinner-color":"white","data-aos":"fade-right"}))]),s.assessmentScore==100?(H(),re("div",bf,[x("p",{class:"text-h5 q-mb-lg text-white Citi-Sans-Text-Bold",innerHTML:s.locale=="sk"||s.locale=="fr"||s.locale=="es"||s.locale=="cs"||s.locale=="de-DE"?s.tm("assessment.course_level_test_out.congrats.text1")+" "+s.assessmentScore+" % ":s.locale=="hu"?s.tm("assessment.course_level_test_out.congrats.text1")+" "+s.assessmentScore+"%, ":s.locale=="tr"?s.tm("assessment.course_level_test_out.congrats.text1")+" %"+s.assessmentScore+", ":s.tm("assessment.course_level_test_out.congrats.text1")+" "+s.assessmentScore+"% "},null,8,Tf),x("p",{class:"q-mb-lg text-white",innerHTML:s.tm("assessment.course_level_test_out.congrats.text2")},null,8,xf),x("p",{class:"q-mb-lg text-white",innerHTML:s.tm("assessment.course_level_test_out.congrats.text3")},null,8,wf),x("p",{class:"q-mb-lg text-white",innerHTML:s.tm("assessment.course_level_test_out.congrats.text4")},null,8,Sf),x("div",Cf,[x("div",kf,[F(Xr,{class:"q-mb-sm q-mr-md-lg bg-white full-width",padding:"sm xl",unelevated:"","no-caps":"",rounded:"",outline:"",color:"primary",label:s.t("assessment.course_level_test_out.btnReviewContent"),onClick:e[2]||(e[2]=l=>{s.store.setInteraction(s.pageId+"_testout_complete_passed",null,null,!1),s.continueClicked()})},null,8,["label"])])])])):Yt("",!0),s.assessmentScore>=80&&s.assessmentScore<100?(H(),re("div",Mf,[x("p",{class:"text-h5 q-mb-lg text-white Citi-Sans-Text-Bold",innerHTML:s.locale=="sk"||s.locale=="fr"||s.locale=="es"||s.locale=="cs"||s.locale=="de-DE"?s.tm("assessment.course_level_test_out.passed.text1")+" "+s.assessmentScore+" % ":s.locale=="hu"?s.tm("assessment.course_level_test_out.passed.text1")+" "+s.assessmentScore+"%, ":s.locale=="tr"?s.tm("assessment.course_level_test_out.passed.text1")+" %"+s.assessmentScore+", ":s.tm("assessment.course_level_test_out.passed.text1")+" "+s.assessmentScore+"% "},null,8,Pf),x("p",{class:"q-mb-lg text-white",innerHTML:s.tm("assessment.course_level_test_out.passed.text2")},null,8,Of),x("p",{class:"q-mb-lg text-white",innerHTML:s.tm("assessment.course_level_test_out.passed.text3")},null,8,Df),x("p",{class:"q-mb-lg text-white",innerHTML:s.tm("assessment.course_level_test_out.passed.text4")},null,8,Lf),x("div",Af,[x("div",Rf,[F(Xr,{class:"q-mb-sm q-mr-md-lg bg-white full-width",padding:"sm xl",unelevated:"","no-caps":"",rounded:"",outline:"",color:"primary",label:s.t("assessment.course_level_test_out.btnReviewContent"),onClick:e[3]||(e[3]=l=>{s.store.setInteraction(s.pageId+"_testout_complete_passed",null,null,!1),s.continueClicked()})},null,8,["label"])])]),x("div",Ef,[(H(!0),re(Kt,null,Or(s.selectedQuestions,(l,u)=>(H(),re(Kt,{key:u},[s.store.getInteractionSubmitted(s.selectedQuestions[u]).result=="incorrect"?(H(),re("div",qf,[x("div",If,[x("div",Ff,[x("h5",{class:"text-h4 q-mb-lg Citi-Sans-Display-Regular",innerHTML:s.findQuestionById(l,s.testoutQuestions).title},null,8,zf)])]),x("div",$f,[x("div",Bf,[x("p",Hf,[F(o,{propFirstLevel:"p",propListData:s.findQuestionById(l,s.testoutQuestions).text,propMarginFirstLevel:"1rem",propMarginSecondLevel:"0.5rem"},null,8,["propListData"])])]),x("div",Nf,[(H(!0),re(Kt,null,Or(s.findQuestionById(l,s.testoutQuestions).options,(c,d)=>(H(),re("div",{key:d,class:"row items-center q-mb-md no-wrap"},[s.findQuestionById(l,s.testoutQuestions).correctResponse.includes(c.value)?(H(),xr(Ps,{key:0,class:"text-positive q-mr-sm",size:"30px",name:"bi-check-circle"})):(H(),xr(Ps,{key:1,class:"text-negative q-mr-sm",size:"30px",name:"bi-x-circle"})),x("p",{innerHTML:c.label},null,8,Yf)]))),128)),F(Pt,{class:"q-my-lg","aria-hidden":"true",size:"2px","data-aos":"zoom-in"}),F(o,{propFirstLevel:"p",propListData:s.findQuestionById(l,s.testoutQuestions).correctFeedbackText,propMarginFirstLevel:"1rem",propMarginSecondLevel:"0.5rem"},null,8,["propListData"]),F(Pt,{class:"q-my-lg","aria-hidden":"true",size:"2px","data-aos":"zoom-in"})])])])):Yt("",!0)],64))),128))])])):s.assessmentScore<60&&s.store.getInteractionsFailedCount(s.selectedQuestions)==2?(H(),re("div",Xf,[s.hideFailedFeedback?Yt("",!0):(H(),re("div",Vf,[x("p",{class:"q-mb-lg text-white",innerHTML:s.tm("assessment.course_level_test_out.failed.text2")},null,8,Wf),F(Xr,{class:"bg-white q-mx-xs",padding:"sm xl",unelevated:"","no-caps":"",rounded:"",outline:"",color:"primary",label:s.t("assessment.course_level_test_out.btnContinueCourse"),onClick:e[4]||(e[4]=l=>{s.store.setInteraction(s.pageId+"_testout_complete_failed",null,null,!1),s.scrollTop(),s.setFocusById("a11yIntro",400),s.refreshAOS(),s.continueClicked()})},null,8,["label"])]))])):s.assessmentScore<80?(H(),re("div",Qf,[x("p",{class:"q-mb-lg text-white",innerHTML:s.tm("assessment.course_level_test_out.failed.text2")},null,8,Uf),F(Xr,{class:"bg-white",padding:"sm xl",unelevated:"","no-caps":"",rounded:"",outline:"",color:"primary",label:s.t("assessment.course_level_test_out.btnContinueCourse"),onClick:e[5]||(e[5]=l=>{s.store.setInteraction(s.pageId+"_testout_complete_failed",null,null,!1),s.scrollTop(),s.setFocusById("a11yIntro",400),s.refreshAOS(),s.continueClicked()})},null,8,["label"])])):Yt("",!0)])])])],2)):Yt("",!0)])):Yt("",!0)])):Yt("",!0)])}var jf=os(Yc,[["render",Gf]]),Kf="assets/cube.67d90dba.svg",Zf="assets/1.812aff7a.svg",Jf="assets/2.1af946a2.png",ed="assets/wavePositive.8ad57931.png",td="assets/3.a1b0cb14.png",rd="assets/4.0300793d.png";vo.registerPlugin(U);const id=eo({name:"PageOne",components:{Vue3Lottie:Ql,BoxListComponent:qc,ImageBoxTextBox:Nc,AssessmentCourseLevelTestOut:jf,ListComponent:ss},setup(){const s=ca(),e={...Vn()},r={...Wn()},{locale:t}=Xn({useScope:"global"}),{t:i,tm:n}=Xn();Bl();const o=Gi(!1),a=u=>{u&&s.setInteraction(u,null,null,!0),e.checkNetwork(()=>l())},l=()=>{e.completePageAndRoute("page1","/page2")};return fa(()=>{console.log(t.value);const u=vo.timeline({scrollTrigger:{trigger:".trigger",scrub:1,start:"center center",end:"+=500",markers:!1,once:!0}});u.fromTo(".slice",1,{scale:.7,y:-50},{scale:1,y:-100,ease:"Power1.easeInOut"}),u.from(".bg-banner-img",1,{backgroundPosition:"50% 25%"},{ease:"Power1.easeInOut"}),console.log("***------ PageOne - onMounted ------***"),s.rte_load("page1"),s.getPageStatusById("page1")==1&&e.setFocusById("a11yIntro",400),r.refreshAOS()}),{store:s,check1:Gl,graphic1:jl,...Vn(),...Wn(),locale:t,t:i,tm:n,animations:Gi(!1),openTranscript:o,continueClicked:a,globe:Ul}}}),Il=s=>(Vl("data-v-067ea09d"),s=s(),Wl(),s),nd={class:"sr-only q-py-md"},sd=["innerHTML"],od=["innerHTML"],ad={class:"container","data-aos":"zoom-out"},ld={class:"row text-center justify-center"},ud={class:"col-md-8 q-pt-md"},cd=["innerHTML"],fd=["innerHTML"],dd=["innerHTML"],hd={class:""},_d={class:"container"},pd={class:"row"},gd={class:"q-mb-xl col-12 col-lg-7"},md=["innerHTML"],yd=["innerHTML"],vd={class:"row q-col-gutter-xl"},bd=["innerHTML"],Td={key:0,class:"q-pt-sm row justify-between"},xd=Il(()=>x("div",{class:"trigger"},null,-1)),wd={class:"bg-grey"},Sd={class:"container"},Cd={class:"row"},kd={class:"col-12 col-lg-7"},Md=["innerHTML"],Pd={class:"bg-banner-img"},Od={class:"bg-split"},Dd={"aria-hidden":"true",fluid:"",class:"px-0 bg-waves",style:{display:"flex","justify-content":"center"}},Ld={class:"container",style:{"margin-top":"-1rem"}},Ad={"data-aos":"fade",class:"row justify-center items-center q-mb-md-xl q-pb-xl"},Rd={class:"col-10 col-md-4 q-mb-xs-xl","data-aos":"fade-right"},Ed={class:"col-md-5 offset-md-1","data-aos":"fade-left"},qd=Il(()=>x("div",{class:"q-my-lg",style:{"border-top":"1px solid #255BE3",width:"10%"}},null,-1)),Id=["innerHTML"],Fd=["innerHTML"],zd={"data-aos":"fade",class:"row justify-center items-center"},$d={class:"col-md-5 order-last order-md-first","data-aos":"fade-right"},Bd=["innerHTML"],Hd=["innerHTML"],Nd={class:"col-6 col-10 col-md-5 order-first order-md-last","data-aos":"fade-left"},Yd={class:"container"},Xd={"data-aos":"fade",class:"row justify-center items-center q-pt-lg q-pb-xl q-mb-sm"},Vd={class:"col-11 col-md-10"},Wd=["innerHTML"],Qd={class:"col-6 col-10 col-md-5 q-pb-lg q-mb-md-none","data-aos":"fade-right"},Ud={class:"col-md-5 q-pa-md-md","data-aos":"fade-left"},Gd={"data-aos":"fade",class:"row justify-center items-center q-pb-xl q-mb-lg"},jd={class:"col-md-5 q-pa-md-md order-last order-md-first","data-aos":"fade-right"},Kd={class:"col-6 col-10 col-md-5 order-first order-md-last q-mb-lg q-mb-md-none","data-aos":"fade-left"},Zd={class:"row q-pb-xxl justify-center","data-aos":"fade"},Jd={class:"col-lg-10"},eh={"data-aos":"fade",class:"row justify-center items-center q-mb-xl q-pb-xl"},th={class:"col-6 col-10 col-md-4 q-mb-md q-mb-md-none","data-aos":"fade-right"},rh={class:"col-md-5 offset-md-1","data-aos":"fade-left"},ih={key:1,class:"bg-footer"},nh={class:"q-py-xl container row justify-center"},sh={class:"col-12 col-lg text-center q-mb-xxl"},oh=["innerHTML"];function ah(s,e,r,t,i,n){const o=ur("box-list-component"),a=ur("image-box-text-box"),l=ur("ListComponent"),u=ur("Vue3Lottie"),c=ur("AssessmentCourseLevelTestOut");return H(),xr($l,{class:"overflow-hidden"},{default:Xl(()=>[x("div",nd,[x("p",{class:"q-mb-md",innerHTML:s.t("ui.pageLoaded"),id:"a11yIntro",style:{outline:"0"},tabindex:"0"},null,8,sd),x("p",{innerHTML:(s.store.getPagesCompleted?s.store.getPagesCompleted:0)+s.$t("ui.of")+s.store.manifest.content.length+s.t("ui.pagesCompleted")},null,8,od)]),x("div",ad,[x("div",ld,[x("div",ud,[F(Ut,{class:"q-mt-xl q-mb-lg",src:Kl,"spinner-color":"white",style:{width:"55px"}}),x("h1",{class:"q-mb-md q-pt-lg q-mt-md",style:{"border-top":"1px solid #255BE3"},innerHTML:s.tm("page1.sectionTop.title1")},null,8,cd),x("p",{innerHTML:s.t("page1.sectionTop.title2")},null,8,fd),x("p",{class:"text-bold q-pt-xl q-mt-lg-lg",innerHTML:s.t("ui.scrollc")},null,8,dd)])])]),F(Pt,{"aria-hidden":"true",size:s.$q.screen.gt.md?"150px":"100px",color:"transparent"},null,8,["size"]),x("div",hd,[x("div",_d,[x("div",pd,[x("div",gd,[x("h2",{class:"q-pb-lg",innerHTML:s.tm("page1.section1.title1"),"data-aos":"fade-right"},null,8,md),x("p",{class:"",innerHTML:s.tm("page1.section1.instr1"),"data-aos":"fade-right"},null,8,yd)])]),x("div",vd,[(H(!0),re(Kt,null,Or(s.tm("page1.section1.list1"),(d,_)=>(H(),re("div",{class:"col-12 col-md-6",key:_,"data-aos":"zoom-in"},[x("div",{class:"q-px-lg q-pt-lg shadow-1",style:yi([s.$q.screen.gt.md?"min-height:310px":s.$q.screen.md?"min-height:400px":"min-height:350px",{"background-color":"#F0F5F7","border-radius":"25px"}])},[F(Ps,{class:"q-mb-md text-secondary",size:"55px",name:d.icon1},null,8,["name"]),x("p",{innerHTML:d.text1},null,8,bd),_===3?(H(),re("div",Td,[F(zl,{modelValue:s.store.aosEnable,"onUpdate:modelValue":[e[0]||(e[0]=f=>s.store.aosEnable=f),e[1]||(e[1]=f=>{s.initAOS(),s.refreshAOS()})],label:s.store.aosEnable?s.t("ui.on"):s.t("ui.off")},null,8,["modelValue","label"])])):Yt("",!0)],4)]))),128))])])]),F(Pt,{size:s.$q.screen.gt.md?"100px":"75px","aria-hidden":"true",color:"transparent"},null,8,["size"]),xd,x("div",wd,[x("div",Sd,[F(Pt,{size:s.$q.screen.gt.md?"100px":"75px","aria-hidden":"true",color:"transparent"},null,8,["size"]),x("div",Cd,[x("div",kd,[x("h2",{class:"q-mb-lg",innerHTML:s.tm("page1.sectionWhyWhyWhyWhy.title"),"data-aos":"fade-right"},null,8,Md)])]),F(o,{"list-items":s.tm("page1.sectionWhyWhyWhyWhy.list").slice(0,3),"background-class":void 0,"text-class":void 0},null,8,["list-items"]),F(Pt,{size:"2rem",color:"transparent"}),F(a,{"image-path":"assets/page1/whats_the_win.jpg",title:s.tm("page1.sectionWhyWhyWhyWhy.list[3].title"),paragraphs:s.tm("page1.sectionWhyWhyWhyWhy.list[3].paragraphs"),"text-right":void 0,"image-always-top-on-mobile":void 0,"title-class":void 0,"background-class":void 0,"text-class":void 0},null,8,["title","paragraphs"]),F(Pt,{size:s.$q.screen.gt.md?"100px":"75px","aria-hidden":"true",color:"transparent"},null,8,["size"])])]),x("div",Pd,[F(Pt,{"aria-hidden":"true",size:"250px",color:"transparent"}),x("div",Od,[x("div",Dd,[F(Ut,{class:"slice",loading:"eager",width:"250px",style:{"margin-top":"-0rem"},"no-transition":"",src:Kf})])])]),x("div",Ld,[x("div",Ad,[x("div",Rd,[F(Ut,{loading:"eager","no-transition":"",src:Zf,"spinner-color":"white"})]),x("div",Ed,[qd,x("p",{innerHTML:s.t("page1.section2.text1")},null,8,Id),x("ul",null,[(H(!0),re(Kt,null,Or(s.tm("page1.section2.list1"),(d,_)=>(H(),re("li",{key:_,innerHTML:d,class:"q-mb-md last-in-list li-style"},null,8,Fd))),128))])])]),x("div",zd,[x("div",$d,[x("h3",{class:"q-mb-lg",innerHTML:s.t("page1.section2.text2")},null,8,Bd),x("ul",null,[(H(!0),re(Kt,null,Or(s.tm("page1.section2.list2"),(d,_)=>(H(),re("li",{key:_,innerHTML:d,class:"q-mb-md last-in-list li-style"},null,8,Hd))),128))])]),x("div",Nd,[F(Ut,{loading:"eager","no-transition":"",src:Jf,class:"q-mb-lg q-mb-md-none","spinner-color":"white",style:yi(s.$q.screen.gt.xs?"width:85%; margin-left:20px":"")},null,8,["style"])])])]),F(Ut,{loading:"eager","no-transition":"",src:ed,"spinner-color":"white"}),F(Pt,{"aria-hidden":"true",size:s.$q.screen.gt.xs?"100px":"25px",color:"transparent"},null,8,["size"]),x("div",Yd,[x("div",Xd,[x("div",Vd,[x("h2",{class:"Citi-Sans-Display-Bold q-mb-xl",innerHTML:s.t("page1.section3.title")},null,8,Wd)]),x("div",Qd,[F(Ut,{loading:"eager","no-transition":"",src:td,style:yi(s.$q.screen.gt.xs?"width: 85%;":""),"spinner-color":"white"},null,8,["style"])]),x("div",Ud,[F(l,{class:"Citi-Sans-Display-Regular q-mb-md",propFirstLevel:"p",propListData:s.tm("page1.section3.paragraphs"),propMarginFirstLevel:"1rem",propMarginSecondLevel:"0.5rem","prop-animation":!1},null,8,["propListData"])])]),x("div",Gd,[x("div",jd,[F(l,{class:"Citi-Sans-Display-Regular q-mb-md",propFirstLevel:"p",propListData:s.tm("page1.section4.paragraphs"),propMarginFirstLevel:"1rem",propMarginSecondLevel:"0.5rem","prop-animation":!1},null,8,["propListData"])]),x("div",Kd,[F(Ut,{loading:"eager","no-transition":"",src:rd,style:yi(s.$q.screen.gt.xs?"width: 85%;":""),"spinner-color":"white"},null,8,["style"])])]),x("div",Zd,[x("div",Jd,[F(l,{class:"Citi-Sans-Display-Regular text-h3 text-secondary q-mb-md",propFirstLevel:"p",propListData:s.tm("page1.section5.paragraphs"),propMarginFirstLevel:"1rem",propMarginSecondLevel:"0.5rem","prop-animation":!1},null,8,["propListData"]),F(l,{class:"Citi-Sans-Display-Regular q-mb-md last-in-list-no-margin",propFirstLevel:"p",propListData:s.tm("page1.section6.paragraphs"),propMarginFirstLevel:"1rem",propMarginSecondLevel:"0.5rem","prop-animation":!1},null,8,["propListData"])])]),x("div",eh,[x("div",th,[F(u,{"auto-play":s.store.aosEnable,"pause-animation":!s.store.aosEnable,class:"lottie",loop:!0,scale:1,animationData:s.graphic1},null,8,["auto-play","pause-animation","animationData"])]),x("div",rh,[F(l,{class:"Citi-Sans-Display-Regular q-mb-md last-in-list-no-margin",propFirstLevel:"p",propListData:s.tm("page1.section7.paragraphs"),propMarginFirstLevel:"1rem",propMarginSecondLevel:"0.5rem","prop-animation":!1},null,8,["propListData"]),F(l,{class:"Citi-Sans-Display-Regular q-mb-md last-in-list-no-margin",propFirstLevel:"p",propListData:s.tm("page1.section8.paragraphs"),propMarginFirstLevel:"1rem",propMarginSecondLevel:"0.5rem","prop-animation":!1},null,8,["propListData"])])])]),!s.store.getInteractionSubmitted("page1_testout_complete_passed").submitted&&!s.store.getInteractionSubmitted("page1_testout_complete_failed").submitted&&!s.store.getInteractionSubmitted("page1_testout_complete_skiped").submitted?(H(),xr(c,{key:0,pageId:"page1",nextRoute:"/page2",pagesToCompleteWhenPassed:["page1","page2","page3","page4","page5","page6","page7","page8","page11"],testoutQuestions:s.tm("assessmentFinal.questionsSection"),numberQuestions:10},null,8,["testoutQuestions"])):Yt("",!0),s.store.getInteractionSubmitted("page1_testout_complete_passed").submitted||s.store.getInteractionSubmitted("page1_testout_complete_failed").submitted||s.store.getInteractionSubmitted("page1_testout_complete_skiped").submitted?(H(),re("div",ih,[x("div",nh,[x("div",sh,[x("h3",{class:"q-mb-xl",innerHTML:s.tm("page1.footer.text1")},null,8,oh),F(Xr,{onClick:e[2]||(e[2]=d=>s.continueClicked()),label:s.t("ui.continue"),style:{"border-radius":"7.5px"},"no-caps":"",outline:"",padding:"sm xl",class:"btn-fixed-width bg-white",color:"secondary"},null,8,["label"])])])])):Yt("",!0)]),_:1})}var bh=os(id,[["render",ah],["__scopeId","data-v-067ea09d"]]);export{bh as default};
