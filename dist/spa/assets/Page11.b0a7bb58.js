import{Q as N}from"./QPage.75be44cb.js";import{H as R,S as B,U as y,V as L,r as T,c as I,w as E,o as D,I as z,ad as q,W as w,J as n,X as i,O as s,f as l,F as b,$ as C,N as r,K as g,L as P,Z as j}from"./index.c6ba88b2.js";import{u as V}from"./use-quasar.de58b715.js";import{u as M}from"./vue-i18n.esm-bundler.5dea4c24.js";import{Q as H}from"./QSeparator.3707aaf2.js";import{Q}from"./QImg.f55ff82e.js";import{l as $,Q as A}from"./QBtn.c6cd36c1.js";import{Q as O,L as W}from"./QuestionCard.9699d84c.js";import"./use-dark.0de4478a.js";const J=R({name:"AssessmentFinal",components:{QuestionCard:O,ListComponent:W},props:{pagesToCompleteWhenPassed:{type:Array},assessmentQuestions:{type:Array}},setup(e){const t=B(),h={...y()};({...L()});const{locale:f}=M({useScope:"global"}),{t:_,tm:S}=M(),a=T(),c=T();T(!1);const o=I(()=>t.getInteractionsScore(a.value,c.value)),m=I(()=>!a.value||!c.value?!1:!!(o.value>=80&&t.getInteractionsCompleted(a.value,c.value)));E(m,(u,d)=>{console.log("passedAndCompleted - newValue: ",u),console.log("passedAndCompleted - oldValue: ",d),u&&(e.pagesToCompleteWhenPassed.forEach(F=>{h.completePage(F)}),t.setInteraction("course_complete_passed",null,null,!0))});const v=()=>{if(t.getInteractionSubmitted("course_complete_passed").submitted){const u=t.getInteractionSubmitted("assessment_final_questions");a.value=u.learner_response.split(",");const d=Number(t.getInteractionSubmitted("assessment_attempts").learner_response);c.value=d.toString()+"_"}else{t.getInteractionSubmitted("page2_testout_take").submitted&&t.getInteractionSubmitted("page2_testout_end").submitted,Number(t.getInteractionSubmitted("assessment_attempts").learner_response)%2;const u=e.assessmentQuestions.filter(p=>p.bank==="bankA");let d=[];if(console.log("test qs --------------------, ",t.testQuestions),t.testQuestions){const p=q(u,10);d=d.concat(p)}else{const p=q(u,10);d=d.concat(p)}t.testQuestions=!t.testQuestions,console.log("concat_array",d);const F=q(d,10);a.value=F.map(p=>p.id),console.log(a.value),t.setInteraction("assessment_final_questions",a.value.join(","),null,!0);const k=Number(t.getInteractionSubmitted("assessment_attempts").learner_response)+1;c.value=k.toString()+"_",t.setInteraction("assessment_attempts",k,null,!0)}};return D(()=>{v()}),{store:t,...y(),...L(),locale:f,t:_,tm:S,assessmentScore:o,selectedQuestions:a,assessmentAttempts:c,mountQuestions:v}}}),K={class:"bg-primary text-white"},U={class:"container"},X={class:"row q-py-xxl justify-center"},Z={class:"col-12 col-lg-6"},G=["innerHTML"],Y=["innerHTML"],x=["innerHTML"],ee={key:0},se={class:"bg-grey"},te={class:"container"},ne={class:"row justify-center"},ae={class:"col-lg-11"},oe={key:0},ie={key:0,class:"bg-testout_assessment_pass"},le={class:"container"},re={class:"row items-center"},ce={class:"col-12 col-md-4 q-py-xl"},de={class:"q-py-xl q-py-md-xxl col-12 offset-md-1 col-md-7"},me=["innerHTML"],ue=["innerHTML"],pe=["innerHTML"],ge=["innerHTML"],he=["innerHTML"],fe={key:1,class:"bg-testout_assessment_pass"},_e={class:"container"},Se={class:"row items-center"},ve={class:"col-12 col-md-4 q-py-xl"},be={class:"q-py-xl q-py-md-xxl col-12 offset-md-1 col-md-7"},Te=["innerHTML"],ye=["innerHTML"],Le=["innerHTML"],Me=["innerHTML"],Fe=["innerHTML"],qe={key:2,class:"bg-white"},Ce={class:"container"},He={class:"row justify-center q-py-xxl"},Qe={class:"col-lg-11"},we={key:0,class:"q-pa-lg q-pa-md-xxl q-mb-xl shadow-3 rounded-borders bg-white","data-aos":"flip-right"},ke={class:"row"},Ie={class:"col-12"},Pe=["innerHTML"],$e={class:"row q-col-gutter-lg"},Ae={class:"col-lg-6"},Re={class:"text-h5 Citi-Sans-Display-Regular q-mb-lg"},Be={class:"col-lg-6"},De=["innerHTML"],ze={key:3,class:"bg-testout_assessment_fail"},Ne={class:"container"},Ee={class:"row items-center"},je={class:"col-12 col-md-4 q-py-xl"},Ve={class:"q-py-xl q-py-md-xxl col-12 offset-md-1 col-md-7"},Oe=["innerHTML"],We=["innerHTML"],Je=["innerHTML"],Ke=["innerHTML"],Ue=["innerHTML"];function Xe(e,t,h,f,_,S){const a=w("ListComponent"),c=w("QuestionCard");return n(),i("section",null,[s("div",K,[s("div",U,[s("div",X,[s("div",Z,[s("p",{class:"Citi-Sans-Text-Regular q-mb-md",innerHTML:e.t("assessmentFinal.text1a")},null,8,G),s("p",{class:"Citi-Sans-Text-Regular q-mb-md",innerHTML:e.t("assessmentFinal.text1b",{numberOfQuestions:e.selectedQuestions?e.selectedQuestions.length:0})},null,8,Y),l(a,{propFirstLevel:"p",propListData:e.tm("assessmentFinal.list1"),propMarginFirstLevel:"1rem",propMarginSecondLevel:"0.5rem",propFirstLevelPClass:"Citi-Sans-Text-Regular"},null,8,["propListData"]),s("p",{class:"Citi-Sans-Text-Bold",innerHTML:e.tm("assessmentFinal.text2"),"data-aos":"fade-right"},null,8,x)])])])]),e.selectedQuestions?(n(),i("section",ee,[s("div",se,[l(H,{"aria-hidden":"true",size:"60px",color:"transparent"}),s("div",te,[s("div",ne,[s("div",ae,[(n(!0),i(b,null,C(e.selectedQuestions,(o,m)=>(n(),i(b,{key:m},[m==0||e.store.getInteractionSubmitted(e.assessmentAttempts+e.selectedQuestions[m-1]).submitted?(n(),g(c,{class:"q-mb-xl",key:e.locale+"-"+o+e.assessmentAttempts,questionId:o,questionIdAttempt:e.assessmentAttempts,questionsArray:e.assessmentQuestions,alternativeTitle:e.t("ui.questionTitleTemplate",{a:m+1,b:e.selectedQuestions.length}),submitButtonText:e.t("ui.submit"),defaultColor:"secondary",feedbackType:"correct_incorrect",headerInsideQuestionCard:!1},null,8,["questionId","questionIdAttempt","questionsArray","alternativeTitle","submitButtonText"])):r("",!0)],64))),128))])])])]),e.store.getInteractionsCompleted(e.selectedQuestions,e.assessmentAttempts)?(n(),i("section",oe,[e.assessmentScore===100?(n(),i("div",ie,[s("div",le,[s("div",re,[s("div",ce,[l(Q,{loading:"eager","no-transition":"",src:"assets/global/course_testout_pass.png","spinner-color":"white","data-aos":"fade-right"})]),s("div",de,[s("h3",{class:"text-h4 q-mb-lg text-white Citi-Sans-Display-Bold",innerHTML:e.tm("assessmentFinal.finalPassSection.title")},null,8,me),s("p",{class:"text-h5 q-mb-lg text-white Citi-Sans-Text-Bold",innerHTML:e.locale=="sk"||e.locale=="fr"||e.locale=="es"||e.locale=="cs"||e.locale=="de-DE"?e.t("assessmentFinal.finalPassSection.text1")+" "+e.assessmentScore+" % ":e.locale=="hu"?e.t("assessmentFinal.finalPassSection.text1")+" "+e.assessmentScore+"%, ":e.locale=="tr"?e.t("assessmentFinal.finalPassSection.text1")+" %"+e.assessmentScore+", ":e.t("assessmentFinal.finalPassSection.text1")+" "+e.assessmentScore+"% "},null,8,ue),s("p",{class:"text-h5 q-mb-lg text-white Citi-Sans-Text-Regular",innerHTML:e.tm("assessmentFinal.finalPassSection.text2")},null,8,pe),s("p",{class:"text-h5 q-mb-lg text-white Citi-Sans-Text-Regular",innerHTML:e.tm("assessmentFinal.finalPassSection.text3")},null,8,ge),s("p",{class:"text-h5 text-white Citi-Sans-Text-Regular",innerHTML:e.tm("assessmentFinal.finalPassSection.text4")},null,8,he)])])])])):r("",!0),e.assessmentScore>=80&&e.assessmentScore<100?(n(),i("div",fe,[s("div",_e,[s("div",Se,[s("div",ve,[l(Q,{loading:"eager","no-transition":"",src:"assets/global/course_testout_pass.png","spinner-color":"white","data-aos":"fade-right"})]),s("div",be,[s("h3",{class:"text-h4 q-mb-lg text-white Citi-Sans-Display-Bold",innerHTML:e.tm("assessmentFinal.finalPassSection.title")},null,8,Te),s("p",{class:"text-h5 q-mb-lg text-white Citi-Sans-Text-Bold",innerHTML:e.locale=="sk"||e.locale=="fr"||e.locale=="es"||e.locale=="cs"||e.locale=="de-DE"?e.t("assessmentFinal.finalPassSection.text1")+" "+e.assessmentScore+" % ":e.locale=="hu"?e.t("assessmentFinal.finalPassSection.text1")+" "+e.assessmentScore+"%, ":e.locale=="tr"?e.t("assessmentFinal.finalPassSection.text1")+" %"+e.assessmentScore+", ":e.t("assessmentFinal.finalPassSection.text1")+" "+e.assessmentScore+"% "},null,8,ye),s("p",{class:"text-h5 q-mb-lg text-white Citi-Sans-Text-Regular",innerHTML:e.tm("assessmentFinal.finalPassSection.text2")},null,8,Le),s("p",{class:"text-h5 q-mb-lg text-white Citi-Sans-Text-Regular",innerHTML:e.tm("assessmentFinal.finalPassSection.text3")},null,8,Me),s("p",{class:"text-h5 text-white Citi-Sans-Text-Regular",innerHTML:e.tm("assessmentFinal.finalPassSection.text4")},null,8,Fe)])])])])):r("",!0),e.assessmentScore>=80&&e.assessmentScore<100?(n(),i("div",qe,[s("div",Ce,[s("div",He,[s("div",Qe,[(n(!0),i(b,null,C(e.selectedQuestions,(o,m)=>(n(),i(b,{key:m},[e.store.getInteractionSubmitted(e.assessmentAttempts+e.selectedQuestions[m]).result=="incorrect"?(n(),i("div",we,[s("div",ke,[s("div",Ie,[s("h5",{class:"text-h4 q-mb-lg Citi-Sans-Display-Regular",innerHTML:e.findQuestionById(o,e.assessmentQuestions).title},null,8,Pe)])]),s("div",$e,[s("div",Ae,[s("p",Re,[l(a,{propFirstLevel:"p",propListData:e.findQuestionById(o,e.assessmentQuestions).text,propMarginFirstLevel:"1rem",propMarginSecondLevel:"0.5rem"},null,8,["propListData"])])]),s("div",Be,[(n(!0),i(b,null,C(e.findQuestionById(o,e.assessmentQuestions).options,(v,u)=>(n(),i("div",{key:u,class:"row items-center q-mb-md no-wrap"},[e.findQuestionById(o,e.assessmentQuestions).correctResponse.includes(v.value)?(n(),g(A,{key:0,class:"text-positive q-mr-sm",size:"30px",name:"bi-check-circle"})):(n(),g(A,{key:1,class:"text-negative q-mr-sm",size:"30px",name:"bi-x-circle"})),s("p",{innerHTML:v.label},null,8,De)]))),128)),l(H,{class:"q-my-lg","aria-hidden":"true",size:"2px","data-aos":"zoom-in"}),l(a,{propFirstLevel:"p",propListData:e.findQuestionById(o,e.assessmentQuestions).correctFeedbackText,propMarginFirstLevel:"1rem",propMarginSecondLevel:"0.5rem"},null,8,["propListData"]),l(H,{class:"q-my-lg","aria-hidden":"true",size:"2px","data-aos":"zoom-in"})])])])):r("",!0)],64))),128))])])])])):r("",!0),e.assessmentScore<80?(n(),i("div",ze,[s("div",Ne,[s("div",Ee,[s("div",je,[l(Q,{loading:"eager","no-transition":"",src:"assets/global/course_testout_fail.png","spinner-color":"white","data-aos":"fade-right"})]),s("div",Ve,[s("h4",{class:"q-mb-lg text-white Citi-Sans-Display-Bold",innerHTML:e.tm("assessmentFinal.finalFailSection.title")},null,8,Oe),s("h5",{class:"q-mb-lg text-white Citi-Sans-Display-Bold",innerHTML:e.locale=="sk"||e.locale=="fr"||e.locale=="es"||e.locale=="cs"||e.locale=="de-DE"?e.t("assessmentFinal.finalFailSection.text1")+" "+e.assessmentScore+" % ":e.locale=="hu"?e.t("assessmentFinal.finalFailSection.text1")+" "+e.assessmentScore+"%, ":e.locale=="tr"?e.t("assessmentFinal.finalFailSection.text1")+" %"+e.assessmentScore+". ":e.t("assessmentFinal.finalFailSection.text1")+" "+e.assessmentScore+"% "},null,8,We),s("p",{class:"q-mb-lg text-white Citi-Sans-Text-Regular",innerHTML:e.tm("assessmentFinal.finalFailSection.text2")},null,8,Je),s("p",{class:"q-mb-lg text-white Citi-Sans-Text-Regular",innerHTML:e.tm("assessmentFinal.finalFailSection.text3")},null,8,Ke),s("p",{class:"q-mb-lg text-white Citi-Sans-Text-Regular",innerHTML:e.tm("assessmentFinal.finalFailSection.text4")},null,8,Ue),l($,{class:"bg-white",padding:"sm xl",unelevated:"","no-caps":"",rounded:"",outline:"",color:"primary",label:e.t("assessmentFinal.finalFailSection.btnRetry"),onClick:t[0]||(t[0]=o=>{e.mountQuestions(),e.scrollTop(),e.setFocusById("a11yIntro",400),e.refreshAOS()})},null,8,["label"]),l($,{class:"bg-white",padding:"sm xl",unelevated:"","no-caps":"",rounded:"",outline:"",color:"primary",label:e.t("assessmentFinal.finalFailSection.btnReturn"),onClick:t[1]||(t[1]=o=>e.routerTo("/page1"))},null,8,["label"])])])])])):r("",!0)])):r("",!0)])):r("",!0)])}var Ze=z(J,[["render",Xe]]);const Ge=R({name:"PageEleven",components:{AssessmentFinal:Ze},setup(){const e=B(),t={...y()},h={...L()},{locale:f}=M({useScope:"global"}),{t:_,tm:S}=M();V();const a=T(!1),c=()=>{t.checkNetwork(()=>o())},o=()=>{t.completePageAndRoute("page2","/page1")};return D(()=>{console.log("***------ PageEleven - onMounted ------***"),e.rte_load("page11"),setTimeout(()=>{a.value=!0,h.refreshAOS()},100),t.setFocusById("a11yIntro",400)}),{pageReady:a,store:e,...y(),...L(),locale:f,t:_,tm:S,continueClicked:c}}}),Ye={class:"sr-only q-py-md"},xe=["innerHTML"],es=["innerHTML"],ss={class:"bg-11_1","data-aos":"zoom-out"},ts={class:"container"},ns={class:"row justify-center items-center q-py-lg",style:{"min-height":"299px"}},as={class:"col-8"},os=["innerHTML"],is={key:1},ls={class:"bg-passed-assessment"},rs={class:"container"},cs={class:"row items-center",style:{"min-height":"496px"}},ds={class:"offset-md-5 col-md-6","data-aos":"zoom-in"},ms=["innerHTML"],us=["innerHTML"];function ps(e,t,h,f,_,S){const a=w("AssessmentFinal");return n(),g(j,{mode:"out-in",appear:"","enter-active-class":"animated fadeIn slower","leave-active-class":"animated fadeOut slower"},{default:P(()=>[e.pageReady?(n(),g(N,{key:0,class:"overflow-hidden"},{default:P(()=>[s("div",Ye,[s("p",{class:"q-mb-md",innerHTML:e.t("ui.pageLoaded"),id:"a11yIntro",style:{outline:"0"},tabindex:"0"},null,8,xe),s("p",{innerHTML:(e.store.getPagesCompleted?e.store.getPagesCompleted:0)+e.$t("ui.of")+e.store.manifest.content.length+e.t("ui.pagesCompleted")},null,8,es)]),s("div",ss,[s("div",ts,[s("div",ns,[s("div",as,[s("h1",{class:"text-center Citi-Sans-Display-Regular",innerHTML:e.t("page11.topSection.title")},null,8,os)])])])]),e.store.getInteractionSubmitted("page1_testout_end").submitted?r("",!0):(n(),g(a,{key:0,pagesToCompleteWhenPassed:["page1","page2","page3","page4","page5","page6","page7","page8","page11"],assessmentQuestions:e.tm("assessmentFinal.questionsSection")},null,8,["assessmentQuestions"])),e.store.getInteractionSubmitted("page1_testout_end").submitted?(n(),i("section",is,[s("div",ls,[s("div",rs,[s("div",cs,[s("div",ds,[s("h3",{class:"q-mb-lg text-white",innerHTML:e.t("page11.topSection.text1")},null,8,ms),s("h4",{class:"q-mb-lg text-white Citi-Sans-Display-Regular",innerHTML:e.t("page11.topSection.text2")},null,8,us)])])])])])):r("",!0)]),_:1})):r("",!0)]),_:1})}var Ls=z(Ge,[["render",ps]]);export{Ls as default};
