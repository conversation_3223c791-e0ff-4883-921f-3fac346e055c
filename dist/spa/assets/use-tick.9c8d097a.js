import{t as n}from"./QBtn.c6cd36c1.js";import{ab as r,k as s,g as u,z as a}from"./index.c6ba88b2.js";function l(){let e=null;const o=u();function t(){e!==null&&(clearTimeout(e),e=null)}return r(t),s(t),{removeTimeout:t,registerTimeout(i,m){t(),n(o)===!1&&(e=setTimeout(i,m))}}}function T(){let e;const o=u();function t(){e=void 0}return r(t),s(t),{removeTick:t,registerTick(i){e=i,a(()=>{e===i&&(n(o)===!1&&e(),e=void 0)})}}}export{T as a,l as u};
