import{c,h as q,i as me,j as x,l as te,r as z,w as k,k as he,g as W,m as Z,n as nt,p as kt,q as oe,s as ke,t as Oe,u as Se,v as ze,x as Te,y as St,o as Be,z as Fe,A as R,B as ot,C as Tt,D as lt,E as _t,G as le,a as Mt,H as rt,I as it,J as D,K,L as w,f as m,M as $t,N as De,O as $,P as Ee,R as ce,S as Ht,U as Ae,V as Ne,W as Re,X as Q,Y as zt,Z as Dt,$ as re,F as ee,a0 as Et,_ as ie}from"./index.c6ba88b2.js";import{Q as _e}from"./QSeparator.3707aaf2.js";import{c as P,h as pe,a as Bt,b as xt,d as Ue,u as Pt,e as Vt,f as xe,g as It,i as Qt,j as Ot,k as Ft,Q as At,l as O}from"./QBtn.c6cd36c1.js";import{Q as Nt}from"./QImg.f55ff82e.js";import{u as Rt,a as Ut,g as Wt,b as jt,c as Xt,d as Me,Q as We,e as se,C as Y}from"./ClosePopup.b5270395.js";import{Q as fe,g as je,s as Xe,c as Yt}from"./selection.3c67842b.js";import{Q as Ye,a as ve,b as Kt,c as Ke}from"./QItem.cc743337.js";import{u as Gt,a as Jt,b as Zt,Q as F}from"./QCardSection.d8eae2a5.js";import{u as ea}from"./use-tick.9c8d097a.js";import{u as st,a as ut}from"./use-dark.0de4478a.js";import{Q as ue}from"./QCard.8d7aab57.js";import{u as ta}from"./use-quasar.de58b715.js";import{u as Ge}from"./vue-i18n.esm-bundler.5dea4c24.js";import{_ as aa}from"./logo-blue-red.20d53d90.js";var na=P({name:"QToolbar",props:{inset:Boolean},setup(e,{slots:n}){const r=c(()=>"q-toolbar row no-wrap items-center"+(e.inset===!0?" q-toolbar--inset":""));return()=>q("div",{class:r.value,role:"toolbar"},pe(n.default))}}),oa=P({name:"QHeader",props:{modelValue:{type:Boolean,default:!0},reveal:Boolean,revealOffset:{type:Number,default:250},bordered:Boolean,elevated:Boolean,heightHint:{type:[String,Number],default:50}},emits:["reveal","focusin"],setup(e,{slots:n,emit:r}){const{proxy:{$q:d}}=W(),t=me(te,x);if(t===x)return console.error("QHeader needs to be child of QLayout"),x;const a=z(parseInt(e.heightHint,10)),u=z(!0),y=c(()=>e.reveal===!0||t.view.value.indexOf("H")>-1||d.platform.is.ios&&t.isContainer.value===!0),o=c(()=>{if(e.modelValue!==!0)return 0;if(y.value===!0)return u.value===!0?a.value:0;const h=a.value-t.scroll.value.position;return h>0?h:0}),s=c(()=>e.modelValue!==!0||y.value===!0&&u.value!==!0),i=c(()=>e.modelValue===!0&&s.value===!0&&e.reveal===!0),_=c(()=>"q-header q-layout__section--marginal "+(y.value===!0?"fixed":"absolute")+"-top"+(e.bordered===!0?" q-header--bordered":"")+(s.value===!0?" q-header--hidden":"")+(e.modelValue!==!0?" q-layout--prevent-focus":"")),L=c(()=>{const h=t.rows.value.top,H={};return h[0]==="l"&&t.left.space===!0&&(H[d.lang.rtl===!0?"right":"left"]=`${t.left.size}px`),h[2]==="r"&&t.right.space===!0&&(H[d.lang.rtl===!0?"left":"right"]=`${t.right.size}px`),H});function p(h,H){t.update("header",h,H)}function g(h,H){h.value!==H&&(h.value=H)}function S({height:h}){g(a,h),p("size",h)}function T(h){i.value===!0&&g(u,!0),r("focusin",h)}k(()=>e.modelValue,h=>{p("space",h),g(u,!0),t.animate()}),k(o,h=>{p("offset",h)}),k(()=>e.reveal,h=>{h===!1&&g(u,e.modelValue)}),k(u,h=>{t.animate(),r("reveal",h)}),k(t.scroll,h=>{e.reveal===!0&&g(u,h.direction==="up"||h.position<=e.revealOffset||h.position-h.inflectionPoint<100)});const f={};return t.instances.header=f,e.modelValue===!0&&p("size",a.value),p("space",e.modelValue),p("offset",o.value),he(()=>{t.instances.header===f&&(t.instances.header=void 0,p("size",0),p("offset",0),p("space",!1))}),()=>{const h=Bt(n.default,[]);return e.elevated===!0&&h.push(q("div",{class:"q-layout__shadow absolute-full overflow-hidden no-pointer-events"})),h.push(q(fe,{debounce:0,onResize:S})),q("header",{class:_.value,style:L.value,onFocusin:T},h)}}});function $e(e,n,r){const d=ze(e);let t,a=d.left-n.event.x,u=d.top-n.event.y,y=Math.abs(a),o=Math.abs(u);const s=n.direction;s.horizontal===!0&&s.vertical!==!0?t=a<0?"left":"right":s.horizontal!==!0&&s.vertical===!0?t=u<0?"up":"down":s.up===!0&&u<0?(t="up",y>o&&(s.left===!0&&a<0?t="left":s.right===!0&&a>0&&(t="right"))):s.down===!0&&u>0?(t="down",y>o&&(s.left===!0&&a<0?t="left":s.right===!0&&a>0&&(t="right"))):s.left===!0&&a<0?(t="left",y<o&&(s.up===!0&&u<0?t="up":s.down===!0&&u>0&&(t="down"))):s.right===!0&&a>0&&(t="right",y<o&&(s.up===!0&&u<0?t="up":s.down===!0&&u>0&&(t="down")));let i=!1;if(t===void 0&&r===!1){if(n.event.isFirst===!0||n.event.lastDir===void 0)return{};t=n.event.lastDir,i=!0,t==="left"||t==="right"?(d.left-=a,y=0,a=0):(d.top-=u,o=0,u=0)}return{synthetic:i,payload:{evt:e,touch:n.event.mouse!==!0,mouse:n.event.mouse===!0,position:d,direction:t,isFirst:n.event.isFirst,isFinal:r===!0,duration:Date.now()-n.event.time,distance:{x:y,y:o},offset:{x:a,y:u},delta:{x:d.left-n.event.lastX,y:d.top-n.event.lastY}}}}let la=0;var He=xt({name:"touch-pan",beforeMount(e,{value:n,modifiers:r}){if(r.mouse!==!0&&Z.has.touch!==!0)return;function d(a,u){r.mouse===!0&&u===!0?St(a):(r.stop===!0&&Se(a),r.prevent===!0&&Oe(a))}const t={uid:"qvtp_"+la++,handler:n,modifiers:r,direction:je(r),noop:nt,mouseStart(a){Xe(a,t)&&kt(a)&&(oe(t,"temp",[[document,"mousemove","move","notPassiveCapture"],[document,"mouseup","end","passiveCapture"]]),t.start(a,!0))},touchStart(a){if(Xe(a,t)){const u=a.target;oe(t,"temp",[[u,"touchmove","move","notPassiveCapture"],[u,"touchcancel","end","passiveCapture"],[u,"touchend","end","passiveCapture"]]),t.start(a)}},start(a,u){if(Z.is.firefox===!0&&ke(e,!0),t.lastEvt=a,u===!0||r.stop===!0){if(t.direction.all!==!0&&(u!==!0||t.modifiers.mouseAllDir!==!0&&t.modifiers.mousealldir!==!0)){const s=a.type.indexOf("mouse")>-1?new MouseEvent(a.type,a):new TouchEvent(a.type,a);a.defaultPrevented===!0&&Oe(s),a.cancelBubble===!0&&Se(s),Object.assign(s,{qKeyEvent:a.qKeyEvent,qClickOutside:a.qClickOutside,qAnchorHandled:a.qAnchorHandled,qClonedBy:a.qClonedBy===void 0?[t.uid]:a.qClonedBy.concat(t.uid)}),t.initialEvent={target:a.target,event:s}}Se(a)}const{left:y,top:o}=ze(a);t.event={x:y,y:o,time:Date.now(),mouse:u===!0,detected:!1,isFirst:!0,isFinal:!1,lastX:y,lastY:o}},move(a){if(t.event===void 0)return;const u=ze(a),y=u.left-t.event.x,o=u.top-t.event.y;if(y===0&&o===0)return;t.lastEvt=a;const s=t.event.mouse===!0,i=()=>{d(a,s);let p;r.preserveCursor!==!0&&r.preservecursor!==!0&&(p=document.documentElement.style.cursor||"",document.documentElement.style.cursor="grabbing"),s===!0&&document.body.classList.add("no-pointer-events--children"),document.body.classList.add("non-selectable"),Yt(),t.styleCleanup=g=>{if(t.styleCleanup=void 0,p!==void 0&&(document.documentElement.style.cursor=p),document.body.classList.remove("non-selectable"),s===!0){const S=()=>{document.body.classList.remove("no-pointer-events--children")};g!==void 0?setTimeout(()=>{S(),g()},50):S()}else g!==void 0&&g()}};if(t.event.detected===!0){t.event.isFirst!==!0&&d(a,t.event.mouse);const{payload:p,synthetic:g}=$e(a,t,!1);p!==void 0&&(t.handler(p)===!1?t.end(a):(t.styleCleanup===void 0&&t.event.isFirst===!0&&i(),t.event.lastX=p.position.left,t.event.lastY=p.position.top,t.event.lastDir=g===!0?void 0:p.direction,t.event.isFirst=!1));return}if(t.direction.all===!0||s===!0&&(t.modifiers.mouseAllDir===!0||t.modifiers.mousealldir===!0)){i(),t.event.detected=!0,t.move(a);return}const _=Math.abs(y),L=Math.abs(o);_!==L&&(t.direction.horizontal===!0&&_>L||t.direction.vertical===!0&&_<L||t.direction.up===!0&&_<L&&o<0||t.direction.down===!0&&_<L&&o>0||t.direction.left===!0&&_>L&&y<0||t.direction.right===!0&&_>L&&y>0?(t.event.detected=!0,t.move(a)):t.end(a,!0))},end(a,u){if(t.event!==void 0){if(Te(t,"temp"),Z.is.firefox===!0&&ke(e,!1),u===!0)t.styleCleanup!==void 0&&t.styleCleanup(),t.event.detected!==!0&&t.initialEvent!==void 0&&t.initialEvent.target.dispatchEvent(t.initialEvent.event);else if(t.event.detected===!0){t.event.isFirst===!0&&t.handler($e(a===void 0?t.lastEvt:a,t).payload);const{payload:y}=$e(a===void 0?t.lastEvt:a,t,!0),o=()=>{t.handler(y)};t.styleCleanup!==void 0?t.styleCleanup(o):o()}t.event=void 0,t.initialEvent=void 0,t.lastEvt=void 0}}};if(e.__qtouchpan=t,r.mouse===!0){const a=r.mouseCapture===!0||r.mousecapture===!0?"Capture":"";oe(t,"main",[[e,"mousedown","mouseStart",`passive${a}`]])}Z.has.touch===!0&&oe(t,"main",[[e,"touchstart","touchStart",`passive${r.capture===!0?"Capture":""}`],[e,"touchmove","noop","notPassiveCapture"]])},updated(e,n){const r=e.__qtouchpan;r!==void 0&&(n.oldValue!==n.value&&(typeof value!="function"&&r.end(),r.handler=n.value),r.direction=je(n.modifiers))},beforeUnmount(e){const n=e.__qtouchpan;n!==void 0&&(n.event!==void 0&&n.end(),Te(n,"main"),Te(n,"temp"),Z.is.firefox===!0&&ke(e,!1),n.styleCleanup!==void 0&&n.styleCleanup(),delete e.__qtouchpan)}});function de(e,n,r){return r<=n?n:Math.min(r,Math.max(n,e))}const Je=150;var Ze=P({name:"QDrawer",inheritAttrs:!1,props:{...Gt,...st,side:{type:String,default:"left",validator:e=>["left","right"].includes(e)},width:{type:Number,default:300},mini:Boolean,miniToOverlay:Boolean,miniWidth:{type:Number,default:57},noMiniAnimation:Boolean,breakpoint:{type:Number,default:1023},showIfAbove:Boolean,behavior:{type:String,validator:e=>["default","desktop","mobile"].includes(e),default:"default"},bordered:Boolean,elevated:Boolean,overlay:Boolean,persistent:Boolean,noSwipeOpen:Boolean,noSwipeClose:Boolean,noSwipeBackdrop:Boolean},emits:[...Jt,"onLayout","miniState"],setup(e,{slots:n,emit:r,attrs:d}){const t=W(),{proxy:{$q:a}}=t,u=ut(e,a),{preventBodyScroll:y}=Ut(),{registerTimeout:o,removeTimeout:s}=ea(),i=me(te,x);if(i===x)return console.error("QDrawer needs to be child of QLayout"),x;let _,L=null,p;const g=z(e.behavior==="mobile"||e.behavior!=="desktop"&&i.totalWidth.value<=e.breakpoint),S=c(()=>e.mini===!0&&g.value!==!0),T=c(()=>S.value===!0?e.miniWidth:e.width),f=z(e.showIfAbove===!0&&g.value===!1?!0:e.modelValue===!0),h=c(()=>e.persistent!==!0&&(g.value===!0||dt.value===!0));function H(l,C){if(V(),l!==!1&&i.animate(),B(0),g.value===!0){const E=i.instances[ae.value];E!==void 0&&E.belowBreakpoint===!0&&E.hide(!1),A(1),i.isContainer.value!==!0&&y(!0)}else A(0),l!==!1&&qe(!1);o(()=>{l!==!1&&qe(!0),C!==!0&&r("show",l)},Je)}function b(l,C){G(),l!==!1&&i.animate(),A(0),B(j.value*T.value),Ce(),C!==!0?o(()=>{r("hide",l)},Je):s()}const{show:v,hide:M}=Zt({showing:f,hideOnRouteChange:h,handleShow:H,handleHide:b}),{addToHistory:V,removeFromHistory:G}=Rt(f,M,h),U={belowBreakpoint:g,hide:M},I=c(()=>e.side==="right"),j=c(()=>(a.lang.rtl===!0?-1:1)*(I.value===!0?1:-1)),Pe=z(0),X=z(!1),ge=z(!1),Ve=z(T.value*j.value),ae=c(()=>I.value===!0?"left":"right"),ye=c(()=>f.value===!0&&g.value===!1&&e.overlay===!1?e.miniToOverlay===!0?e.miniWidth:T.value:0),be=c(()=>e.overlay===!0||e.miniToOverlay===!0||i.view.value.indexOf(I.value?"R":"L")>-1||a.platform.is.ios===!0&&i.isContainer.value===!0),J=c(()=>e.overlay===!1&&f.value===!0&&g.value===!1),dt=c(()=>e.overlay===!0&&f.value===!0&&g.value===!1),ct=c(()=>"fullscreen q-drawer__backdrop"+(f.value===!1&&X.value===!1?" hidden":"")),ft=c(()=>({backgroundColor:`rgba(0,0,0,${Pe.value*.4})`})),Ie=c(()=>I.value===!0?i.rows.value.top[2]==="r":i.rows.value.top[0]==="l"),vt=c(()=>I.value===!0?i.rows.value.bottom[2]==="r":i.rows.value.bottom[0]==="l"),mt=c(()=>{const l={};return i.header.space===!0&&Ie.value===!1&&(be.value===!0?l.top=`${i.header.offset}px`:i.header.space===!0&&(l.top=`${i.header.size}px`)),i.footer.space===!0&&vt.value===!1&&(be.value===!0?l.bottom=`${i.footer.offset}px`:i.footer.space===!0&&(l.bottom=`${i.footer.size}px`)),l}),ht=c(()=>{const l={width:`${T.value}px`,transform:`translateX(${Ve.value}px)`};return g.value===!0?l:Object.assign(l,mt.value)}),pt=c(()=>"q-drawer__content fit "+(i.isContainer.value!==!0?"scroll":"overflow-auto")),gt=c(()=>`q-drawer q-drawer--${e.side}`+(ge.value===!0?" q-drawer--mini-animate":"")+(e.bordered===!0?" q-drawer--bordered":"")+(u.value===!0?" q-drawer--dark q-dark":"")+(X.value===!0?" no-transition":f.value===!0?"":" q-layout--prevent-focus")+(g.value===!0?" fixed q-drawer--on-top q-drawer--mobile q-drawer--top-padding":` q-drawer--${S.value===!0?"mini":"standard"}`+(be.value===!0||J.value!==!0?" fixed":"")+(e.overlay===!0||e.miniToOverlay===!0?" q-drawer--on-top":"")+(Ie.value===!0?" q-drawer--top-padding":""))),yt=c(()=>{const l=a.lang.rtl===!0?e.side:ae.value;return[[He,Ct,void 0,{[l]:!0,mouse:!0}]]}),bt=c(()=>{const l=a.lang.rtl===!0?ae.value:e.side;return[[He,Qe,void 0,{[l]:!0,mouse:!0}]]}),wt=c(()=>{const l=a.lang.rtl===!0?ae.value:e.side;return[[He,Qe,void 0,{[l]:!0,mouse:!0,mouseAllDir:!0}]]});function we(){Lt(g,e.behavior==="mobile"||e.behavior!=="desktop"&&i.totalWidth.value<=e.breakpoint)}k(g,l=>{l===!0?(_=f.value,f.value===!0&&M(!1)):e.overlay===!1&&e.behavior!=="mobile"&&_!==!1&&(f.value===!0?(B(0),A(0),Ce()):v(!1))}),k(()=>e.side,(l,C)=>{i.instances[C]===U&&(i.instances[C]=void 0,i[C].space=!1,i[C].offset=0),i.instances[l]=U,i[l].size=T.value,i[l].space=J.value,i[l].offset=ye.value}),k(i.totalWidth,()=>{(i.isContainer.value===!0||document.qScrollPrevented!==!0)&&we()}),k(()=>e.behavior+e.breakpoint,we),k(i.isContainer,l=>{f.value===!0&&y(l!==!0),l===!0&&we()}),k(i.scrollbarWidth,()=>{B(f.value===!0?0:void 0)}),k(ye,l=>{N("offset",l)}),k(J,l=>{r("onLayout",l),N("space",l)}),k(I,()=>{B()}),k(T,l=>{B(),Le(e.miniToOverlay,l)}),k(()=>e.miniToOverlay,l=>{Le(l,T.value)}),k(()=>a.lang.rtl,()=>{B()}),k(()=>e.mini,()=>{e.noMiniAnimation||e.modelValue===!0&&(qt(),i.animate())}),k(S,l=>{r("miniState",l)});function B(l){l===void 0?Fe(()=>{l=f.value===!0?0:T.value,B(j.value*l)}):(i.isContainer.value===!0&&I.value===!0&&(g.value===!0||Math.abs(l)===T.value)&&(l+=j.value*i.scrollbarWidth.value),Ve.value=l)}function A(l){Pe.value=l}function qe(l){const C=l===!0?"remove":i.isContainer.value!==!0?"add":"";C!==""&&document.body.classList[C]("q-body--drawer-toggle")}function qt(){L!==null&&clearTimeout(L),t.proxy&&t.proxy.$el&&t.proxy.$el.classList.add("q-drawer--mini-animate"),ge.value=!0,L=setTimeout(()=>{L=null,ge.value=!1,t&&t.proxy&&t.proxy.$el&&t.proxy.$el.classList.remove("q-drawer--mini-animate")},150)}function Ct(l){if(f.value!==!1)return;const C=T.value,E=de(l.distance.x,0,C);if(l.isFinal===!0){E>=Math.min(75,C)===!0?v():(i.animate(),A(0),B(j.value*C)),X.value=!1;return}B((a.lang.rtl===!0?I.value!==!0:I.value)?Math.max(C-E,0):Math.min(0,E-C)),A(de(E/C,0,1)),l.isFirst===!0&&(X.value=!0)}function Qe(l){if(f.value!==!0)return;const C=T.value,E=l.direction===e.side,ne=(a.lang.rtl===!0?E!==!0:E)?de(l.distance.x,0,C):0;if(l.isFinal===!0){Math.abs(ne)<Math.min(75,C)===!0?(i.animate(),A(1),B(0)):M(),X.value=!1;return}B(j.value*ne),A(de(1-ne/C,0,1)),l.isFirst===!0&&(X.value=!0)}function Ce(){y(!1),qe(!0)}function N(l,C){i.update(e.side,l,C)}function Lt(l,C){l.value!==C&&(l.value=C)}function Le(l,C){N("size",l===!0?e.miniWidth:C)}return i.instances[e.side]=U,Le(e.miniToOverlay,T.value),N("space",J.value),N("offset",ye.value),e.showIfAbove===!0&&e.modelValue!==!0&&f.value===!0&&e["onUpdate:modelValue"]!==void 0&&r("update:modelValue",!0),Be(()=>{r("onLayout",J.value),r("miniState",S.value),_=e.showIfAbove===!0;const l=()=>{(f.value===!0?H:b)(!1,!0)};if(i.totalWidth.value!==0){Fe(l);return}p=k(i.totalWidth,()=>{p(),p=void 0,f.value===!1&&e.showIfAbove===!0&&g.value===!1?v(!1):l()})}),he(()=>{p!==void 0&&p(),L!==null&&(clearTimeout(L),L=null),f.value===!0&&Ce(),i.instances[e.side]===U&&(i.instances[e.side]=void 0,N("size",0),N("offset",0),N("space",!1))}),()=>{const l=[];g.value===!0&&(e.noSwipeOpen===!1&&l.push(R(q("div",{key:"open",class:`q-drawer__opener fixed-${e.side}`,"aria-hidden":"true"}),yt.value)),l.push(Ue("div",{ref:"backdrop",class:ct.value,style:ft.value,"aria-hidden":"true",onClick:M},void 0,"backdrop",e.noSwipeBackdrop!==!0&&f.value===!0,()=>wt.value)));const C=S.value===!0&&n.mini!==void 0,E=[q("div",{...d,key:""+C,class:[pt.value,d.class]},C===!0?n.mini():pe(n.default))];return e.elevated===!0&&f.value===!0&&E.push(q("div",{class:"q-layout__shadow absolute-full overflow-hidden no-pointer-events"})),l.push(Ue("aside",{ref:"content",class:gt.value,style:ht.value},E,"contentclose",e.noSwipeClose!==!0&&g.value===!0,()=>bt.value)),q("div",{class:"q-drawer-container"},l)}}}),ra=P({name:"QPageContainer",setup(e,{slots:n}){const{proxy:{$q:r}}=W(),d=me(te,x);if(d===x)return console.error("QPageContainer needs to be child of QLayout"),x;ot(Tt,!0);const t=c(()=>{const a={};return d.header.space===!0&&(a.paddingTop=`${d.header.size}px`),d.right.space===!0&&(a[`padding${r.lang.rtl===!0?"Left":"Right"}`]=`${d.right.size}px`),d.footer.space===!0&&(a.paddingBottom=`${d.footer.size}px`),d.left.space===!0&&(a[`padding${r.lang.rtl===!0?"Right":"Left"}`]=`${d.left.size}px`),a});return()=>q("div",{class:"q-page-container",style:t.value},pe(n.default))}});const ia={xs:2,sm:4,md:6,lg:10,xl:14};function et(e,n,r){return{transform:n===!0?`translateX(${r.lang.rtl===!0?"-":""}100%) scale3d(${-e},1,1)`:`scale3d(${e},1,1)`}}var sa=P({name:"QLinearProgress",props:{...st,...Pt,value:{type:Number,default:0},buffer:Number,color:String,trackColor:String,reverse:Boolean,stripe:Boolean,indeterminate:Boolean,query:Boolean,rounded:Boolean,animationSpeed:{type:[String,Number],default:2100},instantFeedback:Boolean},setup(e,{slots:n}){const{proxy:r}=W(),d=ut(e,r.$q),t=Vt(e,ia),a=c(()=>e.indeterminate===!0||e.query===!0),u=c(()=>e.reverse!==e.query),y=c(()=>({...t.value!==null?t.value:{},"--q-linear-progress-speed":`${e.animationSpeed}ms`})),o=c(()=>"q-linear-progress"+(e.color!==void 0?` text-${e.color}`:"")+(e.reverse===!0||e.query===!0?" q-linear-progress--reverse":"")+(e.rounded===!0?" rounded-borders":"")),s=c(()=>et(e.buffer!==void 0?e.buffer:1,u.value,r.$q)),i=c(()=>`with${e.instantFeedback===!0?"out":""}-transition`),_=c(()=>`q-linear-progress__track absolute-full q-linear-progress__track--${i.value} q-linear-progress__track--${d.value===!0?"dark":"light"}`+(e.trackColor!==void 0?` bg-${e.trackColor}`:"")),L=c(()=>et(a.value===!0?1:e.value,u.value,r.$q)),p=c(()=>`q-linear-progress__model absolute-full q-linear-progress__model--${i.value} q-linear-progress__model--${a.value===!0?"in":""}determinate`),g=c(()=>({width:`${e.value*100}%`})),S=c(()=>`q-linear-progress__stripe absolute-${e.reverse===!0?"right":"left"} q-linear-progress__stripe--${i.value}`);return()=>{const T=[q("div",{class:_.value,style:s.value}),q("div",{class:p.value,style:L.value})];return e.stripe===!0&&a.value===!1&&T.push(q("div",{class:S.value,style:g.value})),q("div",{class:o.value,style:y.value,role:"progressbar","aria-valuemin":0,"aria-valuemax":1,"aria-valuenow":e.indeterminate===!0?void 0:e.value},xe(n.default,T))}}});const ua=[q("g",{transform:"translate(20 50)"},[q("rect",{x:"-10",y:"-30",width:" 20",height:"60",fill:"currentColor",opacity:"0.6"},[q("animateTransform",{attributeName:"transform",type:"scale",from:"2",to:"1",begin:"0s",repeatCount:"indefinite",dur:"1s",calcMode:"spline",keySplines:"0.1 0.9 0.4 1",keyTimes:"0;1",values:"2;1"})])]),q("g",{transform:"translate(50 50)"},[q("rect",{x:"-10",y:"-30",width:" 20",height:"60",fill:"currentColor",opacity:"0.8"},[q("animateTransform",{attributeName:"transform",type:"scale",from:"2",to:"1",begin:"0.1s",repeatCount:"indefinite",dur:"1s",calcMode:"spline",keySplines:"0.1 0.9 0.4 1",keyTimes:"0;1",values:"2;1"})])]),q("g",{transform:"translate(80 50)"},[q("rect",{x:"-10",y:"-30",width:" 20",height:"60",fill:"currentColor",opacity:"0.9"},[q("animateTransform",{attributeName:"transform",type:"scale",from:"2",to:"1",begin:"0.2s",repeatCount:"indefinite",dur:"1s",calcMode:"spline",keySplines:"0.1 0.9 0.4 1",keyTimes:"0;1",values:"2;1"})])])];var da=P({name:"QSpinnerFacebook",props:It,setup(e){const{cSize:n,classes:r}=Qt(e);return()=>q("svg",{class:r.value,width:n.value,height:n.value,viewBox:"0 0 100 100",xmlns:"http://www.w3.org/2000/svg",preserveAspectRatio:"xMidYMid"},ua)}}),ca=P({name:"QFooter",props:{modelValue:{type:Boolean,default:!0},reveal:Boolean,bordered:Boolean,elevated:Boolean,heightHint:{type:[String,Number],default:50}},emits:["reveal","focusin"],setup(e,{slots:n,emit:r}){const{proxy:{$q:d}}=W(),t=me(te,x);if(t===x)return console.error("QFooter needs to be child of QLayout"),x;const a=z(parseInt(e.heightHint,10)),u=z(!0),y=z(lt.value===!0||t.isContainer.value===!0?0:window.innerHeight),o=c(()=>e.reveal===!0||t.view.value.indexOf("F")>-1||d.platform.is.ios&&t.isContainer.value===!0),s=c(()=>t.isContainer.value===!0?t.containerHeight.value:y.value),i=c(()=>{if(e.modelValue!==!0)return 0;if(o.value===!0)return u.value===!0?a.value:0;const v=t.scroll.value.position+s.value+a.value-t.height.value;return v>0?v:0}),_=c(()=>e.modelValue!==!0||o.value===!0&&u.value!==!0),L=c(()=>e.modelValue===!0&&_.value===!0&&e.reveal===!0),p=c(()=>"q-footer q-layout__section--marginal "+(o.value===!0?"fixed":"absolute")+"-bottom"+(e.bordered===!0?" q-footer--bordered":"")+(_.value===!0?" q-footer--hidden":"")+(e.modelValue!==!0?" q-layout--prevent-focus"+(o.value!==!0?" hidden":""):"")),g=c(()=>{const v=t.rows.value.bottom,M={};return v[0]==="l"&&t.left.space===!0&&(M[d.lang.rtl===!0?"right":"left"]=`${t.left.size}px`),v[2]==="r"&&t.right.space===!0&&(M[d.lang.rtl===!0?"left":"right"]=`${t.right.size}px`),M});function S(v,M){t.update("footer",v,M)}function T(v,M){v.value!==M&&(v.value=M)}function f({height:v}){T(a,v),S("size",v)}function h(){if(e.reveal!==!0)return;const{direction:v,position:M,inflectionPoint:V}=t.scroll.value;T(u,v==="up"||M-V<100||t.height.value-s.value-M-a.value<300)}function H(v){L.value===!0&&T(u,!0),r("focusin",v)}k(()=>e.modelValue,v=>{S("space",v),T(u,!0),t.animate()}),k(i,v=>{S("offset",v)}),k(()=>e.reveal,v=>{v===!1&&T(u,e.modelValue)}),k(u,v=>{t.animate(),r("reveal",v)}),k([a,t.scroll,t.height],h),k(()=>d.screen.height,v=>{t.isContainer.value!==!0&&T(y,v)});const b={};return t.instances.footer=b,e.modelValue===!0&&S("size",a.value),S("space",e.modelValue),S("offset",i.value),he(()=>{t.instances.footer===b&&(t.instances.footer=void 0,S("size",0),S("offset",0),S("space",!1))}),()=>{const v=xe(n.default,[q(fe,{debounce:0,onResize:f})]);return e.elevated===!0&&v.push(q("div",{class:"q-layout__shadow absolute-full overflow-hidden no-pointer-events"})),q("footer",{class:p.value,style:g.value,onFocusin:H},v)}}}),tt=P({name:"QCardActions",props:{...Ot,vertical:Boolean},setup(e,{slots:n}){const r=Ft(e),d=c(()=>`q-card__actions ${r.value} q-card__actions--${e.vertical===!0?"vert column":"horiz row"}`);return()=>q("div",{class:d.value},pe(n.default))}});const{passive:at}=_t,fa=["both","horizontal","vertical"];var va=P({name:"QScrollObserver",props:{axis:{type:String,validator:e=>fa.includes(e),default:"vertical"},debounce:[String,Number],scrollTarget:{default:void 0}},emits:["scroll"],setup(e,{emit:n}){const r={position:{top:0,left:0},direction:"down",directionChanged:!1,delta:{top:0,left:0},inflectionPoint:{top:0,left:0}};let d=null,t,a;k(()=>e.scrollTarget,()=>{o(),y()});function u(){d!==null&&d();const _=Math.max(0,jt(t)),L=Xt(t),p={top:_-r.position.top,left:L-r.position.left};if(e.axis==="vertical"&&p.top===0||e.axis==="horizontal"&&p.left===0)return;const g=Math.abs(p.top)>=Math.abs(p.left)?p.top<0?"up":"down":p.left<0?"left":"right";r.position={top:_,left:L},r.directionChanged=r.direction!==g,r.delta=p,r.directionChanged===!0&&(r.direction=g,r.inflectionPoint=r.position),n("scroll",{...r})}function y(){t=Wt(a,e.scrollTarget),t.addEventListener("scroll",s,at),s(!0)}function o(){t!==void 0&&(t.removeEventListener("scroll",s,at),t=void 0)}function s(_){if(_===!0||e.debounce===0||e.debounce==="0")u();else if(d===null){const[L,p]=e.debounce?[setTimeout(u,e.debounce),clearTimeout]:[requestAnimationFrame(u),cancelAnimationFrame];d=()=>{p(L),d=null}}}const{proxy:i}=W();return k(()=>i.$q.lang.rtl,u),Be(()=>{a=i.$el.parentNode,y()}),he(()=>{d!==null&&d(),o()}),Object.assign(i,{trigger:s,getPosition:()=>r}),nt}}),ma=P({name:"QLayout",props:{container:Boolean,view:{type:String,default:"hhh lpr fff",validator:e=>/^(h|l)h(h|r) lpr (f|l)f(f|r)$/.test(e.toLowerCase())},onScroll:Function,onScrollHeight:Function,onResize:Function},setup(e,{slots:n,emit:r}){const{proxy:{$q:d}}=W(),t=z(null),a=z(d.screen.height),u=z(e.container===!0?0:d.screen.width),y=z({position:0,direction:"down",inflectionPoint:0}),o=z(0),s=z(lt.value===!0?0:Me()),i=c(()=>"q-layout q-layout--"+(e.container===!0?"containerized":"standard")),_=c(()=>e.container===!1?{minHeight:d.screen.height+"px"}:null),L=c(()=>s.value!==0?{[d.lang.rtl===!0?"left":"right"]:`${s.value}px`}:null),p=c(()=>s.value!==0?{[d.lang.rtl===!0?"right":"left"]:0,[d.lang.rtl===!0?"left":"right"]:`-${s.value}px`,width:`calc(100% + ${s.value}px)`}:null);function g(b){if(e.container===!0||document.qScrollPrevented!==!0){const v={position:b.position.top,direction:b.direction,directionChanged:b.directionChanged,inflectionPoint:b.inflectionPoint.top,delta:b.delta.top};y.value=v,e.onScroll!==void 0&&r("scroll",v)}}function S(b){const{height:v,width:M}=b;let V=!1;a.value!==v&&(V=!0,a.value=v,e.onScrollHeight!==void 0&&r("scrollHeight",v),f()),u.value!==M&&(V=!0,u.value=M),V===!0&&e.onResize!==void 0&&r("resize",b)}function T({height:b}){o.value!==b&&(o.value=b,f())}function f(){if(e.container===!0){const b=a.value>o.value?Me():0;s.value!==b&&(s.value=b)}}let h=null;const H={instances:{},view:c(()=>e.view),isContainer:c(()=>e.container),rootRef:t,height:a,containerHeight:o,scrollbarWidth:s,totalWidth:c(()=>u.value+s.value),rows:c(()=>{const b=e.view.toLowerCase().split(" ");return{top:b[0].split(""),middle:b[1].split(""),bottom:b[2].split("")}}),header:le({size:0,offset:0,space:!1}),right:le({size:300,offset:0,space:!1}),footer:le({size:0,offset:0,space:!1}),left:le({size:300,offset:0,space:!1}),scroll:y,animate(){h!==null?clearTimeout(h):document.body.classList.add("q-body--layout-animate"),h=setTimeout(()=>{h=null,document.body.classList.remove("q-body--layout-animate")},155)},update(b,v,M){H[b][v]=M}};if(ot(te,H),Me()>0){let M=function(){b=null,v.classList.remove("hide-scrollbar")},V=function(){if(b===null){if(v.scrollHeight>d.screen.height)return;v.classList.add("hide-scrollbar")}else clearTimeout(b);b=setTimeout(M,300)},G=function(U){b!==null&&U==="remove"&&(clearTimeout(b),M()),window[`${U}EventListener`]("resize",V)},b=null;const v=document.body;k(()=>e.container!==!0?"add":"remove",G),e.container!==!0&&G("add"),Mt(()=>{G("remove")})}return()=>{const b=xe(n.default,[q(va,{onScroll:g}),q(fe,{onResize:S})]),v=q("div",{class:i.value,style:_.value,ref:e.container===!0?void 0:t,tabindex:-1},b);return e.container===!0?q("div",{class:"q-layout-container overflow-hidden",ref:t},[q(fe,{onResize:T}),q("div",{class:"absolute-full",style:L.value},[q("div",{class:"scroll",style:p.value},[v])])]):v}}});const ha=rt({name:"EssentialLink",props:{propTitle:{type:String,required:!0},propA11yTitle:{type:String,required:!0},propLink:{type:String,default:"#"},propIcon:{type:String,default:""},propIconColor:{type:String,default:""},propIsDisabled:{type:Boolean,default:!1}}}),pa=["innerHTML"];function ga(e,n,r,d,t,a){return D(),K(Kt,{disable:e.propIsDisabled,clickable:"",tag:"a",to:"/"+e.propLink,"active-class":"text-secondary"},{default:w(()=>[e.propIcon?(D(),K(Ye,{key:0,avatar:""},{default:w(()=>[m(At,{class:$t(e.propIconColor),name:e.propIcon},null,8,["class","name"])]),_:1})):De("",!0),m(Ye,null,{default:w(()=>[m(ve,{"aria-hidden":"true"},{default:w(()=>[$("span",{innerHTML:e.propTitle},null,8,pa)]),_:1}),m(ve,{class:"sr-only"},{default:w(()=>[Ee(ce(e.propA11yTitle),1)]),_:1})]),_:1})]),_:1},8,["disable","to"])}var ya=it(ha,[["render",ga]]);const ba={"../../node_modules/quasar/lang/ar.mjs":()=>ie(()=>import("./ar.4424b2ca.js"),[]),"../../node_modules/quasar/lang/en-US.mjs":()=>ie(()=>import("./index.c6ba88b2.js").then(function(e){return e.ao}),["assets/index.c6ba88b2.js","assets/index.dbbf3f0b.css"]),"../../node_modules/quasar/lang/ru.mjs":()=>ie(()=>import("./ru.f960bf45.js"),[]),"../../node_modules/quasar/lang/uk.mjs":()=>ie(()=>import("./uk.b80d3112.js"),[])},wa=rt({name:"MainLayout",components:{EssentialLink:ya},setup(){const e=Ht(),n={...Ae()},r={...Ne()},{locale:d}=Ge({useScope:"global"}),{t,tm:a}=Ge(),u=ta(),y=z([{isoName:"en-US",nativeName:"English (US)",nameInEnglish:"English"},{isoName:"ar",nativeName:"\u0627\u0644\u0639\u0631\u0628\u064A\u0629",nameInEnglish:"Arabic"},{isoName:"id",nativeName:"Bahasa Indonesia",nameInEnglish:"Indonesian"},{isoName:"ru",nativeName:"P\u0443\u0441\u0441\u043A\u0438\u0439",nameInEnglish:"Russian"},{isoName:"es",nativeName:"Espa\xF1ol",nameInEnglish:"Spanish"},{isoName:"fr",nativeName:"Fran\xE7ais",nameInEnglish:"French"}]),o=z(!1),s=z(!1),i=z(!1),_=z(!1),L=z(!1),p=z(!1),g=c(()=>e.com.lmsError.callSucceeded);k(g,(f,h)=>{h==!0&&f==!1&&(console.log("lmsErrorCallSucceeded - newValue: ",f),console.log("lmsErrorCallSucceeded - oldValue: ",h),f==!1&&(e.com.willCheck_Connection||e.com.willCheck_SCORMApi)&&(L.value=!0))});const S=f=>{console.log("***------ changeLanguage ***------ ",f),d.value=f;const h=f=="ar"||f=="ru"||f=="uk"?f:"en-US";ba[`../../node_modules/quasar/lang/${h}.mjs`]().then(b=>{u.lang.set(b.default)}),document.title=t("manifest.title"),e.com_setLanguage(f);let H=document.querySelector(":root");f=="ru"&&H.style.setProperty("--font_size","17px"),f=="sk"||f=="de-DE"||f=="fr"||f=="id"||f=="es"?H.style.setProperty("--font_size","17px"):H.style.setProperty("--font_size","19px"),r.refreshAOS()},T=()=>{e.com.lmsError={callSucceeded:!0,lastErrorCode:0,lastErrorMsg:""}};return Be(()=>{document.title=t("manifest.title"),e.rte_init(null),e.rte_init(function(){const f=e.com_getLanguage;f!="null"&&f!=""&&f!==void 0&&f!==0&&f!=="0"?(console.log("***------ returned saved lang from LMS ***------"),S(f)):(S("en-US"),p.value=!0),e.rte_getReturn&&(i.value=!0)})}),{store:e,...Ae(),...Ne(),leftDrawerOpen:o,rightDrawerOpen:s,toggleLeftDrawer(){o.value=!o.value,o.value&&n.setFocusById("idMenuItems",500)},toggleRightDrawer(){s.value=!s.value,s.value&&n.setFocusById("resourcesTitle",500)},locale:d,t,tm:a,openBookmarkDialog:i,openCloseDialog:_,openLMSErrorDialog:L,openLanguageDialog:p,hideError:T,languages:y,changeLanguage:S}}}),qa=["innerHTML"],Ca={class:"q-px-sm bg-grey-1 text-body2 row justify-end items-center",style:{height:"28px"}},La=["innerHTML"],ka=["innerHTML"],Sa=["innerHTML"],Ta=["innerHTML"],_a=["innerHTML"],Ma=["innerHTML"],$a=["innerHTML"],Ha={class:"row flex-center container"},za={class:"col-10 col-md-3"},Da=["innerHTML"],Ea=["innerHTML"],Ba={class:"row q-col-gutter-md"},xa=["innerHTML"],Pa=["innerHTML"],Va=["innerHTML"],Ia=["innerHTML"],Qa=["innerHTML"],Oa=["innerHTML"],Fa={class:"row"},Aa={class:"col-12"},Na=["innerHTML"],Ra=["innerHTML"],Ua=["innerHTML"];function Wa(e,n,r,d,t,a){const u=Re("EssentialLink"),y=Re("router-view");return D(),K(ma,{view:"hHh LpR fFf"},{default:w(()=>[m(oa,{elevated:""},{default:w(()=>[e.store.getPageStatusById("pageintro")!==1?(D(),Q("p",{key:0,class:"sr-only",innerHTML:e.t("ui.sr")},null,8,qa)):De("",!0),$("div",Ca,[m(_e,{"aria-hidden":"true",class:"q-mx-md",vertical:"",inset:""}),$("span",{class:"q-mr-sm text-primary",innerHTML:e.$t("ui.chooselang")},null,8,La),$("a",{class:"text-link q-mr-sm text-primary",href:"#",onClick:n[0]||(n[0]=zt(o=>{e.openLanguageDialog=!0},["prevent"])),innerHTML:e.locale.toUpperCase()},null,8,ka)]),m(na,{class:"bg-white text-secondary relative-position"},{default:w(()=>[m(Dt,{mode:"out-in",appear:"","enter-active-class":"animated rotateIn","leave-active-class":"animated rotateOut"},{default:w(()=>[(D(),K(O,{"aria-hidden":"true",key:e.leftDrawerOpen,size:"27px",padding:"sm",dense:"",flat:"",round:"",icon:e.leftDrawerOpen?"bi-three-dots-vertical":"bi-list",onClick:n[1]||(n[1]=o=>e.toggleLeftDrawer())},null,8,["icon"]))]),_:1}),$("button",{class:"sr-only",role:"button",size:"27px",onClick:n[2]||(n[2]=o=>e.toggleLeftDrawer())},ce(e.t("ui.menu")),1),m(Nt,{class:"gt-md absolute-center",src:aa,"spinner-color":"white",style:{width:"55px"}}),m(We),m(_e,{"aria-hidden":"true",class:"gt-sm q-mx-md",vertical:"",inset:""}),m(O,{onClick:n[3]||(n[3]=o=>e.toggleRightDrawer()),padding:"sm",flat:"",round:"",color:"secondary",icon:"bi-info-circle","aria-label":e.t("ui.resources")},null,8,["aria-label"]),m(O,{onClick:n[4]||(n[4]=o=>e.openCloseDialog=!e.openCloseDialog),padding:"sm",flat:"",round:"",color:"secondary",icon:"bi-x-circle","aria-label":e.t("ui.close")},null,8,["aria-label"])]),_:1})]),_:1}),m(Ze,{class:"bg-grey",modelValue:e.leftDrawerOpen,"onUpdate:modelValue":n[5]||(n[5]=o=>e.leftDrawerOpen=o),side:"left",overlay:"",behavior:"desktop",bordered:"",width:e.$q.screen.gt.sm?600:null},{default:w(()=>[m(Ke,{class:"q-pt-md",separator:""},{default:w(()=>[m(ve,{id:"idMenuItems",class:"sr-only",header:"",style:{outline:"0"},tabindex:"0"},{default:w(()=>[Ee(ce(e.t("ui.menuItems")),1)]),_:1}),(D(!0),Q(ee,null,re(e.store.manifest.content,(o,s)=>(D(),Q(ee,null,[o.id!=="pageintro"&&o.id!=="pagefinal"?(D(),K(u,{key:s,propIsDisabled:o.status===-1,propIcon:o.status===-1?"bi-lock":o.status===1?"bi-check2-square":"bi-unlock",propIconColor:o.status===-1?"text-warning":o.status===1?"text-positive":"text-primary",propLink:o.id,propTitle:e.tm("manifest.content")[s].title,propA11yTitle:(o.status===-1?e.t("ui.itemLocked"):o.status===1?e.t("ui.itemComplete"):e.t("ui.itemUnLocked"))+" - "+e.tm("manifest.content")[s].title},null,8,["propIsDisabled","propIcon","propIconColor","propLink","propTitle","propA11yTitle"])):De("",!0)],64))),256))]),_:1})]),_:1},8,["modelValue","width"]),m(Ze,{class:"bg-grey",modelValue:e.rightDrawerOpen,"onUpdate:modelValue":n[6]||(n[6]=o=>e.rightDrawerOpen=o),side:"right",overlay:"",behavior:"desktop",bordered:"",width:e.$q.screen.gt.sm?600:null},{default:w(()=>[m(Ke,{class:"q-px-lg q-pt-md",separator:""},{default:w(()=>[m(ve,{id:"idMenuItems",class:"sr-only",header:"",style:{outline:"0"},tabindex:"0"},{default:w(()=>[Ee(ce(e.t("ui.menuItems")),1)]),_:1}),$("h2",{id:"resourcesTitle",class:"q-pb-lg",innerHTML:e.t("resources.title1")},null,8,Sa),$("h3",{class:"q-pb-md",innerHTML:e.t("resources.title2")},null,8,Ta),(D(!0),Q(ee,null,re(e.tm("resources.list1"),(o,s)=>(D(),Q("p",{key:s,class:"q-pb-xs",innerHTML:o},null,8,_a))),128)),$("h3",{class:"q-mt-sm q-py-md",innerHTML:e.t("resources.title3")},null,8,Ma),(D(!0),Q(ee,null,re(e.tm("resources.list2"),(o,s)=>(D(),Q("p",{key:s,class:"q-pb-xs",innerHTML:o},null,8,$a))),128))]),_:1})]),_:1},8,["modelValue","width"]),m(ra,{style:{"max-width":"1920px !important",margin:"0 auto !important"}},{default:w(()=>[m(y)]),_:1}),m(ca,{elevated:"",class:"q-py-sm bg-primary text-white",style:{"z-index":"50"}},{default:w(()=>[$("div",Ha,[$("div",za,[m(sa,{color:"secondary","track-color":"secondary",stripe:"",rounded:"",size:"10px",value:e.store.com.settings.suspendData.progressMeasure?e.store.com.settings.suspendData.progressMeasure:0},null,8,["value"])]),R(m(da,{style:{position:"absolute",right:"15px"},color:"white",size:"1em"},null,512),[[Et,e.store.com.checkingConnection]])])]),_:1}),m(se,{modelValue:e.openLanguageDialog,"onUpdate:modelValue":n[7]||(n[7]=o=>e.openLanguageDialog=o),persistent:"","transition-show":"scale","transition-hide":"scale"},{default:w(()=>[m(ue,{class:"bg-grey-1",style:{width:"800px"}},{default:w(()=>[m(F,{class:"q-pa-lg"},{default:w(()=>[$("h3",{class:"Citi-Sans-Text-Bold",innerHTML:e.t("ui.langselection")},null,8,Da)]),_:1}),m(F,{class:"q-px-lg q-pt-none q-pb-lg"},{default:w(()=>[$("p",{innerHTML:e.t("ui.chooselang")},null,8,Ea)]),_:1}),m(F,{class:"bg-white q-pa-lg scroll"},{default:w(()=>[$("div",Ba,[(D(!0),Q(ee,null,re(e.languages,(o,s)=>(D(),Q("div",{class:"col-12 col-md-6",key:s},[R((D(),K(O,{onClick:i=>e.changeLanguage(o.isoName),class:"full-width full-height",unelevated:"","no-caps":"",rounded:"",color:"secondary"},{default:w(()=>[$("div",null,[$("p",{innerHTML:o.nativeName},null,8,xa),$("p",{class:"text-caption",innerHTML:o.nameInEnglish},null,8,Pa)])]),_:2},1032,["onClick"])),[[Y]])]))),128))])]),_:1})]),_:1})]),_:1},8,["modelValue"]),m(se,{modelValue:e.openBookmarkDialog,"onUpdate:modelValue":n[9]||(n[9]=o=>e.openBookmarkDialog=o),persistent:"","transition-show":"scale","transition-hide":"scale"},{default:w(()=>[m(ue,{class:"bg-grey-1",style:{width:"600px"}},{default:w(()=>[m(F,{class:"q-pa-lg"},{default:w(()=>[$("h3",{class:"Citi-Sans-Text-Bold",innerHTML:e.t("bookmark.title")},null,8,Va)]),_:1}),m(F,{class:"q-px-lg q-pt-none q-pb-lg"},{default:w(()=>[$("p",{innerHTML:e.t("bookmark.message")},null,8,Ia)]),_:1}),m(tt,{vertical:!e.$q.screen.gt.md,align:"center",class:"bg-white q-pa-lg"},{default:w(()=>[R(m(O,{class:"btn-fixed-width",unelevated:"","no-caps":"",rounded:"",color:"secondary",label:e.t("bookmark.cancel")},null,8,["label"]),[[Y]]),R(m(O,{class:"btn-fixed-width",unelevated:"","no-caps":"",rounded:"",color:"secondary",label:e.t("bookmark.ok"),onClick:n[8]||(n[8]=o=>e.routerTo("/"+e.store.rte.settings.location))},null,8,["label"]),[[Y]])]),_:1},8,["vertical"])]),_:1})]),_:1},8,["modelValue"]),m(se,{modelValue:e.openCloseDialog,"onUpdate:modelValue":n[11]||(n[11]=o=>e.openCloseDialog=o),persistent:"","transition-show":"scale","transition-hide":"scale"},{default:w(()=>[m(ue,{class:"bg-grey-1",style:{width:"600px"}},{default:w(()=>[m(F,{class:"q-pa-lg"},{default:w(()=>[$("h3",{class:"Citi-Sans-Text-Bold",innerHTML:e.t("closemodule.title")},null,8,Qa)]),_:1}),m(F,{class:"q-px-lg q-pt-none q-pb-lg"},{default:w(()=>[$("p",{innerHTML:e.t("closemodule.message")},null,8,Oa)]),_:1}),m(tt,{vertical:!e.$q.screen.gt.md,align:"center",class:"bg-white q-pa-lg"},{default:w(()=>[R(m(O,{class:"btn-fixed-width",unelevated:"","no-caps":"",rounded:"",color:"secondary",label:e.t("closemodule.cancel")},null,8,["label"]),[[Y]]),R(m(O,{class:"btn-fixed-width",unelevated:"","no-caps":"",rounded:"",color:"secondary",label:e.t("closemodule.ok"),onClick:n[10]||(n[10]=o=>e.store.com_exit())},null,8,["label"]),[[Y]])]),_:1},8,["vertical"])]),_:1})]),_:1},8,["modelValue"]),m(se,{modelValue:e.openLMSErrorDialog,"onUpdate:modelValue":n[13]||(n[13]=o=>e.openLMSErrorDialog=o),persistent:"","transition-show":"scale","transition-hide":"scale"},{default:w(()=>[m(ue,{style:{width:"600px"}},{default:w(()=>[m(F,{class:"row q-py-sm items-center"},{default:w(()=>[m(We),R(m(O,{onClick:n[12]||(n[12]=o=>e.hideError()),icon:"close",flat:"",round:"",dense:""},null,512),[[Y]])]),_:1}),m(_e,{"aria-hidden":"true"}),m(F,{class:"q-pa-lg q-pa-lg-xl scroll",style:{"max-height":"70vh"}},{default:w(()=>[$("div",Fa,[$("div",Aa,[$("p",{class:"q-mb-md",innerHTML:e.tm("errorLMS.text1")},null,8,Na),$("p",{class:"q-mb-md",innerHTML:e.tm("errorLMS.text2")},null,8,Ra),$("p",{class:"q-mb-md",innerHTML:e.tm("errorLMS.text3")},null,8,Ua)])])]),_:1})]),_:1})]),_:1},8,["modelValue"])]),_:1})}var un=it(wa,[["render",Wa]]);export{un as default};
