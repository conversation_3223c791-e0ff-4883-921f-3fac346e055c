var s={isoName:"lv",nativeName:"Latvie\u0161u valoda",label:{clear:"Att\u012Br\u012Bt",ok:"OK",cancel:"Atcelt",close:"Aizv\u0113rt",set:"Iestat\u012Bt",select:"Izv\u0113l\u0113ties",reset:"Atiestat\u012Bt",remove:"No\u0146emt",update:"Atjaunin\u0101t",create:"Izveidot",search:"Mekl\u0113t",filter:"Filt\u0113t",refresh:"Atjaunot",expand:t=>t?`Papla\u0161in\u0101t "${t}"`:"Izv\u0113rst",collapse:t=>t?`Sak\u013Caut "${t}"`:"Sak\u013Caut"},date:{days:"Sv\u0113tdiena_Pirmdiena_Otrdiena_Tre\u0161diena_Ceturtdiena_Piektdiena_Sestdiena".split("_"),daysShort:"Sv_Pi_Ot_Tr_Ce_Pi_Se".split("_"),months:"Janv\u0101ris_Febru\u0101ris_Marts_Apr\u012Blis_Maijs_J\u016Bnijs_J\u016Blijs_Augusts_Septembris_Okrobris_Novembris_Decembris".split("_"),monthsShort:"Jan_Feb_Mar_Apr_Mai_J\u016Bn_J\u016Bl_Aug_Sep_Okt_Nov_Dec".split("_"),firstDayOfWeek:1,format24h:!0,pluralDay:"dienas"},table:{noData:"Nav datu",noResults:"Ieraksti nav atrasti",loading:"Atjaunojas...",selectedRecords:t=>t===1?"1 izv\u0113l\u0113ta rinda.":(t===0?"Nav":t)+" izv\u0113l\u0113tas rindas.",recordsPerPage:"Rindas lap\u0101:",allRows:"Visas",pagination:(t,a,e)=>t+"-"+a+" no "+e,columns:"Kolonnas"},editor:{url:"URL",bold:"Trekns",italic:"Kurs\u012Bvs",strikethrough:"Nosv\u012Btrots",underline:"Apak\u0161sv\u012Btra",unorderedList:"Mar\u0137\u0113tais saraksts",orderedList:"Numur\u0113tais saraksts",subscript:"Apak\u0161raksts",superscript:"Aug\u0161raksts",hyperlink:"Saite",toggleFullscreen:"Pilnekr\u0101na re\u017E\u012Bms",quote:"Cit\u0101ts",left:"Izl\u012Bdzin\u0101t gar kreiso malu",center:"Centr\u0113t",right:"Izl\u012Bdzin\u0101t gar labo malu",justify:"Izl\u012Bdzin\u0101t gar ab\u0101m mal\u0101m",print:"Druk\u0101t",outdent:"Samazin\u0101t atk\u0101pi",indent:"Palielin\u0101t atk\u0101pi",removeFormat:"No\u0146emt format\u0113jumu",formatting:"Format\u0113t",fontSize:"Fonta izm\u0113rs",align:"Izl\u012Bdzin\u0101t",hr:"Ievietot horizont\u0101lo l\u012Bniju",undo:"Atsaukt",redo:"Atk\u0101rtot",heading1:"Virsraksts 1",heading2:"Virsraksts 2",heading3:"Virsraksts 3",heading4:"Virsraksts 4",heading5:"Virsraksts 5",heading6:"Virsraksts 6",paragraph:"Rindkopa",code:"Kods",size1:"\u013Boti mazs",size2:"Mazs",size3:"Norm\u0101ls",size4:"Vid\u0113js",size5:"Liels",size6:"\u013Boti liels",size7:"Maksim\u0101ls",defaultFont:"Fonts p\u0113c noklus\u0113juma",viewSource:"Skat\u012Bt avotu"},tree:{noNodes:"Nav pieejami mezgli",noResults:"Nav atrasti atbilsto\u0161ie mezgli"}};export{s as default};
