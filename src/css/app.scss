// app global css in SCSS form

//-------------------------------------------------------------------------------------
// Container <PERSON><PERSON><PERSON> style
//-------------------------------------------------------------------------------------
.container {
	/* Set horizontal margins to auto to center the container */
	margin-right: auto;
	margin-left: auto;

	/* Set the maximum width of the container based on the breakpoint */
	/* The following values are for the default Bootstrap breakpoints */
	/* Change these values depending on the breakpoint you are targeting */

	/* Extra small devices (phones, 576px and down) */
	max-width: 100%;
	@media (max-width: 575px) {
		padding-right: 14px;
		padding-left: 14px;
	}

	/* Small devices (landscape phones, 576px and up) */
	@media (min-width: 576px) {
		max-width: 540px;
	}

	/* Medium devices (tablets, 768px and up) */
	@media (min-width: 768px) {
		max-width: 720px;
	}

	/* Large devices (desktops, 992px and up) */
	@media (min-width: 992px) {
		max-width: 960px;
	}

	/* Extra large devices (large desktops, 1200px and up) */
	@media (min-width: 1200px) {
		max-width: 1140px;
	}
}




//-------------------------------------------------------------------------------------
// Fonts
//-------------------------------------------------------------------------------------
@font-face {
	font-family: 'Citi-Serif-Display-Regular';
	src: url('./fonts/Citi-Serif-Display-Regular.woff2') format('woff2');
}
.Citi-Serif-Display-Regular {
	font-family: 'Citi-Serif-Display-Regular', 'Arial';
}

@font-face {
	font-family: 'Citi-Serif-Display-Regular-Italic';
	src: url('./fonts/Citi-Serif-Display-Regular-Italic.woff2') format('woff2');
}
.Citi-Serif-Display-Regular-Italic {
	font-family: 'Citi-Serif-Display-Regular-Italic', 'Arial';
}

@font-face {
	font-family: 'Citi-Sans-Display-Regular';
	src: url('./fonts/Citi-Sans-Display-Regular.woff2') format('woff2');
}
.Citi-Sans-Display-Regular {
	font-family: 'Citi-Sans-Display-Regular', 'Arial';
}

@font-face {
	font-family: 'Citi-Sans-Text-Regular';
	src: url('./fonts/Citi-Sans-Text-Regular.woff2') format('woff2');
}
.Citi-Sans-Text-Regular {
	font-family: 'Citi-Sans-Text-Regular', 'Arial';
}

@font-face {
	font-family: 'Citi-Sans-Text-Regular-Italic';
	src: url('./fonts/Citi-Sans-Text-Regular-Italic.woff2') format('woff2');
}
.Citi-Sans-Text-Regular-Italic {
	font-family: 'Citi-Sans-Text-Regular-Italic', 'Arial';
}

@font-face {
	font-family: 'Citi-Sans-Text-Bold';
	src: url('./fonts/Citi-Sans-Text-Bold.woff2') format('woff2');
}
.Citi-Sans-Text-Bold {
	font-family: 'Citi-Sans-Text-Bold', 'Arial';
}




//-------------------------------------------------------------------------------------
// SETTING THE ROOT/HTML/BODY STYLES: REM and FONT-FAMILY
// Note: the $body-font-size inside quasar.variables sets the body font not the HTML/root font
//-------------------------------------------------------------------------------------
:root {
    --font_size: $body-font-size;
}
html, body {
	font-size: var(--font_size);

	color: $primary;
}




//-------------------------------------------------------------------------------------
// Cleaning default paddings and margings
//-------------------------------------------------------------------------------------
p, h1, h2, h3 {
    padding: 0;
    margin: 0;
}




//-------------------------------------------------------------------------------------
// links
//-------------------------------------------------------------------------------------
a.text-link {
    text-decoration: underline;
	color: $secondary;
}
a.text-link:hover,
a.text-link:focus {
    color: $primary;
}

a.text-link-dark-bg {
    text-decoration: underline;
	color: white;
}
a.text-link-dark-bg:hover,
a.text-link-dark-bg:focus {
    color: $grey;
}




//-------------------------------------------------------------------------------------
// overwrites max width for pop-ups
//-------------------------------------------------------------------------------------
@media (min-width: 600px) {
	.q-dialog__inner--minimized > div {
		max-width: 70vw;
	}
}




//-------------------------------------------------------------------------------------
// flex-break class
//-------------------------------------------------------------------------------------
.flex-break {
	flex: 1 0 100% !important;
    height: 0 !important;
}




//-------------------------------------------------------------------------------------
// btn-fixed-width
//-------------------------------------------------------------------------------------
.btn-fixed-width {
	min-width: 200px !important;
}




//-------------------------------------------------------------------------------------
// default button border size
//-------------------------------------------------------------------------------------
.q-btn--outline:before {
    border: 2px solid currentColor !important;
}




// spaacing between options
.q-radio, .q-checkbox {
	padding-top: 5px;
	padding-bottom: 5px;
}




//opacityZero
.opacity-zero {
	opacity: 0;
}

.sr-only {
	position: absolute;
	width: 1px;
	height: 1px;
	margin: -1px;
	padding: 0;
	overflow: hidden;
	clip: rect(0, 0, 0, 0);
	border: 0;
}

.last-in-list:last-child {
    margin-bottom: 0 !important;
    padding-bottom: 0 !important;
}
.last-in-list-no-margin:last-child {
    margin-bottom: 0 !important;
    padding-bottom: 0 !important;
}

.balance-this-text {
    text-wrap: balance !important;
}

.absolute-center {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
}




// -----------------------------------------------
// animations
// -----------------------------------------------
.flip-in-ver-left{animation:flip-in-ver-left .3s cubic-bezier(.25,.46,.45,.94) both}
 @keyframes flip-in-ver-left{0%{transform:rotateY(80deg);opacity:0}100%{transform:rotateY(0);opacity:1}}

 .flip-in-ver-right{animation:flip-in-ver-right .3s cubic-bezier(.25,.46,.45,.94) both}
@keyframes flip-in-ver-right{0%{transform:rotateY(-80deg);opacity:0}100%{transform:rotateY(0);opacity:1}}

.flip-out-ver-right{animation:flip-out-ver-right .3s cubic-bezier(.55,.085,.68,.53) both}
 @keyframes flip-out-ver-right{0%{transform:rotateY(0);opacity:1}100%{transform:rotateY(70deg);opacity:0}}

 .flip-out-ver-left{animation:flip-out-ver-left .3s cubic-bezier(.55,.085,.68,.53) both}
@keyframes flip-out-ver-left{0%{transform:rotateY(0);opacity:1}100%{transform:rotateY(-70deg);opacity:0}}

.fade-in-top{animation:fade-in-top 1.5s cubic-bezier(.39,.575,.565,1.000) both}
@keyframes fade-in-top{0%{transform:translateY(-100px);opacity:0}100%{transform:translateY(0);opacity:1}}

.fade-out-right{animation:fade-out-right 0.8s cubic-bezier(.25,.46,.45,.94) both}
 @keyframes fade-out-right{0%{transform:translateX(0);opacity:1}100%{transform:translateX(100px);opacity:0}}

.wave-background {
	background: linear-gradient(90deg, white, $positive, white);
	background-size: 200% 100%;
	animation: waveAnimation 3s infinite linear;
  }
  @keyframes waveAnimation {
	0% {
	  background-position: 0% 50%;
	}
	100% {
	  background-position: 100% 50%;
	}
  }


  // ASSESSMENT TEST OUT
.bg-testout_assessment_fail {
    background-color: $primary;
    background-image: url("../assets/global/testout_assessment_bg.png");
    background-position: left center !important;
    background-repeat: no-repeat !important;
    background-size: auto 100% !important;
}
body.screen--xs,
body.screen--sm {
    .bg-testout_assessment_fail {
        background-image: none !important;
    }
}
[dir=rtl] .bg-testout_assessment_fail{
	background-image: url("../assets/global/testout_assessment_bg_rtl.png");
}

.bg-testout_assessment_pass {
    background-color: $secondary;
    background-image: url("../assets/global/testout_assessment_bg.png");
    background-position: left top !important;
    background-repeat: no-repeat !important;
    background-size: 50% 100% !important;
}
body.screen--xs,
body.screen--sm {
    .bg-testout_assessment_pass {
        background-image: none !important;
    }
}
[dir=rtl] .bg-testout_assessment_pass{
	background-image: url("../assets/global/testout_assessment_bg_rtl.png");
}