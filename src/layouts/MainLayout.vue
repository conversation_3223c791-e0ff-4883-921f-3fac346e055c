<template>
    <q-layout view="hHh LpR fFf">
        <q-header elevated>


            <!-- A11y INTRO -->
            <p
                class="sr-only"
                v-if="store.getPageStatusById('pageintro') !== 1"
                v-html="t('ui.sr')"
            ></p>

            <!-- language section -->
            <div class="q-px-sm bg-grey-1 text-body2 row justify-end items-center" style="height: 28px">
				<q-separator aria-hidden="true" class="q-mx-md" vertical inset></q-separator>
				<span class="q-mr-sm text-primary" v-html="$t('ui.chooselang')"></span>
				<a class="text-link q-mr-sm text-primary" href="#" @click.prevent="openLanguageDialog = true;" v-html="locale.toUpperCase()"></a>
			</div>

            <!-- toolbar -->
            <q-toolbar class="bg-white text-secondary relative-position">
                <!-- menu toggle -->
                <transition
                    mode="out-in"
                    appear
                    enter-active-class="animated rotateIn"
                    leave-active-class="animated rotateOut"
                >
                    <q-btn
                        aria-hidden="true"
                        :key="leftDrawerOpen"
                        size="27px"
                        padding="sm"
                        dense
                        flat
                        round
                        :icon="!leftDrawerOpen ? 'bi-list' : 'bi-three-dots-vertical'"
                        @click="toggleLeftDrawer()"
                    />
                </transition>

                <button
                    class="sr-only"
                    role="button"
                    size="27px"
                    @click="toggleLeftDrawer()"
                >
                    {{ t("ui.menu") }}
                </button>

                <!-- logo -->
                <q-img
                    class="gt-md absolute-center"
                    src="~assets/logo-blue-red.svg"
                    spinner-color="white"
                    style="width: 55px"
                ></q-img>

                <q-space></q-space>

                <q-separator
                    aria-hidden="true"
                    class="gt-sm q-mx-md"
                    vertical
                    inset
                ></q-separator>

                <!-- resources -->
                <q-btn
                    @click="toggleRightDrawer()"
                    padding="sm"
                    flat
                    round
                    color="secondary"
                    icon="bi-info-circle"
                    :aria-label="t('ui.resources')"
                ></q-btn>

                <!-- close button -->
                <q-btn
                    @click="openCloseDialog = !openCloseDialog"
                    padding="sm"
                    flat
                    round
                    color="secondary"
                    icon="bi-x-circle"
                    :aria-label="t('ui.close')"
                ></q-btn>
            </q-toolbar>

        </q-header>

        <!-- left menu ORIGINAL SPOT -->
        <!-- <q-drawer></q-drawer> -->
        <q-drawer
            class="bg-grey"
            v-model="leftDrawerOpen"
            side="left"
            overlay
            behavior="desktop"
            bordered
            :width="$q.screen.gt.sm ? 600 : null"
        >
            <q-list class="q-pt-md" separator>
                <!-- intro just for a11y -->
                <q-item-label
                    id="idMenuItems"
                    class="sr-only"
                    header
                    style="outline: 0"
                    tabindex="0"
                >
                    {{ t("ui.menuItems") }}
                </q-item-label>

                <template v-for="(page, index) in store.manifest.content">
                    <EssentialLink
                        v-if="page.id !== 'pageintro' && page.id !== 'pagefinal'"
                        :key="index"
                        :propIsDisabled="page.status === -1"
                        :propIcon="
                            page.status === -1
                                ? 'bi-lock'
                                : page.status === 1
                                ? 'bi-check2-square'
                                : 'bi-unlock'
                        "
                        :propIconColor="
                            page.status === -1
                                ? 'text-warning'
                                : page.status === 1
                                ? 'text-positive'
                                : 'text-primary'
                        "
                        :propLink="page.id"
                        :propTitle="tm('manifest.content')[index].title"
                        :propA11yTitle="
                            (page.status === -1
                                ? t('ui.itemLocked')
                                : page.status === 1
                                ? t('ui.itemComplete')
                                : t('ui.itemUnLocked')) +
                            ' - ' +
                            tm('manifest.content')[index].title
                        "
                    />
                </template>
            </q-list>
        </q-drawer>

        <q-drawer
            class="bg-grey"
            v-model="rightDrawerOpen"
            side="right"
            overlay
            behavior="desktop"
            bordered
            :width="$q.screen.gt.sm ? 600 : null"
        >
            <q-list class="q-px-lg q-pt-md" separator>
                <!-- intro just for a11y -->
                <q-item-label
                    id="idMenuItems"
                    class="sr-only"
                    header
                    style="outline: 0"
                    tabindex="0"
                >
                    {{ t("ui.menuItems") }}
                </q-item-label>

                <h2
                    id="resourcesTitle"
                    class="q-pb-lg"
                    v-html="t('resources.title1')"
                ></h2>
                <h3 class="q-pb-md" v-html="t('resources.title2')"></h3>

                <p
                    v-for="(item, index) in tm('resources.list1')"
                    :key="index"
                    class="q-pb-xs"
                    v-html="item"
                ></p>
                <h3 class="q-mt-sm q-py-md" v-html="t('resources.title3')"></h3>
                <p
                    v-for="(item, index) in tm('resources.list2')"
                    :key="index"
                    class="q-pb-xs"
                    v-html="item"
                ></p>
            </q-list>
        </q-drawer>

        <!-- main page section -->
        <q-page-container style="max-width: 1920px !important; margin: 0 auto !important">
            <router-view />
        </q-page-container>

        <!-- footer -->
        <q-footer elevated class="q-py-sm bg-primary text-white" style="z-index: 50">
            <div class="row flex-center container">
                <div class="col-10 col-md-3">
                    <q-linear-progress
                        color="secondary"
                        track-color="secondary"
                        stripe
                        rounded
                        size="10px"
                        :value="
                            store.com.settings.suspendData.progressMeasure
                                ? store.com.settings.suspendData.progressMeasure
                                : 0
                        "
                    ></q-linear-progress>
                </div>
                <q-spinner-facebook
                    v-show="store.com.checkingConnection"
                    style="position: absolute; right: 15px"
                    color="white"
                    size="1em"
                ></q-spinner-facebook>
            </div>
        </q-footer>

        <!-- language select dialog window -->

        <!-- /////////////////////////////////////////////// TODO: v-model tagged out for building -->

        <q-dialog
            v-model="openLanguageDialog"
            persistent
            transition-show="scale"
            transition-hide="scale"
        >
            <q-card class="bg-grey-1" style="width: 800px">
                <q-card-section class="q-pa-lg">
                    <h3 class="Citi-Sans-Text-Bold" v-html="t('ui.langselection')"></h3>
                </q-card-section>

                <q-card-section class="q-px-lg q-pt-none q-pb-lg">
                    <p v-html="t('ui.chooselang')"></p>
                </q-card-section>

                <q-card-section class="bg-white q-pa-lg scroll">
                    <div class="row q-col-gutter-md">
                        <div
                            class="col-12 col-md-6"
                            v-for="(item, index) in languages"
                            :key="index"
                        >
                            <q-btn
                                @click="changeLanguage(item.isoName)"
                                class="full-width full-height"
                                unelevated
                                no-caps
                                rounded
                                color="secondary"
                                v-close-popup
                            >
                                <div>
                                    <p v-html="item.nativeName"></p>
                                    <p
                                        class="text-caption"
                                        v-html="item.nameInEnglish"
                                    ></p>
                                </div>
                            </q-btn>
                        </div>
                    </div>
                </q-card-section>
            </q-card>
        </q-dialog>



        <!-- bookmark resume/restart dialog window -->
        <q-dialog
            v-model="openBookmarkDialog"
            persistent
            transition-show="scale"
            transition-hide="scale"
        >
            <q-card class="bg-grey-1" style="width: 600px">
                <q-card-section class="q-pa-lg">
                    <h3 class="Citi-Sans-Text-Bold" v-html="t('bookmark.title')"></h3>
                </q-card-section>

                <q-card-section class="q-px-lg q-pt-none q-pb-lg">
                    <p v-html="t('bookmark.message')"></p>
                </q-card-section>

                <q-card-actions
                    :vertical="$q.screen.gt.md ? false : true"
                    align="center"
                    class="bg-white q-pa-lg"
                >
                    <q-btn
                        class="btn-fixed-width"
                        unelevated
                        no-caps
                        rounded
                        color="secondary"
                        :label="t('bookmark.cancel')"
                        v-close-popup
                    >
                    </q-btn>
                    <q-btn
                        class="btn-fixed-width"
                        unelevated
                        no-caps
                        rounded
                        color="secondary"
                        :label="t('bookmark.ok')"
                        v-close-popup
                        @click="routerTo('/' + store.rte.settings.location)"
                    >
                    </q-btn>
                </q-card-actions>
            </q-card>
        </q-dialog>



        <!-- Close course dialog window -->
        <q-dialog
            v-model="openCloseDialog"
            persistent
            transition-show="scale"
            transition-hide="scale"
        >
            <q-card class="bg-grey-1" style="width: 600px">
                <q-card-section class="q-pa-lg">
                    <h3 class="Citi-Sans-Text-Bold" v-html="t('closemodule.title')"></h3>
                </q-card-section>

                <q-card-section class="q-px-lg q-pt-none q-pb-lg">
                    <p v-html="t('closemodule.message')"></p>
                </q-card-section>

                <q-card-actions
                    :vertical="$q.screen.gt.md ? false : true"
                    align="center"
                    class="bg-white q-pa-lg"
                >
                    <q-btn
                        class="btn-fixed-width"
                        unelevated
                        no-caps
                        rounded
                        color="secondary"
                        :label="t('closemodule.cancel')"
                        v-close-popup
                    >
                    </q-btn>
                    <q-btn
                        class="btn-fixed-width"
                        unelevated
                        no-caps
                        rounded
                        color="secondary"
                        :label="t('closemodule.ok')"
                        v-close-popup
                        @click="store.com_exit()"
                    >
                    </q-btn>
                </q-card-actions>
            </q-card>
        </q-dialog>



        <!-- LMS ERROR DIALOG Window -->
        <q-dialog
            v-model="openLMSErrorDialog"
            persistent
            transition-show="scale"
            transition-hide="scale"
        >
            <q-card style="width: 600px">
                <q-card-section class="row q-py-sm items-center">
                    <q-space></q-space>
                    <q-btn
                        @click="hideError()"
                        icon="close"
                        flat
                        round
                        dense
                        v-close-popup
                    ></q-btn>
                </q-card-section>

                <q-separator aria-hidden="true"></q-separator>

                <q-card-section
                    class="q-pa-lg q-pa-lg-xl scroll"
                    style="max-height: 70vh"
                >
                    <div class="row">
                        <div class="col-12">
                            <p class="q-mb-md" v-html="tm('errorLMS.text1')"></p>
                            <p class="q-mb-md" v-html="tm('errorLMS.text2')"></p>
                            <p class="q-mb-md" v-html="tm('errorLMS.text3')"></p>
                        </div>
                    </div>
                </q-card-section>
            </q-card>
        </q-dialog>


    </q-layout>
</template>

<script>
import { defineComponent, ref, watch, computed, onMounted } from "vue";
import { useQuasar } from "quasar";
import { useI18n } from "vue-i18n";

import { useStore } from "stores/store";
import { useCore } from "stores/composables/core";
import { useAos } from "stores/composables/aos";

import EssentialLink from "components/EssentialLink.vue";

// const langList = import.meta.glob('../../node_modules/quasar/lang/*.mjs')
// or just a select few (example below with only DE and FR):
const langList = import.meta.glob("../../node_modules/quasar/lang/(ru|uk|en-US|ar).mjs");

export default defineComponent({
    name: "MainLayout",

    components: {
        EssentialLink,
    },

    setup() {
        const store = useStore();
        const core = { ...useCore() };
        const aos = { ...useAos() };

        const { locale } = useI18n({ useScope: "global" });
        const { t, tm } = useI18n();
        const $q = useQuasar();

        const languages = ref([
            {
                isoName: "en-US",
                nativeName: "English (US)",
                nameInEnglish: "English",
            },
            {
                isoName: "ar",
                nativeName: "العربية",
                nameInEnglish: "Arabic",
            },
            {
                isoName: "id",
                nativeName: "Bahasa Indonesia",
                nameInEnglish: "Indonesian",
            },
            // {
            // 	"isoName": "ja",
            // 	"nativeName": "日本語 (にほんご)",
            // 	"nameInEnglish": "Japanese"
            // },
            // {
            // 	"isoName": "ko-KR",
            // 	"nativeName": "한국어",
            // 	"nameInEnglish": "Korean"
            // },
            // {
            // 	"isoName": "zh-CN",
            // 	"nativeName": "中文(简体)",
            // 	"nameInEnglish": "Chinese (Simplified)"
            // },
            // {
            // 	"isoName": "cs",
            // 	"nativeName": "Čeština",
            // 	"nameInEnglish": "Czech"
            // },
            // {
            // 	"isoName": "hu",
            // 	"nativeName": "Magyar",
            // 	"nameInEnglish": "Hungarian"
            // },
            // {
            // 	"isoName": "pl",
            // 	"nativeName": "Polski",
            // 	"nameInEnglish": "Polish"
            // },
            {
                isoName: "ru",
                nativeName: "Pусский",
                nameInEnglish: "Russian",
            },
            // {
            // 	"isoName": "sk",
            // 	"nativeName": "Slovenčina",
            // 	"nameInEnglish": "Slovak"
            // },
            // {
            // 	"isoName": "tr",
            // 	"nativeName": "Türkçe",
            // 	"nameInEnglish": "Turkish"
            // },
            // {
            // 	"isoName": "uk",
            // 	"nativeName": "Українська",
            // 	"nameInEnglish": "Ukrainian"
            // },
            {
                isoName: "es",
                nativeName: "Español",
                nameInEnglish: "Spanish",
            },
            {
                isoName: "fr",
                nativeName: "Français",
                nameInEnglish: "French",
            },
            // {
            // 	"isoName": "de-DE",
            // 	"nativeName": "Deutsch (DE)",
            // 	"nameInEnglish": "German (Germany)"
            // },
            // {
            // 	"isoName": "it",
            // 	"nativeName": "Italiano",
            // 	"nameInEnglish": "Italian"
            // },
            // {
            // 	"isoName": "pt-BR",
            // 	"nativeName": "Português (BR)",
            // 	"nameInEnglish": "Portuguese (Brazil)"
            // }
        ]);
        const leftDrawerOpen = ref(false);
        const rightDrawerOpen = ref(false);
        const openBookmarkDialog = ref(false);
        const openCloseDialog = ref(false);
        const openLMSErrorDialog = ref(false);
        const openLanguageDialog = ref(false);

        const lmsErrorCallSucceeded = computed(() => {
            return store.com.lmsError.callSucceeded;
        });

        watch(lmsErrorCallSucceeded, (newValue, oldValue) => {
            // debugger;
            if (oldValue == true && newValue == false) {
                console.log("lmsErrorCallSucceeded - newValue: ", newValue);
                console.log("lmsErrorCallSucceeded - oldValue: ", oldValue);

                if (
                    newValue == false &&
                    (store.com.willCheck_Connection || store.com.willCheck_SCORMApi)
                ) {
                    // if ( (newValue==false ) && (store.com.willCheck_Connection) || (store.com.willCheck_SCORMApi) ) {
                    openLMSErrorDialog.value = true;
                }
            }
        });

        const changeLanguage = (lang) => {
            // debugger;
            console.log("***------ changeLanguage ***------ ", lang);

            // update the actual i18n localte value. That's what is going to "change" the active JSON file
            locale.value = lang;

            // update the language package
            const langPackage =
                lang == "ar" || lang == "ru" || lang == "uk" ? lang : "en-US";
            langList[`../../node_modules/quasar/lang/${langPackage}.mjs`]().then(
                (lang) => {
                    $q.lang.set(lang.default);
                }
            );

            //updates the title reflecting the udpated language
            document.title = t("manifest.title");

            // saves the new language in the LMS
            store.com_setLanguage(lang);

            // update the root level font size
            let r = document.querySelector(":root");
            if (lang == "ru") {
                r.style.setProperty("--font_size", "17px");
            }
            if (lang == "sk") {
                r.style.setProperty("--font_size", "17px");
            } else if (lang == "de-DE") {
                r.style.setProperty("--font_size", "17px");
            } else if (lang == "fr") {
                r.style.setProperty("--font_size", "17px");
            } else if (lang == "id") {
                r.style.setProperty("--font_size", "17px");
            } else if (lang == "es") {
                r.style.setProperty("--font_size", "17px");
            } else {
                r.style.setProperty("--font_size", "19px");
            }

            aos.refreshAOS();
        };

        const hideError = () => {
            store.com.lmsError = {
                callSucceeded: true,
                lastErrorCode: 0,
                lastErrorMsg: "",
            };
        };

        onMounted(() => {
            document.title = t("manifest.title");

            // LMS Store - Init
            store.rte_init(null);

            // LMS Store - Init
            store.rte_init(function () {
                const lang = store.com_getLanguage;

                if (
                    lang != "null" &&
                    lang != "" &&
                    lang !== undefined &&
                    lang !== 0 &&
                    lang !== "0"
                ) {
                    console.log("***------ returned saved lang from LMS ***------");
                    changeLanguage(lang);
                } else {
                    changeLanguage("en-US");

                    //////////// 🚨🚨🚨 clear the comment below when multi-language
                    /////////////// 🚨🚨🚨 it's the language selection modal pop-up
                    openLanguageDialog.value = true;
                }

                if (store.rte_getReturn) {
                    openBookmarkDialog.value = true;
                }
            });
        });

        return {
            store,
            ...useCore(),
            ...useAos(),

            leftDrawerOpen,
            rightDrawerOpen,

            toggleLeftDrawer() {
                leftDrawerOpen.value = !leftDrawerOpen.value;
                if (leftDrawerOpen.value) {
                    core.setFocusById("idMenuItems", 500);
                }
            },

            toggleRightDrawer() {
                rightDrawerOpen.value = !rightDrawerOpen.value;
                if (rightDrawerOpen.value) {
                    core.setFocusById("resourcesTitle", 500);
                }
            },

            locale,
            t,
            tm,

            openBookmarkDialog,
            openCloseDialog,
            openLMSErrorDialog,
            openLanguageDialog,

            hideError,

            languages,

            changeLanguage,
        };
    },
});
</script>

<style lang="scss"></style>
