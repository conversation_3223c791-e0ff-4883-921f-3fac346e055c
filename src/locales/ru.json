{"manifest": {"title": "Введение в методологию создания корпоративной архитектуры Citi и управление процессами", "content": [{"title": "Добро пожаловать!"}, {"title": "Что представляет собой методология создания корпоративной архитектуры?"}, {"title": "Основа корпоративной архитектуры"}, {"title": "Кто такие владельцы процессов?"}, {"title": "Элементы <span class='sr-only'> C E A M </span><span aria-hidden='true'>CEAM</span>"}, {"title": "Упрощение технологий в действии"}, {"title": "Кто отвечает за применение корпоративной архитектуры и управление процессами?"}, {"title": "Краткое содержание курса"}, {"title": "Оценка"}]}, "resources": {"title1": "Ресурсы", "title2": "Список аббревиатур", "title3": "Ссылки", "list1": ["CEAM — Citi Enterprise Architecture Methodology (методология создания корпоративной архитектуры Citi)", "DSMT — Data Standards Management Tool (система управления стандартами данных)", "GPMP  — Global Process MCA Profile (программа оценки контроля глобальных процессов со стороны руководства)", "EAPGP — Enterprise Architecture and Process Governance Policy (Политика в отношении корпоративной архитектуры и управления процессами)", "ПВ — End User Computing (пользовательские вычисления)", "MCA — Manager Control Assessment (оценка контроля со стороны руководства)", "POA — Process Ownership Assessment Platform (платформа оценки владения процессами)", "PTS — Project Tracking System (система отслеживания проектов)", "I@C – Investments@Citi"], "list2": ["<a href='https://citi.sharepoint.com/sites/Enterprise-Architecture/SitePages/Process-Ownership.aspx?csf=1&web=1&e=1oNYIZ' target='_blank'>Информация о владельцах процессов бизнес-архитектуры</a>", "<a href='https://citi.sharepoint.com/sites/Enterprise-Architecture/SitePages/ReferenceMaterials.aspx' target='_blank'>Справочные материалы по бизнес-архитектуре</a>", "<a href='https://citi.hostedconnectedrisk.com/CitiCPD/react?page=O&CID=sid552268563&DeletedOnly=0&HTMFile=instancePolicyUser_View.htm&InstanceNo=2001487&NodeNo=182&inlineEdit=0&originalRequestNodeNo=182&pageType=42&resetTreeView=0&rightClickContextMenu=0&runOnce=1&scrollableViewer=1&treeViewNode0=253&treeViewNode0OverviewPage=instancePolicyVersionAuthor_Title.htm&treeViewNode1=281&treeViewNode10=371&treeViewNode10OverviewPage=instancePolicySectionLevel10VersionAuthorView.htm&treeViewNode1OverviewPage=instancePolicySectionLevel1VersionAuthor_View.htm&treeViewNode2=282&treeViewNode2OverviewPage=instancePolicySectionLevel2VersionAuthor_View.htm&treeViewNode3=283&treeViewNode3OverviewPage=instancePolicySectionLevel3VersionAuthor_View.htm&treeViewNode4=285&treeViewNode4OverviewPage=instancePolicySectionLevel4VersionAuthor_View.htm&treeViewNode5=284&treeViewNode5OverviewPage=instancePolicySectionLevel5VersionAuthor_View.htm&treeViewNode6=372&treeViewNode6OverviewPage=instancePolicySectionLevel6VersionAuthorView.htm&treeViewNode7=373&treeViewNode7OverviewPage=instancePolicySectionLevel7VersionAuthorView.htm&treeViewNode8=374&treeViewNode8OverviewPage=instancePolicySectionLevel8VersionAuthorView.htm&treeViewNode9=375&treeViewNode9OverviewPage=instancePolicySectionLevel9VersionAuthorView.htm&useCustomFieldForAddChild=0&useOriginalRequestNodeAsCurrent=1&usingpost=1' target='_blank'>Enterprise Architecture and Process Governance Policy (EAPGP)</a>", "<a href='https://www.citigroup.net/clicks/?version=3&utm_campaign=1625863048700868QguJhvH%2fCbsXACX4DonJcmabp9YzQXIRFAJJBbS86kyYTSzXYlBAUFpRk0RutsWxa8CMCIUYkcHJLo6cv6d3YBWMrc7TkgakWaIfQxu2lMf%2bid2Rdit0w40I%2fNZOeMBvDlfOKPiiQtXA5yGrK6rSWg%3d%3d&ctid=1625863048700883DEHnfsoz07WcImMs4XRY3vYrwPekdV1fxVqQQCX3pzC5YN%2bERTNzWzsOqx00Lz6snxX0eMKW%2bpdhG8bC%2fnGLrDr%2fxCAAA1wMNmGYs7aAwh7DHMIPOFne2kz71lbbn6WiTKelhg0XoVQN5%2b5MxQTyVg%3d%3d' target='_blank'>Process Governance Standard (PGrS)</a>"]}, "errors": {"error": "Ошибка", "lostcommunication": "Закройте этот модуль и запустите его снова. Возникла ошибка LMS. Этот модуль останется активным, но не будет регистрировать выполняемые вами действия.", "close": "Закрыть"}, "errorLMS": {"text1": "<strong>Внимание!‎</strong> Соединение с системой управления обучением (Learning Management System, LMS) отсутствует. Все ранее выполненные действия были сохранены. Однако ваши текущие действия не сохраняются.", "text2": "Обычно это происходит из-за прерывания сетевого соединения. Другая возможная причина — истекло время ожидания системы LMS.", "text3": "<strong>Чтобы устранить эту проблему:</strong> Закройте это окно с курсом, затем закройте все окна браузера.  Откройте новое окно браузера и подключитесь к системе LMS. Найдите этот курс и нажмите на кнопку запуска.  Состояние курса будет восстановлено на момент последнего зафиксированного подключения."}, "ui": {"sr": "Пользователям программ чтения с экрана: данный курс адаптирован к таким программам и полностью поддерживается ими.", "langselection": "Выбор языка", "chooselang": "Выберите язык:", "menu": "<PERSON>е<PERSON><PERSON>", "menuItems": "Разделы меню", "exit": "Выход", "exitCourse": "Выйти из курса", "logo": "Логотип Citi", "notattempted": "Попытка не выполнена", "incomplete": "Не завершено", "complete": "Завершено", "locked": "Заблокировано", "itemComplete": "Раздел завершен", "itemLocked": "Раздел заблокирован", "itemUnLocked": "Раздел разблокирован", "pageLoaded": "Страница загружена", "pagesCompleted": " страниц(ы) завершено", "resources": "Ресурсы", "scroll": "Прокрутите вниз, чтобы продолжить.", "scrollc": "Прокрутите вниз, чтобы продолжить прохождение курса.", "previous": "Назад", "next": "Далее", "continue": "Продолжить", "close": "Закрыть", "gotit": "Понятно!", "submit": "Отправить", "learn": "Узнать больше", "return": "Вернуться наверх", "citigroup": "Citigroup Inc", "selectToBegin": "Нажмите, чтобы начать", "settings": "Настройки", "animations": "Анимация", "on": "Анимация вкл.", "off": "Анимация выкл.", "audio": "Звук", "reportconcern": "Сообщить о проблеме", "pressescape": "Чтобы закрыть, нажмите клавишу ESCAPE", "of": " из ", "itemscompleted": " разделов завершено", "questionTitleTemplate": "Вопрос {a} из {b}", "correct": "Правильно.", "incorrect": "Не совсем."}, "videoplayer": {"Play": "Воспроизведение", "Pause": "Пауза", "Current Time": "Текущее время", "Duration": "Продолжительность", "Remaining Time": "Оставшееся время", "Loaded": "Загружено", "Progress": "Ход выполнения", "Progress Bar": "Индикатор выполнения", "progress bar timing: currentTime={1} duration={2}": "{1} из {2}", "Fullscreen": "Полноэкранный режим", "Non-Fullscreen": "Выход из полноэкранного режима", "Mute": "Выключить звук", "Unmute": "Включить звук", "Volume Level": "Уровень громкости", "You aborted the media playback": "Вы приостановили воспроизведение видео.", "A network error caused the media download to fail part-way.": "Ошибка сети вызвала сбой во время загрузки данных.", "The media could not be loaded, either because the server or network failed or because the format is not supported.": "Видео не удается загрузить либо из-за ошибки сервера или сети, либо из-за того, что формат не поддерживается.", "The media playback was aborted due to a corruption problem or because the media used features your browser did not support.": "Воспроизведение видео было приостановлено из-за повреждения данных или из-за того, что в нем используются функции, не поддерживаемые вашим браузером.", "No compatible source was found for this media.": "Для этого видео не найдено ни одного совместимого источника."}, "bookmark": {"title": "Возобновить курс с закладки", "message": "Вы хотите возобновить курс или начать курс заново?", "cancel": "Начать курс заново", "ok": "Возобновить курс"}, "closemodule": {"title": "Выйти из курса", "message": "Вы действительно хотите закрыть этот курс?", "cancel": "Отмена", "ok": "ОК"}, "page1": {"sectionTop": {"title1": "Введение в корпоративную архитектуру Citi и управление процессами", "title2": "‎20-минутный обучающий онлайн-курс "}, "section1": {"title1": "Навигация по данному курсу", "instr1": "Ознакомьтесь с карточками о навигации по курсу, чтобы получить максимум информации.", "list1": [{"text1": "Нажмите на значок меню для перехода к различным разделам курса. Новый раздел становится доступен только после выполнения всех требований предыдущего раздела.", "text2": null, "icon1": "bi-list"}, {"text1": "Нажмите на значок «Ресурсы» в правом верхнем углу для доступа к дополнительным ресурсам по теме курса. В этом курсе приведены гиперссылки на процедуры и Политику по теме. ", "text2": null, "icon1": "bi-info-circle"}, {"text1": "Этот курс содержит <strong><span class='text-secondary' style='text-decoration: underline'>гиперссылки</span></strong> на различные источники информации. Если вы проходите курс с личного устройства, подключенного к сети Интернет напрямую (за пределами сети Citi), некоторые ссылки на документы, расположенные в сети Citi, могут не работать. Это не повлияет на вашу возможность пройти этот курс.", "text2": null, "icon1": "bi-link-45deg"}, {"text1": "Если у вас возникнут проблемы с просмотром анимированных изображений, вы можете выключить анимацию в данном курсе. Анимация улучшает общие впечатления от прохождения курса. Отключение анимации не лишит вас доступа к каким-либо материалам. ", "text2": null, "icon1": "bi-gear"}, {"text1": "Если в какой-либо момент вам потребуется выйти из курса до его завершения, нажмите на этот значок в верхней части страницы, чтобы сохранить свои результаты и выйти.", "text2": null, "icon1": "bi-x-circle"}]}, "sectionWhyWhyWhyWhy": {"title": "Учебный курс о корпоративной архитектуре Citi и управлении процессами", "list": [{"title": "Почему это важно?", "paragraphs": ["Рост Citi в рамках различных бизнес-подразделений и рынков привел к формированию сложной экосистемы цифровых платформ и приложений. Хотя это расширение обеспечило немало выгод, оно также способствовало дублированию, частичному совпадению наборов функций и нехватке представления о сквозной работе наших процессов. Корпоративная архитектура Citi и управление процессами позволяют единообразно оценивать состояние процессов и связанные с ними риски, чтобы принимать обоснованные решения на общекорпоративном уровне."]}, {"title": "Почему именно сейчас?", "paragraphs": ["Корпоративная архитектура крайне важна для процесса трансформации Citi и совершенно необходима для поддержания организации в функциональном и стабильном состоянии с учетом роста конкуренции на рынке."]}, {"title": "Почему именно мы?", "paragraphs": ["Корпоративная архитектура и управление процессами уже входят в наши рабочие обязанности в Citi. Каждый сотрудник участвует в упрощении процессов и их связывании в масштабах Citi с целью совершенствования систем компании. Применение знаний, касающихся этой инфраструктуры, позволяет нам сокращать риски, повышать качество решений и работать эффективнее."]}, {"title": "Каково содержание курса?", "paragraphs": ["Корпоративная архитектура и управление процессами обеспечивают процессный подход, который используется для упрощения процессов и сокращения связанных с ними рисков, что ведет к оптимизации обслуживания клиентов и облегчению работы сотрудников. "]}]}, "section2": {"text1": "Этот тренинг поможет вам понять: ", "list1": ["систему, охватывающую корпоративную архитектуру, которая также называется методологией создания корпоративной архитектуры Citi (Citi Enterprise Architecture Methodology, CEAM);", "как корпоративная архитектура, управление процессами и упрощение технологий помогают нам снижать уровень риска посредством упрощения процессов Citi и прикладных систем, которые их реализуют."], "text2": "После прохождения данного курса вы:", "list2": ["поймете назначение корпоративной архитектуры и управления процессами;", "разберетесь в том, как управление процессами помогает упрощать и совершенствовать работу Citi;", "уясните роль классификаций в обеспечении единообразия в масштабах Citi;", "запомните обязанности владельцев процессов в части, которая касается корпоративной архитектуры и управления процессами;", "узнаете о том, кто отвечает за применение корпоративной архитектуры и управления процессами;", "определите, какие основные элементы используются в CEAM."]}, "section_assessment_disclaimer": {"paragraphs": ["Обратите внимание: в конце этого курса вас ждет заключительная оценка. Чтобы получить сертификат о завершении тренинга, вы должны набрать не менее 80%. "]}, "section3": {"title": "Добро пожаловать на учебный курс о корпоративной архитектуре и управлении процессами! ", "paragraphs": ["Корпоративная архитектура и управление процессами помогают Citi сосредоточиться на единообразии и согласовании процессов и прикладных систем, которые их реализуют, с целью сокращения уровня риска и упрощения технологий.", "Данные практики также позволяют осуществлять анализ процессов, который обеспечивает более ясное понимание взаимосвязей между нашими процессами, прикладными системами и рисками. С помощью этих данных мы можем выявлять частичные совпадения, уменьшать сложность структуры и принимать более обоснованные решения о том, что следует упростить, куда инвестировать и где внедрить средства контроля."]}, "section4": {"paragraphs": ["Такая сложность структуры проявляется различным образом, например:", ["в наличии ряда прикладных систем, выполняющих схожие функции;", "в наличии прикладных систем с чрезмерным функционалом;", "в отсутствии стандартного представления наших основных процессов для оценки качества их структуры."], "Как следствие, затруднено понимание полного рабочего цикла наших процессов, связанных с ними рисков и причин наличия нескольких прикладных систем, которые с высокой вероятностью обладают одинаковым функционалом. "]}, "section5": {"paragraphs": ["Чтобы разрешить данную ситуацию, Citi разработала стандартные требования к корпоративной архитектуре и управлению процессами."]}, "section6": {"paragraphs": ["Эти требования позволяют нам лучше понять: ", ["наши процессы и прикладные системы, которые их реализуют; ", "наличие возможностей для упрощения реестра наших прикладных систем, которые реализуют процессы;", "в каких случаях необходимо воспользоваться средствами контроля для снижения уровня риска наших процессов."]]}, "section7": {"paragraphs": ["<strong>Корпоративная архитектура</strong> отражает взаимосвязи между нашими процессами, прикладными системами и другими ключевыми элементами, обеспечивающими нашу деятельность. "]}, "section8": {"paragraphs": ["В <strong>основе корпоративной архитектуры лежит классификация процессов.</strong> Когда необходимо принять объективное корпоративное решение (например, куда мы можем направить наши стратегические инвестиции и каким образом их осуществить), процессы в данной классификации и четкая структура управления процессами позволяют <strong>последовательно учитывать наши риски, прикладные системы и другие факторы</strong>."]}, "footer": {"text1": "Давайте узнаем, что такое корпоративная архитектура и как она применяется в Citi."}}, "assessmentFinal": {"text1a": "Добро пожаловать на страницу заключительной оценки знаний.", "text1b": "Чтобы успешно сдать тест и получить зачет за курс, вы должны ответить на следующие {numberOfQuestions} вопроса(-ов) и набрать не менее 80%.", "list1": [["Количество попыток не ограничено.", "После того как вы начнете тест, вы должны ответить на все вопросы, чтобы зарегистрировать свой результат. ", "Если вы выйдете из теста до того, как ответите на все вопросы, вам нужно будет пройти тест с начала."]], "text2": "Прокрутите вниз, чтобы пройти тест.", "questionsSection": [{"id": "kc_bankA_1", "bank": "bankA", "title": " ", "text": ["Корпоративная архитектура и управление процессами (Enterprise Architecture and Process Governance, EAPGP) разработана с целью выявления возможностей для консолидации прикладных систем с дублированием функций.  ", "Какие еще ключевые моменты позволяет выявлять EAPGP?"], "instructions": "Выберите из четырех вариантов наиболее подходящий ответ и затем нажмите «Отправить».", "options": [{"label": "Стоимость обслуживания каждой прикладной системы за некоторый период времени", "value": "a"}, {"label": "Какие команды отвечают за ввод данных в каждой из систем", "value": "b"}, {"label": "Где введенные вручную данные связаны с некоторым процессом", "value": "c"}, {"label": "Все перечисленное", "value": "d"}], "correctResponse": "c", "correctFeedbackTitle": "Это правильный ответ.", "incorrectFeedbackTitle": "Это неправильный ответ.", "correctFeedbackText": ["EAPGP также позволяет узнать, где введенные вручную данные связаны с некоторым процессом. Это помогает разобраться, почему эти данные вводятся вручную, а команды поддержки смогут найти более эффективные процессы."], "incorrectFeedbackText": ["Чтобы правильно ответить на этот вопрос, ознакомьтесь с разделом <strong>«Введение»</strong>‎ на странице <strong>«Добро пожаловать!»</strong>."], "universalFeedbackText": ["общие результаты оценки"]}, {"id": "kc_bankA_2", "bank": "bankA", "title": " ", "text": ["Корпоративная архитектура регулирует классификацию процессов и классификацию функций. Что из перечисленного также относится к регулируемым классификациям?"], "instructions": "Выберите из четырех вариантов наиболее подходящий ответ и затем нажмите «Отправить».", "options": [{"label": "Классификация сокращения корпоративных рисков", "value": "a"}, {"label": "Классификация корпоративных процессов аудита ", "value": "b"}, {"label": "Контролируемый словарь концепций данных", "value": "c"}, {"label": "Классификация корпоративных прикладных систем ", "value": "d"}], "correctResponse": "c", "correctFeedbackTitle": "Это правильный ответ.", "incorrectFeedbackTitle": "Это неправильный ответ.", "correctFeedbackText": ["Классификации создают «общий язык», способствующий общекорпоративному взаимодействию и пониманию. Бизнес-архитектура управляет тремя классификациями. ", "Классификация процессов, классификация функций и контролируемый словарь концепций данных обеспечивают согласованность элементов, создаваемых в рамках CEAM в компании Citi."], "incorrectFeedbackText": ["Чтобы правильно ответить на этот вопрос, ознакомьтесь с разделом <strong>«Основа корпоративной архитектуры»</strong>."], "universalFeedbackText": ["общие результаты оценки"]}, {"id": "kc_bankA_3", "bank": "bankA", "title": " ", "text": ["Как управление процессами способствует упрощению Citi?"], "instructions": "Выберите из четырех вариантов наиболее подходящий ответ и затем нажмите «Отправить».", "options": [{"label": "Обеспечивает способ объективного измерения качества процесса и возможностей для упрощения и сокращения рисков.", "value": "a"}, {"label": "Определяет классификации, отражающие терминологию бизнес-подразделения, не обеспечивая единообразия в масштабах Citi.", "value": "b"}, {"label": "Формирует хранилище процессов, к которому команды сотрудников могут обращаться во время аудитов и проверок.", "value": "c"}, {"label": "Обязывает глобальные команды исполнять идентичные процессы вне зависимости от локальных потребностей и контекста.", "value": "d"}], "correctResponse": "a", "correctFeedbackTitle": "Это правильный ответ.", "incorrectFeedbackTitle": "Это неправильный ответ.", "correctFeedbackText": ["Управление процессами обеспечивает упрощение в Citi, обязывая владельцев процессов понимать, как сегодня функционирует компания, и находить возможности для оптимизации. Такие инструменты, как модели процессов, отображение прикладных систем и анализ процессов, помогают измерить качество процесса и найти возможности для упрощения структуры, сокращения рисков и обеспечения оптимальных результатов."], "incorrectFeedbackText": ["Чтобы правильно ответить на этот вопрос, ознакомьтесь с разделом <strong>«Введение»</strong>‎ на странице <strong>«Добро пожаловать!»</strong>. "], "universalFeedbackText": ["общие результаты оценки"]}, {"id": "kc_bankA_4", "bank": "bankA", "title": " ", "text": ["Каковы обязанности владельца процесса?"], "instructions": "Выберите из четырех вариантов наиболее подходящий ответ и затем нажмите «Отправить».", "options": [{"label": "Использование анализа процессов и данных CEAM с целью объективного совершенствования процессов.", "value": "a"}, {"label": "Надзор за CEAM и другими элементами, связанными с их процессами.", "value": "b"}, {"label": "Определение неотъемлемых рисков и средств контроля для снижения уровня рисков своих процессов.", "value": "c"}, {"label": "Все перечисленное.", "value": "d"}], "correctResponse": "d", "correctFeedbackTitle": "Это правильный ответ.", "incorrectFeedbackTitle": "Это неправильный ответ.", "correctFeedbackText": ["Владельцы процессов являются специалистами в соответствующей области. Они отвечают за CEAM и другие элементы, связанные с их процессами. ", "Владельцы процессов осуществляют надзор и определяют области деятельности, требующие улучшения. Для этого они повышают качество и согласованность процессов Citi и определяют неотъемлемые риски и средства контроля для снижения уровня рисков своих процессов с помощью анализа процессов и данных CEAM. "], "incorrectFeedbackText": ["Чтобы правильно ответить на этот вопрос, ознакомьтесь с разделами <strong>«Кто такие владельцы процессов?»</strong> и <strong>«Кто отвечает за применение корпоративной архитектуры и управление процессами?»</strong>."], "universalFeedbackText": ["общие результаты оценки"]}, {"id": "kc_bankA_5", "bank": "bankA", "title": " ", "text": ["Что представляет собой классификация? "], "instructions": "Выберите из четырех вариантов наиболее подходящий ответ и затем нажмите «Отправить».", "options": [{"label": "Классификация — это порядок наименования, систематизации и структурирования связанных друг с другом объектов.", "value": "a"}, {"label": "Классификация — это способ развития функций в Citi. ", "value": "b"}, {"label": "Классификация — это система для поощрения эффективного использования существующих процессов.", "value": "c"}, {"label": "Классификация — это особая роль, предназначенная для систематизации процессов. ", "value": "d"}], "correctResponse": "a", "correctFeedbackTitle": "Это правильный ответ.", "incorrectFeedbackTitle": "Это неправильный ответ.", "correctFeedbackText": ["Классификация — это порядок наименования, систематизации и структурирования связанных друг с другом объектов. Она обеспечивает единство при передаче информации, управлении и подготовке отчетности в компании. В результате классификация повышает согласованность взаимодействия с разнообразными заинтересованными сторонами и его качество. "], "incorrectFeedbackText": ["Чтобы правильно ответить на этот вопрос, ознакомьтесь с разделом <strong>«Основа корпоративной архитектуры»</strong>."], "universalFeedbackText": ["общие результаты оценки"]}, {"id": "kc_bankA_6", "bank": "bankA", "title": " ", "text": ["Каковы преимущества корпоративной архитектуры и управления процессами? "], "instructions": "Выберите из четырех вариантов наиболее подходящий ответ и затем нажмите «Отправить».", "options": [{"label": "Способствует упрощению процессов и обеспечивает способ объективного измерения сокращения уровня риска в процессах.", "value": "a"}, {"label": "Сосредоточено исключительно на технологическом обновлении, а не совершенствовании процессов и средств контроля.", "value": "b"}, {"label": "Позволяет командам самостоятельно определять управление, не согласовывая деятельность с корпоративными стандартами. ", "value": "c"}, {"label": "Отдает предпочтение быстрым коммерческим победам, а не долгосрочному единообразию и архитектурной дисциплине.", "value": "d"}], "correctResponse": "a", "correctFeedbackTitle": "Это правильный ответ.", "incorrectFeedbackTitle": "Это неправильный ответ.", "correctFeedbackText": ["Задача корпоративной архитектуры и управления процессами — улучшение качества обслуживания наших клиентов и условий труда для сотрудников в Citi. Этот процессный подход способствует упрощению процессов и совершенствованию средств контроля по всей компании, устраняя разрозненность между функциями."], "incorrectFeedbackText": ["Чтобы правильно ответить на этот вопрос, ознакомьтесь с разделом <strong>«Введение» </strong>на странице <strong>«Добро пожаловать!» </strong>или <strong>«Краткое содержание курса»</strong>."], "universalFeedbackText": ["общие результаты оценки"]}, {"id": "kc_bankA_7", "bank": "bankA", "title": " ", "text": ["Владельцы процессов, пользователи оценки контроля со стороны руководства (MCA) и владельцы ПВ отвечают за применение корпоративной архитектуры и управление процессами. ", "Какие еще роли отвечают за применение корпоративной архитектуры и управление процессами?"], "instructions": "Выберите из пяти вариантов лучший вариант ответа и затем нажмите «Отправить».", "options": [{"label": "Ответственные за риск ", "value": "a"}, {"label": "Руководители на всех уровнях", "value": "b"}, {"label": "Инвестиционные спонсоры ", "value": "c"}, {"label": "Все перечисленные", "value": "d"}, {"label": "Никто из перечисленных", "value": "e"}], "correctResponse": "d", "correctFeedbackTitle": "Это правильный ответ.", "incorrectFeedbackTitle": "Это неправильный ответ.", "correctFeedbackText": ["Корпоративная архитектура и управление процессами применяются самыми разными сотрудниками Citi, включая владельцев процессов, владельцев ПВ, инвестиционных спонсоров и пользователей оценки контроля со стороны руководства (MCA). ", "Кроме того, необходимо помнить, что все сотрудники Citi, включая подрядчиков, должны правильно выбирать процессы для соответствующих прикладных систем, проектов или средств контроля в различных системах, где это необходимо. "], "incorrectFeedbackText": ["Чтобы правильно ответить на этот вопрос, ознакомьтесь с разделами <strong>«Каким образом специалисты, выполняющие различные роли, используют корпоративную архитектуру и управление процессами?»</strong> и <strong>«Кто отвечает за применение корпоративной архитектуры и управление процессами?»</strong>."], "universalFeedbackText": ["общие результаты оценки"]}, {"id": "kc_bankA_8", "bank": "bankA", "title": " ", "text": ["Какая классификация содержит этапы или задачи, которые выполняются в прикладных системах в рамках процесса?"], "instructions": "Выберите из четырех вариантов наиболее подходящий ответ и затем нажмите «Отправить».", "options": [{"label": "Классификация процессов", "value": "a"}, {"label": "Классификация функций", "value": "b"}, {"label": "Словарь контролируемых терминов, связанных с данными", "value": "c"}, {"label": "Бизнес-услуги", "value": "d"}], "correctResponse": "b", "correctFeedbackTitle": "Это правильный ответ.", "incorrectFeedbackTitle": "Это неправильный ответ.", "correctFeedbackText": ["Классификация функций содержит этапы, задачи или «функции», которые выполняются в рамках процесса. ", "Это одноуровневая иерархия действий, ведущих к определенному результату (наименование функции), когда одна функция может использоваться несколькими процессами. "], "incorrectFeedbackText": ["Чтобы правильно ответить на этот вопрос, ознакомьтесь с разделом <strong>«Основа корпоративной архитектуры»</strong>."], "universalFeedbackText": ["общие результаты оценки"]}, {"id": "kc_bankA_9", "bank": "bankA", "title": " ", "text": ["Что из перечисленного завершает ряд назначений функции прикладной системы вместе со «Стратегическое» и «Вывести из эксплуатации»?"], "instructions": "Выберите из четырех вариантов наиболее подходящий ответ и затем нажмите «Отправить».", "options": [{"label": "Сохранить", "value": "a"}, {"label": "Архивировать ", "value": "b"}, {"label": "Управлять", "value": "c"}, {"label": "Сохранить в текущем виде", "value": "d"}], "correctResponse": "d", "correctFeedbackTitle": "Это правильный ответ.", "incorrectFeedbackTitle": "Это неправильный ответ.", "correctFeedbackText": ["Назначение функции прикладной системы указывает на стратегическую цель использования функции прикладной системы в процессе. ", "Возможны три значения: «Стратегическое» (функционал прикладной системы имеет стратегическое значение), «Сохранить в текущем виде» (функционал прикладной системы не имеет стратегического значения) и «Вывести из эксплуатации» (прикладная система не подходит для реализации указанного функционала). "], "incorrectFeedbackText": ["Чтобы правильно ответить на этот вопрос, ознакомьтесь с разделом <strong>«Модели процессов»</strong> на странице <strong>«Элементы CEAM»</strong>."], "universalFeedbackText": ["общие результаты оценки"]}, {"id": "kc_bankA_10", "bank": "bankA", "title": " ", "text": ["Какова цель применения моделей процессов в корпоративной архитектуре?"], "instructions": "Выберите из трех вариантов лучший вариант ответа и затем нажмите «Отправить».", "options": [{"label": "Модели процессов описывают этапы создания процесса.", "value": "a"}, {"label": "Модели процессов помогают нам понять процессы, связанные с ними действия, функции прикладных систем и вводимые вручную данные.", "value": "b"}, {"label": "Модели процессов используются для распределения ролей и обязанностей в рамках команд проектов.", "value": "c"}], "correctResponse": "b", "correctFeedbackTitle": "Это правильный ответ.", "incorrectFeedbackTitle": "Это неправильный ответ.", "correctFeedbackText": ["Модели процессов помогают нам понять процессы и связанные с ними действия структурированным и единообразным способом. Они дают представление о потоках работ в масштабах организации и обеспечивают соответствие классификации процессов."], "incorrectFeedbackText": ["Чтобы правильно ответить на этот вопрос, ознакомьтесь с разделом <strong>«Модели процессов»</strong> на странице <strong>«Элементы CEAM»</strong>."], "universalFeedbackText": ["общие результаты оценки"]}], "finalPassSection": {"title": "Результаты оценки", "text1": "Вы набрали", "text2": "<strong>Поздравляем!</strong> Вы успешно прошли тестирование и соответствуете всем требованиям для завершения обучающего курса.", "text3": "Теперь вы можете <strong>выйти</strong> из курса, нажав на кнопку закрытия (Х) в верхнем правом углу окна браузера. Если хотите, вы можете ознакомиться со своими ответами на вопросы теста.", "text4": "Также вы можете ознакомиться с содержанием курса, нажав на кнопку <strong>«Меню»</strong> в левом верхнем углу панели инструментов курса.", "btnReturn": "Просмотреть оценку"}, "finalFailSection": {"title": "Результаты оценки", "text1": "Вы набрали", "text2": "К сожалению, вы не набрали минимально необходимый для прохождения оценки балл. Перед повторным прохождением тестирования рекомендуется еще раз просмотреть содержание.", "text3": "Вы можете перейти к содержанию курса, нажав на кнопку «Просмотреть содержание».", "text4": "Если вы готовы пройти тест повторно, нажмите на кнопку «Пройти тест еще раз».", "btnRetry": "Пройти тест еще раз", "btnReturn": "Просмотреть содержание"}, "congratsSection": {"text1": "Теперь вы можете выйти из курса.", "text2": "Нажмите на кнопку закрытия (Х) в верхнем правом углу окна браузера, чтобы выйти из курса."}}, "assessment": {"course_level_test_out": {"this_course_contains_a_test_out_and_a_final_assessment": ["В конце этого курса вас ждет заключительная оценка. Вы должны набрать не менее 80% для получения зачета по этому курсу.", "Курс также содержит необязательное экстерн-тестирование. Если вы пройдете его, то сможете пропустить ознакомление с материалами курса и заключительную оценку, сразу получив зачет.", "Если хотите, вы можете пропустить экстерн-тестирование и перейти непосредственно к материалам курса."], "btnTakeTestOut": "Пройти экстерн-тестирование", "btnSkipTestOut": "Пропустить экстерн-тестирование", "title1": "Экстерн-тестирование", "text1": "Добро пожаловать на экстерн-тестирование.", "text2": "Чтобы успешно сдать тест, вы должны ответить на следующие {numberOfQuestions} вопроса(-ов) и набрать не менее 80%.", "text3": "Если вы не наберете проходной балл, вам придется полностью ознакомиться с содержанием курса.", "list1": ["На каждый вопрос дается одна попытка.", "После того как вы начнете тест, вы должны ответить на все вопросы, чтобы зарегистрировать свой результат. ", "Если вы выйдете из курса до завершения тестирования, ход его выполнения не будет сохранен, и вам придется полностью ознакомиться с содержанием курса."], "text4": "Прокрутите вниз, чтобы пройти экстерн-тестирование.", "btnContinueTest": "Продолжить экстерн-тестирование", "btnContinuePage": "Просмотреть содержание предыдущего раздела", "btnContinueCourse": "Начать курс", "btnReviewContent": "Просмотреть оценку", "if_perfect_score": "ЕСЛИ БАЛЛ МАКСИМАЛЬНЫЙ", "congrats": {"title1": null, "text1": "Вы набрали", "text2": "<strong>Поздравляем!</strong> Вы успешно прошли тестирование и соответствуете всем требованиям для завершения обучающего курса. ", "text3": "Теперь вы можете <strong>выйти</strong> из курса, нажав на кнопку закрытия (Х) в верхнем правом углу окна браузера. Если хотите, вы можете ознакомиться со своими ответами на вопросы теста.", "text4": "Также вы можете ознакомиться с содержанием курса, нажав на кнопку <strong>«Меню»</strong> в левом верхнем углу панели инструментов курса."}, "if_score_is_80_100": "ЕСЛИ БАЛЛ 80–100%", "passed": {"title1": null, "text1": "Вы набрали", "text2": "<strong>Поздравляем!</strong> Вы успешно прошли тестирование и соответствуете всем требованиям для завершения обучающего курса. ", "text3": "Теперь вы можете <strong>выйти</strong> из курса, нажав на кнопку закрытия (Х) в верхнем правом углу окна браузера. Если хотите, вы можете ознакомиться со своими ответами на вопросы теста.", "text4": "Также вы можете ознакомиться с содержанием курса, нажав на кнопку <strong>«Меню»</strong> в левом верхнем углу панели инструментов курса."}, "failed": {"title1": null, "text1": "Вы набрали", "text2": "К сожалению, вы не набрали минимально необходимый для прохождения экстерн-тестирования балл. Вы должны полностью ознакомиться с содержанием и пройти заключительную оценку в конце курса."}}, "test_out": {"title1": "Экстерн-тестирование", "text1": "Это необязательное тематическое экстерн-тестирование по каждому разделу содержит {numberOfQuestions} вопроса(-ов). Если вы ответите на все вопросы правильно, вы сможете сразу перейти к следующему разделу данного курса.", "text2": "На каждый вопрос дается одна попытка, поэтому экстерн-тестирование нужно пройти за один подход.", "text3": "Если вы не сможете правильно ответить на все вопросы или выйдете из курса до того, как дадите все ответы, вам придется ознакомиться с содержанием раздела и правильно ответить на вопросы в конце.", "btnTakeTest": "Пройти экстерн-тестирование", "btnContinueSection": "Перейти к содержанию раздела", "passed": {"title1": "Поздравляем!", "text1": "Вы продемонстрировали уверенное понимание материала данного раздела. Вы можете сразу перейти к следующему разделу или же ознакомиться с содержанием этого раздела, а затем перейти к следующему.", "btnContinueCourse": "Перейти к курсу", "btnReviewSection": "Просмотреть содержание раздела"}, "failed": {"text1": "К сожалению, вы ответили неверно на один или несколько вопросов; теперь вы должны ознакомиться с содержанием раздела и ответить на вопросы в конце.", "btnContinueSection": "Перейти к содержанию раздела"}}, "knowledge_check": {"title1": "Оценка", "text1": "Для перехода к следующему разделу необходимо правильно ответить на следующие {numberOfQuestions} вопроса(-ов).", "text2": "Если вы ответите на один или несколько вопросов неправильно или выйдете из теста до того, как ответите на все вопросы, вам придется пройти тест с начала.", "text3": "<strong>Прокрутите вниз, чтобы пройти тест.</strong>", "passed": {"title1": "Поздравляем! Теперь вы можете перейти к следующему разделу."}, "failed": {"title1": "Результаты оценки", "text1": "К сожалению, вы неправильно ответили на один или более вопросов. Перед повторным прохождением тестирования рекомендуется еще раз просмотреть содержание", "btnReviewSection": "Просмотреть содержание раздела", "btnRetake": "Пройти тест еще раз"}}, "questions": []}, "page2": {"sectionTop": {"title1": "Что представляет собой корпоративная архитектура?", "title2": "Определение корпоративной архитектуры"}, "section1": {"title1": "Наша культура, наши обязанности, наши решения", "text1": "Корпоративная архитектура основана на методологии, известной как методология создания корпоративной архитектуры Citi (CEAM). Она используется в качестве дисциплины для разработки моделей процессов, которые позволяют комплексно описать, понять, систематизировать и стандартизировать процессы и связанные с ними прикладные системы.", "text2": "Чтобы понять, насколько она важна для Citi, сначала рассмотрим, каким образом концепции, используемые в CEAM, связаны с такими повседневными операциями, как осуществление платежа.", "list1": ["Чтобы купить товары и услуги в Интернете, вы, скорее всего, пользуетесь кредитной картой, дебетовой картой или услугами мобильных платежей. ", "Клиенты ожидают, что их платежи будут обрабатываться верно, с надлежащей защитой и вовремя, и поставщики платежных услуг, такие как Citi, стремятся к тому же.", "Поскольку Citi — крупный международный банк, функционал обработки платежей используется во множестве процессов компании в рамках различных бизнес- и функциональных подразделений. За многие годы своей работы мы накопили множество прикладных систем, реализующих данный функционал. Это усложняет обеспечение работоспособности, совершенствование и разработку надежных средств контроля, которые позволяют добиться верного, своевременного и защищенного осуществления платежей.", "CEAM позволяет нам выявлять прикладные системы с общим функционалом, таким как инициация или валидация платежей. Консолидируя подобные выявленные возможности, мы упрощаем ситуацию в сфере прикладных систем, делая управление ими более эффективным благодаря меньшему числу прикладных систем, нуждающихся в обслуживании в соответствии с актуальными требованиями и средствами контроля. Это способствует упрощению технологий и стимулирует его."], "text3": "Корпоративная архитектура и управление процессами позволяют нам работать по существу над упрощением процессов по всей компании."}, "footer": {"text1": "Давайте ознакомимся с классификациями и элементами корпоративной архитектуры Citi.", "btnText1": "Продолжить"}}, "page3": {"sectionTop": {"title1": "Основа корпоративной архитектуры"}, "section1": {"title": "Классификации процессов и функций", "text1": "Давайте начнем с рассмотрения ситуации с платежом, на примере которой мы увидим, как общая терминология способствует упрощению и как этого можно добиться с помощью классификаций корпоративной архитектуры.", "example_lorenzo": ["Лоренцо — клие<PERSON>т Citi, который владеет малым предприятием и пользуется услугами Citi для проведения платежей своим поставщикам по всему миру. ", "В идеале процесс осуществления платежей через Citi должен быть максимально удобным для Лоренцо. К сожалению, в реальности этот процесс сложен и труден.", "Почему? ", "Если взглянуть за кулисы, то в Citi используется непростая платежная технология. Чтобы соответствовать потребностям клиентов по всему миру и поддерживать глобальное развитие в сфере продукции, было разработано множество платежных прикладных систем, у многих из которых функции схожи. ", "Как следствие, Лоренцо приходится пользоваться тремя различными платежными платформами всего лишь для оплаты услуг поставщиков. В зависимости от их местоположения для отправки средств используются различные региональные прикладные системы. Процесс фрагментирован из-за наличия частично пересекающихся систем, призванных выполнять одну и ту же функцию — отправку средств некоторому лицу в некоторой стране.", "Ситуация с Лоренцо показывает, как корпоративные архитектурные системы могут помочь Citi найти возможности для сокращения прикладных систем. ", "Применяя корпоративные архитектурные системы, мы обеспечиваем новым и существующим клиентам более комфортную работу и повышаем их удовлетворенность предлагаемыми Citi продуктами и услугами."], "text8": "Теперь рассмотрим, каким образом классификации, особенно классификации процессов и функций, создают «общий язык», способствующий общекорпоративному взаимодействию и пониманию, а также являются основой для корпоративной архитектуры.", "cta_accordion": "Нажмите на определение каждой классификации, чтобы узнать больше.", "accordion1": [{"title": "Что представляет собой классификация?", "text": ["Возможно, вы знакомы с иерархией регулируемых сегментов, иерархией регулируемых регионов или назначением юридических лиц Citi. Все это примеры корпоративных классификаций в Citi.", "<strong>Классификация</strong> — это порядок наименования, систематизации и структурирования связанных друг с другом объектов. Она обеспечивает единство при передаче информации, управлении и подготовке отчетности в компании. В результате повышается согласованность взаимодействия с разнообразными заинтересованными сторонами и его качество. "]}, {"title": "Что представляют собой корпоративные классификации?", "text": ["Бизнес-архитектурой управляются следующие корпоративные классификации:"], "list": ["Классификация процессов", "Классификация функций", "Контролируемый словарь концепций данных"]}], "callout1": "<strong>Бизнес-архитектура</strong> отвечает за Политику в отношении корпоративной архитектуры и управления процессами.", "text9": "Рассмотрим каждую из корпоративных классификаций более подробно."}, "section2": {"title1": "Классификация процессов", "text1": "Эта классификация описывает и организует процессы. Она включает в себя реестр процессов, комплексно описывающих нашу деятельность в масштабах компании, — от открытия счетов до управления персоналом и управления рисками. ", "text2": "Обратите внимание!", "list1": ["<strong>Процес<PERSON></strong> — это набор действий или функций, выполняемых в определенной последовательности для реализации бизнес-цели, такой как услуга, продукт или выгода для Citi. ", "Один или несколько входных ресурсов процесса преобразуются в один или несколько конечных результатов с помощью ряда действий или функций. Эти конечные результаты могут переходить в другой процесс, а также предоставляться клиенту, сотруднику и (или) третьей стороне. "], "text3": "<strong>Классификация процессов</strong> в Citi определяется независимо от бизнес-направления и продукта.", "text6": "У каждого процесса есть владелец процесса — руководитель Citi, отвечающий за управление процессом на всем его протяжении, включая упрощение и снижение уровня риска.", "text7": "В ходе данного тренинга вы получите дополнительную информацию о владельцах процессов.", "title2": "Классификация функций", "text8": "Эта классификация содержит реестр этапов, задач или «функций», которые выполняются в рамках прикладной системы, реализующей процесс.", "text9": "<strong>Классификация функций</strong> — это реестр действий, ведущих к определенному результату (наименование функции). Нужно отметить, что одна и та же функция может выполняться множеством прикладных систем во множестве процессов. Каждая прикладная система обладает определенным рядом функций (называемых функциями прикладной системы).", "text10": "Например, процес<PERSON> <strong>обработки платежей</strong> включает в себя несколько этапов (функций) и прикладных систем. К функциям в прикладных системах относятся в том числе следующие:", "list3": ["регистрация подачи платежа;", "валидация платежа;", "проверка нормативно-правового соответствия;", "хранение платежа;", "перевод средств."], "title3": "Контролируемый словарь концепций данных", "text11": "<strong>Контролируемый словарь концепций данных</strong> — это иерархически упорядоченный набор концепций данных. ", "text12": "Концепция данных — это представление набора элементов данных, представляющих человека, место или объект, участвующих в коммерческой деятельности компании. Это термины, имеющие названия и определения, которые компания признает и понимает. Примеры концепций данных — клиенты, счета, сделки и т. д.", "text13": "Нужно отметить, что концепции данных создаются или используются функциями. ", "text14": "Чтобы получить более подробную информацию о классификации процессов, классификации функций и контролируемом словаре концепций данных, изучите и добавьте в закладки страницу со <a href='https://citi.sharepoint.com/sites/Enterprise-Architecture/SitePages/ReferenceMaterials.aspx' target='_blank'>справочными материалами по бизнес-архитектуре</a>. ", "text15": "Помните, что все указанные <strong>классификации в совокупности позволяют понять, каким образом можно систематически использовать (в том числе повторно) процессы, функции и данные в Citi</strong> для одинаковых продуктов и услуг компании."}, "footer": {"text1": "А теперь давайте узнаем больше о владельцах процессов и их обязанностях."}}, "page4": {"sectionTop": {"title1": "Кто такие владельцы процессов?"}, "section1": {"text1": "Мы определили набор процессов, но кто осуществляет надзор и использует связанные с процессами данные для выявления областей деятельности, требующих улучшения?", "text2": "Это владельцы процессов. Владелец процесса отвечает за управление процессом, включая упрощение и снижение уровня риска своего процесса.", "text3": "Рассмотрим эту роль более подробно и узнаем об обязанностях владельцев процессов.", "text4": "Обязанности владельцев процессов", "text5": "У владельцев процессов, назначенных каждому процессу в классификации процессов, есть ряд обязанностей по управлению их процессами. ", "text5AltText": "Это пример трех уровней классификации бизнес-процессов. Группа процессов уровня 1 — это движение денежных средств. Родительский процесс уровня 2 — это платежи. Процесс уровня 3 — это обработка платежей. Владельцы процессов отвечают за третий уровень.", "text7": "Владельцы процессов отвечают за следующее: ", "list1": ["Создание и сопровождение моделей процессов CEAM, которые затем используются для выявления возможностей для упрощения технологий или консолидации прикладных систем со схожим функционалом в рамках соответствующего процесса.", "Создание и сопровождение программы оценки контроля глобальных процессов со стороны руководства (GPMP) для соответствующего процесса, включая следующее:", ["Определение неотъемлемых рисков и средств контроля для снижения уровня рисков своих процессов. Так формируется GPMP в пересмотренной оценке контроля со стороны руководства (Manager Control Assessment, MCA). ", "Рассмотрение и утверждение запросов на добавление или удаление GPMP из глобальных подразделений, подлежащих оценке."], "Валидация других выявленных связей с процессом, например со средствами ПВ, которые входят в число факторов, используемых для измерения состояния соответствующего процесса в рамках оценки риск-профиля процесса. "], "text8": "Чтобы получить дополнительную информацию о владении процессом, посетите страницу с <a href='https://citi.sharepoint.com/sites/Enterprise-Architecture/SitePages/Process-Ownership.aspx?csf=1&web=1&e=1oNYIZ' target='_blank'>информацией о владельцах процессов бизнес-архитектуры.</a>"}, "section_poa": {"title": "Оценка владения процессами (POA) и результат оценки рисков процесса", "paragraphs": ["Оценка владения процессами (Process Ownership Assessment, POA) обеспечивает владельцам процессов комплексное представление утвержденных связей, к которым относятся принятые сведения о рисках и средствах контроля, а также проблемы iCAP, средства ПВ и данные моделей процессов CEAM, используемые для упрощения технологий и автоматизации процессов. ", "Утвержденные связи являются входными данными для оценки профиля процессов и получаемого показателя риска процесса. Данный показатель помогает владельцам процессов выявлять возможности для упрощения и сокращения уровня риска. "]}, "footer": {"text1": "А теперь перейдем к ландшафту бизнес-архитектуры Citi и рассмотрим несколько отдельных элементов моделей процессов CEAM."}}, "page5": {"sectionTop": {"title1": "Элементы <span class='sr-only'> C E A M </span><span aria-hidden='true'>CEAM</span>"}, "section1": {"title1": "Модели процессов ", "text1": "Модели процессов являются представлениями того, как в настоящий момент функционирует процесс с соответствующими прикладными системами. ", "text2": "Мы используем модели процессов для понимания процессов и действий в рамках каждого процесса в классификации. ", "text3": "Такие модели визуально представляют этапы процесса с использованием классификации функций, необходимых для успешного выполнения процесса и достижения требуемого результата. Они позволяют привязать функции прикладных систем и концепции данных к процессам.", "callout1": "Владельцы процессов отвечают за точность составления моделей для соответствующих процессов.", "text7": "Рассмотрим модели процессов более подробно, изучив отдельные элементы на примере процесса <strong>обработки платежей</strong>. Важно обратить внимание на то, что элементы в данном примере упрощены. ", "cta_tabs": "Нажмите поочередно на каждую из четырех вкладок, чтобы узнать больше о каждом элементе, необходимом для исполнения процесса обработки платежей.", "tabs1": [{"img": "../assets/page5/tab1.svg", "title": "Полный обзор", "title_full": "Полный обзор (только для иллюстрации)", "text": ["Помните: модели процессов включают несколько элементов, которые необходимы для реализации процесса. "], "alt": "Схема со всеми элементами, из которых состоит процесс обработки платежей. В первом ряду указаны девять функций от начала до конца процесса. К ним относятся: 1. получение платежа, 2. проверка нормативно-правового соответствия, 3. хранение платежа, 4. запрос о цене, 5. обработка кредита, 6. обработка сделки с иностранной валютой, 7. изменение/отмена поручения, 8. осуществление платежа и 9. проводка по счету и занесение в гроссбух. Эти функции связаны между собой концепциями данных. Первая, вторая, третья и четвертая функции связаны между собой концепцией «Платеж». Четвертая, пятая и шестая функции связаны между собой концепциями «Платеж» и «Курс обмена иностранной валюты». Шестая, седьмая, восьмая и девятая функции снова связаны между собой концепцией «Платеж». Над блоками с функциями указана связь с концепциями данных (буквы). Во втором ряду расположены четыре разновидности основных или справочных данных. К ним относятся: счет, расчетное время для клиринговой системы, номера БИК/ABA, которые зависят от страны и валюты, и счет учета / счет «ностро». Последний ряд содержит три уровня автоматизации. К ним относятся: полная автоматизация, комбинированный ввод и полностью ручной ввод."}, {"img": "./assets/page5/tab2.svg", "title": "Функции прикладных систем", "title_full": "Функции прикладных систем (только для иллюстрации)", "text": ["Они включают в себя девять этапов / основных операций процесса обработки платежей (например, получение платежа, проверка нормативно-правового соответствия).", "Эти функции связаны с прикладными системами с функциями, которые могут повторно использоваться в различных процессах."], "alt": "На схеме выделен только первый ряд, содержащий функции прикладных систем. Их всего девять от начала до конца процесса обработки платежей. К ним относятся: 1. получение платежа, 2. проверка нормативно-правового соответствия, 3. хранение платежа, 4. запрос о цене, 5. обработка кредита, 6. обработка сделки с иностранной валютой, 7. изменение/отмена поручения, 8. осуществление платежа и 9. проводка по счету и занесение в гроссбух."}, {"img": "./assets/page5/tab3.svg", "title": "Концепции данных", "title_full": "Концепции данных (только для иллюстрации)", "text": ["Они включают данные, которые используют или получают в процессе обработки платежей (например, размер платежа, курс обмена иностранной валюты).", "Они определяются согласно контролируемому словарю концепций данных."], "alt": "На схеме выделен второй ряд, содержащий основные / справочные данные, а также связи «Платеж» и «Курс обмена иностранной валюты», которые соединяют девять функций. Эти концепции данных связаны со средствами контроля А и B над первой и девятой функциями."}, {"img": "./assets/page5/tab4.svg", "title": "Вводимые вручную данные", "title_full": "Вводимые вручную данные (только для иллюстрации)", "text": ["Вводимые вручную данные, или этапы ручной обработки, выявляются владельцем процесса для каждой функции в соответствующем процессе.", "При выявлении вводимых вручную данных они подразделяются по способу ручного ввода или этапу процесса, например «необходимые исходя из суждения» или «автоматизируемые»."], "alt": "На этой схеме выделен последний ряд, содержащий вводимые вручную данные / уровень автоматизации и соответствующий показатель автоматизации для каждой функции. Перечислен каждый уровень автоматизации: полная автоматизация, комбинированный ввод и полностью ручной ввод. Для каждой функции представлено девять уровней автоматизации от начала до конца процесса обработки платежей. "}]}, "section2": {"title1": "Функции, назначения и отображение прикладных систем", "text2": "Полученные функции прикладных систем, описанные в разделе о классификации функций, сопоставляются с моделями процессов.", "text5": "Что представляет собой назначение функции прикладной системы?", "text6": "Назначение функции прикладной системы указывает на стратегическую цель использования функции прикладной системы в процессе.  ", "text7": "Значения, присваиваемые каждой функции прикладной системы, могут быть следующими:", "text8": "Стратегическое", "list2": ["Прикладные системы, в которые Citi планирует инвестировать, и конкретная функция имеет стратегическое значение. Это означает, что другие прикладные системы с данным функционалом могут быть консолидированы как прикладные системы со стратегическим назначением. "], "text9": "Сохранить в текущем виде ", "list3": ["Данная прикладная система в достаточной степени соответствует требованиям к функции, но не является стратегической (т. е. миграция из других прикладных систем не должна осуществляться в данную прикладную систему). "], "text10": "Вывести из эксплуатации", "list4": ["Данная прикладная система соответствует критериям для выведения из эксплуатации и (или) функция прикладной системы не соответствует цели данной функции. Данную систему следует в разумные сроки консолидировать, желательно в стратегическую прикладную систему для данной функции. "], "paragraphs_11": ["Назначения функций прикладных систем — это входные данные, необходимые для оценивания нашего прогресса в рационализации или сокращении прикладных систем, а также возможности повторного использования прикладной системы с целью упрощения технологий в рамках наших процессов. Полученные данные позволяют нам контролировать наше соответствие нормативно-правовым требованиям в ходе реализации технологических проектов.", "В конечном итоге он позволяет нам: ", ["понять, где одни и те же функции выполняют различные прикладные системы; ", "понять, где при реализации функции используется надлежащая прикладная система (зависит от процесса);", "рационализировать и упростить взаимодействие между функциями и прикладными системами, присвоив каждой функции прикладной системы «назначение функции прикладной системы»."]], "text12": "Владельцы процессов обеспечивают точность и полноту моделей своих процессов и назначений функций прикладных систем для своих процессов."}, "section3": {"title": "Упрощение технологий ", "subtitle": "Что такое упрощение технологий и почему это важно?", "paragraphs": ["<strong>Упрощение технологий</strong> — это система, направленная на совершенствование технологической инфраструктуры Citi посредством выявления прикладных систем с общим функционалом с целью консолидации. Она позволяет нам эффективнее управлять нашим реестром прикладных систем и обеспечивать лучший в своем классе функционал, что в конечном счете позволяет сократить уровень риска и улучшить обслуживание клиентов. ", "Отображение прикладной системы обеспечивает выявление и измерение возможностей для упрощения технологий в процессах посредством моделей процессов."]}, "section4": {"paragraphs": ["Сокращая с ее помощью сложность, присущую Citi, можно добиться улучшений в следующих сферах:", ["Скорость выхода на рынок", "Общая гибкость", "Конкурентоспособность", "Эффективность "], "Наприм<PERSON><PERSON>, функ<PERSON>и<PERSON>нал <strong>валидации платежа</strong> выявил 156 прикладных систем, используемых во множестве различных процессов в Citi."], "callout": "Наприм<PERSON><PERSON>, функ<PERSON>и<PERSON>нал <strong>валидации платежа</strong> выявил 156 прикладных систем, используемых во множестве различных процессов в Citi."}, "footer": {"text1": "Мы рассмотрели процессы, модели процессов и владельцев процессов, и теперь на примере следующей ситуации мы узнаем, как корпоративная архитектура помогает упрощать процессы."}}, "page6": {"sectionTop": {"title1": "Упрощение технологий в действии"}, "section1": {"title1": "За пределами Citi", "text1": "Это Грета.", "text2": "Она обожает онлайн-шопинг, и для покупки товаров пользуется мобильным платежным приложением. Затем она платит своему поставщику денежных средств с помощью процесса оплаты электронного счета от Citi. ", "text3": "Грете нравится удобство оплаты товаров одним нажатием.", "text4": "<strong>Рассмотрим обслуживание Греты более подробно. </strong>", "text6": "Кроме онлайн-шопинга, Грета периодически инвестирует в рыночные инструменты. Когда она размещает биржевые приказы, она пользуется процессом обработки транзакций, основанным на процессе обработке платежей, для оплаты ценных бумаг, в которые она хочет вложиться. Использование этих процессов в Citi должно быть удобным, безошибочным и безопасным.", "text7": "Эти продукты и услуги предоставляются при помощи нескольких процессов, задача которых — обеспечить целостный опыт от начала до конца покупки и оставить у клиента ощущение удовлетворенности.", "text8": "Этого можно достичь следующими способами: ", "list1": ["разбить процессы на элементы, чтобы изучить действия, лежащие в их основе, и функции прикладных систем, реализующие процессы; ", "определить, какие прикладные системы необходимы для реализации каждой функции в процессе. "], "text9": "Например, когда Грета заказывает продукты питания на неделю или продлевает подписку на услуги стриминговой платформы, она ожидает, что безопасность исполнения платежа будет неизменной, вне зависимости от платформы. ", "text10": "Независимо от того, заказывает ли Грета продукты или продлевает подписку, она пройдет на сайте одни и те же этапы оформления заказа, обработки платежа, проверки транзакции на признаки мошеннических действий и уплаты необходимых налогов. ", "text12": "Если посмотреть на весь этот процесс через призму корпоративной архитектуры, мы увидим, что определенные функции используются многократно на различных платформах и их необходимо стандартизировать по различным сервисам сайта. ", "text13": "Такая организация процесса полезна не только Грете, но и сайту (корпорации), поскольку она направлена на оптимизацию клиентского опыта и повышение эффективности процесса с одновременным сокращением уровня риска.", "title2": "Как это соотносится с Citi? ", "text14": "Способность выявлять прикладные системы с дублированными функциям не только поможет повысить эффективность предоставления наших продуктов и услуг, но и позволит нам по возможности повторно использовать одни и те же прикладные системы в нескольких процессах.", "text15": "Учитывая опыт Греты, вернемся к нашему клиенту Лоренцо.", "text17": "<strong>История Лоренцо</strong>", "text18": "Как вы помните, Лоренцо был не особо доволен взаимодействием с Citi при приобретении продукта. Ему не понравилось то, что с ним связывались несколько раз, чтобы получить одну и ту же информацию, и он озвучил свое недовольство команде. ", "text19": "К счастью, сам продукт Лоренцо понравился, несмотря на первоначальное впечатление от обслуживания, и он хочет приобрести у банка больше продуктов.", "text20": "Сейчас Лоренцо хочет получить новую кредитную карту и оформить ипотечный кредит. Пока Лоренцо оформляет заявления на эти продукты, мы посмотрим, как команда владельца процесса применила элементы <span class='sr-only'> C E A M </span><span aria-hidden='true'>CEAM</span> и анализ процесса, чтобы обеспечить более рациональный подход, интегрировать инфраструктуру прикладных систем, согласовать данные между командами и, в конечном итоге, повысить качество обслуживания.", "lorenzos_journey_paragraphs": ["Во время нашей первой встречи с Лоренцо он обращался к Citi для оплаты услуг своих поставщиков, но был вынужден пользоваться несколькими платежными приложениями для оформления платежей поставщикам. ", "Упрощение технологий способствует снижению сложности инфраструктуры прикладных систем с помощью выявления и консолидации прикладных систем, выполняющих схожие функции. ", "Стремление Citi проводить упрощение технологий в сфере платежей привело к запуску Citi Payments Express — стратегической прикладной системы, обеспечивающей потоки мгновенных платежей. ", "Вспомните, как Лоренцо приходилось пользоваться тремя различными платежными платформами для оплаты услуг своих поставщиков. Citi удалось упростить сложные аспекты, связанные с мгновенными платежами клиентов, и оптимизировать их опыт. Система Citi Payments Express стала решением, объединившим локальные платежные функции в США и Великобритании в единую глобальную прикладную систему для мгновенных платежей.", "Запуск Citi Payments Express упростил жизнь Лоренцо — теперь он может оплачивать услуги своих поставщиков с помощью единой оптимизированной платформы. Упрощение опыта работы позволило удовлетворить его потребности в цифровых платежах. ", "Для Citi вывод лишних прикладных систем из эксплуатации с помощью введения стратегической прикладной системы, выполняющей ту же функцию, обеспечивает движение к более эффективной и менее сложной операционной среде со сниженным уровнем риска и операционных расходов, поскольку нам приходится поддерживать меньше прикладных систем в рамках организации.", "Давайте рассмотрим, как команда владельца процесса с помощью элементов CEAM и анализа процессов создала интегрированную инфраструктуру прикладных систем, обеспечила согласованность данных между различными командами и в итоге улучшила клиентский опыт. "], "text21": "Упрощение технологий на практике", "text22": "Пользуясь корпоративными классификациями, команда выявила ряд относящихся к платежам функций и соответствующих прикладных систем, используемых в различных процессах через CEAM. ", "text23": "Используя платежные функции в прикладных системах, обозначенные как «вывести из эксплуатации», владельцы процессов доработали свои процессы, устранив из них такие функции в пользу Citi Payments Express. В результате платежный функционал реализуется в меньшем количестве прикладных систем, клиентам обеспечен более комфортный опыт, а Citi продемонстрировала свое стремление к позитивным переменам.", "text27": "Лоренцо больше не нужно пользоваться несколькими платежными приложениями для перевода средств своим поставщикам в США и Великобритании. Теперь он может использовать одну платформу для обработки и отслеживания своих цифровых платежей. Благодаря сокращению сложности Лоренцо может начать поиск возможностей для экономии, обращаясь к поставщикам в других частях света! ", "text28": "Посредством упрощения технологий банк обеспечил внутренней команде более связный и эффективный процесс, а Лоренцо — более комфортную работу с приложением."}, "footer": {"text1": "Теперь давайте узнаем, кто использует корпоративную архитектуру и управление процессами в Citi. "}}, "page7": {"sectionTop": {"title1": "Кто отвечает за применение корпоративной архитектуры и управление процессами?"}, "section1": {"text1": "Помните, что цель корпоративной архитектуры — гармонизировать бизнес-цели Citi с нашей стратегией в отношении прикладных систем. Она помогает выявить внутри процессов возможности для повышения эффективности и результативности, что идет на благо нашим сотрудникам, клиентам и компании Citi в целом. ", "text2": "Упрощение и управление процессами в компании не могут быть достигнуты за счет разрозненности, а скорее за счет того, что каждый из нас: ", "list1": ["меняет то, как он выполняет свои повседневные обязанности; ", "использует общий язык; ", "привязывает информацию в нашей операционной деятельности к процессам для реализации анализа процессов. "], "text3": "Корпоративная архитектура и управление процессами в широком смысле касаются всех сотрудников Citi, когда нужно объединить элементы с процессами в каких-либо системах регистрации данных. Используя свои знания, они должны обеспечивать точность этого объединения при выборе соответствующего процесса.", "text3b": "Например, это может быть привязывание процессов к средствам пользовательских вычислений (ПВ) или инвестиционным запросам.", "text4": "Рассмотрим, как специалисты, выполняющие различные роли в Citi, используют корпоративную архитектуру. ", "title1": "Каким образом специалисты, выполняющие различные роли, используют корпоративную архитектуру и управление процессами? ", "text5": "<strong>Нажмите на каждую роль, чтобы узнать о соответствующих обязанностях по применению корпоративной архитектуры.</strong>", "cards1": [{"id": "card1", "title": "Владелец пользовательских вычислений <span class='sr-only'> (П В) </span><span aria-hidden='true'>(ПВ)</span>", "text": ["При регистрации новых средств ПВ их владельцам будет предложено выбрать, реализации какого процесса или процессов способствуют эти средства ПВ. <br><br>Владельцы процессов должны проверить и утвердить эту связь в качестве входных данных для показателя риска их процесса. Утвержденные средства ПВ будут встроены в платформу оценки владения процессами (POA)."]}, {"id": "card2", "title": "Инвестиционные спонсоры", "text": ["Инвестиционным спонсорам, создающим запрос на инвестиции, будет предложено выбрать процесс или процессы, которые затрагивает их запрос на инвестиции. <br><br>В ходе создания запроса на инвестиции и его утверждения инвестиционным спонсорам может понадобиться предоставить входные данные для модели процесса, используемые для обеспечения технологических изменений и выявления возможностей для упрощения технологий."]}, {"id": "card3", "title": "Пользователи оценки контроля со стороны руководства (MCA)", "text": ["Владельцы процессов создают и актуализируют программы оценки контроля глобальных процессов со стороны руководства (Global Process MCA Profile, GPMP), которые связывают процессы с рисками и эталонными средствами контроля. <br><br>Ответственные за риски в MCA выявляют и принимают процессы и соответствующие GPMP с целью управления рисками, сопряженными с задачами, поставленными руководством их подразделения, подлежащего оценке. <br><br>Принятые в результате этого средства контроля будут видны владельцу процесса в рамках оценки владения процессом (POA)."]}], "text6": "Несмотря на то, что в рамках своей должности вы можете не использовать корпоративную архитектуру ежедневно, с большой вероятностью возникнут ситуации, когда вы будете ее применять, поскольку классификация процессов используется в основных программах, таких как оценка контроля со стороны руководства (MCA) и ПВ. <br><br><strong>Поэтому вы должны правильно использовать корпоративную архитектуру в рамках наших общих усилий по упрощению процессов Citi.</strong>", "text7": "Дополнительную информацию о том, как Citi использует корпоративную архитектуру в различных областях, вы можете получить на странице со <a href='https://citi.sharepoint.com/sites/Enterprise-Architecture/SitePages/ReferenceMaterials.aspx' target='_blank'>справочными материалами по бизнес-архитектуре</a>."}, "footer": {"text1": "Вкратце рассмотрим изученный материал курса перед заключительной оценкой."}}, "page8": {"sectionTop": {"title1": "Краткое содержание курса"}, "section1": {"text1": "В ходе данного тренинга мы рассмотрели следующее:", "cards1": [{"text": "Компания Citi внедрила корпоративную архитектуру и управление процессами, чтобы сформировать процессный подход с целью упростить процессы и сократить сопряженные с процессами риски, что в итоге приведет к повышению качества обслуживания клиентов."}, {"text": "Бизнес-архитектура управляет следующими тремя классификациями: классификация процессов, классификация функций и контролируемый словарь концепций данных. "}, {"paragraphs": ["Бизнес-архитектура и управление процессами помогают нам лучше понять, где наша компания находится сейчас и где она хочет находиться. ", "Это возможно с помощью выявления пробелов и потенциальных возможностей, благодаря которым прикладные системы Citi согласуются с бизнес-целями."]}, {"text": "Упрощение технологий способствует консолидации прикладных систем с дублированием функций. Управление процессами способствует оптимизации процессов в целом."}, {"paragraphs": ["Существует ряд определенных специалистов, которые будут применять корпоративную архитектуру в рамках своих обязанностей. ", "Однако успешное применение корпоративной архитектуры в упрощении процессов Citi зависит от того, насколько каждый сотрудник Citi берет на себя ответственность за надлежащее применение процессов и соблюдение принципов управления процессами."]}], "text2": "Ознакомьтесь с <a href='https://citi.hostedconnectedrisk.com/CitiCPD/react?page=O&CID=sid552268563&DeletedOnly=0&HTMFile=instancePolicyUser_View.htm&InstanceNo=2001487&NodeNo=182&inlineEdit=0&originalRequestNodeNo=182&pageType=42&resetTreeView=0&rightClickContextMenu=0&runOnce=1&scrollableViewer=1&treeViewNode0=253&treeViewNode0OverviewPage=instancePolicyVersionAuthor_Title.htm&treeViewNode1=281&treeViewNode10=371&treeViewNode10OverviewPage=instancePolicySectionLevel10VersionAuthorView.htm&treeViewNode1OverviewPage=instancePolicySectionLevel1VersionAuthor_View.htm&treeViewNode2=282&treeViewNode2OverviewPage=instancePolicySectionLevel2VersionAuthor_View.htm&treeViewNode3=283&treeViewNode3OverviewPage=instancePolicySectionLevel3VersionAuthor_View.htm&treeViewNode4=285&treeViewNode4OverviewPage=instancePolicySectionLevel4VersionAuthor_View.htm&treeViewNode5=284&treeViewNode5OverviewPage=instancePolicySectionLevel5VersionAuthor_View.htm&treeViewNode6=372&treeViewNode6OverviewPage=instancePolicySectionLevel6VersionAuthorView.htm&treeViewNode7=373&treeViewNode7OverviewPage=instancePolicySectionLevel7VersionAuthorView.htm&treeViewNode8=374&treeViewNode8OverviewPage=instancePolicySectionLevel8VersionAuthorView.htm&treeViewNode9=375&treeViewNode9OverviewPage=instancePolicySectionLevel9VersionAuthorView.htm&useCustomFieldForAddChild=0&useOriginalRequestNodeAsCurrent=1&usingpost=1' target='_blank'>Политикой в отношении корпоративной архитектуры и управления процессами</a> (Enterprise Architecture and Process Governance Policy, EAPGP), чтобы получить дополнительную информацию о корпоративной архитектуре и управлении процессами. Ее обязаны соблюдать все сотрудники, подрядчики, консультанты Citi, а также стороны, на которые распространяется требование соблюдать политики Citi.", "text3": "Помните, что применение корпоративной архитектуры и управления процессами приводит к упрощению и повышению эффективности, а это выгодно всем — и сотрудникам, и клиентам Citi."}, "footer": {"text1": "А теперь давайте проверим ваши знания."}}, "page11": {"topSection": {"title": "Заключительная оценка", "text1": "Поздравляем! Вы выполнили все требования, необходимые для завершения обучающего курса. Теперь вы можете снова просмотреть содержание или выйти из курса.", "text2": "Нажмите на кнопку закрытия (Х) в верхнем правом углу окна браузера, чтобы выйти из курса."}}}