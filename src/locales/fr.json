{"manifest": {"title": "Introduction à la méthodologie d’architecture d’entreprise (Citi Enterprise Architecture Methodology, CEAM) et à la gouvernance des processus de Citi", "content": [{"title": "Bienvenue"}, {"title": "Qu’est-ce que la Méthodologie d’architecture d’entreprise ?"}, {"title": "Fondation de l’architecture d’entreprise"}, {"title": "Qui sont les responsables des processus ?"}, {"title": "Éléments fondamentaux de la <span class='sr-only'> C E A M </span><span aria-hidden='true'>CEAM</span>"}, {"title": "La simplification technologique en action"}, {"title": "Qui est responsable de l’application de l’architecture d’entreprise et de la gouvernance des processus ?"}, {"title": "Synthèse de la formation"}, {"title": "Évaluation"}]}, "resources": {"title1": "Ressources", "title2": "Acronymes", "title3": "<PERSON><PERSON>", "list1": ["CEAM – Citi Enterprise Architecture Methodology (Méthodologie d’architecture d’entreprise de Citi)", "DSMT – Data Standards Management Tool (Outil de gestion des normes de données)", "GPMP – Global Process MCA Profile (Profil MCA du processus mondial)", "EAPGP – Enterprise Architecture and Process Governance Policy (Politique d’application de l’architecture d’entreprise et de la gouvernance des processus)", "EUC – End User Computing (Informatique utilisateur)", "MCA – Manager’s Control Assessment (Évaluation des contrôles des managers)", "POA – Process Ownership Assessment Platform (Plateforme d’évaluation des responsabilités liées aux processus)", "PTS – Project Tracking System (Système de suivi des projets)", "I@C – Investments@Citi"], "list2": ["<a href='https://citi.sharepoint.com/sites/Enterprise-Architecture/SitePages/Process-Ownership.aspx?csf=1&web=1&e=1oNYIZ' target='_blank'>Ressources pour les responsables de processus d’architecture d’entreprise</a>", "<a href='https://citi.sharepoint.com/sites/Enterprise-Architecture/SitePages/ReferenceMaterials.aspx' target='_blank'>Documents de référence sur l’architecture d’entreprise</a>", "<a href='https://citi.hostedconnectedrisk.com/CitiCPD/react?page=O&CID=sid552268563&DeletedOnly=0&HTMFile=instancePolicyUser_View.htm&InstanceNo=2001487&NodeNo=182&inlineEdit=0&originalRequestNodeNo=182&pageType=42&resetTreeView=0&rightClickContextMenu=0&runOnce=1&scrollableViewer=1&treeViewNode0=253&treeViewNode0OverviewPage=instancePolicyVersionAuthor_Title.htm&treeViewNode1=281&treeViewNode10=371&treeViewNode10OverviewPage=instancePolicySectionLevel10VersionAuthorView.htm&treeViewNode1OverviewPage=instancePolicySectionLevel1VersionAuthor_View.htm&treeViewNode2=282&treeViewNode2OverviewPage=instancePolicySectionLevel2VersionAuthor_View.htm&treeViewNode3=283&treeViewNode3OverviewPage=instancePolicySectionLevel3VersionAuthor_View.htm&treeViewNode4=285&treeViewNode4OverviewPage=instancePolicySectionLevel4VersionAuthor_View.htm&treeViewNode5=284&treeViewNode5OverviewPage=instancePolicySectionLevel5VersionAuthor_View.htm&treeViewNode6=372&treeViewNode6OverviewPage=instancePolicySectionLevel6VersionAuthorView.htm&treeViewNode7=373&treeViewNode7OverviewPage=instancePolicySectionLevel7VersionAuthorView.htm&treeViewNode8=374&treeViewNode8OverviewPage=instancePolicySectionLevel8VersionAuthorView.htm&treeViewNode9=375&treeViewNode9OverviewPage=instancePolicySectionLevel9VersionAuthorView.htm&useCustomFieldForAddChild=0&useOriginalRequestNodeAsCurrent=1&usingpost=1' target='_blank'>Enterprise Architecture and Process Governance Policy (EAPGP)</a>", "<a href='https://www.citigroup.net/clicks/?version=3&utm_campaign=1625863048700868QguJhvH%2fCbsXACX4DonJcmabp9YzQXIRFAJJBbS86kyYTSzXYlBAUFpRk0RutsWxa8CMCIUYkcHJLo6cv6d3YBWMrc7TkgakWaIfQxu2lMf%2bid2Rdit0w40I%2fNZOeMBvDlfOKPiiQtXA5yGrK6rSWg%3d%3d&ctid=1625863048700883DEHnfsoz07WcImMs4XRY3vYrwPekdV1fxVqQQCX3pzC5YN%2bERTNzWzsOqx00Lz6snxX0eMKW%2bpdhG8bC%2fnGLrDr%2fxCAAA1wMNmGYs7aAwh7DHMIPOFne2kz71lbbn6WiTKelhg0XoVQN5%2b5MxQTyVg%3d%3d' target='_blank'>Process Governance Standard (PGrS)</a>"]}, "errors": {"error": "<PERSON><PERSON><PERSON>", "lostcommunication": "Veuillez fermer le module, puis le relancer. Une erreur LMS est survenue. Le module poursuivra son dérou<PERSON>, mais il ne suivra pas votre progression.", "close": "<PERSON><PERSON><PERSON>"}, "errorLMS": {"text1": "<strong>Attention !‎</strong> Cette formation ne peut être reliée à la plateforme de gestion de l’apprentissage (Learning Management System, LMS). Votre progression passée a été enregistrée. Cependant, votre progression actuelle ne l’est plus.", "text2": "Cela se produit généralement en cas d’interruption de la connexion au réseau. Ou lorsque la connexion avec la plateforme de gestion de l’apprentissage (Learning Management System, LMS) a expiré.", "text3": "<strong><PERSON>ur remédier à ce problème :</strong> <PERSON><PERSON><PERSON> cette fenêtre de cours, puis toutes les autres fenêtres du navigateur.  Ouvrez une nouvelle fenêtre de navigateur et accédez à la LMS. Localisez cette formation et cliquez sur le bouton « Démarrer ».  Votre progression dans le cours sera rétablie au dernier point où une connexion a été enregistrée."}, "ui": {"sr": "Message à l’attention des utilisateurs de lecteurs d’écrans : cette formation a été optimisée à des fins d’accessibilité et prend complètement en charge les lecteurs d’écrans.", "langselection": "Sélection de la langue", "chooselang": "Veuillez sélectionner votre langue :", "menu": "<PERSON><PERSON>", "menuItems": "Éléments du menu", "exit": "<PERSON><PERSON><PERSON>", "exitCourse": "Sortir de la formation", "logo": "Logo Citi", "notattempted": "Non tenté", "incomplete": "Non terminé", "complete": "<PERSON><PERSON><PERSON><PERSON>", "locked": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "itemComplete": "<PERSON><PERSON><PERSON> terminé", "itemLocked": "<PERSON><PERSON><PERSON>", "itemUnLocked": "Élément d<PERSON>", "pageLoaded": "<PERSON> chargée", "pagesCompleted": " pages terminées", "resources": "Ressources", "scroll": "Faites défiler la page vers le bas pour continuer.", "scrollc": "Faites défiler vers le bas pour consulter le contenu de la formation.", "previous": "Précédent", "next": "Suivant", "continue": "<PERSON><PERSON><PERSON>", "close": "<PERSON><PERSON><PERSON>", "gotit": "Compris !", "submit": "Envoyer", "learn": "En savoir plus", "return": "Rev<PERSON><PERSON> en haut de page", "citigroup": "Citigroup Inc.", "selectToBegin": "Cliquez pour commencer", "settings": "Paramètres", "animations": "Animations", "on": "Animations activées", "off": "Animations désactivées", "audio": "Audio", "reportconcern": "Signaler une préoccupation", "pressescape": "Appuyez sur la touche ÉCHAP. pour fermer", "of": " sur ", "itemscompleted": " éléments terminés", "questionTitleTemplate": "Question {a} sur {b}", "correct": "Bonne réponse.", "incorrect": "Pas tout à fait."}, "videoplayer": {"Play": "Lecture", "Pause": "Pause", "Current Time": "Temps actuel", "Duration": "<PERSON><PERSON><PERSON>", "Remaining Time": "Temps restant", "Loaded": "<PERSON><PERSON><PERSON>", "Progress": "Progression", "Progress Bar": "Barre de progression", "progress bar timing: currentTime={1} duration={2}": "{1} sur {2}", "Fullscreen": "Plein écran", "Non-Fullscreen": "<PERSON><PERSON><PERSON> le plein écran", "Mute": "Son d<PERSON><PERSON><PERSON><PERSON>", "Unmute": "Son activé", "Volume Level": "Niveau du volume", "You aborted the media playback": "La lecture de la vidéo est terminée.", "A network error caused the media download to fail part-way.": "Le téléchargement du support a échoué à cause d’une erreur réseau.", "The media could not be loaded, either because the server or network failed or because the format is not supported.": "Il n’a pas été possible de charger la vidéo, soit en raison d’une erreur de serveur ou rés<PERSON>, soit parce que son format n’est pas pris en charge.", "The media playback was aborted due to a corruption problem or because the media used features your browser did not support.": "La lecture de la vidéo a été interrompue en raison d’un problème de corruption ou parce que la vidéo utilisait des fonctionnalités que votre navigateur ne prend pas en charge.", "No compatible source was found for this media.": "Aucune source compatible n’a été trouvée pour cette vidéo."}, "bookmark": {"title": "Reprendre la formation avec le signet", "message": "Souhaitez-vous reprendre ou redémarrer la formation ?", "cancel": "Redémarrer la formation", "ok": "Reprendre la formation"}, "closemodule": {"title": "Sortir de la formation", "message": "Êtes-vous sûr(e) de vouloir quitter le module ?", "cancel": "Annuler", "ok": "OK"}, "page1": {"sectionTop": {"title1": "Introduction à l’architecture d’entreprise et à la gouvernance des processus de Citi", "title2": "Formation en ligne de 20 minutes "}, "section1": {"title1": "Consignes de navigation au sein de la présente formation", "instr1": "Lisez les cartes pour bénéficier d’une meilleure expérience de navigation dans le cours.", "list1": [{"text1": "Sélectionnez l’icône de menu pour naviguer vers les différentes sections du cours. Chaque section devient accessible une fois que vous avez rempli toutes les conditions de la section précédente.", "text2": null, "icon1": "bi-list"}, {"text1": "Sélectionnez l’icône des ressources dans le coin supérieur droit pour obtenir des ressources supplémentaires liées à ce cours. Vous trouverez dans cette formation des hyperliens vers les procédures et la politique connexes. ", "text2": null, "icon1": "bi-info-circle"}, {"text1": "Ce cours comprend des <strong><span class='text-secondary' style='text-decoration: underline'>liens hypertextes</span></strong> vers diverses ressources. Si vous accédez au cours à partir d’un appareil personnel directement sur Internet (en dehors du réseau Citi), certains liens peuvent ne pas fonctionner s’ils renvoient au contenu du réseau Citi. Cela n’aura aucune incidence sur votre capacité à terminer la formation.", "text2": null, "icon1": "bi-link-45deg"}, {"text1": "Si vous avez des difficultés à afficher du contenu animé, vous pouvez choisir de désactiver les fonctionnalités animées de ce cours dès maintenant. Les animations sont conçues pour améliorer l’expérience du cours. La désactivation des animations ne vous empêchera pas d’accéder au contenu. ", "text2": null, "icon1": "bi-gear"}, {"text1": "<PERSON>, à tout moment, vous devez quitter le cours avant la fin, cliquez sur cette icône en haut de la page pour enregistrer votre progression et quitter le cours.", "text2": null, "icon1": "bi-x-circle"}]}, "sectionWhyWhyWhyWhy": {"title": "Formation à l’architecture d’entreprise et à la gouvernance des processus de Citi", "list": [{"title": "Pourquoi ce thème ?", "paragraphs": ["La croissance de Citi sur divers marchés et activités a donné naissance à un écosystème complexe de plateformes et d’applications numériques. Si cette expansion a apporté de nombreux avantages, elle a également entraîné des doublons, des chevauchements de fonctionnalités et un manque de visibilité sur le fonctionnement de bout en bout de nos processus. L’architecture d’entreprise et la gouvernance des processus de Citi offrent un moyen unifié d’évaluer l’état des processus et les risques associés, afin que nous puissions prendre des décisions informées au niveau de l’entreprise."]}, {"title": "Pourquoi maintenant ?", "paragraphs": ["L’architecture d’entreprise est essentielle à la transformation de Citi et au maintien de la santé et de la durabilité de l’organisation sur un marché de plus en plus concurrentiel."]}, {"title": "Pourquoi nous ?", "paragraphs": ["L’architecture d’entreprise et la gouvernance des processus font déjà partie de notre travail au sein de Citi. Chacun joue un rôle dans la simplification des processus et leur mise en relation au sein de Citi afin d’améliorer nos systèmes. Notre connaissance de cette infrastructure nous permet de réduire les risques, d’améliorer les décisions et de gagner en efficacité."]}, {"title": "Qu’est-ce que l’on y gagne ?", "paragraphs": ["L’architecture d’entreprise et la gouvernance des processus fournissent une approche centrée sur les processus utilisée pour simplifier et réduire les risques liés aux processus, afin d’améliorer l’expérience des clients et des employés. "]}]}, "section2": {"text1": "Cette formation vous aidera à comprendre : ", "list1": ["Le cadre de l’architecture d’entreprise ou Méthodologie d’architecture d’entreprise de Citi (Citi Enterprise Architecture Methodology, CEAM)", "Comment l’architecture d’entreprise, la gouvernance des processus et la simplification technologique nous aident à réduire les risques en simplifiant les processus de Citi et les applications correspondantes."], "text2": "À la fin de la présente formation, vous pourrez :", "list2": ["Expliquer l’objectif de l’architecture d’entreprise et de la gouvernance des processus.", "Décrire comment la gouvernance des processus contribue à simplifier et à améliorer les opérations de Citi.", "Reconnaître le rôle des taxonomies dans le soutien de la cohérence au sein de Citi.", "Décrire les responsabilités des responsables de processus dans le cadre de l’architecture d’entreprise et de la gouvernance des processus.", "Reconnaître les rôles responsables de l’application de l’architecture d’entreprise et de la gouvernance des processus.", "Identifier les composants clés utilisés dans la CEAM."]}, "section_assessment_disclaimer": {"paragraphs": ["Veuillez noter que ce cours contient une évaluation finale. Pour recevoir un crédit d’achèvement pour cette formation, vous devez obtenir un score de 80 % ou plus. "]}, "section3": {"title": "Bienvenue à la formation à l’architecture d’entreprise et à la gouvernance des processus de Citi ", "paragraphs": ["L’architecture d’entreprise et la gouvernance des processus aident Citi à se concentrer sur la cohérence et l’alignement des processus et des applications sous-jacentes pour la réduction des risques et la simplification technologique.", "Ces pratiques génèrent également des informations sur les processus, permettant une compréhension claire des liens entre nos processus, nos applications et nos risques. Gr<PERSON><PERSON> à ces informations, nous pouvons identifier les chevauchements, réduire la complexité et prendre des décisions plus éclairées sur les éléments à simplifier, à contrôler ou dans lesquels investir."]}, "section4": {"paragraphs": ["Cette complexité apparaît de diverses manières, notamment :", ["Plusieurs applications avec des fonctionnalités similaires.", "Des applications avec trop de fonctionnalités.", "Une absence de représentation normalisée de nos processus clés pour évaluer la qualité de la conception de nos processus."], "Par conséquent, il peut être difficile de comprendre comment nos processus professionnels fonctionnent de bout en bout, les risques associés et pourquoi nous avons plusieurs applications qui exécutent probablement les mêmes fonctions. "]}, "section5": {"paragraphs": ["Pour y remédier, Citi a établi des exigences normalisées en matière d’architecture d’entreprise et de gouvernance des processus."]}, "section6": {"paragraphs": ["Ces exigences nous permettent de mieux comprendre : ", ["Nos processus et les applications à l’appui. ", "Les possibilités de simplifier notre inventaire d’applications soutenant nos processus.", "Les cas où des contrôles sont nécessaires pour réduire le risque de nos processus."]]}, "section7": {"paragraphs": ["L’<strong>architecture d’entreprise</strong> représente l’interconnectivité de nos processus, de nos applications et d’autres éléments clés qui soutiennent nos opérations. "]}, "section8": {"paragraphs": ["L’<strong>architecture d’entreprise repose sur la taxonomie des processus</strong>. Les processus inclus dans cette taxonomie, ainsi que la gouvernance des processus, nous permettent de <strong>visualiser systématiquement nos risques, applications et autres facteurs</strong> dans la prise de décisions professionnelles objectives, ce qui contribue à concentrer nos investissements sur la façon dont nous exécutons ces investissements stratégiques."]}, "footer": {"text1": "Examinons ce qu’est l’architecture d’entreprise et comment nous l’utilisons au sein de Citi."}}, "assessmentFinal": {"text1a": "Bienvenue à l’évaluation finale.", "text1b": "Pour valider cette formation et valider vos crédits, vous devez répondre aux {numberOfQuestions} questions suivantes et obtenir une note minimale de 80 %.", "list1": [["Il n’existe pas de limite au nombre d’essais possibles.", "Une fois que vous avez commencé l’évaluation, vous devez répondre à l’ensemble des questions pour obtenir un score. ", "Si vous abandonnez avant d’avoir répondu à l’ensemble des questions, vous devrez recommencer l’évaluation."]], "text2": "Faites défiler la page vers le bas pour continuer vers l’évaluation.", "questionsSection": [{"id": "kc_bankA_1", "bank": "bankA", "title": " ", "text": ["L’architecture d’entreprise et la gouvernance des processus (EAPGP) ont été développées pour identifier les possibilités de consolider les applications dont les fonctions font double emploi.  ", "Quels autres aspects clés l’EAPGP nous aide-t-elle à identifier ?"], "instructions": "Choisissez la meilleure réponse parmi les quatre options, puis cliquez sur Envoyer.", "options": [{"label": "Le coût de la maintenance de chaque application au fil du temps", "value": "a"}, {"label": "Les équipes responsables de la saisie des données dans chaque système", "value": "b"}, {"label": "Les cas où des saisies manuelles sont associées à un processus", "value": "c"}, {"label": "Toutes les réponses proposées", "value": "d"}], "correctResponse": "c", "correctFeedbackTitle": "Cette réponse est correcte.", "incorrectFeedbackTitle": "Cette réponse est incorrecte.", "correctFeedbackText": ["L’EAPGP nous aide également à identifier les cas où des saisies manuelles sont associées à un processus. Cela nous permet de comprendre pourquoi ces étapes sont manuelles et d’aider les équipes à trouver des processus plus efficaces."], "incorrectFeedbackText": ["Pour cette question, ve<PERSON><PERSON><PERSON> consulter la section <strong>Introduction</strong> de la page <strong>Bienvenue</strong>‎."], "universalFeedbackText": ["universel - feedback"]}, {"id": "kc_bankA_2", "bank": "bankA", "title": " ", "text": ["L’architecture d’entreprise régit la taxonomie des processus et la taxonomie des fonctions. Lesquels des éléments suivants sont également des taxonomies régies ?"], "instructions": "Choisissez la meilleure réponse parmi les quatre options, puis cliquez sur Envoyer.", "options": [{"label": "Taxonomie de l’atténuation des risques de l’entreprise", "value": "a"}, {"label": "Taxonomie du processus d’audit de l’entreprise ", "value": "b"}, {"label": "Vocabulaire contrôlé des concepts des données", "value": "c"}, {"label": "Taxonomie des applications commerciales d’entreprise ", "value": "d"}], "correctResponse": "c", "correctFeedbackTitle": "Cette réponse est correcte.", "incorrectFeedbackTitle": "Cette réponse est incorrecte.", "correctFeedbackText": ["Les taxonomies qui créent un langage commun prenant en charge la communication et la compréhension entre les entreprises, dont trois sont régies par l’architecture d’entreprise. ", "La taxonomie des processus, la taxonomie des fonctions et le vocabulaire contrôlé des concepts des données assurent la cohérence des artefacts produits dans le cadre de la CEAM au sein de Citi."], "incorrectFeedbackText": ["Pour cette question, ve<PERSON><PERSON><PERSON> consulter la page <strong>Fondation de l’architecture d’entreprise</strong>."], "universalFeedbackText": ["universel - feedback"]}, {"id": "kc_bankA_3", "bank": "bankA", "title": " ", "text": ["Comment la gouvernance des processus nous aide-t-elle à simplifier Citi ?"], "instructions": "Choisissez la meilleure réponse parmi les quatre options, puis cliquez sur Envoyer.", "options": [{"label": "Fournit un moyen objectif de mesurer la qualité d’un processus et les possibilités de simplification et de réduction des risques.", "value": "a"}, {"label": "Définit des taxonomies qui reflètent la terminologie des unités opérationnelles sans exiger de cohérence sur l’ensemble de Citi.", "value": "b"}, {"label": "Fournit un référentiel de processus auquel les équipes peuvent se référer lors des audits et des révisions.", "value": "c"}, {"label": "Exige des équipes mondiales qu’elles suivent des processus identiques, quels que soient les besoins ou le contexte locaux.", "value": "d"}], "correctResponse": "a", "correctFeedbackTitle": "Cette réponse est correcte.", "incorrectFeedbackTitle": "Cette réponse est incorrecte.", "correctFeedbackText": ["La gouvernance des processus soutient la simplification chez Citi : les responsables de processus sont chargés de comprendre le fonctionnement actuel et d’identifier des moyens de l’améliorer. Des outils tels que les modèles de processus, la cartographie des applications et les informations sur les processus permettent de mesurer la qualité d’un processus et de trouver des possibilités de le simplifier, de réduire les risques et d’obtenir de meilleurs résultats."], "incorrectFeedbackText": ["Pour cette question, ve<PERSON><PERSON><PERSON> consulter la section <strong>Introduction</strong> de la page <strong>Bienvenue</strong>‎. "], "universalFeedbackText": ["universel - feedback"]}, {"id": "kc_bankA_4", "bank": "bankA", "title": " ", "text": ["Quelles sont les responsabilités d’un responsable de processus ?"], "instructions": "Choisissez la meilleure réponse parmi les quatre options, puis cliquez sur Envoyer.", "options": [{"label": "Utiliser les informations sur les processus et les données CEAM pour améliorer objectivement les processus.", "value": "a"}, {"label": "Superviser la CEAM et d’autres artefacts associés au ou aux processus qui leur sont attribués.", "value": "b"}, {"label": "Identifier les risques inhérents et les contrôles pour atténuer les risques de leurs processus.", "value": "c"}, {"label": "Toutes les réponses proposées.", "value": "d"}], "correctResponse": "d", "correctFeedbackTitle": "Cette réponse est correcte.", "incorrectFeedbackTitle": "Cette réponse est incorrecte.", "correctFeedbackText": ["Les responsables des processus sont des experts en la matière. Ils sont chargés de superviser la CEAM et d’autres artefacts associés au ou aux processus qui leur sont attribués. ", "Les responsables de processus assurent la supervision et identifient les domaines à améliorer. Pour ce faire, ils améliorent la qualité et la cohérence des processus de Citi et identifient les risques inhérents et les contrôles pour atténuer les risques de leurs processus grâce à l’intelligence des processus et aux points de données de CEAM. "], "incorrectFeedbackText": ["Pour cette question, ve<PERSON><PERSON>z consulter les sections <strong>Qui sont les responsables des processus ?</strong> et <strong>Qui est responsable de l’application de l’architecture d’entreprise et de la gouvernance des processus ?</strong>."], "universalFeedbackText": ["universel - feedback"]}, {"id": "kc_bankA_5", "bank": "bankA", "title": " ", "text": ["Qu’est-ce qu’une taxonomie ? "], "instructions": "Choisissez la meilleure réponse parmi les quatre options, puis cliquez sur Envoyer.", "options": [{"label": "Une taxonomie est un moyen de nommer, d’organiser et de structurer des termes associés.", "value": "a"}, {"label": "Une taxonomie est un moyen de développer des fonctions chez Citi. ", "value": "b"}, {"label": "Une taxonomie est une discipline utilisée pour encourager une utilisation efficace des processus existants.", "value": "c"}, {"label": "Une taxonomie est un rôle spécifique dédié à l’organisation des processus. ", "value": "d"}], "correctResponse": "a", "correctFeedbackTitle": "Cette réponse est correcte.", "incorrectFeedbackTitle": "Cette réponse est incorrecte.", "correctFeedbackText": ["Une taxonomie est un moyen de nommer, d’organiser et de structurer des termes associés. Elle assure la cohérence de la communication, de la gouvernance et des rapports dans l’ensemble de l’entreprise. Par conséquent, une taxonomie améliore l’alignement et la gestion des divers intervenants. "], "incorrectFeedbackText": ["Pour cette question, ve<PERSON><PERSON><PERSON> consulter la page <strong>Fondation de l’architecture d’entreprise</strong>."], "universalFeedbackText": ["universel - feedback"]}, {"id": "kc_bankA_6", "bank": "bankA", "title": " ", "text": ["Quels sont les avantages que présentent l’architecture d’entreprise et la gouvernance des processus ? "], "instructions": "Choisissez la meilleure réponse parmi les quatre options, puis cliquez sur Envoyer.", "options": [{"label": "Elles favorisent la simplification des processus et fournissent un moyen objectif de mesurer la réduction des risques liés aux processus.", "value": "a"}, {"label": "Elles se concentrent uniquement sur les mises à jour technologiques sans améliorer les processus ou les contrôles.", "value": "b"}, {"label": "Elles permettent aux équipes de définir la gouvernance par elles-mêmes, sans s’aligner sur les normes de l’entreprise. ", "value": "c"}, {"label": "Elles privilégient les gains commerciaux rapides plutôt que la cohérence à long terme et la discipline architecturale.", "value": "d"}], "correctResponse": "a", "correctFeedbackTitle": "Cette réponse est correcte.", "incorrectFeedbackTitle": "Cette réponse est incorrecte.", "correctFeedbackText": ["L’architecture d’entreprise et la gouvernance des processus ont été mises en place pour offrir une meilleure expérience aux clients et aux employés de Citi. Cette approche centrée sur les processus permet de simplifier ceux-ci et d’améliorer les contrôles dans l’ensemble de l’entreprise, en supprimant les cloisonnements au sein des fonctions."], "incorrectFeedbackText": ["Pour cette question, ve<PERSON><PERSON><PERSON> consulter la section <strong>Introduction </strong>de la page <strong>Bienvenue</strong> ou <strong>Synthèse de la formation</strong>."], "universalFeedbackText": ["universel - feedback"]}, {"id": "kc_bankA_7", "bank": "bankA", "title": " ", "text": ["Les responsables des processus, les utilisateurs de l’Évaluation des contrôles des managers (MCA) et les responsables EUC sont chargés de l’application de l’architecture d’entreprise et de la gouvernance des processus. ", "Quels sont les autres rôles chargés de l’application de l’architecture d’entreprise et de la gouvernance des processus ?"], "instructions": "Choisissez la meilleure réponse parmi les cinq options, puis cliquez sur Envoyer.", "options": [{"label": "Les responsables des risques ", "value": "a"}, {"label": "Les managers à tous les niveaux", "value": "b"}, {"label": "Les promoteurs d’investissement ", "value": "c"}, {"label": "Toutes les réponses proposées", "value": "d"}, {"label": "Aucune des réponses proposées", "value": "e"}], "correctResponse": "d", "correctFeedbackTitle": "Cette réponse est correcte.", "incorrectFeedbackTitle": "Cette réponse est incorrecte.", "correctFeedbackText": ["L’architecture d’entreprise et la gouvernance des processus sont appliquées par différents membres du personnel de Citi, notamment les responsables de processus, les responsables d’EUC, les promoteurs d’investissement et les utilisateurs de l’évaluation des contrôles des managers. ", "En outre, il est important de se rappeler que tout le personnel de Citi, y compris les sous-traitants, est chargé de sélectionner avec précision le ou les processus appropriés à une application, à un projet ou à un contrôle particuliers dans les différents systèmes où cela s’avère nécessaire. "], "incorrectFeedbackText": ["Pour cette question, ve<PERSON><PERSON><PERSON> consulter les sections suivants : <strong>Comment les différents rôles utilisent-ils l’architecture d’entreprise et la gouvernance des processus ?</strong> et <strong>Qui est responsable de l’application de l’architecture d’entreprise et de la gouvernance des processus ?</strong>."], "universalFeedbackText": ["universel - feedback"]}, {"id": "kc_bankA_8", "bank": "bankA", "title": " ", "text": ["Quelle taxonomie comprend un inventaire des étapes ou des tâches effectuées au sein des applications et exécutées au sein d’un processus ?"], "instructions": "Choisissez la meilleure réponse parmi les quatre options, puis cliquez sur Envoyer.", "options": [{"label": "Taxonomie des processus", "value": "a"}, {"label": "Taxonomie des fonctions", "value": "b"}, {"label": "Vocabulaire contrôlé relatif aux données", "value": "c"}, {"label": "Service commercial", "value": "d"}], "correctResponse": "b", "correctFeedbackTitle": "Cette réponse est correcte.", "incorrectFeedbackTitle": "Cette réponse est incorrecte.", "correctFeedbackText": ["La taxonomie des fonctions comprend un inventaire des étapes, des tâches ou des « fonctions » exécutées au sein d’un processus. ", "Il s’agit d’une hiérarchie d’actions à un niveau qui produit un résultat spécifique (nom de la fonction), une fonction pouvant être utilisée par de nombreux processus. "], "incorrectFeedbackText": ["Pour cette question, ve<PERSON><PERSON><PERSON> consulter la page <strong>Fondation de l’architecture d’entreprise</strong>."], "universalFeedbackText": ["universel - feedback"]}, {"id": "kc_bankA_9", "bank": "bankA", "title": " ", "text": ["Lequel des éléments suivants complète l’ensemble des désignations de fonctions d’application avec « Stratégique » et « Obsolète » ?"], "instructions": "Choisissez la meilleure réponse parmi les quatre options, puis cliquez sur Envoyer.", "options": [{"label": "Conserver", "value": "a"}, {"label": "Archiver ", "value": "b"}, {"label": "<PERSON><PERSON><PERSON>", "value": "c"}, {"label": "Maintenir", "value": "d"}], "correctResponse": "d", "correctFeedbackTitle": "Cette réponse est correcte.", "incorrectFeedbackTitle": "Cette réponse est incorrecte.", "correctFeedbackText": ["Une désignation de fonction d’application indique l’intention stratégique de l’utilisation d’une fonction d’application dans le processus. ", "Trois valeurs peuvent être assignées : Stratégique (la fonctionnalité de l’application est stratégique), Maintenir (la fonctionnalité de l’application n’est pas stratégique) et Obsolète (l’application n’est pas adaptée pour supporter la fonctionnalité donnée). "], "incorrectFeedbackText": ["Pour cette question, ve<PERSON><PERSON>z consulter la section <strong><PERSON><PERSON><PERSON><PERSON> processus</strong> de la page <strong>Éléments fondamentaux de la CEAM</strong>."], "universalFeedbackText": ["universel - feedback"]}, {"id": "kc_bankA_10", "bank": "bankA", "title": " ", "text": ["Quel est l’objectif de l’utilisation des modèles de processus dans l’architecture d’entreprise ?"], "instructions": "Choisissez la meilleure réponse parmi les trois options, puis cliquez sur Envoyer.", "options": [{"label": "Les modèles de processus décrivent les étapes de la création d’un processus.", "value": "a"}, {"label": "Les modèles de processus nous aident à comprendre les processus, leurs activités, les fonctions d’application et les saisies manuelles.", "value": "b"}, {"label": "Les modèles de processus sont utilisés pour attribuer les rôles et les responsabilités au sein des équipes de projet.", "value": "c"}], "correctResponse": "b", "correctFeedbackTitle": "Cette réponse est correcte.", "incorrectFeedbackTitle": "Cette réponse est incorrecte.", "correctFeedbackText": ["Les modèles de processus nous aident à comprendre les processus et leurs activités connexes d’une manière structurée et cohérente. Ils offrent une visibilité sur la manière dont le travail circule au sein de l’organisation et soutiennent la cohérence avec la taxonomie des processus."], "incorrectFeedbackText": ["Pour cette question, ve<PERSON><PERSON>z consulter la section <strong><PERSON><PERSON><PERSON><PERSON> de processus</strong> de la page <strong>Éléments fondamentaux de la CEAM</strong>."], "universalFeedbackText": ["universel - feedback"]}], "finalPassSection": {"title": "Résultats de l’évaluation", "text1": "Votre score est de", "text2": "<strong>Félicitations !</strong> Vous avez terminé l’évaluation et répondu aux critères de réussite.", "text3": "Vous pouvez maintenant <strong><PERSON><PERSON><PERSON></strong> la formation en sélectionnant le « X » dans le coin supérieur droit de la fenêtre du navigateur ou, si vous le souhaitez, revoir votre évaluation.", "text4": "Vous pouvez également revoir le contenu de la formation à l’aide du bouton <strong><PERSON>u</strong> situé dans le coin supérieur gauche de la barre d’outils de la formation.", "btnReturn": "Revoir l’évaluation"}, "finalFailSection": {"title": "Résultats de l’évaluation", "text1": "Votre score est de", "text2": "Vous n’avez malheureusement pas obtenu le score minimum requis pour valider l’évaluation. Pensez à revoir le contenu avant de repasser l’évaluation.", "text3": "Vous pouvez revenir au contenu du cours en sélectionnant le bouton Revoir le contenu.", "text4": "Si vous êtes prêt(e) à réessayer l’évaluation, sélectionnez Recommencer l’évaluation.", "btnRetry": "Recommencer l’évaluation", "btnReturn": "Revoir le contenu"}, "congratsSection": {"text1": "Vous pouvez maintenant quitter la formation.", "text2": "Pour quitter la formation, sélectionnez le « X » dans le coin supérieur droit de la fenêtre du navigateur."}}, "assessment": {"course_level_test_out": {"this_course_contains_a_test_out_and_a_final_assessment": ["Cette formation contient une évaluation finale. <PERSON><PERSON>, pour valider les crédits de formation, obtenir un score supérieur ou égal à 80 % lors de l’évaluation finale.", "Cette formation inclut également une évaluation intermédiaire facultative. Si vous la réussissez, vous n’aurez pas besoin de consulter le contenu du cours ni de réaliser l’évaluation finale afin de recevoir les crédits y afférents.", "Si vous le souhaitez, vous pouvez ne pas effectuer l’évaluation intermédiaire et accéder directement au contenu."], "btnTakeTestOut": "Passer l’évaluation intermédiaire", "btnSkipTestOut": "Sauter l’évaluation intermédiaire", "title1": "Évaluation intermédiaire", "text1": "Bienvenue à l’évaluation intermédiaire.", "text2": "Pour valider cette formation, vous devez répondre aux {numberOfQuestions} questions suivantes et obtenir une note minimale de 80 %.", "text3": "Si vous ne parvenez pas à obtenir le score minimal requis, vous devrez revoir l’ensemble de la formation.", "list1": ["Vous disposez d’une seule tentative par question.", "Une fois que vous avez commencé l’évaluation, vous devez répondre à l’ensemble des questions pour obtenir un score. ", "Si vous quittez la formation avant de l’avoir terminée, votre progression ne sera pas enregistrée et vous devrez reprendre l’ensemble du contenu de la formation."], "text4": "Faites défiler la page vers le bas pour continuer vers l’évaluation intermédiaire.", "btnContinueTest": "Continuer l’évaluation intermédiaire", "btnContinuePage": "Revoir le contenu précédent", "btnContinueCourse": "Démarrer la formation", "btnReviewContent": "Revoir l’évaluation", "if_perfect_score": "SI SCORE PARFAIT", "congrats": {"title1": null, "text1": "Votre score est de", "text2": "<strong>Félicitations !</strong> Vous avez terminé l’évaluation et répondu aux critères de réussite. ", "text3": "Vous pouvez maintenant <strong><PERSON><PERSON><PERSON></strong> la formation en sélectionnant le « X » dans le coin supérieur droit de la fenêtre du navigateur ou, si vous le souhaitez, revoir votre évaluation.", "text4": "Vous pouvez également revoir le contenu de la formation à l’aide du bouton <strong><PERSON>u</strong> situé dans le coin supérieur gauche de la barre d’outils de la formation."}, "if_score_is_80_100": "SI LE SCORE EST COMPRIS ENTRE 80 ET 100 %", "passed": {"title1": null, "text1": "Votre score est de", "text2": "<strong>Félicitations !</strong> Vous avez terminé l’évaluation et répondu aux critères de réussite. ", "text3": "Vous pouvez maintenant <strong><PERSON><PERSON><PERSON></strong> la formation en sélectionnant le « X » dans le coin supérieur droit de la fenêtre du navigateur ou, si vous le souhaitez, revoir votre évaluation.", "text4": "Vous pouvez également revoir le contenu de la formation à l’aide du bouton <strong><PERSON>u</strong> situé dans le coin supérieur gauche de la barre d’outils de la formation."}, "failed": {"title1": null, "text1": "Votre score est de", "text2": "Vous n’avez malheureusement pas obtenu le score minimum requis pour valider l’évaluation intermédiaire. Il vous est désormais demandé de revoir l’ensemble de la formation et de réaliser l’évaluation finale à la fin de celle-ci."}}, "test_out": {"title1": "Évaluation intermédiaire", "text1": "L’évaluation intermédiaire optionnelle pour cette section comporte {numberOfQuestions} question(s). Si vous répondez correctement à toutes les questions, vous pourrez passer directement à la section suivante de ce cours.", "text2": "Vous disposez d’une seule tentative par question et devez terminer l’évaluation intermédiaire en une seule fois.", "text3": "Si vous ne parvenez pas à répondre correctement à toutes les questions ou quittez avant de finir les questions, il vous sera demandé de passer en revue le contenu de la section et de répondre correctement aux questions à la fin de la section.", "btnTakeTest": "Passer l’évaluation intermédiaire", "btnContinueSection": "Accéder au contenu de la section", "passed": {"title1": "Félicitations !", "text1": "Vous avez démontré une bonne compréhension du thème de cette section. Vous pouvez passer directement à la section suivante ou, si vous le souhaitez, revoir le contenu de cette section avant d’aller plus loin.", "btnContinueCourse": "<PERSON><PERSON><PERSON> le cours", "btnReviewSection": "Revoir le contenu de la section"}, "failed": {"text1": "Malheureusement, vous avez répondu incorrectement à une ou plusieurs questions et vous devez maintenant revoir le contenu de la section et répondre aux questions d’évaluation à la fin de la section.", "btnContinueSection": "Accéder au contenu de la section"}}, "knowledge_check": {"title1": "Évaluation", "text1": "V<PERSON> devez répondre correctement à la (aux) {numberOfQuestions} question(s) suivante(s) avant de poursuivre.", "text2": "Si vous ne répondez pas correctement à une ou plusieurs questions ou si vous quittez l’évaluation avant d’avoir répondu à toutes les questions, vous devrez recommencer l’évaluation.", "text3": "<strong>Faites défiler la page vers le bas pour continuer vers l’évaluation.</strong>", "passed": {"title1": "Félicitations ! Vous pouvez maintenant passer à la section suivante."}, "failed": {"title1": "Résultats de l’évaluation", "text1": "Malheureusement, vous avez mal répondu à une ou plusieurs questions. Pensez à revoir le contenu avant de repasser l’évaluation.", "btnReviewSection": "Revoir le contenu de la section", "btnRetake": "Recommencer l’évaluation"}}, "questions": []}, "page2": {"sectionTop": {"title1": "Qu’est-ce que l’architecture d’entreprise ?", "title2": "Définition de l’architecture d’entreprise"}, "section1": {"title1": "Notre culture, nos responsabilités, nos décisions", "text1": "L’architecture d’entreprise repose sur une méthodologie, communément appelée Méthodologie de l’architecture d’entreprise de Citi (Citi Enterprise Architecture Methodology, CEAM), qui est utilisée comme discipline pour développer des modèles de processus qui définissent, comprennent, organisent et normalisent de manière exhaustive les processus et les applications associées.", "text2": "Pour mieux comprendre sa valeur au sein de Citi, commençons par examiner comment les concepts utilisés dans la CEAM se rapportent aux activités quotidiennes, telles qu’effectuer un paiement.", "list1": ["Lorsque vous achetez des produits et des services en ligne, il y a de fortes chances que vous payiez par carte de crédit, carte de débit ou paiement mobile. ", "Les clients s’attendent à ce que leurs paiements soient traités correctement, en toute sécurité et en temps voulu, tout comme les prestataires de services de paiement tels que Citi.", "En tant que grande banque mondiale, le traitement des paiements est une fonctionnalité utilisée dans un grand nombre de nos processus au sein des divisions et des fonctions. Au fil des années, nous avons accumulé de nombreuses applications à cet effet. Cela rend complexes le maintien, l’amélioration et la mise en place de contrôles solides pour atteindre l’objectif de paiements corrects, opportuns et sûrs.", "La CEAM nous permet d’identifier les applications ayant des fonctionnalités communes, telles que l’initiation ou la validation d’un paiement. En consolidant les opportunités identifiées, nous simplifions notre environnement applicatif, qui peut être géré plus efficacement, avec moins d’applications à maintenir en conformité avec les exigences et les contrôles les plus récents. Cela favorise et soutient la simplification technologique."], "text3": "Le processus et la gouvernance de l’architecture d’entreprise nous permettent de travailler objectivement à la simplification dans l’ensemble de l’entreprise."}, "footer": {"text1": "Explorons plus en détail les taxonomies et les composants de l’architecture d’entreprise de Citi.", "btnText1": "<PERSON><PERSON><PERSON>"}}, "page3": {"sectionTop": {"title1": "Fondation de l’architecture d’entreprise"}, "section1": {"title": "Taxonomies des processus et fonctions", "text1": "Commençons par un scénario de paiement qui illustre comment l’utilisation d’une terminologie commune permet de simplifier les choses, et en quoi les taxonomies de l’architecture d’entreprise y contribuent.", "example_lorenzo": ["Voici <PERSON>, un client actuel de Citi qui dirige une petite entreprise et utilise Citi pour payer ses fournisseurs dans le monde entier. ", "Idéalement, les paiements effectués par l’intermédiaire de Citi devraient se dérouler de manière transparente pour Lorenzo. Malheureusement, dans les faits, le processus est compliqué.", "Pourquoi ? ", "En coulisses, la technologie de paiement de Citi est complexe. Pour répondre aux besoins des clients dans le monde entier et à la croissance mondiale des produits, de nombreuses applications de paiement ont été développées. Beaucoup remplissent des fonctions similaires. ", "Par conséquent, <PERSON> doit se connecter à trois plateformes de paiement différentes et naviguer entre elles pour payer ses fournisseurs. En fonction de l’emplacement des fournisseurs, différentes applications régionales sont nécessaires pour envoyer des fonds. Cette expérience fragmentée découle de systèmes qui se chevauchent et qui sont conçus pour remplir la même fonction, à savoir envoyer des fonds à quelqu’un quelque part dans le monde.", "La situation de Lorenzo montre que les cadres d’architecture d’entreprise peuvent aider Citi à identifier les possibilités de réduction du nombre d’applications. ", "En appliquant les cadres d’architecture d’entreprise, les clients nouveaux et existants bénéficieront d’une meilleure expérience client et d’une plus grande satisfaction à l’égard des produits et services offerts par Citi."], "text8": "Voyons maintenant comment les taxonomies, et plus particulièrement les taxonomies des processus et des fonctions, créent une terminologie commune qui favorise la communication et la compréhension entre les divisions et sert de base à l’architecture de l’entreprise.", "cta_accordion": "Sélectionnez chaque définition de taxonomie pour en savoir plus.", "accordion1": [{"title": "Qu’est-ce qu’une taxonomie ?", "text": ["Vous connaissez peut-être la hiérarchie des segments gérés, la hiérarchie des emplacements gérés ou les désignations des instruments juridiques de Citi. Tous ces éléments sont des exemples de taxonomies d’entreprise chez Citi.", "Une <strong>taxonomie</strong> est un moyen de nommer, d’organiser et de structurer des termes associés. Elle assure la cohérence de la communication, de la gouvernance et des rapports dans l’ensemble de l’entreprise. Par conséquent, elle améliore l’alignement et la gestion des divers intervenants. "]}, {"title": "Que sont les taxonomies d’entreprise ?", "text": ["Les taxonomies d’entreprise suivantes sont gérées par l’architecture d’entreprise :"], "list": ["Taxonomie des processus", "Taxonomie des fonctions", "Vocabulaire contrôlé des concepts des données"]}], "callout1": "L’<strong>architecture commerciale</strong> est chargée de la politique de gouvernance en matière d’architecture et de processus d’entreprise.", "text9": "Examinons chacune de ces taxonomies d’entreprise plus en détail."}, "section2": {"title1": "Taxonomie des processus", "text1": "Cette taxonomie décrit et organise les processus. Il s’agit d’un inventaire des processus qui s’appliquent à l’ensemble des activités de l’entreprise, de l’ouverture d’un compte à la gestion des talents ou des risques. ", "text2": "Veuillez prendre note de ce qui suit :", "list1": ["Un <strong>processus</strong> est un ensemble d’activités ou de fonctions exécutées dans un ordre spécifique pour atteindre un objectif commercial, tel qu’un service, un produit ou un profit pour Citi. ", "Ils reçoivent un ou plusieurs intrants, qui sont transformés par un ensemble d’activités ou de fonctions pour produire un ou plusieurs extrants. Ces extrants peuvent être transmis à un autre processus, à un client, à un employé et/ou à un tiers. "], "text3": "La <strong>Taxonomie des processus</strong> est définie indépendamment des secteurs d’activité ou des produits.", "text6": "Chaque processus dispose d’un responsable de processus qui est un cadre de Citi chargé de gérer un processus de bout en bout par le biais de la simplification et de la réduction des risques.", "text7": "Vous en apprendrez plus sur les responsables de processus plus loin dans cette formation.", "title2": "Taxonomie des fonctions", "text8": "Cette taxonomie comprend un inventaire des étapes, des tâches ou des « fonctions » exécutées au sein d’une application soutenant un processus.", "text9": "La <strong>taxonomie des fonctions</strong> est un inventaire des actions qui produit un résultat spécifique (nom de la fonction). Il est important de noter qu’une fonction peut être exécutée par de nombreuses applications et dans de nombreux processus. Chaque application possède un ensemble défini de fonctions, ou fonctions d’application.", "text10": "Par exemple, le processus de <strong>traitement des paiements</strong> se compose de plusieurs étapes/fonctions et applications. Les fonctions des applications comprennent, sans s’y limiter, les éléments suivants :", "list3": ["Saisie des demandes de paiement", "Validation des paiements", "Contrôle de conformité", "Entreposage des paiements", "Transfert de fonds"], "title3": "Vocabulaire contrôlé des concepts des données", "text11": "Le <strong>vocabulaire contrôlé des concepts des données</strong> est un ensemble de concepts de données organisés en une structure hiérarchique. ", "text12": "Un concept de données est une représentation d’une collection d’éléments de données représentant une personne, un lieu ou une chose qui participe aux activités de l’entreprise.  Il s’agit de termes dont l’entreprise reconnaît et comprend les noms et les définitions. Les concepts de données comprennent, par exemple, les clients, les comptes, les transactions, etc.", "text13": "Il est important de noter que les concepts de données sont créés ou utilisés par les fonctions. ", "text14": "Pour en savoir plus sur la taxonomie des processus, la taxonomie des fonctions et le vocabulaire contrôlé des concepts des données, consultez la <a href='https://citi.sharepoint.com/sites/Enterprise-Architecture/SitePages/ReferenceMaterials.aspx' target='_blank'>page de référence de l’architecture d’entreprise</a> et ajoutez-la à vos favoris. ", "text15": "N’oubliez pas que ces trois <strong>taxonomies fonctionnent de concert pour fournir un aperçu de la façon dont les processus, les fonctions et les données de Citi peuvent être systématiquement exploités et réutilisés</strong> pour des produits et services similaires dans l’entreprise."}, "footer": {"text1": "À présent, examinons plus en détail les responsables de processus et leurs responsabilités."}}, "page4": {"sectionTop": {"title1": "Qui sont les responsables des processus ?"}, "section1": {"text1": "Maintenant que nous disposons d’un ensemble défini de processus, qui assure la supervision et utilise les données associées aux processus pour aider à identifier les domaines à améliorer ?", "text2": "Il s’agit des responsables de processus. Un responsable de processus est chargé de gérer un processus à des fins de simplification et de réduction des risques de son processus.", "text3": "Explorons plus avant ce rôle en examinant ses responsabilités.", "text4": "Responsabilités du responsable de processus :", "text5": "Les responsables de processus, assignés à chaque processus de la taxonomie des processus, ont un ensemble de responsabilités pour régir les processus dont ils sont chargés. ", "text5AltText": "Voici un exemple des trois niveaux de taxonomie des processus professionnels. Le groupe de processus de niveau 1 est Mouvement de fonds. Le processus parent de niveau 2 est Paiements. Le processus de niveau 3 est Traitement des paiements. Les responsables de processus sont responsables du niveau 3.", "text7": "Les responsables des processus sont chargés des éléments suivants : ", "list1": ["<PERSON><PERSON>er et maintenir des modèles de processus CEAM, qui sont ensuite utilisés pour identifier les possibilités de simplification technologique ou de consolidation des applications ayant des fonctionnalités similaires, dans leur processus.", "<PERSON><PERSON><PERSON> et maintenir le profil MCA du processus mondial (GPMP) pour leur processus. Cela comprend :", ["Identifier les risques inhérents et les contrôles pour atténuer les risques de leurs processus. Il s’agit du GPMP de la nouvelle version de l’évaluation des contrôles des managers (Manager Control Assessment, MCA) ; ", "Examiner et approuver les demandes d’ajout ou de suppression de GPMP dans les unités d’évaluation globale."], "Valider d’autres liens identifiés avec le processus, par exemple les EUC, qui font partie des divers facteurs utilisés pour mesurer l’intégrité de leur processus, ou une évaluation du profil de risque du processus. "], "text8": "Pour en savoir plus sur la responsabilité des processus, consultez la page <a href='https://citi.sharepoint.com/sites/Enterprise-Architecture/SitePages/Process-Ownership.aspx?csf=1&web=1&e=1oNYIZ' target='_blank'>Ressources pour les responsables de processus d’architecture d’entreprise</a>."}, "section_poa": {"title": "Évaluation des responsabilités liées aux processus (POA) et score d’évaluation du risque du processus", "paragraphs": ["L’évaluation des responsabilités liées aux processus (POA) fournit aux responsables de processus une vue d’ensemble des liens approuvés, qui comprend les détails des risques et des contrôles tels qu’ils ont été adoptés, et inclura les questions iCAP, les EUC et les données du modèle de processus CEAM pour la simplification technique et l’automatisation des processus. ", "Les liens approuvés sont utilisés pour l’évaluation du profil du processus et le score de risque du processus qui en résulte. Le score aidera les responsables de processus à identifier les possibilités de simplification et de réduction des risques. "]}, "footer": {"text1": "Ensuite, afin de mieux comprendre le paysage de l’architecture d’entreprise de Citi, nous allons explorer certains des composants spécifiques des modèles de processus de la CEAM."}}, "page5": {"sectionTop": {"title1": "Éléments fondamentaux de la <span class='sr-only'> C E A M </span><span aria-hidden='true'>CEAM</span>"}, "section1": {"title1": "<PERSON><PERSON><PERSON><PERSON> processus ", "text1": "Les modèles de processus sont des représentations de la manière dont le processus fonctionne aujourd’hui avec les applications associées. ", "text2": "Nous utilisons les modèles de processus pour nous aider à comprendre les processus et leurs activités pour chaque processus de la taxonomie. ", "text3": "Ces modèles décrivent visuellement les étapes des processus à l’aide de la taxonomie des fonctions, nécessaires pour exécuter/terminer avec succès un processus et fournir le résultat requis. Ils relient les fonctions d’application et les concepts de données aux processus.", "callout1": "Les responsables des processus sont chargés de l’exactitude des modèles de processus pour leur processus.", "text7": "Examinons de plus près des modèles de processus en explorant les différents éléments fondamentaux pour un échantillon illustratif d’un processus : <strong>Traitement des paiements</strong>. Il est important de noter que les composants de cet échantillon représentent une vue simpliste. ", "cta_tabs": "Sélectionnez chacun des quatre onglets pour en savoir plus sur les composants nécessaires à l’exécution du processus : Traitement des paiements.", "tabs1": [{"img": "../assets/page5/tab1.svg", "title": "Aperçu complet", "title_full": "Aperçu complet (à des fins d’illustration uniquement)", "text": ["N’oubliez pas que les modèles de processus intègrent plusieurs composants nécessaires à l’exécution d’un processus. "], "alt": "Un graphique décrivant tous les éléments du processus : Traitement des paiements. La première rangée se compose de neuf fonctions de l’intrant à l’extrant. Ces fonctions sont les suivantes : 1. saisie des paiements, 2. vérification de la conformité, 3. entreposage des paiements, 4. demande de devis, 5. traitement de la facilité de crédit, 6. exécution d’une opération de change, 7. annulation d’une réparation d’instruction, 8. validation d’un paiement et 9. comptabilisation et GL. Ces fonctions sont connectées par des concepts de données. Les fonctions 1, 2, 3 et 4 sont connectées par le concept de données « paiement ». Les fonctions 4, 5 et 6 sont connectées par les concepts de données « paiement » et « taux de change ». Et les fonctions 6, 7, 8 et 9 sont connectées par le concept de données « paiement ». Au-dessus des fonctions, liées aux concepts de données par des lettres. La deuxième rangée se compose de quatre données de référence. Il s’agit notamment de : compte, coupure du système de compensation, BIC/ABA – spécifiques aux devises et pays, et compte GL/Nostro. La dernière ligne comprend trois niveaux d’automatisation. Cela inclut les éléments suivants : entièrement automatisés, manuels hybrides et entièrement manuels."}, {"img": "./assets/page5/tab2.svg", "title": "Fonctions d’application", "title_full": "Fonctions d’application (à des fins d’illustration uniquement)", "text": ["Le processus de paiement comporte neuf étapes/activités de base (par exemple, saisie des paiements, vérification de la conformité).", "Ces fonctions sont associées à des applications avec des fonctions, qui peuvent être répétées dans plusieurs processus."], "alt": "Dans ce graphique, seule la première ligne des fonctions d’applications est mise en avant. Il existe neuf fonctions d’applications de l’intrant à l’extrant pour le processus : Traitement des paiements. Ces fonctions sont les suivantes : 1. saisie des paiements, 2. vérification de la conformité, 3. entreposage des paiements, 4. demande de devis, 5. traitement de la facilité de crédit, 6. exécution d’une opération de change, 7. annulation d’une réparation d’instruction, 8. validation d’un paiement et 9. comptabilisation et GL."}, {"img": "./assets/page5/tab3.svg", "title": "Concepts de données", "title_full": "Concepts de données (à des fins d’illustration uniquement)", "text": ["Il s’agit notamment des données qui sont consommées ou produites dans le cadre du processus de paiement (p. ex., paiement, taux de change).", "Elles sont basées sur le vocabulaire contrôlé des concepts des données."], "alt": "Dans ce graphique, la ligne deux des données de référence est mise en avant, ainsi que le « paiement » et le « taux de change », qui relient chacune des neuf fonctions. Ces concepts de données sont liés aux contrôles A et B sur les fonctions 1 et 9."}, {"img": "./assets/page5/tab4.svg", "title": "Saisies manuelles", "title_full": "Saisies manuelles (à des fins d’illustration uniquement)", "text": ["Les saisies manuelles ou les étapes de traitement manuel sont identifiées par le responsable du processus pour chaque fonction de son processus.", "Lorsque des données manuelles sont identifiées, elles sont classées en fonction du type de données manuelles ou de l’étape du processus, par exemple, nécessaire en raison d’un jugement ou automatisable."], "alt": "Dans ce graphique, la dernière ligne des saisies manuelles/niveau d’automatisation et l’indicateur d’automatisation correspondant à chaque fonction sont mis en évidence. Chaque niveau d’automatisation est répertorié : entièrement automatisé, manuel hybride et entièrement manuel. Neuf niveaux d’automatisation sont représentés pour chaque fonction, de l’intrant à l’extrant, pour le processus : Traitement des paiements. "}]}, "section2": {"title1": "Fonctions, désignations et cartographie des applications", "text2": "Les fonctions d’application qui en résultent, décrites dans la section Taxonomie des fonctions, sont mises en correspondance avec les modèles de processus.", "text5": "Qu’est-ce qu’une désignation de fonction d’application ?", "text6": "Une désignation de fonction d’application indique l’intention stratégique de l’utilisation d’une fonction d’application dans le processus.  ", "text7": "Les différentes valeurs qui peuvent être assignées à chaque fonction d’application sont :", "text8": "Stratégique", "list2": ["Des applications dans lesquelles Citi prévoit d’investir, et la fonction spécifique est stratégique, ce qui indique que d’autres applications dotées de cette fonctionnalité peuvent être consolidées dans l’application désignée comme « stratégique ». "], "text9": "Maintenir ", "list3": ["Cette application répond suffisamment aux exigences de la fonction, mais n’est pas stratégique (par exemple, la migration depuis d’autres applications ne devrait pas se faire vers cette application). "], "text10": "Obsolète", "list4": ["Cette application doit être retirée et/ou la fonction d’application n’est pas adaptée à la fonction donnée et doit être consolidée, dans un délai raisonnable, de préférence vers une application stratégique pour la fonction. "], "paragraphs_11": ["Les désignations des fonctions d’application sont les données nécessaires pour évaluer nos progrès en matière de rationalisation ou de réduction des applications, ainsi que de réutilisation d’une application lorsque cela s’avère possible, à des fins de simplification technologique pour l’ensemble de nos processus. Les données collectées nous permettent de contrôler notre conformité au fur et à mesure que nous exécutons des projets technologiques.", "Cela nous permet en fin de compte de : ", ["Connaître les cas où nous avons différentes applications prenant en charge les mêmes fonctions ; ", "Comprendre si l’application appropriée est utilisée pour une fonction selon le processus ;", "Soutenir la rationalisation et la simplification des applications aux fonctions en attribuant à chaque fonction d’application une valeur « Désignation de fonction d’application »."]], "text12": "Les responsables de processus sont tenus de s’assurer de l’exactitude et de l’exhaustivité de leurs modèles de processus, ainsi que des désignations des fonctions d’application pour leur processus."}, "section3": {"title": "Simplification technologique ", "subtitle": "Qu’est-ce que la simplification technologique et pourquoi ce concept est-il fondamental ?", "paragraphs": ["La <strong>simplification technologique</strong> est un cadre destiné à améliorer l’infrastructure technologique de Citi par l’identification des applications qui remplissent des fonctions communes en vue de leur consolidation. Elle nous permet d’être plus efficaces dans la gestion de notre inventaire d’applications avec les meilleures fonctionnalités, ce qui réduit les risques et améliore l’expérience de nos clients. ", "La cartographie des applications permet d’identifier et de mesurer la simplification technologique des processus grâce à des modèles de processus."]}, "section4": {"paragraphs": ["En réduisant les complexités inhérentes à Citi, gr<PERSON><PERSON> à la simplification technologique, les améliorations suivantes peuvent être réalisées :", ["Rapidité de mise sur le marché", "Agi<PERSON><PERSON>", "Compétitivité", "<PERSON><PERSON><PERSON><PERSON><PERSON> "], "Par exemple, la fonctionnalité de <strong>validation des paiements</strong> a permis d’identifier 156 applications utilisées par Citi dans le cadre de nombreux processus différents."], "callout": "Par exemple, la fonctionnalité de <strong>validation des paiements</strong> a permis d’identifier 156 applications utilisées par Citi dans le cadre de nombreux processus différents."}, "footer": {"text1": "Maintenant que nous avons abordé les processus, les modèles et les responsables de processus, passons à un scénario pour mieux comprendre comment l’architecture d’entreprise nous aide à simplifier nos processus."}}, "page6": {"sectionTop": {"title1": "La simplification technologique en action"}, "section1": {"title1": "En dehors de Citi", "text1": "Voici Greta.", "text2": "Greta adore faire des achats en ligne et compte sur l’application de paiement mobile pour acheter des articles. Elle paie ensuite son fournisseur de fonds par le biais du paiement électronique des factures de Citi. ", "text3": "Greta apprécie la fluidité du paiement en un clic.", "text4": "<strong>Découvrons-en davantage sur l’expérience de Greta.</strong>", "text6": "Outre les achats en ligne, Greta investit régulièrement sur le marché. Lorsqu’elle émet des ordres, elle utilise un processus de transaction qui s’appuie sur le traitement des paiements pour payer les actions dans lesquelles elle a choisi d’investir. Son expérience de l’utilisation de ces différents processus chez Citi doit être transparente, précise et sécurisée.", "text7": "La fourniture de ces produits et services est gérée au moyen d’une série de processus qui garantissent une expérience cohérente de bout en bout et procurent satisfaction aux clients.", "text8": "Cela est possible grâce aux éléments suivants : ", "list1": ["Décomposer ces processus pour examiner leurs activités sous-jacentes et les fonctions d’application qui les soutiennent. ", "Identifier les applications nécessaires pour soutenir chaque fonction du processus. "], "text9": "Par exemple, si G<PERSON> passe une commande de courses pour la semaine ou décide de renouveler son compte de streaming en ligne, elle attend le même niveau de sécurité dans l’exécution du paiement, quelle que soit la plateforme. ", "text10": "Quelle que soit la commande de Greta (qu’il s’agisse de produits d’épicerie ou d’un renouvellement de compte de streaming en ligne) le site fera suivre à Greta les mêmes étapes de paiement, de traitement du paiement, de détection de tout signe de fraude et de gestion de toute taxe due. ", "text12": "Si l’on considère cette question sous l’angle de l’architecture d’entreprise, il apparaît clairement que certaines fonctions sont réutilisées sur différentes plateformes et doivent être normalisées dans les différents services du site. ", "text13": "<PERSON><PERSON> profite à la fois à Greta et au site (entreprise), en mettant l’accent sur la rationalisation de l’expérience d’achat et la réalisation d’économies, tout en réduisant les risques.", "title2": "Comment cela s’applique-t-il à Citi ? ", "text14": "Être capable d’identifier les applications avec des fonctions en doublon contribuera non seulement à améliorer l’efficacité de la fourniture de nos produits et services, mais nous permettra également de réutiliser les mêmes applications pour plusieurs processus, le cas échéant.", "text15": "En gardant l’expérience de Greta en tête, retournons à notre client Lorenzo.", "text17": "<strong>Le parcours de Lorenzo</strong>", "text18": "Lors de notre première rencontre avec <PERSON>, rappelez-vous qu’il n’a pas eu une expérience très positive pour souscrire un produit Citi. Lorenzo a été frustré par le nombre de fois où il a été contacté pour la même information et a exprimé son mécontentement à l’équipe. ", "text19": "<PERSON><PERSON><PERSON><PERSON>, comme Lorenzo est satisfait des produits au-delà de l’expérience initiale du service à la clientèle, il envisage d’acheter plus de produits à la banque.", "text20": "<PERSON><PERSON><PERSON>, <PERSON> cherche aussi à obtenir une nouvelle carte de crédit et un prêt immobilier. <PERSON>ors que Lorenzo effectue les demandes pour ces produits, voyons comment l’équipe responsable des processus a utilisé les artefacts de la <span class='sr-only'> C E A M </span><span aria-hidden='true'>CEAM</span> et l’intelligence du processus pour fournir une approche plus rationalisée, intégrer l’infrastructure des applications, aligner les données entre les équipes et, finalement, améliorer l’expérience client.", "lorenzos_journey_paragraphs": ["Lors de notre première rencontre avec Lorenzo, il utilisait Citi pour payer ses fournisseurs, mais devait utiliser plusieurs applications de paiement pour traiter les paiements. ", "La simplification technologique permet de réduire la complexité de l’infrastructure des applications grâce à l’identification et à la consolidation des applications remplissant des fonctions similaires. ", "L’engagement de Citi à fournir une simplification technologique pour l’espace de paiement s’est traduit par le lancement de « Citi Payments Express », une application stratégique pour soutenir les flux de paiement instantanés. ", "Souvenez-vous que <PERSON> a dû naviguer entre trois plateformes de paiement différentes pour payer ses fournisseurs. Citi a réussi à simplifier les complexités et l’expérience client en matière de paiement instantané. « Citi Payments Express » est devenu notre solution unique en intégrant les fonctions de paiement locales aux États-Unis et au Royaume-Uni dans une application globale de paiement instantané.", "Le lancement de « Citi Payments Express » a facilité la vie de Lorenzo, puisqu’il peut désormais utiliser une plateforme unique et rationalisée pour payer ses fournisseurs. Ses besoins en matière de paiement numérique sont satisfaits à l’aide d’une expérience simplifiée. ", "Pour Citi, le remplacement d’applications superflues par une application stratégique capable de remplir la même fonction nous permet de progresser vers un environnement opérationnel plus efficace et moins complexe, avec des risques et des coûts opérationnels réduits, étant donné que nous avons moins d’applications à prendre en charge dans l’ensemble de l’organisation.", "Voyons comment l’équipe responsable des processus a utilisé les artefacts de la CEAM et les informations sur les processus pour établir une infrastructure intégrée des applications, aligner les données entre les équipes et, finalement, améliorer l’expérience client. "], "text21": "La simplification technologique dans les faits", "text22": "En utilisant les taxonomies d’entreprise, l’équipe a identifié un ensemble distinct de fonctions liées aux paiements et les applications de soutien utilisées dans les processus par l’intermédiaire de la CEAM. ", "text23": "Les responsables des processus utilisant la fonctionnalité de paiement des applications désignées comme « obsolètes » ont entrepris des efforts pour améliorer leurs processus afin d’éliminer les fonctions obsolètes, pour les remplacer par « Citi Payments Express ». Cela a permis de réduire le nombre d’applications utilisées pour les fonctions de paiement, d’améliorer l’expérience des clients et de démontrer l’engagement de Citi à apporter des modifications positives.", "text27": "<PERSON> n’a plus besoin d’utiliser plusieurs applications de paiement pour payer ses fournisseurs américains et britanniques. Il peut désormais utiliser une plateforme unique pour traiter et contrôler ses paiements numériques. Du fait de la complexité réduite, <PERSON> peut commencer à explorer les possibilités de réduire les coûts en recherchant des fournisseurs dans d’autres régions du monde ! ", "text28": "Grâce à la simplification technologique, la banque a fourni un processus plus cohérent et efficace pour l’équipe interne et une expérience client beaucoup plus fluide pour Lorenzo."}, "footer": {"text1": "Voyons maintenant qui utilise l’architecture d’entreprise et la gouvernance des processus chez Citi. "}}, "page7": {"sectionTop": {"title1": "Qui est responsable de l’application de l’architecture d’entreprise et de la gouvernance des processus ?"}, "section1": {"text1": "N’oubliez pas que l’objectif de l’architecture d’entreprise est d’aligner les objectifs commerciaux de Citi avec notre stratégie d’applications commerciales. Elle aide à identifier les opportunités au sein de nos processus pour être plus efficaces et efficients, au bénéfice de nos employés, de nos clients et de Citi dans son ensemble. ", "text2": "Simplifier et gérer nos processus ne se fera pas par des projets cloisonnés ; chacun de nous doit : ", "list1": ["Changer la façon dont nous nous acquittons de nos responsabilités quotidiennes ; ", "Utiliser une terminologie cohérente ; ", "Relier les informations contenues dans notre quotidien aux processus pour contribuer à la mise en place d’une intelligence des processus. "], "text3": "L’architecture d’entreprise et la gouvernance des processus s’appliquent à l’ensemble du personnel de Citi au sens large, lorsque les collaborateurs doivent associer des artefacts aux processus dans tous les systèmes d’archivage. Selon leurs connaissances, ils sont responsables de l’exactitude du lien lors de la sélection d’un processus à associer.", "text3b": "Il peut s’agir, par exemple, d’associer des processus à l’informatique utilisateur (End User Computing, EUC) ou aux demandes d’investissements.", "text4": "Explorons quelques-unes des façons dont nous utilisons l’architecture d’entreprise dans divers rôles chez Citi. ", "title1": "Comment les différents rôles utilisent-ils l’architecture d’entreprise et la gouvernance des processus ? ", "text5": "<strong>Sélectionnez chaque rôle pour découvrir ses responsabilités dans l’application de l’architecture d’entreprise.</strong>", "cards1": [{"id": "card1", "title": "Responsable de l’informatique utilisateur <span class='sr-only'> (E U C) </span><span aria-hidden='true'>(EUC)</span>", "text": ["Lors de l’enregistrement de nouvelles EUC, les responsables d’EUC seront invités à sélectionner le ou les processus que leur EUC prend en charge. <br><br>Les responsables des processus sont tenus d’examiner et d’approuver le lien en tant qu’élément du score de risque de leur processus. Les EUC approuvées seront intégrées dans la plateforme d’évaluation des responsabilités liées aux processus (POA)."]}, {"id": "card2", "title": "Promoteurs d’investissement", "text": ["Les promoteurs d’investissement qui créent une demande d’investissement seront invités à sélectionner le ou les processus sur lesquels leur demande d’investissement a des répercussions. <br><br>Dans le cadre du processus de création et d’approbation des investissements, les promoteurs d’investissements peuvent être amenés à fournir des données d’entrée du modèle de processus utilisé pour mener les changements technologiques et soutenir l’identification des opportunités de simplification technologique."]}, {"id": "card3", "title": "Utilisateurs de l’évaluation des contrôles des managers (MCA)", "text": ["Les responsables des processus créent et maintiennent des profils MCA de processus mondiaux (GPMP), qui relient les processus aux risques et aux contrôles de référence. <br><br>Les responsables des risques MCA identifient et adoptent le ou les processus et les GPMP respectifs pour gérer les risques liés aux objectifs de gestion de leur unité d’évaluation. <br><br>Les contrôles qui en résultent, tels qu’ils ont été adoptés, seront visibles pour le responsable du processus par le biais de l’évaluation des responsabilités liées aux processus (POA)."]}], "text6": "Même si votre fonction ne fait pas appel à l’architecture d’entreprise dans vos pratiques quotidiennes, il est probable que vous y soyez confronté à différents moments, car la taxonomie des processus est utilisée dans des programmes de base tels que la MCA et l’EUC. <br><br><strong>Vous êtes donc responsable de l’application précise de l’architecture d’entreprise au fur et à mesure que nous simplifions Citi ensemble.</strong>", "text7": "Pour en savoir plus sur la façon dont les différents domaines de Citi tirent parti de l’architecture d’entreprise, vous pouvez consulter la page <a href='https://citi.sharepoint.com/sites/Enterprise-Architecture/SitePages/ReferenceMaterials.aspx' target='_blank'>Documents de référence sur l’architecture d’entreprise</a>."}, "footer": {"text1": "Passons rapidement en revue ce que nous avons vu dans ce cours avant l’évaluation finale."}}, "page8": {"sectionTop": {"title1": "Synthèse de la formation"}, "section1": {"text1": "Nous avons abordé les points suivants dans cette formation :", "cards1": [{"text": "Citi a mis en place une architecture d’entreprise et une gouvernance des processus afin de fournir une approche centrée sur les processus pour la simplification et la réduction des risques liés, ce qui se traduit en fin de compte par une meilleure expérience client."}, {"text": "L’architecture d’entreprise régit trois taxonomies : la taxonomie des processus, la taxonomie des fonctions et le vocabulaire contrôlé des concepts de données. "}, {"paragraphs": ["L’architecture d’entreprise et la gouvernance des processus nous aident à mieux comprendre où nous en sommes aujourd’hui et comment atteindre nos objectifs. ", "Pour ce faire, elle identifie à la fois les lacunes et les opportunités potentielles pour aider les applications commerciales de Citi à s’aligner sur les objectifs commerciaux."]}, {"text": "La simplification technologique entraîne la consolidation des applications dont les fonctions font double emploi. La gouvernance des processus conduit à l’amélioration globale des processus."}, {"paragraphs": ["Des rôles spécifiques appliqueront l’architecture d’entreprise dans le cadre de leurs responsabilités. ", "Cependant, le succès de l’architecture d’entreprise dans la simplification des processus de Citi repose sur la responsabilité de chaque employé de Citi d’appliquer correctement les processus et d’adhérer à la gouvernance des processus."]}], "text2": "Consultez la <a href='https://citi.hostedconnectedrisk.com/CitiCPD/react?page=O&CID=sid552268563&DeletedOnly=0&HTMFile=instancePolicyUser_View.htm&InstanceNo=2001487&NodeNo=182&inlineEdit=0&originalRequestNodeNo=182&pageType=42&resetTreeView=0&rightClickContextMenu=0&runOnce=1&scrollableViewer=1&treeViewNode0=253&treeViewNode0OverviewPage=instancePolicyVersionAuthor_Title.htm&treeViewNode1=281&treeViewNode10=371&treeViewNode10OverviewPage=instancePolicySectionLevel10VersionAuthorView.htm&treeViewNode1OverviewPage=instancePolicySectionLevel1VersionAuthor_View.htm&treeViewNode2=282&treeViewNode2OverviewPage=instancePolicySectionLevel2VersionAuthor_View.htm&treeViewNode3=283&treeViewNode3OverviewPage=instancePolicySectionLevel3VersionAuthor_View.htm&treeViewNode4=285&treeViewNode4OverviewPage=instancePolicySectionLevel4VersionAuthor_View.htm&treeViewNode5=284&treeViewNode5OverviewPage=instancePolicySectionLevel5VersionAuthor_View.htm&treeViewNode6=372&treeViewNode6OverviewPage=instancePolicySectionLevel6VersionAuthorView.htm&treeViewNode7=373&treeViewNode7OverviewPage=instancePolicySectionLevel7VersionAuthorView.htm&treeViewNode8=374&treeViewNode8OverviewPage=instancePolicySectionLevel8VersionAuthorView.htm&treeViewNode9=375&treeViewNode9OverviewPage=instancePolicySectionLevel9VersionAuthorView.htm&useCustomFieldForAddChild=0&useOriginalRequestNodeAsCurrent=1&usingpost=1' target='_blank'>Politique d’application de l’architecture d’entreprise et de la gouvernance des processus (EAPGP)</a> pour en savoir plus sur l’architecture d’entreprise et la gouvernance des processus auxquelles doivent adhérer tous les employés, les sous-traitants, les consultants de Citi, ainsi que les parties tenues de respecter les politiques de Citi.", "text3": "N’oubliez pas que l’application de l’architecture d’entreprise et de la gouvernance des processus se traduit par une simplification et une efficacité qui profitent à tous au sein de Citi, ainsi qu’à nos clients."}, "footer": {"text1": "Il est maintenant temps d’évaluer vos connaissances."}}, "page11": {"topSection": {"title": "Évaluation finale", "text1": "¡Felicitaciones! Ya cumplió con los requisitos para la finalización del curso. Ahora puede ver el contenido de nuevo o salir del curso.", "text2": "Pour quitter le cours, sélection<PERSON>z le « X » dans le coin supérieur droit de la fenêtre du navigateur."}}}