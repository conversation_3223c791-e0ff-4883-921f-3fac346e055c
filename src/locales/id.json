{"manifest": {"title": "Pengantar Tata Kelola Proses dan Metodologi Arsitektur Perusahaan Citi (CEAM, Citi Enterprise Architecture Methodology)", "content": [{"title": "Selamat Datang"}, {"title": "Apa itu Metodologi Arsitektur Perusahaan?"}, {"title": "Dasar Arsitektur <PERSON>"}, {"title": "Siapa Pemilik Proses?"}, {"title": "Komponen <span class='sr-only'> C E A M </span><span aria-hidden='true'>CEAM</span>"}, {"title": "<PERSON><PERSON><PERSON>"}, {"title": "Siapa yang Bertanggung Jawab untuk Menerapkan Tata Kelola Proses dan Arsitektur Perusahaan?"}, {"title": "<PERSON><PERSON><PERSON>"}, {"title": "<PERSON><PERSON><PERSON>"}]}, "resources": {"title1": "Sumber Informasi", "title2": "<PERSON><PERSON>", "title3": "Tautan", "list1": ["CEAM - Citi Enterprise Architecture Methodology (Metodologi Arsitektur Perusahaan Citi)", "DSMT - Data Standards Management Tool (Alat Manajemen Standar Data)", "GPMP - Global Process MCA Profile (Profil MCA Proses Global)", "EAPGP - Enterprise Architecture and Process Governance Policy (Kebijakan Tata Kelola Proses dan Arsitektur Perusahaan)", "EUC - End User Computing (Komputasi Pengguna Akhir)", "MCA - Manager Control Assessment (Penila<PERSON>)", "POA - Process Ownership Assessment Platform (Platform Penilaian Kepemilikan Proses)", "PTS - Project Tracking System (Sistem Pelacakan Proyek)", "I@C – Investments@Citi"], "list2": ["<a href='https://citi.sharepoint.com/sites/Enterprise-Architecture/SitePages/Process-Ownership.aspx?csf=1&web=1&e=1oNYIZ' target='_blank'>Sumber Informasi Pemilik Proses Arsitektur Bisnis</a>", "<a href='https://citi.sharepoint.com/sites/Enterprise-Architecture/SitePages/ReferenceMaterials.aspx' target='_blank'>Materi Referensi Arsitektur Bisnis</a>", "<a href='https://citi.hostedconnectedrisk.com/CitiCPD/react?page=O&CID=sid552268563&DeletedOnly=0&HTMFile=instancePolicyUser_View.htm&InstanceNo=2001487&NodeNo=182&inlineEdit=0&originalRequestNodeNo=182&pageType=42&resetTreeView=0&rightClickContextMenu=0&runOnce=1&scrollableViewer=1&treeViewNode0=253&treeViewNode0OverviewPage=instancePolicyVersionAuthor_Title.htm&treeViewNode1=281&treeViewNode10=371&treeViewNode10OverviewPage=instancePolicySectionLevel10VersionAuthorView.htm&treeViewNode1OverviewPage=instancePolicySectionLevel1VersionAuthor_View.htm&treeViewNode2=282&treeViewNode2OverviewPage=instancePolicySectionLevel2VersionAuthor_View.htm&treeViewNode3=283&treeViewNode3OverviewPage=instancePolicySectionLevel3VersionAuthor_View.htm&treeViewNode4=285&treeViewNode4OverviewPage=instancePolicySectionLevel4VersionAuthor_View.htm&treeViewNode5=284&treeViewNode5OverviewPage=instancePolicySectionLevel5VersionAuthor_View.htm&treeViewNode6=372&treeViewNode6OverviewPage=instancePolicySectionLevel6VersionAuthorView.htm&treeViewNode7=373&treeViewNode7OverviewPage=instancePolicySectionLevel7VersionAuthorView.htm&treeViewNode8=374&treeViewNode8OverviewPage=instancePolicySectionLevel8VersionAuthorView.htm&treeViewNode9=375&treeViewNode9OverviewPage=instancePolicySectionLevel9VersionAuthorView.htm&useCustomFieldForAddChild=0&useOriginalRequestNodeAsCurrent=1&usingpost=1' target='_blank'>Enterprise Architecture and Process Governance Policy (EAPGP)</a>", "<a href='https://www.citigroup.net/clicks/?version=3&utm_campaign=1625863048700868QguJhvH%2fCbsXACX4DonJcmabp9YzQXIRFAJJBbS86kyYTSzXYlBAUFpRk0RutsWxa8CMCIUYkcHJLo6cv6d3YBWMrc7TkgakWaIfQxu2lMf%2bid2Rdit0w40I%2fNZOeMBvDlfOKPiiQtXA5yGrK6rSWg%3d%3d&ctid=1625863048700883DEHnfsoz07WcImMs4XRY3vYrwPekdV1fxVqQQCX3pzC5YN%2bERTNzWzsOqx00Lz6snxX0eMKW%2bpdhG8bC%2fnGLrDr%2fxCAAA1wMNmGYs7aAwh7DHMIPOFne2kz71lbbn6WiTKelhg0XoVQN5%2b5MxQTyVg%3d%3d' target='_blank'>Process Governance Standard (PGrS)</a>"]}, "errors": {"error": "<PERSON><PERSON><PERSON>", "lostcommunication": "Tutup modul ini dan jalankan kembali. Terjadi kesalahan LMS. Modul ini akan di<PERSON>, tetapi tidak akan melacak bagian yang sudah Anda se<PERSON>aikan.", "close": "<PERSON><PERSON><PERSON>"}, "errorLMS": {"text1": "<strong>Penting!‎</strong> Kursus ini tidak dapat terhubung ke Sistem Manajemen Pembelajaran (LMS). Kemajuan Anda sebelumnya telah disimpan. <PERSON><PERSON>, k<PERSON><PERSON><PERSON> Anda saat ini tidak lagi disimpan.", "text2": "Hal ini biasanya terjadi apabila ada gangguan pada koneksi jaringan <PERSON>. <PERSON><PERSON>, batas waktu koneksi dengan LMS telah habis.", "text3": "<strong>Untuk memperbaiki masalah ini:</strong> <PERSON><PERSON><PERSON> jendela kursus ini, lalu tutup semua jendela browser lainnya.  Buka jendela browser baru dan akses LMS. Cari pelatihan ini dan pilih tombol mulai.  Ke<PERSON><PERSON>an Anda dalam kursus akan dikembalikan ke titik terakhir saat ada koneksi yang direkam."}, "ui": {"sr": "Perhatian bagi pengguna pembaca layar: kursus ini sudah dioptimalkan untuk bisa diakses dan sepenuhnya mendukung pembaca layar.", "langselection": "<PERSON><PERSON>han Bahasa", "chooselang": "<PERSON><PERSON><PERSON> bahasa <PERSON>:", "menu": "<PERSON><PERSON>", "menuItems": "<PERSON><PERSON>", "exit": "<PERSON><PERSON><PERSON>", "exitCourse": "<PERSON><PERSON><PERSON>", "logo": "Logo Citi", "notattempted": "Tidak <PERSON>", "incomplete": "<PERSON><PERSON>", "complete": "Se<PERSON><PERSON>", "locked": "Terkun<PERSON>", "itemComplete": "<PERSON><PERSON>", "itemLocked": "<PERSON><PERSON>", "itemUnLocked": "<PERSON>em tidak terkunci", "pageLoaded": "Halaman Dimuat", "pagesCompleted": " halaman se<PERSON>ai", "resources": "Sumber Informasi", "scroll": "<PERSON><PERSON><PERSON> turun untuk melanjutkan.", "scrollc": "<PERSON><PERSON><PERSON> ke bawah untuk melanjutkan kursus.", "previous": "Sebelumnya", "next": "Selanjutnya", "continue": "Lanjutkan", "close": "<PERSON><PERSON><PERSON>", "gotit": "<PERSON><PERSON><PERSON>!", "submit": "<PERSON><PERSON>", "learn": "<PERSON><PERSON><PERSON><PERSON>", "return": "Ke<PERSON>li ke Atas", "citigroup": "Citigroup Inc", "selectToBegin": "<PERSON><PERSON><PERSON> un<PERSON>", "settings": "<PERSON><PERSON><PERSON><PERSON>", "animations": "<PERSON><PERSON><PERSON>", "on": "Animasi aktif", "off": "<PERSON><PERSON><PERSON>", "audio": "Audio", "reportconcern": "Laporkan Masalah", "pressescape": "Tekan tombol ESCAPE untuk menutup", "of": " dari ", "itemscompleted": " item selesai", "questionTitleTemplate": "<PERSON><PERSON><PERSON> {a} dari {b}", "correct": "<PERSON><PERSON>.", "incorrect": "Kurang tepat."}, "videoplayer": {"Play": "Putar", "Pause": "<PERSON><PERSON>", "Current Time": "Waktu <PERSON> In<PERSON>", "Duration": "<PERSON><PERSON><PERSON>", "Remaining Time": "<PERSON><PERSON>", "Loaded": "Dimuat", "Progress": "<PERSON><PERSON><PERSON><PERSON>", "Progress Bar": "<PERSON><PERSON><PERSON>", "progress bar timing: currentTime={1} duration={2}": "{1} dari {2}", "Fullscreen": "<PERSON><PERSON>", "Non-Fullscreen": "<PERSON><PERSON><PERSON>", "Mute": "Senyapkan", "Unmute": "Bunyikan", "Volume Level": "Tingkat Volume", "You aborted the media playback": "<PERSON><PERSON> pemutaran video.", "A network error caused the media download to fail part-way.": "<PERSON><PERSON><PERSON> jaringan menye<PERSON> unduhan video gagal di tengah jalan.", "The media could not be loaded, either because the server or network failed or because the format is not supported.": "Video tidak bisa dimuat karena kegagalan server atau jaringan atau karena formatnya tidak didukung.", "The media playback was aborted due to a corruption problem or because the media used features your browser did not support.": "Pemutaran video dihentikan karena video rusak atau fiturnya tidak didukung peramban <PERSON>.", "No compatible source was found for this media.": "Tidak ditemukan sumber yang kompatibel untuk video ini."}, "bookmark": {"title": "Lanjutkan kursus dari bagian yang ditandai", "message": "<PERSON><PERSON><PERSON><PERSON> Anda ingin melanjutkan atau memulai ulang kursus?", "cancel": "<PERSON><PERSON>", "ok": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "closemodule": {"title": "<PERSON><PERSON><PERSON> dari k<PERSON>", "message": "<PERSON><PERSON><PERSON><PERSON> Anda yakin ingin menutup kursus ini?", "cancel": "<PERSON><PERSON>", "ok": "OK"}, "page1": {"sectionTop": {"title1": "Pengantar Tata Kelola Proses dan Arsitektur Perusahaan Citi", "title2": "Pelatihan Berbasis Web 20 Menit "}, "section1": {"title1": "Cara Beralih ke Bagian Lain dalam Kursus Ini", "instr1": "Baca seluruh kartu untuk pengalaman navigasi kursus terbaik.", "list1": [{"text1": "Pilih ikon menu untuk membuka bagian lain dari kursus ini. Setiap bagian akan tersedia setelah Anda menyelesaikan semua persyaratan di bagian sebelumnya.", "text2": null, "icon1": "bi-list"}, {"text1": "Pilih ikon sumber daya di sudut kanan atas untuk menemukan sumber informasi tambahan terkait kursus ini. Kursus ini menyediakan pranala ke prosedur dan <PERSON> terkait. ", "text2": null, "icon1": "bi-info-circle"}, {"text1": "Kursus ini menyertakan <strong><span class='text-secondary' style='text-decoration: underline'>pranala</span></strong> ke berbagai sumber informasi. Jika Anda mengakses kursus dari perangkat pribadi secara langsung melalui Internet (di luar jaringan Citi), beberapa tautan mungkin tidak berfungsi jika tertaut ke konten dalam jaringan Citi. Ini tidak akan memengaruhi kemampuan Anda untuk menyelesaikan kursus.", "text2": null, "icon1": "bi-link-45deg"}, {"text1": "Jika Anda kesulitan menampilkan konten animasi, <PERSON><PERSON> dapat nonaktifkan fitur animasi kursus ini sekarang. Animasi dirancang untuk meningkatkan pengalaman kursus. Penonaktifkan animasi tidak akan membatasi akses pada konten apa pun. ", "text2": null, "icon1": "bi-gear"}, {"text1": "Jika suatu saat Anda ingin keluar dari kursus sebelum menyelesai<PERSON>, pilih ikon ini di bagian atas halaman untuk menyimpan kemajuan Anda dan keluar dari kursus.", "text2": null, "icon1": "bi-x-circle"}]}, "sectionWhyWhyWhyWhy": {"title": "<PERSON><PERSON><PERSON><PERSON> Proses dan Arsitektur Perusahaan Citi", "list": [{"title": "Mengapa Perlu Memahami <PERSON>?‎", "paragraphs": ["Pertumbuhan Citi di berbagai bisnis dan pasar telah menciptakan ekosistem platform digital dan aplikasi yang kompleks. Ekspansi ini memiliki banyak manfaat, namun juga menimbulkan duplikasi, tumpang tindih fungsi, serta kurangnya visibilitas terhadap bagaimana Proses kita berjalan dari awal hingga akhir. Tata Kelola Proses dan Arsitektur Perusahaan Citi menyediakan pendekatan terpadu untuk menilai kesehatan proses dan risiko terkait, sehingga kita dapat mengambil keputusan yang tepat di tingkat perusahaan."]}, {"title": "Mengapa Perlu Memahami Hal <PERSON>?‎", "paragraphs": ["Arsitektur Perusahaan sangat penting dalam perjalanan transformasi Citi dan merupakan kunci untuk menjaga kesehatan dan keberlanjutan organisasi di tengah persaingan pasar yang semakin ketat."]}, {"title": "Mengapa Kita Perlu Memahami <PERSON>?", "paragraphs": ["Tata Kelola Proses dan Arsitektur Perusahaan telah menjadi bagian dari cara kerja kita di Citi. Setiap orang berperan dalam menyederhanakan Proses dan menghubungkannya di seluruh Citi untuk meningkatkan sistem kita. Dengan memanfaatkan pengetahuan kita tentang infrastruktur ini, kita dapat mengurangi risiko, meningkatkan pengambilan keputusan, dan beroperasi secara lebih efisien."]}, {"title": "A<PERSON>?", "paragraphs": ["Tata Kelola Proses dan Arsitektur Perusahaan memberikan pendekatan yang berpusat pada Proses untuk menyederhanakan dan mengurangi risikonya, sehingga memberikan pengalaman yang lebih baik bagi klien dan karyawan. "]}]}, "section2": {"text1": "<PERSON><PERSON><PERSON><PERSON> ini akan membantu Anda memahami hal berikut: ", "list1": ["Kerangka kerja untuk menerapkan Arsitektur Perusahaan, yang dikenal sebagai Metodologi Arsitektur Perusahaan Citi (CEAM).", "Cara Arsitektur <PERSON>, <PERSON><PERSON>, dan <PERSON> Teknologi membantu mengurangi risiko dengan menyederhanakan Proses dan aplikasi pendukung di Citi."], "text2": "<PERSON><PERSON><PERSON> k<PERSON> ini, <PERSON><PERSON> akan mampu:", "list2": ["Menjelaskan tujuan <PERSON> Kelola Proses dan Arsitektur Perusahaan.", "Menjelaskan cara Tata Kelola Proses dapat membantu menyederhanakan dan meningkatkan operasi Citi.", "Mengetahui peran taksonomi dalam mendukung konsistensi di seluruh Citi.", "Menjelaskan tanggung jawab Pemilik Proses terkait dengan Tata Kelola Proses dan Arsitektur Perusahaan.", "Mengetahui siapa yang bertanggung jawab dalam menerapkan Tata Kelola Proses dan Arsitektur Perusahaan.", "Mengidentifikasi komponen utama yang digunakan dalam CEAM."]}, "section_assessment_disclaimer": {"paragraphs": ["<PERSON><PERSON>, akan ada penilaian akhir untuk kursus ini. <PERSON>a harus mendapat skor 80% atau lebih untuk mendapatkan kredit penyelesaian kursus ini. "]}, "section3": {"title": "Selamat datang di Pelatihan Tata Kelola Proses dan Arsitektur Perusahaan ", "paragraphs": ["Tata Kelola Proses dan Arsitektur Perusahaan membantu Citi untuk berfokus pada konsistensi dan keselarasan di seluruh Proses dan aplik<PERSON> pendukung, dengan tujuan untuk mengurangi risiko dan Pen<PERSON><PERSON><PERSON>an Teknologi.", "Praktik ini juga mengh<PERSON>lkan Proses <PERSON>, yang memungkinkan pemahaman yang jelas tentang bagaimana Proses, a<PERSON><PERSON><PERSON>, dan risiko kita saling terhubung. <PERSON>gan wawasan ini, kita dapat mengidentifikasi area yang tumpang tindih, menyederhanakan proses yang kompleks, serta membuat keputusan yang lebih tepat terkait penyed<PERSON>, investasi, atau penerapan kontrol."]}, "section4": {"paragraphs": ["Kompleksitas ini hadir dengan sendirinya dalam berbagai cara, seperti:", ["Beberapa aplikasi dengan fungsi serupa.", "Aplikasi yang memiliki terlalu banyak fungsi.", "Tidak ada standar baku dari Proses utama kita untuk menilai kualitas desain Proses tersebut."], "<PERSON><PERSON><PERSON><PERSON>, sulit untuk memahami kese<PERSON><PERSON>han cara kerja <PERSON> kita, r<PERSON><PERSON> yang terka<PERSON>, dan alasan kita memiliki beberapa aplikasi yang kemungkinan menjalankan fungsi yang sama. "]}, "section5": {"paragraphs": ["<PERSON><PERSON>k men<PERSON>, Citi telah menetapkan persyaratan standar untuk Tata Kelola Proses dan Arsitektur Perusahaan."]}, "section6": {"paragraphs": ["<PERSON>gan persy<PERSON>tan ini, kita dapat lebih memahami: ", ["Proses kita dan aplikasi pendukungnya. ", "Peluang untuk menyederhanakan kumpulan aplikasi yang digunakan dalam mendukung Proses kita.", "<PERSON><PERSON><PERSON> yang diperlukan untuk mengurangi risiko Proses kita."]]}, "section7": {"paragraphs": ["<strong>Arsitektur Peru<PERSON></strong> men<PERSON><PERSON><PERSON><PERSON> keterkaitan antara <PERSON>, a<PERSON><PERSON><PERSON>, dan elemen-elemen penting lainnya yang mendukung operasi kita. "]}, "section8": {"paragraphs": ["<strong>Dasar Arsitektur Perusaha<PERSON> ad<PERSON> Proses.</strong> Proses da<PERSON>, bersa<PERSON> dengan <PERSON> Kelola Proses yang telah ditetapkan, memungkinkan kita <strong>me<PERSON><PERSON> risiko, a<PERSON><PERSON><PERSON>, dan faktor la<PERSON>ya secara konsisten</strong> dalam membuat keputusan perusahaan yang objektif, mulai dari saran penempatan investasi yang tepat hingga cara mengeksekusi investasi strategis tersebut."]}, "footer": {"text1": "Mari telusuri apa itu Arsitektur Perusahaan dan cara menggunakannya di Citi."}}, "assessmentFinal": {"text1a": "Selamat datang di penilaian akhir.", "text1b": "<PERSON>gar dapat lulus dalam penilaian ini dan mendapatkan kredit untuk kursus ini, <PERSON><PERSON> harus ber<PERSON>il menjawab {numberOfQuestions} per<PERSON>aan berikut dan mencapai skor minimal 80%.", "list1": [["Tidak ada batasan jumlah per<PERSON>.", "<PERSON><PERSON><PERSON>, <PERSON><PERSON> ha<PERSON> menye<PERSON>aikan semua pertanyaan untuk mendapatkan skor. ", "<PERSON><PERSON> memilih untuk keluar sebelum menyelesaikan semua per<PERSON> penilaian, <PERSON><PERSON> harus memulai ulang penilaian."]], "text2": "<PERSON><PERSON><PERSON> turun untuk melanjutkan ke penilaian.‎", "questionsSection": [{"id": "kc_bankA_1", "bank": "bankA", "title": " ", "text": ["Tata Kelola Proses dan Ars<PERSON><PERSON><PERSON> (EAPGP) dikembangkan untuk mengidentifikasi peluang dalam menyatukan aplikasi yang memiliki fungsi serupa atau tumpang tindih.  ", "Aspek penting apa lagi yang dapat diidentifikasi oleh EAPGP?"], "instructions": "<PERSON><PERSON><PERSON> jawaban <PERSON>a dari empat opsi yang ada, lalu pilih <PERSON>.", "options": [{"label": "<PERSON><PERSON><PERSON> pem<PERSON><PERSON> setiap aplikasi seiring waktu", "value": "a"}, {"label": "<PERSON> yang bertanggung jawab atas entri data di setiap sistem", "value": "b"}, {"label": "Bagian Proses mana yang masih melibatkan input manual", "value": "c"}, {"label": "<PERSON><PERSON><PERSON>", "value": "d"}], "correctResponse": "c", "correctFeedbackTitle": "<PERSON><PERSON><PERSON>.", "incorrectFeedbackTitle": "<PERSON><PERSON><PERSON>.", "correctFeedbackText": ["EAPGP juga membantu kita mengidentifikasi bagian Proses mana yang masih melibatkan input manual. Hal ini membantu kita memahami alasan langkah-langkah tersebut masih dilakukan secara manual, dan membantu tim menemukan proses yang lebih efisien."], "incorrectFeedbackText": ["<PERSON><PERSON><PERSON> mengetahui jawaban atas pertanyaan ini, silakan tinjau bagian <strong>Pengantar</strong> pada halaman <strong><PERSON><PERSON><PERSON></strong>."], "universalFeedbackText": ["universal - umpan balik"]}, {"id": "kc_bankA_2", "bank": "bankA", "title": " ", "text": ["Arsitektur Bisnis mengatur Taksonomi Proses dan <PERSON><PERSON> Fungsi Manakah dari berikut ini yang juga diatur oleh <PERSON>?"], "instructions": "<PERSON><PERSON><PERSON> jawaban <PERSON>a dari empat opsi yang ada, lalu pilih <PERSON>.", "options": [{"label": "<PERSON><PERSON><PERSON><PERSON>", "value": "a"}, {"label": "Taksonomi Proses <PERSON> ", "value": "b"}, {"label": "Kosakata yang Dikendalikan Konsep Data", "value": "c"}, {"label": "<PERSON>ks<PERSON>mi Aplikasi Bis<PERSON> ", "value": "d"}], "correctResponse": "c", "correctFeedbackTitle": "<PERSON><PERSON><PERSON>.", "incorrectFeedbackTitle": "<PERSON><PERSON><PERSON>.", "correctFeedbackText": ["<PERSON><PERSON><PERSON><PERSON> membuat bahasa yang seragam yang mendukung komunikasi serta pemahaman lintas per<PERSON>, dan tiga di antaranya diatur oleh Arsitektur Bisnis. ", "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, dan <PERSON><PERSON> yang Dikendalikan Konsep Data menyediakan konsistensi pada semua artefak yang dihasilkan dalam CEAM di seluruh Citi."], "incorrectFeedbackText": ["<PERSON><PERSON><PERSON> mengetahui jawaban atas pertanyaan ini, silakan tinjau halaman <strong>Dasar Arsitektur Peru<PERSON></strong>."], "universalFeedbackText": ["universal - umpan balik"]}, {"id": "kc_bankA_3", "bank": "bankA", "title": " ", "text": ["Bagaimana cara Tata Kelola Proses mendukung kita dalam menyederhanakan Citi?"], "instructions": "<PERSON><PERSON><PERSON> jawaban <PERSON>a dari empat opsi yang ada, lalu pilih <PERSON>.", "options": [{"label": "Menyediakan cara objektif untuk mengukur kualitas proses, serta peluang untuk penyederhanaan dan pengurangan risiko.", "value": "a"}, {"label": "Mendefinisikan taksonomi yang mencerminkan bahasa unit bisnis tanpa mewajibkan konsistensi di seluruh Citi.", "value": "b"}, {"label": "Menyediakan repositori proses bagi tim sebagai referensi saat audit dan peninjauan di<PERSON>ukan.", "value": "c"}, {"label": "Mengharuskan tim global mengikuti Proses yang sama, tanpa memerhatikan kebutuhan atau konteks lokal.", "value": "d"}], "correctResponse": "a", "correctFeedbackTitle": "<PERSON><PERSON><PERSON>.", "incorrectFeedbackTitle": "<PERSON><PERSON><PERSON>.", "correctFeedbackText": ["Tata Kelola Proses membantu menyederhanakan proses di Citi dengan menetapkan Pemilik Proses sebagai pihak yang bertanggung jawab untuk memahami bagaimana proses berjalan saat ini dan mencari cara untuk memperbaikinya. Alat seperti Model Proses, <PERSON><PERSON><PERSON><PERSON>, dan <PERSON>ses Intelijen membantu mengukur kualitas proses, serta menemukan peluang untuk menyederhanakan, men<PERSON><PERSON><PERSON> risiko, dan menciptakan hasil yang lebih baik."], "incorrectFeedbackText": ["<PERSON><PERSON><PERSON> mengetahui jawaban atas pertanyaan ini, silakan tinjau bagian <strong>Pengantar</strong> pada halaman <strong><PERSON><PERSON><PERSON></strong>. "], "universalFeedbackText": ["universal - umpan balik"]}, {"id": "kc_bankA_4", "bank": "bankA", "title": " ", "text": ["Apa saja tanggung jawab seorang Pemilik Proses?"], "instructions": "<PERSON><PERSON><PERSON> jawaban <PERSON>a dari empat opsi yang ada, lalu pilih <PERSON>.", "options": [{"label": "Menggunakan Proses Intelijen dan data penting CEAM untuk meningkatkan Proses secara objektif.", "value": "a"}, {"label": "Mengawasi CEAM dan artefak lain yang berkaitan dengan Proses yang ditugaskan.", "value": "b"}, {"label": "Mengidentifikasi risiko dan kontrol yang sudah ada untuk memitigasi risiko Proses mereka.", "value": "c"}, {"label": "<PERSON><PERSON><PERSON>.", "value": "d"}], "correctResponse": "d", "correctFeedbackTitle": "<PERSON><PERSON><PERSON>.", "incorrectFeedbackTitle": "<PERSON><PERSON><PERSON>.", "correctFeedbackText": ["Pemilik Proses adalah seorang ahli dalam bidangnya. <PERSON><PERSON><PERSON> bertang<PERSON>g jawab atas CEAM dan artefak lain yang berkaitan dengan Proses yang ditugaskan. ", "Pemilik Proses memberikan pengawasan dan mengidentifikasi area yang perlu ditingkatkan. Hal ini dilakukan dengan meningkatkan kualitas dan konsistensi Proses Citi, serta mengidentifikasi risiko dan kontrol bawaan untuk memitigasi risiko Proses mereka melalui Proses Intelijen dan data penting CEAM. "], "incorrectFeedbackText": ["Untuk mengetahui jawaban atas pertanyaan ini, silakan tinjau bagian <strong>Siapa Pemilik Proses?</strong> dan <strong>Siapa yang <PERSON>anggung Jawab untuk Menerapkan Tata Kelola Proses dan Arsitektur Perusahaan?</strong>"], "universalFeedbackText": ["universal - umpan balik"]}, {"id": "kc_bankA_5", "bank": "bankA", "title": " ", "text": ["Apa itu taksonomi? "], "instructions": "<PERSON><PERSON><PERSON> jawaban <PERSON>a dari empat opsi yang ada, lalu pilih <PERSON>.", "options": [{"label": "<PERSON><PERSON><PERSON><PERSON> adalah cara untuk memberi nama, men<PERSON><PERSON>, dan men<PERSON><PERSON> istilah-istilah terkait.", "value": "a"}, {"label": "Taks<PERSON><PERSON> adalah cara untuk mengembangkan Fungsi di Citi. ", "value": "b"}, {"label": "<PERSON>ks<PERSON><PERSON> adalah pendekatan yang digunakan untuk mendorong penggunaan Proses yang ada secara efisien.", "value": "c"}, {"label": "<PERSON><PERSON><PERSON><PERSON> adalah peran khusus yang ditujukan untuk mengatur Proses. ", "value": "d"}], "correctResponse": "a", "correctFeedbackTitle": "<PERSON><PERSON><PERSON>.", "incorrectFeedbackTitle": "<PERSON><PERSON><PERSON>.", "correctFeedbackText": ["<PERSON><PERSON><PERSON><PERSON> adalah cara untuk memberi nama, men<PERSON><PERSON>, dan men<PERSON>sun istilah-istilah terkait. <PERSON><PERSON><PERSON><PERSON> memungkin<PERSON> komunikasi, tata kelola, dan pelaporan yang konsisten di seluruh per<PERSON>. <PERSON><PERSON><PERSON>, taksonomi meningkatkan keselarasan dan pengelolaan beragam pemangku kepentingan. "], "incorrectFeedbackText": ["<PERSON><PERSON><PERSON> mengetahui jawaban atas pertanyaan ini, silakan tinjau halaman <strong>Dasar Arsitektur Peru<PERSON></strong>."], "universalFeedbackText": ["universal - umpan balik"]}, {"id": "kc_bankA_6", "bank": "bankA", "title": " ", "text": ["Apa saja manfaat dari <PERSON> Proses dan Arsitektur Peru<PERSON>? "], "instructions": "<PERSON><PERSON><PERSON> jawaban <PERSON>a dari empat opsi yang ada, lalu pilih <PERSON>.", "options": [{"label": "Mendorong penyederhanaan proses dan menyediakan cara objektif untuk mengukur pengurangan risiko Proses.", "value": "a"}, {"label": "<PERSON><PERSON> berfokus pada pembaruan teknologi tanpa meningkatkan proses atau kontrol.", "value": "b"}, {"label": "Membiarkan tim menentukan tata kelola sendiri tanpa menyesuaikan dengan standar perusahaan. ", "value": "c"}, {"label": "<PERSON><PERSON><PERSON><PERSON><PERSON> hasil bisnis jangka pendek daripada konsistensi jangka panjang dan kedisiplinan dalam arsitektur.", "value": "d"}], "correctResponse": "a", "correctFeedbackTitle": "<PERSON><PERSON><PERSON>.", "incorrectFeedbackTitle": "<PERSON><PERSON><PERSON>.", "correctFeedbackText": ["Tata Kelola Proses dan Arsitektur Perusahaan didirikan untuk memberikan pengalaman yang lebih baik kepada klien dan karyawan di Citi. Pendekatan yang berpusat pada Proses mendukung penyederhanaan Proses dan meningkatkan kontrol di seluruh perusa<PERSON>, serta memecah silo di seluruh fungsi."], "incorrectFeedbackText": ["<PERSON><PERSON><PERSON> mengetahui jawaban atas pertanyaan ini, harap tinjau bagian <strong><PERSON><PERSON><PERSON><PERSON> </strong>di halaman <strong><PERSON><PERSON><PERSON>ng </strong>atau halaman <strong><PERSON><PERSON><PERSON></strong>."], "universalFeedbackText": ["universal - umpan balik"]}, {"id": "kc_bankA_7", "bank": "bankA", "title": " ", "text": ["Pemilik Proses, Pengguna Penilaian <PERSON> (MCA), dan Pemilik EUC bertanggung jawab untuk menerapkan Tata Kelola Proses dan Arsitektur Perusahaan. ", "Siapa lagi yang bertanggung jawab untuk menerapkan Tata Kelola Proses dan Arsitektur Perusahaan?"], "instructions": "<PERSON><PERSON><PERSON> jawaban terbaik dari lima opsi yang ada, lalu pilih <PERSON>.", "options": [{"label": "Pemilik Risiko ", "value": "a"}, {"label": "<PERSON><PERSON><PERSON> di semua tingkat", "value": "b"}, {"label": "Sponsor Investasi ", "value": "c"}, {"label": "<PERSON><PERSON><PERSON>", "value": "d"}, {"label": "<PERSON><PERSON><PERSON>", "value": "e"}], "correctResponse": "d", "correctFeedbackTitle": "<PERSON><PERSON><PERSON>.", "incorrectFeedbackTitle": "<PERSON><PERSON><PERSON>.", "correctFeedbackText": ["Tata Kelola Proses dan Arsitektur Perusahaan diterapkan oleh berbagai staf di Citi, term<PERSON>uk, misalnya, Pemilik Proses, Pemilik EUC, S<PERSON><PERSON>r <PERSON>i, dan <PERSON> (MCA). ", "<PERSON><PERSON> itu, penting untuk di<PERSON>t bahwa semua sta<PERSON>, term<PERSON><PERSON> kont<PERSON>, bertang<PERSON>g jawab secara tepat untuk memilih Proses yang sesuai dengan aplikasi, proyek, atau kontrol tertentu di berbagai sistem yang memer<PERSON>ya. "], "incorrectFeedbackText": ["Untuk mengetahui jawaban atas per<PERSON>aan ini, silakan tinjau bagian <strong>Bagaimana Cara Setiap Peran Menggunakan Tata Kelola Proses dan Arsitektur Perusahaan?</strong> dan <strong><PERSON><PERSON> yang <PERSON>anggung Jawab untuk Menerapkan Tata Kelola Proses dan Arsitektur Perusahaan?</strong>."], "universalFeedbackText": ["universal - umpan balik"]}, {"id": "kc_bankA_8", "bank": "bankA", "title": " ", "text": ["<PERSON><PERSON><PERSON><PERSON> mana yang mencakup inventarisasi langkah atau tugas yang dilakukan di Aplikasi dan dijalankan dalam suatu Proses?"], "instructions": "<PERSON><PERSON><PERSON> jawaban <PERSON>a dari empat opsi yang ada, lalu pilih <PERSON>.", "options": [{"label": "Taksonomi Proses", "value": "a"}, {"label": "<PERSON><PERSON><PERSON><PERSON>", "value": "b"}, {"label": "Kosakata yang <PERSON>n Data", "value": "c"}, {"label": "<PERSON><PERSON><PERSON>", "value": "d"}], "correctResponse": "b", "correctFeedbackTitle": "<PERSON><PERSON><PERSON>.", "incorrectFeedbackTitle": "<PERSON><PERSON><PERSON>.", "correctFeedbackText": ["<PERSON><PERSON><PERSON><PERSON> men<PERSON> inventarisasi langkah, tugas-tugas, atau \"fungsi\" yang dija<PERSON>an dalam suatu Proses. ", "Ini adalah satu level hierarki tindakan yang men<PERSON><PERSON><PERSON><PERSON> hasil te<PERSON> (Nama Fungsi), di mana satu fungsi dapat digunakan oleh banyak Proses. "], "incorrectFeedbackText": ["<PERSON><PERSON><PERSON> mengetahui jawaban atas pertanyaan ini, silakan tinjau halaman <strong>Dasar Arsitektur Peru<PERSON></strong>."], "universalFeedbackText": ["universal - umpan balik"]}, {"id": "kc_bankA_9", "bank": "bankA", "title": " ", "text": ["Manakah dari berikut ini yang melengkapi kategori Penunjukan Fungsi Aplikasi selain Strategis dan <PERSON>?"], "instructions": "<PERSON><PERSON><PERSON> jawaban <PERSON>a dari empat opsi yang ada, lalu pilih <PERSON>.", "options": [{"label": "Mempertahankan", "value": "a"}, {"label": "<PERSON><PERSON><PERSON><PERSON> ", "value": "b"}, {"label": "Mengelola", "value": "c"}, {"label": "Memelihara", "value": "d"}], "correctResponse": "d", "correctFeedbackTitle": "<PERSON><PERSON><PERSON>.", "incorrectFeedbackTitle": "<PERSON><PERSON><PERSON>.", "correctFeedbackText": ["Penunjukan Fungsi Aplikasi menunjukkan maksud strategis penggunaan Fungsi Aplikasi dalam Proses. ", "Ada tiga nilai yang dapat ditetapkan: Strategis (fungsi aplikasi strategis), <PERSON><PERSON><PERSON><PERSON> (fungsi aplikasi tidak strategis), da<PERSON> (aplikasi tidak cocok untuk mendukung fungsi yang diberikan). "], "incorrectFeedbackText": ["<PERSON><PERSON><PERSON> mengetahui jawaban atas pertanyaan ini, silakan tinjau bagian <strong>Model Proses</strong> di halaman <strong>Komponen CEAM</strong>."], "universalFeedbackText": ["universal - umpan balik"]}, {"id": "kc_bankA_10", "bank": "bankA", "title": " ", "text": ["<PERSON><PERSON> tujuan pengg<PERSON>an Model Proses dalam Arsitektur Bisnis?"], "instructions": "<PERSON><PERSON><PERSON> jawaban terbaik dari tiga opsi yang ada, lalu pilih <PERSON>.", "options": [{"label": "Model Proses menguraikan langkah-langkah untuk membuat suatu Proses.", "value": "a"}, {"label": "Model Proses membantu kita memahami Proses, aktivitas di dalamnya, <PERSON><PERSON><PERSON>, dan <PERSON>.", "value": "b"}, {"label": "Model Proses digunakan untuk menetapkan peran dan tanggung jawab dalam tim proyek.", "value": "c"}], "correctResponse": "b", "correctFeedbackTitle": "<PERSON><PERSON><PERSON>.", "incorrectFeedbackTitle": "<PERSON><PERSON><PERSON>.", "correctFeedbackText": ["Model Proses membantu kita memahami Proses dan aktivitas terkait secara terstruktur dan konsisten. Model ini memberikan gambaran yang jelas tentang alur kerja di seluruh organisasi dan membantu menyelaraskannya dengan Taksonomi Proses."], "incorrectFeedbackText": ["<PERSON><PERSON><PERSON> mengetahui jawaban atas pertanyaan ini, silakan tinjau bagian <strong>Model Proses</strong> di halaman <strong>Komponen CEAM</strong>."], "universalFeedbackText": ["universal - umpan balik"]}], "finalPassSection": {"title": "<PERSON><PERSON>", "text1": "<PERSON>a mendapatkan skor", "text2": "<strong><PERSON><PERSON><PERSON>!</strong> Anda ber<PERSON>il lulus penilaian dan memenuhi persyaratan penyelesaian kursus.", "text3": "<PERSON>a kini dapat <strong><PERSON><PERSON><PERSON></strong> dari kursus dengan mengk<PERSON> \"X\" di sudut kanan atas jendela <PERSON> atau, ji<PERSON>, <PERSON><PERSON> dapat menin<PERSON>u hasil penilaian <PERSON>.", "text4": "Anda juga dapat mempelajari kembali konten kursus menggunakan tombol <strong><PERSON><PERSON></strong> di kiri atas bilah alat kursus.", "btnReturn": "<PERSON><PERSON><PERSON>"}, "finalFailSection": {"title": "<PERSON><PERSON>", "text1": "<PERSON>a mendapatkan skor", "text2": "<PERSON><PERSON>, <PERSON><PERSON> tidak mencapai skor minimum yang diperlukan untuk lulus dalam penilaian ini. Sebaiknya pelajari kembali konten sebelum mengikuti penilaian ulang.", "text3": "<PERSON>a dapat kembali melihat konten kursus dengan memilih tombol <PERSON>elajari Ke<PERSON>li Konten.", "text4": "<PERSON><PERSON> Anda siap untuk mencoba penilaian lagi, pilih <PERSON>.", "btnRetry": "<PERSON><PERSON><PERSON>", "btnReturn": "Pelajari <PERSON>"}, "congratsSection": {"text1": "Sekarang Anda dapat keluar dari kursus.", "text2": "<PERSON>tuk keluar dari k<PERSON>, klik \"X\" di sudut kanan atas jendela peramban."}}, "assessment": {"course_level_test_out": {"this_course_contains_a_test_out_and_a_final_assessment": ["<PERSON>kan ada penilaian akhir untuk kursus ini. <PERSON>a harus mencapai skor 80% atau lebih pada penilaian agar memperoleh kredit untuk pelatihan ini.", "Kursus ini juga menyertakan pengujian yang bersifat opsional. <PERSON><PERSON> l<PERSON>, <PERSON><PERSON> dapat melewati materi kursus dan penilaian akhir serta tetap mendapatkan kredit penyelesaian.", "Anda dapat memilih untuk melewati pengujian dan langsung menuju konten."], "btnTakeTestOut": "<PERSON><PERSON><PERSON>", "btnSkipTestOut": "<PERSON><PERSON>", "title1": "<PERSON><PERSON><PERSON><PERSON>", "text1": "Se<PERSON>at <PERSON>.", "text2": "Agar dapat lulus penilaian ini, <PERSON><PERSON> harus ber<PERSON>il menjawab {numberOfQuestions} per<PERSON><PERSON> berikut dan mencapai skor minimal 80%.", "text3": "<PERSON><PERSON> gagal mencapai skor minimum untuk lulus, <PERSON><PERSON> akan diminta untuk mempelajari kembali seluruh materi kursus.", "list1": ["Anda hanya memiliki satu kesempatan untuk setiap pertanyaan.", "<PERSON><PERSON><PERSON>, <PERSON><PERSON> ha<PERSON> menye<PERSON>aikan semua pertanyaan untuk mendapatkan skor. ", "<PERSON><PERSON> keluar sebelum <PERSON>, k<PERSON><PERSON><PERSON> <PERSON>a tidak akan disimpan, dan <PERSON><PERSON> harus mempelajari kembali seluruh materi kursus."], "text4": "<PERSON><PERSON><PERSON> ke bawah untuk melanjutkan ke pengujian.‎", "btnContinueTest": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "btnContinuePage": "<PERSON><PERSON><PERSON><PERSON> kembali konten sebelumnya", "btnContinueCourse": "<PERSON><PERSON>", "btnReviewContent": "<PERSON><PERSON><PERSON>", "if_perfect_score": "JIKA SKOR SEMPURNA", "congrats": {"title1": null, "text1": "<PERSON>a mendapatkan skor", "text2": "<strong><PERSON><PERSON><PERSON>!</strong> Anda ber<PERSON>il lulus penilaian dan memenuhi persyaratan penyelesaian kursus. ", "text3": "<PERSON>a kini dapat <strong><PERSON><PERSON><PERSON></strong> dari kursus dengan mengk<PERSON> \"X\" di sudut kanan atas jendela <PERSON> atau, ji<PERSON>, <PERSON><PERSON> dapat menin<PERSON>u hasil penilaian <PERSON>.", "text4": "Anda juga dapat mempelajari kembali konten kursus menggunakan tombol <strong><PERSON><PERSON></strong> di kiri atas bilah alat kursus."}, "if_score_is_80_100": "JIKA SKOR 80-100%", "passed": {"title1": null, "text1": "<PERSON>a mendapatkan skor", "text2": "<strong><PERSON><PERSON><PERSON>!</strong> Anda ber<PERSON>il lulus penilaian dan memenuhi persyaratan penyelesaian kursus. ", "text3": "<PERSON>a kini dapat <strong><PERSON><PERSON><PERSON></strong> dari kursus dengan mengk<PERSON> \"X\" di sudut kanan atas jendela <PERSON> atau, ji<PERSON>, <PERSON><PERSON> dapat menin<PERSON>u hasil penilaian <PERSON>.", "text4": "Anda juga dapat mempelajari kembali konten kursus menggunakan tombol <strong><PERSON><PERSON></strong> di kiri atas bilah alat kursus."}, "failed": {"title1": null, "text1": "<PERSON>a mendapatkan skor", "text2": "<PERSON><PERSON>, <PERSON><PERSON> tidak mencapai skor minimum yang diperlukan untuk lulus dalam <PERSON> ini. Anda kini harus mempelajari kembali seluruh materi kursus dan menyelesaikan penilaian di akhir kursus."}}, "test_out": {"title1": "<PERSON><PERSON><PERSON><PERSON>", "text1": "<PERSON><PERSON><PERSON><PERSON> Bagian ini bersifat opsional dan terdiri dari {numberOfQuestions} pertanyaan. <PERSON><PERSON> ber<PERSON><PERSON> menjawab semua pertanyaan dengan benar, <PERSON><PERSON> dapat langsung melanjutkan ke bagian berikutnya dalam kursus ini.", "text2": "Anda hanya memiliki satu kesempatan untuk setiap pertanyaan dan harus menyelesaikan Pengujian dalam satu percobaan.", "text3": "<PERSON><PERSON> tidak ber<PERSON><PERSON> menjawab semua pertanyaan dengan benar atau keluar sebelum menyelesaikan semua pertanyaan, <PERSON><PERSON> harus mempelajari kembali konten pada bagian ini dan menjawab pertanyaan di akhir bagian dengan benar.", "btnTakeTest": "<PERSON><PERSON><PERSON>", "btnContinueSection": "Lanjutkan ke Konten Bagian", "passed": {"title1": "Selamat!", "text1": "Anda be<PERSON><PERSON><PERSON>n pemahaman yang baik terhadap materi yang dijelaskan dalam bagian ini. Anda dapat langsung melanjutkan ke bagian berikutnya atau mempelajari kembali konten bagian ini sebelum melanjutkan.", "btnContinueCourse": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "btnReviewSection": "Pelajari Kembali Konten Bagian"}, "failed": {"text1": "<PERSON><PERSON>, ada satu atau beberapa jawaban yang salah, <PERSON><PERSON><PERSON> <PERSON>a harus mempelajari kembali konten bagian ini dan menyelesaikan pertanyaan penilaian di akhir bagian.", "btnContinueSection": "Lanjutkan ke Konten Bagian"}}, "knowledge_check": {"title1": "<PERSON><PERSON><PERSON>", "text1": "Anda harus menjawab {numberOfQuestions} per<PERSON>aan berikut dengan benar sebelum dapat melanjutkan.", "text2": "<PERSON><PERSON> gagal menjawab satu atau beberapa pertanyaan dengan benar, atau keluar sebelum menyelesaikan semua pertanyaan, <PERSON><PERSON> harus mengulang penilaian.", "text3": "<strong><PERSON><PERSON><PERSON> turun untuk melanjutkan ke penilaian.‎</strong>", "passed": {"title1": "Selamat! Anda dapat melanjutkan ke bagian berikutnya."}, "failed": {"title1": "<PERSON><PERSON>", "text1": "<PERSON><PERSON><PERSON>, <PERSON><PERSON> gagal menjawab satu atau beberapa pertanyaan dengan benar. Sebaiknya pelajari kembali konten sebelum mengikuti penilaian ulang.", "btnReviewSection": "Pelajari Kembali Konten Bagian", "btnRetake": "<PERSON><PERSON><PERSON>"}}, "questions": []}, "page2": {"sectionTop": {"title1": "Apa itu Arsitektur Perusahaan?", "title2": "Definisi Arsitektur Perusahaan"}, "section1": {"title1": "<PERSON><PERSON>, <PERSON><PERSON><PERSON>, Keputusan <PERSON>", "text1": "Arsitektur Perusahaan didasarkan pada metodologi yang dikenal sebagai Metodologi Arsitektur Perusahaan Citi atau CEAM, yang digunakan sebagai pendekatan sistematis untuk mengembangkan Model Proses yang membantu mendefinisikan, me<PERSON><PERSON>, mengatur, dan men<PERSON>arkan Proses serta aplikasi terkait.", "text2": "Untuk lebih memahami manfaat CEAM bagi Citi, mari kita mulai dengan mengetahui bagaimana konsep-konsep yang digunakan dalam CEAM berhubungan dengan aktivitas sehari-hari seperti melakukan pembayaran.", "list1": ["Saat membeli produk dan layanan secara online, kemungkinan besar Anda membayar menggunakan kartu kredit, kartu debit, atau pembayaran seluler. ", "<PERSON><PERSON><PERSON> be<PERSON><PERSON><PERSON> pembayaran mereka diproses dengan benar, aman, dan tepat waktu, dan pen<PERSON>ia layanan pembayaran seperti Citi juga memiliki harapan yang sama.", "Sebagai bank global berskala besar, pemrosesan pembayaran merupakan fungsi yang digunakan dalam banyak Proses di seluruh Bisnis maupun Fungsi kita. <PERSON><PERSON><PERSON> be<PERSON><PERSON>-<PERSON>hun, kita telah membuat banyak aplikasi yang memiliki fungsi tersebut. Hal ini menciptakan kompleksitas dalam hal pemeliharaan, peningkatan, dan pembangunan kontrol yang kuat untuk mencapai tujuan pembayaran yang benar, tepat waktu, dan aman.", "CEAM memungkinkan kita untuk mengidentifikasi aplikasi yang memiliki fungsi umum, seperti inisiasi atau validasi pembayaran. Dengan mengonsolidasikan peluang-peluang yang telah diidentifikasi tersebut, kita dapat menyederhanakan lanskap aplikasi, se<PERSON>ga lebih mudah dikelola, lebih sedikit aplikasi yang harus dipelihara, serta tetap selaras dengan persyaratan dan kontrol terbaru. Upaya ini mendorong dan mendukung Penyederhanaan Teknologi."], "text3": "Tata Kelola Proses dan Arsitektur Perusahaan memungkinkan kita untuk secara objektif mendorong pen<PERSON>hanaan di seluruh per<PERSON>an."}, "footer": {"text1": "<PERSON><PERSON><PERSON><PERSON><PERSON>, mari kita bahas lebih lanjut mengenai Taksonomi dan komponen-komponen Arsitektur Perusahaan di Citi.", "btnText1": "Lanjutkan"}}, "page3": {"sectionTop": {"title1": "Dasar Arsitektur <PERSON>"}, "section1": {"title": "Taksonomi Proses dan <PERSON>", "text1": "Mari kita mulai dengan sebuah skenario pembayaran yang menunjukkan bagaimana penggunaan bahasa yang seragam dapat mendorong pen<PERSON>, dan bagaimana Taksonomi dalam Arsitektur Perusahaan mewujudkannya.", "example_lorenzo": ["<PERSON><PERSON><PERSON><PERSON>, ini <PERSON>—klien Citi yang saat ini menjalankan usaha kecil dan menggunakan layanan Citi untuk membayar pemasoknya di seluruh dunia. ", "<PERSON><PERSON><PERSON><PERSON>, melakukan pembayaran melalui Citi seharusnya berjalan lancar bagi <PERSON>. <PERSON><PERSON>, proses tersebut justru cukup rumit dan penuh tantangan.", "Mengapa? ", "Di balik layar, teknologi pembayaran Citi sangat kompleks. Untuk memenuhi kebutuhan klien di seluruh dunia dan mendukung berbagai produk global, Citi telah mengembangkan banyak aplikasi pembayaran, yang sering kali memiliki fungsi serupa. ", "<PERSON><PERSON><PERSON><PERSON>, <PERSON> harus masuk ke tiga platform pembayaran yang berbeda hanya untuk membayar pemasoknya. Tergantung pada lokasi pemasok, aplikasi regional yang berbeda juga digunakan untuk mengirim dana. Hal ini membuat pengalaman pengguna menjadi terpecah-belah karena adanya sistem yang tumpang tindih, padahal semua tujuannya sama, yaitu mengirim dana ke pihak lain di seluruh dunia.", "Situasi Lorenzo <PERSON>n bagaimana kerangka kerja arsitektur perusahaan dapat membantu Citi mengidentifikasi dan mengurangi aplikasi yang berle<PERSON>han. ", "Dengan menerapkan kerangka kerja arsitektur <PERSON>, kita bisa memberikan pengalaman yang lebih baik bagi klien baru dan yang sudah ada, serta meningkatkan kepuasan mereka terhadap layanan dan produk Citi."], "text8": "<PERSON><PERSON><PERSON>, mari kita lihat bagaimana Taksonomi—k<PERSON><PERSON><PERSON> Taksonomi Proses dan <PERSON>—membantu menciptakan bahasa yang seragam yang mendukung komunikasi dan pemahaman lintas per<PERSON>, serta menjadi dasar dari arsitektur perusa<PERSON>.", "cta_accordion": "<PERSON><PERSON><PERSON> setiap definisi taksonomi untuk mempelajari lebih lanjut.", "accordion1": [{"title": "Apa itu Taksonomi?", "text": ["<PERSON><PERSON> mungkin paham dengan hierarki <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, atau Penunjukan Kendaraan Legal Citi. Ini semua adalah contoh Taksonomi Perusahaan di Citi.", "<strong><PERSON><PERSON><PERSON><PERSON></strong> adalah cara untuk memberi nama, men<PERSON><PERSON>, dan men<PERSON>sun istilah-istilah terkait. Taksonomi memungkinkan komunikasi, tata kelola, dan pelaporan yang konsisten di seluruh perusa<PERSON>an. Sehingga dapat meningkatkan keselarasan dan pengelolaan berbagai pemangku kepentingan. "]}, {"title": "Apa Itu <PERSON>?", "text": ["<PERSON>ks<PERSON><PERSON> berikut dikelola oleh Arsitektur Bisnis:"], "list": ["Taksonomi Proses", "<PERSON><PERSON><PERSON><PERSON>", "Kosakata yang Dikendalikan Konsep Data"]}], "callout1": "<strong>Arsitektur Bisnis</strong> bertang<PERSON>g jawab atas <PERSON> Tata Kelola Proses dan Arsitektur Perusahaan.", "text9": "<PERSON> jelajahi set<PERSON> ini lebih lanjut."}, "section2": {"title1": "Taksonomi Proses", "text1": "Taksonomi ini menjelaskan dan mengatur Proses. Taksonomi ini mencakup inventarisasi Proses yang mencerminkan seluruh aktivitas yang dilakukan di seluruh <PERSON>, mulai da<PERSON> Rekening hingga Manajemen Talenta atau Manajemen Risiko. ", "text2": "<PERSON><PERSON>:", "list1": ["<strong>Proses</strong> ad<PERSON><PERSON> kumpulan aktivitas atau fungsi yang dilakukan dalam urutan tertentu untuk mencapai tujuan bisnis, se<PERSON><PERSON>, <PERSON><PERSON><PERSON>, atau manfaat bagi Citi. ", "Proses menerima satu atau beberapa input, yang ditransformasikan melalui serangkaian aktivitas atau fungsi untuk menghasilkan satu atau beberapa output. Output ini dapat dikirimkan ke Proses, klien, karya<PERSON>, dan/atau pihak ketiga lain. "], "text3": "<strong><PERSON><PERSON><PERSON><PERSON></strong> Citi didefinisikan secara independen, tidak bergantung pada lini bisnis atau jenis produk.", "text6": "Setiap Proses memiliki Pemilik Proses dan merupakan eksekutif Citi yang bertanggung jawab untuk mengelola keseluruhan proses melalui penyed<PERSON>an dan pengurangan risiko.", "text7": "<PERSON>a akan mempelajari lebih lanjut tentang Pemilik Proses nanti dalam pelatihan ini.", "title2": "<PERSON><PERSON><PERSON><PERSON>", "text8": "Taksonomi ini mencakup inventarisasi langkah, tugas, atau \"fungsi\" yang dijalankan dalam suatu Aplikasi yang mendukung Proses.", "text9": " <strong><PERSON><PERSON><PERSON><PERSON></strong> ad<PERSON><PERSON> inventarisasi tindakan yang mengh<PERSON><PERSON><PERSON> hasil terten<PERSON> (<PERSON><PERSON>). Penting untuk diperhatikan bahwa satu fungsi dapat digunakan di banyak Aplikasi dan Proses. Setiap aplikasi memiliki kumpulan Fungsi yang telah ditentukan, atau yang disebut Fungsi Aplikasi.", "text10": "<PERSON><PERSON><PERSON>, <PERSON><PERSON> <strong><PERSON><PERSON><PERSON><PERSON></strong> terdiri dari berbagai langkah atau fungsi, dan melibatkan beberapa aplikasi. Fungsi di aplikasi termasuk, tetapi tidak terbatas pada:", "list3": ["Pencatatan pengiriman pembayaran", "Validasi pem<PERSON>aran", "<PERSON><PERSON><PERSON><PERSON> k<PERSON>an", "Penyimpanan pem<PERSON>", "Transfer dana"], "title3": "Kosakata yang Dikendalikan Konsep Data", "text11": "<strong>Kosakata yang Dikendalikan Konsep Data</strong> ad<PERSON>h kumpulan Konsep Data yang disusun ke dalam struktur hierarki. ", "text12": "Konsep Data adalah representasi dari kumpulan elemen data yang berisi orang, tempat, atau benda yang berpartisipasi dalam aktivitas bisnis perusahaan. Istilah-istilah tersebut memiliki nama dan definisi yang dikenal dan dipahami oleh bisnis. Contoh Konsep Data mencakup <PERSON>gan, Akun, Perdagangan, dll.", "text13": "Penting untuk diperhatikan bahwa Konsep Data dibuat atau digunakan oleh Fungsi. ", "text14": "Untuk mempelajari lebih lanjut tentang Taksonomi Proses, <PERSON><PERSON><PERSON><PERSON>, dan <PERSON> yang Di<PERSON>dal<PERSON>n Konsep Data, tinjau dan tandai halaman <a href='https://citi.sharepoint.com/sites/Enterprise-Architecture/SitePages/ReferenceMaterials.aspx' target='_blank'>Materi Referensi Arsitektur Bisnis</a>. ", "text15": "Ingat, ketiga <strong><PERSON><PERSON><PERSON><PERSON> ini bekerja sama untuk menjabarkan tentang bagaimana proses, fungsi, dan data Citi dapat dimanfaatkan dan digunakan kembali secara konsisten</strong> pada produk dan layanan serupa di seluruh perusa<PERSON>an."}, "footer": {"text1": "Sekarang mari kita pelajari lebih lanjut tentang Pemilik Proses dan tanggung jawab mereka."}}, "page4": {"sectionTop": {"title1": "Siapa Pemilik Proses?"}, "section1": {"text1": "<PERSON><PERSON><PERSON> memahami se<PERSON>an Proses yang telah ditetapkan, siapa yang melakukan pengawasan dan menggunakan data terkait Proses untuk membantu mengidentifikasi area yang perlu ditingkatkan?", "text2": "Ini adalah tugas Pemilik Proses. Pemilik Proses bertanggung jawab untuk mengelola Proses melalui penyederhanaan dan pengurangan risiko Proses yang mereka miliki.", "text3": "Mari jelajahi peran ini lebih dalam dengan mempelajari lebih lanjut tentang tanggung jawab mereka.", "text4": "Tanggung Jawab Pemilik Proses", "text5": "Pem<PERSON>k <PERSON>ses, yang ditu<PERSON>kan pada setiap Proses dari <PERSON>, memiliki se<PERSON>an tanggung jawab untuk mengatur Proses yang mereka miliki. ", "text5AltText": "Ini adalah contoh dari tiga level Taksonomi Proses Bisnis. Level 1 Grup Proses adalah Pergerakan Uang. Level 2 Proses Induk adalah Pembayaran. Level 3 Proses adalah Pemrosesan Pembayaran. Pemilik Proses bertanggung jawab terhadap penyelesaian level ketiga.", "text7": "Pemilik Proses bertanggung jawab untuk: ", "list1": ["Membuat dan memel<PERSON>ara Model Proses CEAM, yang digunakan untuk mengidentifikasi peluang dalam Penyederhanaan Teknologi atau mengonsolidasikan aplikasi dengan fungsi serupa dalam Proses mereka.", "Membuat dan memelihara Profil MCA Proses Global (GPMP) untuk Proses mereka. Hal ini meliputi:", ["Mengidentifikasi risiko dan kontrol yang sudah ada untuk memitigasi risiko Proses mereka. Hal ini membentuk GPMP dalam Penilaian Pen<PERSON><PERSON> (MCA, Manager Control Assessment) Citi yang telah direvisi. ", "Meninjau dan menyetujui permintaan untuk menambah atau menghapus GPMP dari Unit Penilaian Global."], "Memastikan bahwa keterkaitan lain yang teridentifikasi dengan Proses, seperti EUC, telah diperhit<PERSON>, karena hal ini merupakan bagian dari berbagai faktor yang digunakan untuk mengukur kesehatan suatu Proses atau Penilaian Profil Risiko Proses. "], "text8": "Untuk mempelajari lebih lanjut tentang Kepemilikan Proses, tinjau <a href='https://citi.sharepoint.com/sites/Enterprise-Architecture/SitePages/Process-Ownership.aspx?csf=1&web=1&e=1oNYIZ' target='_blank'>halaman Sumber Informasi Pemilik Proses Arsitektur Bisnis.</a>"}, "section_poa": {"title": "Penila<PERSON> (POA) dan Skor Penilaian Risiko Proses", "paragraphs": ["Pen<PERSON>ian <PERSON>ses (POA) memberikan gambaran menyeluruh kepada Pemilik Proses mengenai keterkaitan yang telah disetujui, termasuk detail Risiko dan Kontrol yang diterapkan, serta mencakup masalah iCAP, EUC, dan data Model Proses CEAM untuk Penyederhanaan Teknologi dan Otomatisasi Proses. ", "Keterkaitan yang telah disetujui ini merupakan masukan bagi Penilaian Profil Proses dan Skor Risiko Proses yang dihasilkan. Skor tersebut akan membantu Pemilik Proses dalam mengidentifikasi peluang untuk mendorong Penyederhanaan dan <PERSON> Risiko. "]}, "footer": {"text1": "<PERSON><PERSON><PERSON><PERSON><PERSON>, mari lebih memahami lanskap arsitektur bisnis Citi dengan men<PERSON>jahi beberapa komponen spesifik Model Proses CEAM."}}, "page5": {"sectionTop": {"title1": "Komponen <span class='sr-only'> C E A M </span><span aria-hidden='true'>CEAM</span>"}, "section1": {"title1": "Model Proses ", "text1": "Model Proses ad<PERSON>h representasi dari bagaimana suatu proses berjalan saat ini serta aplikasi yang terkait. ", "text2": "Kita <PERSON>kan Model Proses untuk membantu memahami Proses dan aktivitasnya untuk setiap Proses dalam taksonomi. ", "text3": "Model ini secara visual menggambarkan langkah-langkah Proses menggunakan Taksonomi Fungsi, yang dibutuhkan untuk menjalankan/menyelesaikan suatu Proses dan menghasilkan output yang diinginkan. Model ini menghubungkan Fungsi Aplikasi dan Konsep Data ke dalam Proses.", "callout1": "Pemilik Proses bertanggung jawab atas akurasi Model Proses untuk Proses mereka.", "text7": "Mari lihat lebih dekat Model Proses dengan mengeksplorasi berbagai komponen dari contoh ilustrasi Proses: <strong>P<PERSON>rose<PERSON>em<PERSON></strong>. Penting untuk diperhatikan bahwa komponen dalam contoh ini menggambarkan tampilan yang sederhana. ", "cta_tabs": "<PERSON><PERSON><PERSON> masing-masing dari empat tab untuk mempelajari lebih lanjut tentang komponen-komponen yang diperlukan untuk menjalankan Proses: <PERSON><PERSON><PERSON><PERSON>.", "tabs1": [{"img": "../assets/page5/tab1.svg", "title": "<PERSON><PERSON><PERSON><PERSON>", "title_full": "<PERSON><PERSON><PERSON><PERSON> (hanya untuk tujuan ilustrasi)", "text": ["Ingat, Model Proses mencakup beberapa komponen yang diperlukan untuk menjalankan Proses. "], "alt": "Grafik yang menguraikan semua komponen Proses: <PERSON><PERSON><PERSON>san Pembayaran. Baris pertama terdiri dari sembilan Fungsi, mulai dari input hingga output. Fungsi ini mencakup 1. pencat<PERSON><PERSON> pembayaran, 2. <PERSON><PERSON><PERSON><PERSON> k<PERSON><PERSON><PERSON><PERSON>, 3. pen<PERSON><PERSON><PERSON><PERSON> pem<PERSON>, 4. permin<PERSON><PERSON> untuk penawaran harga, 5. pem<PERSON><PERSON> fasilitas kredit, 6. pelaksanaan transaks<PERSON>, 7. pembatalan perbaikan instruksi, 8. r<PERSON> pem<PERSON>, dan 9. posting rekening dan GL. Fungsi ini dihubungkan oleh Konsep Data. Fungsi satu, dua, tiga, dan empat dihubungkan oleh Konsep Data pembayaran. Fungsi empat, lima, dan enam dihubungkan oleh pembayaran dan Konsep Data Kurs FX. Fungsi enam, tujuh, delapan, dan sembilan dihubungkan oleh Konsep Data pembayaran. Di atas Fungsi, ditautkan ke Konsep Data dengan huruf. Baris kedua terdiri dari empat Data Induk/Referensi. Baris ini berisi rekening, si<PERSON><PERSON> k<PERSON>, BIC/ABA - mata uang dan negara tertentu, dan rekening GL/Nostro. Baris terakhir terdiri dari tiga tingkat otomatisasi. Ketiga tingkat tersebut mencakup sepenuhnya otomatis, perpaduan manual dan otomatis, dan sepenuhnya manual."}, {"img": "./assets/page5/tab2.svg", "title": "Fungsi Aplikasi", "title_full": "<PERSON><PERSON><PERSON>plik<PERSON> (hanya untuk tujuan ilustrasi)", "text": ["Fungsi ini terdiri dari sembilan langkah/aktivitas inti dalam <PERSON>emrose<PERSON> Pembayaran (misalnya, pencatatan pembayaran, penyaringan kepatuhan).", "Fungsi-fungsi ini terkait dengan Aplikasi yang memiliki Fungsi, yang dapat diulang di beberapa proses."], "alt": "Grafik inihanya berfokus pada Fungsi Aplikasi baris pertama. Ada sembilan Fungsi Aplikasi dari input hingga output untuk Proses: Pemrosesan Pembayaran. Fungsi ini mencakup 1. pen<PERSON><PERSON><PERSON> pem<PERSON>, 2. <PERSON><PERSON><PERSON><PERSON>, 3. <PERSON><PERSON><PERSON><PERSON><PERSON> pem<PERSON>, 4. permintaan untuk penawaran harga, 5. pemrosesan fasilitas kredit, 6. Eksek<PERSON>i <PERSON>pakatan FX, 7. pembatalan perbaikan instruksi, 8. <PERSON><PERSON>, dan 9. posting rekening dan GL."}, {"img": "./assets/page5/tab3.svg", "title": "Konsep Data", "title_full": "Konsep Data (hanya untuk tujuan ilustrasi)", "text": ["Konsep ini termasuk data yang digunakan atau dihasilkan dalam <PERSON>emrose<PERSON>embayaran (misalnya, pembayaran, kurs FX).", "Konsep Data didasarkan pada Kosakata yang Dikendalikan Konsep Data."], "alt": "Grafik ini berfokus pada Data Induk/Referensi baris kedua, beser<PERSON> \"pembayaran\" dan \"pembayaran kurs FX\", yang terhubung ke masing-masing dari sembilan Fungsi. Konsep Data ini berkaitan dengan kontrol A dan B pada Fungsi satu dan sembilan."}, {"img": "./assets/page5/tab4.svg", "title": "Input Manual", "title_full": "Input Manual (hanya untuk tujuan ilustrasi)", "text": ["Input manual atau langkah pemrosesan manual diidentifikasi oleh Pemilik Proses untuk setiap Fungsi dalam Proses mereka.", "Jika terdapat input manual, maka input tersebut akan diklasifikasikan lebih lanjut berdasarkan jenis input atau langkah proses manual, misalnya apakah diperlukan karena pertimbangan tertentu atau sebenarnya dapat diotomatisasi."], "alt": "Grafik ini berfokus pada baris terakhir yang menunjukkan Input Manual/Tingkat Otomatisasi serta indikator otomatisasi yang sesuai untuk setiap Fungsi. Tingkat otomatisasi yang ditampilkan mencakup tiga kategori: sepenuhnya otomatis, perpaduan otomatis dan manual, dan sepenuhnya manual. Ada sembilan tingkat otomatisasi yang direpresentasikan untuk setiap Fungsi, dari input hingga output untuk Proses: Pemrosesan Pembayaran. "}]}, "section2": {"title1": "<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, da<PERSON> <PERSON><PERSON>", "text2": "Fungsi Aplikasi yang dijelaskan dalam bagian Taksonomi Fungsi kemudian dipetakan ke dalam Model Proses.", "text5": "Apa itu Penunjukan Fungsi Aplikasi?", "text6": "Penunjukan Fungsi Aplikasi menunjukkan maksud strategis penggunaan Fungsi Aplikasi dalam Proses.  ", "text7": "<PERSON><PERSON><PERSON>i nilai yang dapat diberikan ke setiap Fungsi Aplikasi adalah:", "text8": "Strategis", "list2": ["Aplikasi yang akan diinvestasikan <PERSON><PERSON>, dengan <PERSON> yang di<PERSON>p strategis, menu<PERSON><PERSON><PERSON><PERSON> bahwa aplikasi lain dengan fungsi serupa dapat dikonsolidasikan ke dalam aplikasi yang telah ditetapkan sebagai “Strategis”. "], "text9": "Memelihara ", "list3": ["Ada juga aplikasi yang memang memenuhi keb<PERSON><PERSON>, namun tidak di<PERSON>p strategis, artinya migrasi dari aplikasi lain sebaiknya tidak diarahkan ke aplikasi ini. "], "text10": "Dihentikan", "list4": ["Se<PERSON>ara itu, ada aplikasi yang masuk dalam daftar penghentian dan/atau Fungsi Aplikasi-nya tidak lagi sesuai untuk kebut<PERSON>, se<PERSON>ga perlu dikonsolidasikan dalam jangka waktu yang wajar, idealnya ke aplikasi Strategis dengan Fungsi tersebut. "], "paragraphs_11": ["Penunjukan Fungsi Aplikasi adalah input yang diperlukan untuk mengevaluasi kemajuan kita dalam mendorong rasionalisasi atau pengurangan aplikasi, dan penggunaan kembali aplikasi jika memungkinkan untuk mencapai Penyederhanaan Teknologi di seluruh Proses kita. Data yang dikumpulkan memungkinkan kita memantau kepatuhan saat melaksanakan proyek teknologi.", "<PERSON><PERSON>ga kita akhirnya dapat: ", ["Menemukan di mana kita memiliki berbagai aplikasi yang mendukung fungsi yang sama. ", "Me<PERSON><PERSON> apa<PERSON>h aplikasi yang tepat yang digunakan untuk suatu fungsi sesuai dengan prosesnya.", "Mendukung rasionalisasi dan pen<PERSON><PERSON><PERSON>an aplikasi-ke-fungsi dengan menetapkan nilai \"Penunjukan Fungsi Aplikasi\" pada setiap fungsi aplikasi."]], "text12": "Pemilik Proses bertanggung jawab atas keakuratan dan kelengkapan Model Proses mereka bersama dengan Penunjukan Fungsi Aplikasi untuk Proses mereka."}, "section3": {"title": "<PERSON><PERSON><PERSON><PERSON><PERSON> ", "subtitle": "<PERSON>pa yang dimaksud <PERSON>knologi dan mengapa itu penting?", "paragraphs": ["<strong><PERSON><PERSON><PERSON><PERSON><PERSON></strong> merupakan kerangka kerja yang bertujuan meningkatkan infrastruktur teknologi Citi melalui identifikasi aplikasi dengan fungsi serupa yang dapat dikonsolidasikan. <PERSON><PERSON>logi, kita dapat mengelola inventaris aplikasi secara lebih efisien dengan fungsi yang terbaik di kelasnya, yang pada akhirnya mengurangi risiko dan meningkatkan pengalaman klien. ", "Pemetaan Aplikasi memungkinkan identifikasi dan pengukuran upaya Penyederhanaan Teknologi dalam suatu Proses melalui Model Proses."]}, "section4": {"paragraphs": ["Dengan mengurangi kompleksitas yang melekat di Citi melalui Penyederhanaan Teknologi, kita dapat meningkatkan hal-hal berik<PERSON>, se<PERSON>i:", ["Kecepatan masuk ke pasar", "<PERSON>lin<PERSON><PERSON> secara k<PERSON>han", "<PERSON><PERSON> sa<PERSON>", "E<PERSON><PERSON>nsi "], "<PERSON><PERSON><PERSON> conto<PERSON>, fun<PERSON><PERSON> <strong><PERSON><PERSON><PERSON></strong> te<PERSON> men<PERSON>if<PERSON> 156 aplikasi yang digunakan di Citi dalam berbagai Proses yang berbeda."], "callout": "<PERSON><PERSON><PERSON> conto<PERSON>, fun<PERSON><PERSON> <strong><PERSON><PERSON><PERSON></strong> te<PERSON> men<PERSON>if<PERSON> 156 aplikasi yang digunakan di Citi dalam berbagai Proses yang berbeda."}, "footer": {"text1": "<PERSON><PERSON><PERSON> membahas tentang <PERSON>ses, <PERSON>, da<PERSON> <PERSON><PERSON><PERSON>, sekarang mari kita masuk ke dalam sebuah skenario untuk memahami lebih lanjut cara Arsitektur Perusahaan membantu kita menyederhanakan proses tersebut."}}, "page6": {"sectionTop": {"title1": "<PERSON><PERSON><PERSON>"}, "section1": {"title1": "Di Luar Citi", "text1": "Perkenalkan Greta.", "text2": "<PERSON><PERSON> gema<PERSON> be<PERSON> online dan menggunakan aplikasi pembayaran seluler untuk membeli barang. Ia kemudian melakukan pembayaran ke penyedia dana melalui fitur pembayaran tagihan elektronik dari Citi. ", "text3": "<PERSON><PERSON> k<PERSON>han proses checkout dengan sekali klik.", "text4": "<strong>Mari pelajari lebih lanjut tentang pengalaman Greta. </strong>", "text6": "Selain berbelanja online, <PERSON>reta juga sesekali berinvestasi di pasar saham. Saat melakukan pembelian saham, Greta menggunakan Proses Transaksi yang bergantung pada Pemrosesan Pembayaran untuk menyelesaikan pembayaran investasi tersebut. Pengalaman Greta dalam menggunakan berbagai Proses di Citi seharusnya selalu lancar, a<PERSON><PERSON>, dan aman.", "text7": "Penyediaan produk dan layanan ini dikelola melalui serangkaian Proses yang memastikan pengalaman menyeluruh yang konsisten dan membuat pelanggan merasa puas.", "text8": "Ini dapat dicapai dengan: ", "list1": ["Menganalisis setiap Proses lebih dalam untuk memahami aktivitas yang mendasarinya serta Fungsi Aplikasi yang mendukungnya. ", "Mengidentifikasi aplikasi apa saja yang dibutuhkan untuk mendukung setiap fungsi dalam proses tersebut. "], "text9": "<PERSON><PERSON><PERSON>, jika <PERSON><PERSON> memesan bahan makanan mingguan atau memperpanjang langganan layanan streaming, ia mengharapkan tingkat keamanan dan kemudahan pembayaran yang sama, apa pun platform yang digunakannya. ", "text10": "Terlepas dari apa pun produk yang dipesan Greta-—baik bahan makanan atau perpanjangan langganan layanan streaming—situs ini akan membawa Greta melalui langkah-langkah checkout yang sama, memp<PERSON><PERSON> pembay<PERSON> mereka, men<PERSON><PERSON><PERSON> tanda-tanda penipuan, dan mengel<PERSON> pajak yang harus dibayar. ", "text12": "<PERSON>i sudut pandang arsitektur per<PERSON>, terlih<PERSON> jelas bahwa beberapa fungsi digunakan secara berulang di berbagai platform dan perlu distandarkan agar konsisten di seluruh layanan. ", "text13": "Pendekatan ini menguntungkan baik Greta sebagai pengguna maupun situs (perusahaan), karena berfokus pada pen<PERSON><PERSON><PERSON><PERSON> pen<PERSON>, pening<PERSON><PERSON> e<PERSON><PERSON>, dan pengu<PERSON>an risiko.", "title2": "Bagaimana Hal Ini Berlaku untuk Citi? ", "text14": "Kemampuan untuk mengidentifikasi aplikasi dengan fungsi yang tumpang tindih tidak hanya membantu meningkatkan efisiensi dalam penyampaian produk dan layanan, tetapi juga memungkinkan kita untuk menggunakan kembali aplikasi yang sama di berbagai Proses, jika relevan.", "text15": "<PERSON><PERSON> me<PERSON>mba<PERSON>kan pen<PERSON>aman <PERSON>, mari kita lihat kembali klien kita <PERSON>.", "text17": "<strong><PERSON><PERSON><PERSON><PERSON></strong>", "text18": "Pada pertemuan pertama kita dengan <PERSON>, ingat<PERSON> bahwa dia tidak memiliki pengalaman yang sangat menyenangkan untuk mendapatkan produk Citi. <PERSON> semakin frustrasi karena dihubungi beberapa kali untuk menanyakan informasi yang sama dan menyampaikan ketidakpuasannya kepada tim Citi. ", "text19": "<PERSON><PERSON><PERSON><PERSON>, <PERSON> me<PERSON>a pengalaman layanan klien yang di<PERSON><PERSON>ya atas produk melebihi ekspektasi awal, se<PERSON>ga dia berminat untuk membeli lebih banyak produk di Bank.", "text20": "<PERSON><PERSON><PERSON>, <PERSON> juga ingin mengajukan kartu kredit baru dan pinjaman dana untuk pembelian rumah. Saat Lorenzo melanjutkan dengan aplikasi untuk produk ini, mari pelajari lebih lanjut tentang cara tim Pemilik Proses menggunakan artefak <span class='sr-only'> C E A M </span><span aria-hidden='true'>CEAM</span> dan Proses Intelijen untuk memberikan pendekatan yang lebih efisien, mengintegrasikan infrastruktur aplikasi, menyelaraskan data di seluruh tim, dan pada akhirnya meningkatkan pengalaman klien.", "lorenzos_journey_paragraphs": ["<PERSON><PERSON><PERSON>, <PERSON> mengg<PERSON>kan layanan Citi untuk membayar pemasoknya, namun harus menggunakan beberapa aplikasi pembayaran agar transaksinya dapatdiproses. ", "Penyederhanaan Teknologi bertujuan mengurangi kompleksitas infrastruktur aplikasi dengan mengidentifikasi dan mengonsolidasikan aplikasi yang memiliki fungsi serupa. ", "Komitmen Citi untuk menerapkan Penyederhanaan Teknologi di area pembayaran melahirkan solusi bernama ‘Citi Payments Express’, sebuah aplikasi strategis yang mendukung alur pembayaran instan. ", "Ingat bagaimana Lorenzo harus masuk ke tiga platform berbeda hanya untuk membayar pemasoknya? Citi berhasil menyederhanakan kompleksitas proses pembayaran klien dan meningkatkan pengalaman mereka. ‘Citi Payments Express’ mengintegrasikan fitur pembayaran lokal di AS dan Inggris ke dalam satu aplikasi global yang mendukung pembayaran instan.", "<PERSON><PERSON>, keh<PERSON>ran aplikasi ini membuat segalanya jadi lebih mudah. Kini ia cukup menggunakan satu platform sederhana dan terpadu untuk membayar semua pemasoknya. Kebutuhan digitalnya juga terpenuhi dengan pengalaman yang efisien. ", "<PERSON><PERSON>, menghentikan penggunaan aplikasi yang tidak lagi relevan dan menggantinya dengan satu aplikasi strategis dapat membantu menciptakan lingkungan operasional yang lebih efisien dan se<PERSON>hana, dengan pengurangan risiko dan biaya operasional, karena jumlah aplikasi yang perlu dikelola jadi jauh lebih sedikit.", "Mari kita pelajari lebih lanjut bagaimana tim Pemilik Proses menggunakan artefak dari CEAM dan Proses Intelijen untuk menciptakan infrastruktur aplikasi yang terintegrasi, menyelaraskan data antar tim, serta meningkatkan pengalaman klien. "], "text21": "Pen<PERSON><PERSON><PERSON>an Teknologi Diterapkan", "text22": "Dengan memanfa<PERSON>kan <PERSON>, tim ber<PERSON><PERSON> mengidentifikasi fungsi pembayaran utama dan aplikasi pendukung yang digunakan di berbagai Proses melalui CEAM. ", "text23": "Pemilik Proses yang sebelumnya menggunakan aplikasi lama dengan status “Dihentikan” mulai memperbarui Proses mereka dan beralih ke ‘Citi Payments Express’. Hasilnya? Jumlah aplikasi untuk fungsi pembayaran berkurang, pengalaman klien meningkat, dan <PERSON><PERSON> menunjukkan komitmennya untuk terus menghadirkan inovasi yang positif.", "text27": "Kini Lorenzo tidak lagi perlu menggunakan beberapa aplikasi untuk membayar pemasoknya di AS dan Inggris. Ia dapat menggunakan satu platform saja untuk memproses dan memantau pembayaran digitalnya. Dengan sistem yang lebih sederhana, ia bahkan dapat mulai menjajaki peluang efisiensi biaya dengan mencari pemasok dari negara lain. ", "text28": "<PERSON><PERSON><PERSON>, bank telah menyediakan proses yang lebih kohesif dan efisien untuk tim internal dan pengalaman klien yang jauh lebih lancar bagi Lorenzo."}, "footer": {"text1": "Sekarang mari kita telusuri siapa yang menggunakan Tata Kelola Proses dan Arsitektur Perusahaan di Citi. "}}, "page7": {"sectionTop": {"title1": "Siapa yang Bertanggung Jawab untuk Menerapkan Tata Kelola Proses dan Arsitektur Perusahaan?"}, "section1": {"text1": "<PERSON><PERSON>, tujuan Arsitektur Perusahaan adalah untuk menyelaraskan tujuan bisnis Citi dengan strategi aplikasi bisnis kita. Hal ini juga dapat membantu mengidentifikasi peluang dalam Proses kita menjadi lebih efektif dan e<PERSON>sien, se<PERSON><PERSON> menguntungkan bagi karyawan, k<PERSON><PERSON>, dan <PERSON><PERSON> secara keselu<PERSON>han. ", "text2": "<PERSON><PERSON><PERSON><PERSON><PERSON> dan pengel<PERSON>an Proses kita tidak dapat dicapai melalui proyek yang tertutup, melain<PERSON> oleh kita dengan: ", "list1": ["Mengubah cara kita melakukan tanggung jawab kita sehari-hari. ", "<PERSON>gg<PERSON>kan bahasa yang konsisten. ", "Menghubungkan informasi sehari-hari ke Proses untuk membantu membentuk Proses Intelijen. "], "text3": "Tata Kelola Proses dan Arsitektur Perusahaan berlaku bagi seluruh staf Citi secara umum, terutama ketika diminta untuk mengaitkan artefak ke dalam Proses di berbagai Sistem Pencatatan. Berdasarkan ketentuan yang ada, mereka bertanggung jawab atas keakuratan keterkaitan ketika memilih Proses untuk diasosiasikan.", "text3b": "Se<PERSON><PERSON> contoh, hal ini mungkin termasuk mengaitkan Proses dengan Komputasi Pengguna Akhir (EUC) atau Permintaan Investasi.", "text4": "Mari kita telusuri beberapa cara kita menggunakan Arsitektur Perusahaan dalam berbagai peran di Citi. ", "title1": "Bagaimana Cara Setia<PERSON>kan Tata Kelola Proses dan Arsitektur Peru<PERSON>? ", "text5": "<strong><PERSON><PERSON><PERSON> set<PERSON>p peran untuk menemukan tanggung jawab mereka dalam menerapkan Arsitektur Perusahaan.</strong>", "cards1": [{"id": "card1", "title": "Pemilik Komputasi Pengguna Akhir <span class='sr-only'> (E U C) </span><span aria-hidden='true'>(EUC)</span>", "text": ["Saat mencatat EUC baru, Pemilik EUC akan diminta untuk memilih Proses mana yang didukung oleh EUC mereka. <br><br>Pemilik Proses diwajibkan untuk meninjau dan menyetujui keterkaitan sebagai input dalam penilaian skor risiko Proses mereka. <PERSON><PERSON><PERSON>, EUC akan dimasukkan ke dalam Platform Penilaian Kepemilikan Proses (POA)."]}, {"id": "card2", "title": "Sponsor Investasi", "text": ["Sponsor In<PERSON><PERSON><PERSON> yang membuat Permintaan Investasi akan diminta untuk memilih Proses mana yang terkena dampak Permintaan Investasi mereka. <br><br>Sebagai bagian dari proses pembuatan dan perset<PERSON><PERSON>an investasi, Sponsor Inves<PERSON><PERSON> mungkin perlu menyediakan input Model Proses yang digunakan untuk mendorong perubahan teknologi dan mendukung identifikasi peluang Penyederhanaan Teknologi."]}, {"id": "card3", "title": "Pengguna Penilaian <PERSON> (MCA)", "text": ["Pemilik Proses membuat dan memelihara Profil MCA Proses Global (GPMP), yaitu profil yang mengaitkan Proses dengan Risiko dan Kontrol Referensi. <br><br>Pemilik Risiko dalam MCA akan mengidentifikasi dan menerapkan Proses beserta GPMP yang relevan untuk mengelola risiko terhadap tujuan pengelolaan Unit Penilaian. <br><br>Kontrol yang diterapkan ini akan terlihat oleh Pemilik Proses melalui Penilaian Kepemilikan Proses (POA)."]}], "text6": "Meskipun peran Anda mungkin tidak menggunakan Arsitektur Perusahaan dalam praktik sehari-hari, <PERSON><PERSON> mungkin akan terlibat dengannya di berbagai titik karena Taksonomi Proses digunakan dalam program inti seperti MCA dan EUC. <br><br><strong><PERSON><PERSON>rena itu, <PERSON><PERSON> berta<PERSON> jawab atas penerapan Arsitektur Perusahaan yang akurat saat menyederhanakan proses Citi bersama-sama</strong>.", "text7": "Untuk mempelajari lebih lanjut tentang cara berbagai area di Citi memanfaatkan Arsitektur Perusahaan, Anda dapat melihat halaman <a href='https://citi.sharepoint.com/sites/Enterprise-Architecture/SitePages/ReferenceMaterials.aspx' target='_blank'>Bahan Referensi Arsitektur Bisnis</a>."}, "footer": {"text1": "Mari meninjau apa saja yang telah kita pelajari dalam kursus ini sebelum penilaian akhir."}}, "page8": {"sectionTop": {"title1": "<PERSON><PERSON><PERSON>"}, "section1": {"text1": "Kita telah membahas hal-hal berikut dalam pelatihan ini:", "cards1": [{"text": "Citi menerapkan Tata Kelola Proses dan Arsitektur Bisnis sebagai pendekatan yang berpusat pada proses untuk menyederhanakan dan mengurangi risikonya, yang pada akhirnya bertujuan untuk meningkatkan pengalaman klien."}, {"text": "Arsitektur Bisnis mengatur tiga Taksonomi: <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, dan <PERSON><PERSON><PERSON> yang Dikendalikan Konsep Data. "}, {"paragraphs": ["Tata Kelola Proses dan Arsitektur Bisnis mendukung kita untuk lebih memahami posisi saat ini dan cara agar dapat mencapai tujuan yang kita inginkan. ", "Hal ini dilakukan dengan mengidentifikasi celah dan peluang potensial untuk membantu menyelaraskan aplikasi bisnis Citi dengan tujuan bisnisnya."]}, {"text": "Penyederhanaan Teknologi mendorong konsolidasi aplikasi yang memiliki fungsi serupa atau tumpang tindih. Tata Kelola Proses mendorong peningkatan Proses secara keseluruhan."}, {"paragraphs": ["<PERSON> beberapa peran khusus yang akan menerapkan Arsitektur Perusahaan sebagai bagian dari tanggung jawab mereka. ", "<PERSON><PERSON>, keberhasilan Arsitektur Perusahaan dalam menyederhanakan Proses di Citi sangat bergantung pada tanggung jawab setiap karyawan Citi untuk menerapkan Proses dengan benar dan mematuhi Tata Kelola Proses."]}], "text2": "Tinjau <a href='https://citi.hostedconnectedrisk.com/CitiCPD/react?page=O&CID=sid552268563&DeletedOnly=0&HTMFile=instancePolicyUser_View.htm&InstanceNo=2001487&NodeNo=182&inlineEdit=0&originalRequestNodeNo=182&pageType=42&resetTreeView=0&rightClickContextMenu=0&runOnce=1&scrollableViewer=1&treeViewNode0=253&treeViewNode0OverviewPage=instancePolicyVersionAuthor_Title.htm&treeViewNode1=281&treeViewNode10=371&treeViewNode10OverviewPage=instancePolicySectionLevel10VersionAuthorView.htm&treeViewNode1OverviewPage=instancePolicySectionLevel1VersionAuthor_View.htm&treeViewNode2=282&treeViewNode2OverviewPage=instancePolicySectionLevel2VersionAuthor_View.htm&treeViewNode3=283&treeViewNode3OverviewPage=instancePolicySectionLevel3VersionAuthor_View.htm&treeViewNode4=285&treeViewNode4OverviewPage=instancePolicySectionLevel4VersionAuthor_View.htm&treeViewNode5=284&treeViewNode5OverviewPage=instancePolicySectionLevel5VersionAuthor_View.htm&treeViewNode6=372&treeViewNode6OverviewPage=instancePolicySectionLevel6VersionAuthorView.htm&treeViewNode7=373&treeViewNode7OverviewPage=instancePolicySectionLevel7VersionAuthorView.htm&treeViewNode8=374&treeViewNode8OverviewPage=instancePolicySectionLevel8VersionAuthorView.htm&treeViewNode9=375&treeViewNode9OverviewPage=instancePolicySectionLevel9VersionAuthorView.htm&useCustomFieldForAddChild=0&useOriginalRequestNodeAsCurrent=1&usingpost=1' target='_blank'>Kebijakan Tata Kelola Proses dan Arsitektur Perusahaan (EAPGP, Enterprise Architecture and Process Governance Policy)</a> untuk mengetahui lebih lanjut mengenai Kebijakan Tata Kelola Proses dan Arsitektur Perusahaan yang harus dipatuhi oleh seluruh karyawan, kontraktor, konsultan, dan pihak-pihak yang berkewajiban untuk mematuhi kebijakan-kebijakan Citi.", "text3": "<PERSON><PERSON>, <PERSON><PERSON><PERSON> Kelola Proses dan Arsitektur Perusahaan akan mengh<PERSON><PERSON>an pen<PERSON><PERSON><PERSON> dan e<PERSON><PERSON>, yang menguntungkan semua orang di Citi dan klien kita."}, "footer": {"text1": "Sekarang saatnya menilai pen<PERSON>an <PERSON>."}}, "page11": {"topSection": {"title": "<PERSON><PERSON><PERSON>", "text1": "Selamat! Anda telah memenuhi persyaratan penyelesaian kursus. <PERSON><PERSON><PERSON>, <PERSON>a dapat mempelajari kembali konten atau keluar dari kursus.", "text2": "Untuk keluar dari k<PERSON>, klik “X” di sudut kanan atas jendela browser."}}}