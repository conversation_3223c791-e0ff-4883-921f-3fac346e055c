<template>
	<div>
		<div style="display: none;">

            <!-- ////////////////////////////////////////////////////// -->
            <!-- 40 images so far - update when you add !!!! ////////// -->
            <!-- /////////////////////////////////////////////////////// -->


		</div>

		<router-view  />
	</div>
</template>

<script>
import { defineComponent, ref, onMounted } from 'vue'
import { useStore } from 'stores/store';
import { useCore } from "stores/composables/core";
import { useAos } from "stores/composables/aos";

export default defineComponent({
	name: 'App',

	setup() {
		const imagesAllLoaded = ref(false);
		const imagesLoaded = ref(0);

		const store = useStore();
		const core = useCore();
		const aos = useAos();

		onMounted(() => {
			// AOS Init
			aos.initAOS()
		});


		function imgLoaded() {
			imagesLoaded.value = imagesLoaded.value + 1;

			// update it based on the number of main images
			if (imagesLoaded.value == 40) {
				imagesAllLoaded.value = true;
				console.log("***------ imgLoaded ------***", imagesLoaded.value );
			}
		}

		return {
			store,
			imagesAllLoaded,
			imagesLoaded,
			imgLoaded
		}

	}
})
</script>
