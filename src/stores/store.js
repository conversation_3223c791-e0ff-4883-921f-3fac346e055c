import { defineStore } from "pinia";
import { SCORM } from "pipwerks-scorm-api-wrapper";

import { arraysEqual } from "./composables/utils";
import { useAos } from "./composables/aos";

export const useStore = defineStore("store", {
    state: () => ({
        aosEnable: true,
        // showBadgeComplete - shows the green checkicon in the top nav, it is turned off when the leaner selects "start" button in the animation
        showBadgeComplete: false,

        // Animations Toggle

        // Audio Toggle
        audioEnable: true,

		gated: false,

        com: {
            // properties used for the new connection Issues check
            willCheck_Connection: true,
            willCheck_SCORMApi: false,
            lmsError: {
                callSucceeded: true,
                lastErrorCode: 0,
                lastErrorMsg: ""
            },
            checkingConnection: false,

            // general settings/data
            settings: {
                active: false,
                suspendData: {},
                location: null, // current location (activePage)
                interactions: [],
            },
        },
        rte: {
            settings: {
                return: false, // false: empty suspend data; true: something came from suspend data
                location: null // location saved on the LMS retrieved and updated only on Init
            },
        },
        manifest: {
            // settings more related to general configuration of the App
            settings: {
                language: "en",
                tracking: {
                    version: "2004", // SCORM VERSION
                    on: true,
                },
                nonEmployee: false
            },

            // list of pages and statuses
            content: [
                // change the ids to match path names in router/index.js
                {
                    id: "page1",
                    status: 0 ,
                },
                {
                    id: "page2",
                    status: 0,
                },
                {
                    id: "page3",
                    status: 0,
                },
                {
                    id: "page4",
                    status: 0,
                },
                {
                    id: "page5",
                    status: 0,
                },
                {
                    id: "page6",
                    status: 0,
                },
                {
                    id: "page7",
                    status: 0,
                },
                {
                    id: "page8",
                    status: 0,
                },

                {
                    id: "page11",
                    status: 0,
                },
            ],
        },

        // used to manage the sesstion time
        timeUtility: {
            timeInitialized: null
        }
    }),




    getters: {
        // only content pages, excluding intro and final
        getPagesCompleted: (state) => {
            let pagesCompleted = 0;
            state.manifest.content.forEach((element) => {
                if ( (element.id !== 'pageintro') && (element.id !== 'pagefinal') && element.status==1 ) {
                    pagesCompleted = pagesCompleted + 1;
                }
            });
            return pagesCompleted;
        },

        getPageStatusById: (state) => (pageId) => {
            const item = state.manifest.content.find((item) => item.id === pageId);
            return item ? item.status : null;
        },

        getNonEmployee: (state) => {
            return state.manifest.settings.nonEmployee;
        },

        getActivePage: (state) => {
            return state.com.settings.location;
        },

        rte_getReturn: (state) => {
            return state.rte.settings.return;
        },

        // ------------------------------------------------------------------------------------------
        // Uses SCORM_API. Actually goes to the LMS to get data
        // ------------------------------------------------------------------------------------------
        com_get: () => (cmiParam) => {
            console.log("COM", "Getting: '" + cmiParam + "'");
            var returnValue = SCORM.get(cmiParam);
            return returnValue;
        },

        com_getLocation: (state) => {
            return state.com.settings.location;
        },

        com_getLanguage: (getters) => {
            return getters.com_get("cmi.learner_preference.language");
        },

        com_getAllInteractions: (getters) => {
            // debugger;
            var interactionsChildrenArr = ["id", "type", "learner_response", "description", "result"];
            var interactions = [];
            var interactionsCount = parseInt(getters.com_get("cmi.interactions._count"), 10);
            if (interactionsCount > 0) {
                for (var n = 0; n < interactionsCount; n++) {
                    var interaction = {
                        cmiIndex: n.toString()
                    };
                    for (var i = 0; i < interactionsChildrenArr.length; i++) {
                        var key = interactionsChildrenArr[i];
                        var keyString = "cmi.interactions." + n + "." + key;
                        interaction[key] = getters.com_get(keyString);
                    }

                    // fix interaction.result
                    // not being used anymore, we're now using LMS default "correct"/"incorrect"
                    // if (interaction.result == "correct") {
                    //     interaction.result = true;
                    // } else {
                    //     interaction.result = false;
                    // }

                    interaction.submitted = true;

                    interactions.push(interaction);
                }
            }
            return interactions;
        },

        // get the raw data - core - It might RETURN EMPTY
        getInteraction: (state) => (interactionID) => {
            const foundInteraction = state.com.settings.interactions.slice().reverse().find((el) => {
                return el.id === interactionID
            });

            return foundInteraction;
        },

        // uses getInteraction, but treats the result, so you don't need to treat it in page, IT WILL ALWAYS RETURN AN OBJECT
        getInteractionSubmitted: (getters) => (interactionID) => {
            const interaction = getters.getInteraction(interactionID);

            if  (interaction && interaction.submitted) {
                return interaction
            }
            else {
                return {
                    id: interactionID,
                    submitted: false,
                    learner_response: null,
                    result: null,
                    cmiIndex: null
                }
            }
        },

        // check if all the interaction in the array has been submitted
        getInteractionsCompleted: (getters) => (interactionsArray, assessmentAttemptsPrefix = '') => {
            let allCompleted = true;
            interactionsArray.forEach((element) => {
                const interaction = getters.getInteraction(assessmentAttemptsPrefix + element);
                if (!(interaction && interaction.submitted)) {
                    allCompleted = false;
                }
            });
            return allCompleted;
        },

        // all interactions in the array have been submitted and correct
        getInteractionsPassed: (getters) => (interactionsArray) => {
            let allPassed = true;
            interactionsArray.forEach((element) => {
                const interaction = getters.getInteraction(element);
                if ( !(interaction && interaction.submitted && (interaction.result=='correct')) ) {
                    allPassed = false;
                }
            });
            return allPassed;
        },

        // at least one interaction in the array has been submited and incorrect
        getInteractionsFailed: (getters) => (interactionsArray) => {
            let foundIncorrect = false;
            interactionsArray.forEach((element) => {
                const interaction = getters.getInteraction(element);
                if ( interaction && interaction.submitted && (interaction.result=='incorrect') ) {
                    foundIncorrect = true;
                }
            });
            return foundIncorrect;
        },

        getInteractionsScore: (getters) => (interactionsArray, assessmentAttemptsPrefix) => {
            let totalScore = 0;
            interactionsArray.forEach((element) => {
                const interaction = getters.getInteraction(assessmentAttemptsPrefix + element);
                if (interaction && interaction.submitted && (interaction.result == 'correct')) {
                    totalScore++;
                }
            });

            const calcScore = Math.round((totalScore / interactionsArray.length) * 100);
            return calcScore;
        },

        // updated - apr 2025
        getInteractionsFailedCount: (getters) => (interactionsArray) => {
            let foundIncorrect = false;
            let count = 0
            interactionsArray.forEach((element) => {
                const interaction = getters.getInteraction(element);
                if (
                    interaction &&
                    interaction.submitted &&
                    interaction.result == "incorrect"
                ) {
                    foundIncorrect = true;
                    count++;
                    console.log('counting ***********',count)
                }
            });
                return count;
        },

        getTestoutScore: (getters) => (interactionsArray) => {
            let totalScore = 0;
            interactionsArray.forEach((element) => {
                const interaction = getters.getInteraction(
                element
                );
                if (
                    interaction &&
                    interaction.submitted &&
                    interaction.result == "correct"
                ) {
                    totalScore++;
                }
            });

            const calcScore = Math.round(
                (totalScore / interactionsArray.length) * 100
            );
            return calcScore;
        }

    },




    actions: {

        // ------------------------------------------------------------------------------------------
        // Uses SCORM_API. Actually goes to the LMS to get data
        // ------------------------------------------------------------------------------------------
        com_init(payload) {
            var version = payload.version;
            var callback = payload.callback;

            // case SCORM
            SCORM.version = version;

            // First LMS Call
            console.log("COM", "Initializing course.");
            var callSucceeded = false;
            if (this.com.settings.active) {
                callSucceeded = true;
            } else {
                callSucceeded = SCORM.init();
            }
            console.log("COM", "Call succeeded? " + callSucceeded);

            if (callSucceeded) {
                this.com.settings.active = true;

                this.com_set({ key: "cmi.exit", value: "suspend" });

                this.sco_start();

                this.com_return_suspendData( this.com_get("cmi.suspend_data") );
                this.rte.settings.location = this.com_get("cmi.location");

                this.com.settings.interactions = this.com_getAllInteractions;
                console.log("*** this.com.settings.interactions ***", this.com.settings.interactions);
            } else {
                console.log("COM", "API Error: API not found.");
            }
            if (callback && typeof callback !== "undefined") {
                callback();
            }
        },


        rte_load(pageId) {
            // set the active page
            this.com_setLocation(pageId);

            this.rte_updateContentStatus(
                {
                    arg: pageId,
                    status: 0.5,
                }
            );
        },


        rte_init(callback) {
            if (this.manifest.settings.tracking.on) {
                this.com_init(
                    //payload for com_init
                    {
                        version: this.manifest.settings.tracking.version,
                        callback: () => {
                            // lets rip the suspend_data and write the status in the manifest
                            if (this.rte.settings.return) {
                                for (var k = 0; k < this.com.settings.suspendData.loStatus.length; k++) {
                                    for (var i = 0; i < this.manifest.content.length; i++) {
                                        if (this.com.settings.suspendData.loStatus[k].id == this.manifest.content[i].id) {
                                            this.manifest.content[i].status = this.com.settings.suspendData.loStatus[k].status;
                                        }
                                    }
                                }
                            } else {

                                // I think this will not be needed anymore because the rte_load is being triggered on onMounted of every page
                                // this.rte_load( this.manifest.content[0].id );

                            }

                            if (callback && typeof callback !== "undefined") {
                                callback();
                            }
                        },
                    });
            } else {
                if (callback && typeof callback !== "undefined") {
                    callback();
                }
            }
        },


        rte_updateContentStatus(payload) {
            var arg = payload.arg;
            var status = payload.status;

            console.log("rte_updateContentStatus", arg, status);

            var page = this.manifest.content.find(function(el) {
                return el.id === arg;
            });

            if (page.status != 1) {
                page.status = status;
                this.com_update_suspendData(arg);
            }
        },


        // ------------------------------------------------------------------------------------------
        // Uses SCORM_API. Actually goes to the LMS to get data
        // ------------------------------------------------------------------------------------------
        com_set(payload) {
            // debugger;
            var key = payload.key;
            var value = payload.value;

            console.log("COM:", "Sending: '" + value + "'");
            var callSucceeded = SCORM.set(key, value);

            var lastErrorCode = SCORM.debug.getCode();
            var lastErrorMsg = SCORM.debug.getInfo(lastErrorCode) + " --- " + SCORM.debug.getDiagnosticInfo(lastErrorCode);

            if (this.com.willCheck_SCORMApi) {
                if (!callSucceeded) {
                    this.com.lmsError = {
                        callSucceeded: callSucceeded,
                        lastErrorCode: lastErrorCode,
                        lastErrorMsg: lastErrorMsg
                    }
                }
            }
            else {
                this.com.lmsError = {
                    callSucceeded: true,
                    lastErrorCode: 0,
                    lastErrorMsg: ""
                }
            }

            console.log("COM:", "callSucceeded: " + callSucceeded);
            console.log("COM:", "lastErrorCode: " + lastErrorCode);
            console.log("COM:", "lastErrorMsg: " + lastErrorMsg);

            SCORM.save();
        },


        com_update_suspendData(pageId) {
            // count for progress
            var count = 0;

            // first time creating suspend data !("key" in obj)
            if (!("loStatus" in this.com.settings.suspendData)) {
                this.com.settings.suspendData.loStatus = [];
            }
            for (var i = 0; i < this.manifest.content.length; i++) {
                if (pageId === this.manifest.content[i].id) {
                    var found = false;
                    for (var j = 0; j < this.com.settings.suspendData.loStatus.length; j++) {
                        if (this.com.settings.suspendData.loStatus[j].id === pageId) {
                            this.com.settings.suspendData.loStatus[j].status = this.manifest.content[i].status;
                            console.log("update_suspendData", "pageId: " + pageId);
                            found = true;
                        }
                    }
                    if (!found) {
                        var loStatusObject = {
                            id: this.manifest.content[i].id,
                            status: this.manifest.content[i].status,
                        };
                        this.com.settings.suspendData.loStatus.push(loStatusObject);
                    }
                }
            }
            // count for progress
            for (var k = 0; k < this.manifest.content.length; k++) {
                if (this.manifest.content[k].status == 1) {
                    count++;
                }
            }
            this.com.settings.suspendData.progressMeasure = Math.round((count / this.manifest.content.length) * 100) / 100;
            this.com.settings.suspendData.pagescompleted = count;
            this.com_set(
                {
                    key: "cmi.suspend_data",
                    value: JSON.stringify(this.com.settings.suspendData),
                }
            );

            this.sco_set_session_time();

            if (this.com.settings.suspendData.progressMeasure == 1) {
                // progressMeasure to set complete
                this.com_setCompletion("completed");
                this.com_setSuccessStatus(
                    {
                        value: "passed",
                        set12Status: true,
                    }
                );
            }
        },


        com_return_suspendData(suspendData) {
            console.log("return_suspendData", suspendData);
            if (suspendData.length > 1 && suspendData != null && suspendData != "") {
                this.rte.settings.return = true;
                try {
                    var _suspendData = JSON.parse(suspendData);
                    if (typeof _suspendData.loStatus !== "undefined") {
                        this.com.settings.suspendData = _suspendData;
                    }
                } catch (error) {
                    console.log("Found cmi.suspendData, but encountered error parsing JSON, will reset.");
                    console.warn(error);
                }
            }
        },


        com_setLocation(pageId) {
            this.com_set(
                {
                    key: "cmi.location",
                    value: pageId,
                });

            this.com.settings.location = pageId;
        },


        com_setLanguage(lang) {
            this.com_set(
                {
                    key: "cmi.learner_preference.language",
                    value: lang,
                }
            );
        },


        com_setCompletion(value) {
            this.com_set(
                {
                    key: "cmi.completion_status",
                    value: value,
                }
            );
        },


        com_setSuccessStatus(payload) {
            var value = payload.value;

            this.com_set (
                {
                    key: "cmi.success_status",
                    value: value,
                }
            );
        },


        // ------------------------------------------------------------------------------------------
        // Uses SCORM_API. Actually goes to the LMS to get data
        // ------------------------------------------------------------------------------------------
        com_exit() {
            this.sco_set_session_time();

            console.log("COM", "Terminating connection.");

            var callSucceeded = SCORM.quit();

            console.log("COM", "Call succeeded? " + callSucceeded);
            if (callSucceeded) {
                top.window.close();
            }
        },


        setInteraction(
            param_id,
            param_learner_response,
            param_correct_responses,
            param_canResubmit)
        {
            // ------------------------------------------------------------------------
            // check if the interaction has already been submitted before
            // ------------------------------------------------------------------------
            const foundInteraction = this.getInteraction(param_id);
            if (foundInteraction && ! param_canResubmit) {
                return;
            }


            // ------------------------------------------------------------------------
            // prepare the new/updated interaction
            // ------------------------------------------------------------------------
            const interaction = {};

            // id
            interaction.id = param_id;
            // submitted
            interaction.submitted = true;
            // type
            interaction.type = "choice";
            // learner_response
            if (!param_learner_response) {
                interaction.learner_response = "";
            }
            else {
                interaction.learner_response = param_learner_response;
            }
            // result
            if (!param_correct_responses) {
                interaction.result = "correct";
            }
            else {
                interaction.result = arraysEqual(param_learner_response, param_correct_responses) ? "correct" : "incorrect";
            }

            // saving the cmiIndex incrementally, always incrementaly, so same interactionID will have different cmiID
            const cmiIndex = this.com.settings.interactions.length.toString();

            // new
            interaction.cmiIndex = cmiIndex;



            // --------------------------------------
            // LMS
            // --------------------------------------
            // cleaning lmsError
            this.com.lmsError = {
                callSucceeded: true,
                lastErrorCode: 0,
                lastErrorMsg: ""
            }

            var keyStem = "cmi.interactions." + cmiIndex + ".";

            this.com_set(
                {
                    key: keyStem + "id",
                    value: interaction.id,
                }
            );

            if (interaction.type) {
                this.com_set(
                    {
                        key: keyStem + "type",
                        value: interaction.type,
                    }
                );
            }

            if (interaction.learner_response) {
                this.com_set(
                    {
                        key: keyStem + "learner_response",
                        value: interaction.learner_response,
                    }
                );
            }

            if (interaction.result) {
                this.com_set(
                    {
                        key: keyStem + "result",
                        value: interaction.result,
                    }
                );
            }

            if (interaction.description) {
                this.com_set(
                    {
                        key: keyStem + "description",
                        value: interaction.description,
                    }
                );
            }

            // if it is set to no LMSCHECK the callSucceeded will always be true
            // however if it true and callSucceeded returns false it means that it didn't saved in LMS and so will not be pushed
            if (this.com.lmsError.callSucceeded) {
                this.com.settings.interactions.push(interaction);
            } else {
                console.log("***------ Error inside - setInteraction ------***");
            }

            // refreshes AOS
            const aos = useAos();
            aos.refreshAOS();
        },


        sco_start() {
            this.timeUtility.timeInitialized = new Date();
        },


        sco_set_session_time() {
            const timeUtility = {
                // used only when SCORM 1,2
                MillisecondsToCMIDuration: function(n) {
                    //Convert duration from milliseconds to 0000:00:00.00 format
                    let hms = "";
                    let dtm = new Date();
                    dtm.setTime(n);
                    let h = "000" + Math.floor(n / 3600000);
                    let m = "0" + dtm.getMinutes();
                    let s = "0" + dtm.getSeconds();
                    let cs = "0" + Math.round(dtm.getMilliseconds() / 10);

                    // old deprecated version
                    // hms = h.substr(h.length - 4) + ":" + m.substr(m.length - 2) + ":";
                    // hms += s.substr(s.length - 2) + "." + cs.substr(cs.length - 2);
                    // new one
                    hms = h.slice(-4) + ":" + m.slice(-2) + ":";
                    hms += s.slice(-2) + "." + cs.slice(-2);

                    return hms;
                },
                CentisecsToISODuration: function(n) {
                    n = Math.max(n, 0); // there is no such thing as a negative duration
                    var str = "P";
                    var nCs = n;
                    // Next set of operations uses whole seconds
                    var nY = Math.floor(nCs / 3155760000);
                    nCs -= nY * 3155760000;
                    var nM = Math.floor(nCs / 262980000);
                    nCs -= nM * 262980000;
                    var nD = Math.floor(nCs / 8640000);
                    nCs -= nD * 8640000;
                    var nH = Math.floor(nCs / 360000);
                    nCs -= nH * 360000;
                    var nMin = Math.floor(nCs / 6000);
                    nCs -= nMin * 6000;
                    // Now we can construct string
                    if (nY > 0) str += nY + "Y";
                    if (nM > 0) str += nM + "M";
                    if (nD > 0) str += nD + "D";
                    if (nH > 0 || nMin > 0 || nCs > 0) {
                        str += "T";
                        if (nH > 0) str += nH + "H";
                        if (nMin > 0) str += nMin + "M";
                        if (nCs > 0) str += nCs / 100 + "S";
                    }
                    if (str == "P") str = "PT0H0M0S"; // technically PT0S should do but SCORM test suite assumes longer form.
                    return str;
                }
            }

            if (this.timeUtility.timeInitialized) {
                var dtm = new Date();
                var n = dtm.getTime() - this.timeUtility.timeInitialized.getTime();
                this.com_set(
                    {
                        key: "cmi.session_time",
                        value: timeUtility.CentisecsToISODuration(Math.floor(n / 10)),
                    }
                );
            }
        },


    },

});
