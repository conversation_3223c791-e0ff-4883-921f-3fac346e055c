<svg xmlns="http://www.w3.org/2000/svg" width="350.209" height="360.019" viewBox="0 0 350.209 360.019">
  <g id="Group_146592" data-name="Group 146592" transform="translate(0 0)">
    <path id="Path_261797" data-name="Path 261797" d="M453.2,3896.507c0,10.566-73.809,19.13-164.858,19.13s-164.856-8.563-164.856-19.13,73.81-19.129,164.856-19.129,164.858,8.564,164.858,19.129" transform="translate(-113.238 -3555.618)" fill="#fff"/>
    <path id="Path_261798" data-name="Path 261798" d="M490.437,3899.531c0,10.415-72.732,18.856-162.453,18.856s-162.452-8.441-162.452-18.856,72.733-18.855,162.452-18.855,162.453,8.441,162.453,18.855" transform="translate(-151.796 -3558.642)" fill="#fefefe"/>
    <path id="Path_261799" data-name="Path 261799" d="M527.675,3902.554c0,10.264-71.656,18.583-160.049,18.583s-160.047-8.319-160.047-18.583,71.657-18.582,160.047-18.582,160.049,8.319,160.049,18.582" transform="translate(-190.353 -3561.665)" fill="#fdfdfd"/>
    <path id="Path_261800" data-name="Path 261800" d="M564.913,3905.577c0,10.113-70.579,18.309-157.644,18.309s-157.643-8.2-157.643-18.309,70.58-18.308,157.643-18.308,157.644,8.2,157.644,18.308" transform="translate(-228.911 -3564.688)" fill="#fcfdfd"/>
    <path id="Path_261801" data-name="Path 261801" d="M602.152,3908.6c0,9.962-69.5,18.035-155.24,18.035s-155.238-8.073-155.238-18.035,69.5-18.035,155.238-18.035,155.24,8.074,155.24,18.035" transform="translate(-267.47 -3567.713)" fill="#fbfcfc"/>
    <path id="Path_261802" data-name="Path 261802" d="M639.389,3911.625c0,9.811-68.426,17.762-152.835,17.762s-152.834-7.951-152.834-17.762,68.427-17.761,152.834-17.761,152.835,7.951,152.835,17.761" transform="translate(-306.027 -3570.736)" fill="#fafbfb"/>
    <path id="Path_261803" data-name="Path 261803" d="M676.628,3914.648c0,9.66-67.35,17.488-150.431,17.488s-150.429-7.829-150.429-17.488,67.35-17.488,150.429-17.488,150.431,7.829,150.431,17.488" transform="translate(-344.585 -3573.76)" fill="#f9fafa"/>
    <path id="Path_261804" data-name="Path 261804" d="M713.866,3917.672c0,9.509-66.273,17.215-148.026,17.215s-148.025-7.706-148.025-17.215,66.274-17.214,148.025-17.214,148.026,7.706,148.026,17.214" transform="translate(-383.143 -3576.783)" fill="#f8f9fa"/>
    <path id="Path_261805" data-name="Path 261805" d="M751.1,3920.7c0,9.358-65.2,16.941-145.622,16.941s-145.62-7.583-145.62-16.941,65.2-16.94,145.62-16.94S751.1,3911.339,751.1,3920.7" transform="translate(-421.701 -3579.806)" fill="#f7f8f9"/>
    <path id="Path_261806" data-name="Path 261806" d="M788.342,3923.719c0,9.207-64.12,16.667-143.217,16.667s-143.216-7.461-143.216-16.667,64.121-16.667,143.216-16.667,143.217,7.461,143.217,16.667" transform="translate(-460.259 -3582.83)" fill="#f6f8f8"/>
    <path id="Path_261807" data-name="Path 261807" d="M825.581,3926.742c0,9.055-63.044,16.394-140.813,16.394s-140.811-7.338-140.811-16.394,63.044-16.393,140.811-16.393,140.813,7.339,140.813,16.393" transform="translate(-498.817 -3585.853)" fill="#f5f7f7"/>
    <path id="Path_261808" data-name="Path 261808" d="M862.818,3929.765c0,8.9-61.967,16.12-138.408,16.12S586,3938.67,586,3929.765s61.968-16.119,138.407-16.119,138.408,7.216,138.408,16.119" transform="translate(-537.374 -3588.876)" fill="#f4f6f7"/>
    <path id="Path_261809" data-name="Path 261809" d="M900.057,3932.789c0,8.753-60.891,15.846-136,15.846s-136-7.093-136-15.846,60.891-15.846,136-15.846,136,7.094,136,15.846" transform="translate(-575.933 -3591.9)" fill="#f3f5f6"/>
    <path id="Path_261810" data-name="Path 261810" d="M937.294,3935.812c0,8.6-59.814,15.573-133.6,15.573s-133.6-6.971-133.6-15.573,59.815-15.572,133.6-15.572,133.6,6.971,133.6,15.572" transform="translate(-614.49 -3594.923)" fill="#f2f4f5"/>
    <path id="Path_261811" data-name="Path 261811" d="M974.533,3938.836c0,8.451-58.737,15.3-131.195,15.3s-131.193-6.848-131.193-15.3,58.738-15.3,131.193-15.3,131.195,6.849,131.195,15.3" transform="translate(-653.049 -3597.947)" fill="#f1f4f4"/>
    <path id="Path_261812" data-name="Path 261812" d="M1011.771,3941.859c0,8.3-57.661,15.026-128.79,15.026s-128.789-6.726-128.789-15.026,57.661-15.025,128.789-15.025,128.79,6.727,128.79,15.025" transform="translate(-691.606 -3600.97)" fill="#f0f3f4"/>
    <path id="Path_261813" data-name="Path 261813" d="M1049.009,3944.883c0,8.149-56.584,14.752-126.386,14.752s-126.384-6.6-126.384-14.752,56.585-14.751,126.384-14.751,126.386,6.6,126.386,14.751" transform="translate(-730.164 -3603.994)" fill="#eff2f3"/>
    <path id="Path_261814" data-name="Path 261814" d="M1086.247,3947.906c0,8-55.508,14.479-123.981,14.479s-123.98-6.481-123.98-14.479,55.508-14.478,123.98-14.478,123.981,6.481,123.981,14.478" transform="translate(-768.722 -3607.017)" fill="#eef1f2"/>
    <path id="Path_261815" data-name="Path 261815" d="M1123.484,3950.929c0,7.846-54.431,14.2-121.577,14.2s-121.575-6.358-121.575-14.2,54.432-14.2,121.575-14.2,121.577,6.359,121.577,14.2" transform="translate(-807.28 -3610.041)" fill="#edf0f1"/>
    <path id="Path_261816" data-name="Path 261816" d="M1160.723,3953.953c0,7.7-53.355,13.931-119.172,13.931s-119.171-6.236-119.171-13.931,53.355-13.931,119.171-13.931,119.172,6.237,119.172,13.931" transform="translate(-845.838 -3613.064)" fill="#eceff1"/>
    <path id="Path_261817" data-name="Path 261817" d="M1197.961,3956.976c0,7.544-52.278,13.657-116.768,13.657s-116.766-6.113-116.766-13.657,52.279-13.657,116.766-13.657,116.768,6.114,116.768,13.657" transform="translate(-884.395 -3616.087)" fill="#ebeff0"/>
    <path id="Path_261818" data-name="Path 261818" d="M1235.2,3960c0,7.393-51.2,13.384-114.363,13.384s-114.361-5.991-114.361-13.384,51.2-13.384,114.361-13.384S1235.2,3952.608,1235.2,3960" transform="translate(-922.954 -3619.111)" fill="#eaeeef"/>
    <path id="Path_261819" data-name="Path 261819" d="M1272.437,3963.023c0,7.242-50.125,13.11-111.958,13.11s-111.957-5.868-111.957-13.11,50.126-13.11,111.957-13.11,111.958,5.869,111.958,13.11" transform="translate(-961.512 -3622.134)" fill="#e9edee"/>
    <path id="Path_261820" data-name="Path 261820" d="M1309.675,3966.046c0,7.091-49.049,12.837-109.554,12.837s-109.552-5.746-109.552-12.837,49.049-12.836,109.552-12.836,109.554,5.747,109.554,12.836" transform="translate(-1000.069 -3625.157)" fill="#e8ecee"/>
    <path id="Path_261821" data-name="Path 261821" d="M1346.913,3969.07c0,6.94-47.972,12.563-107.149,12.563s-107.148-5.623-107.148-12.563,47.973-12.563,107.148-12.563,107.149,5.624,107.149,12.563" transform="translate(-1038.627 -3628.181)" fill="#e7ebed"/>
    <path id="Path_261822" data-name="Path 261822" d="M1384.151,3972.093c0,6.789-46.9,12.29-104.745,12.29s-104.743-5.5-104.743-12.29,46.9-12.289,104.743-12.289,104.745,5.5,104.745,12.289" transform="translate(-1077.185 -3631.204)" fill="#e6ebec"/>
    <path id="Path_261823" data-name="Path 261823" d="M1421.389,3975.116c0,6.638-45.819,12.016-102.34,12.016s-102.339-5.378-102.339-12.016,45.819-12.015,102.339-12.015,102.34,5.379,102.34,12.015" transform="translate(-1115.743 -3634.228)" fill="#e5eaeb"/>
    <path id="Path_261824" data-name="Path 261824" d="M1458.628,3978.14c0,6.487-44.743,11.742-99.936,11.742s-99.934-5.256-99.934-11.742,44.743-11.742,99.934-11.742,99.936,5.257,99.936,11.742" transform="translate(-1154.302 -3637.251)" fill="#e4e9eb"/>
    <path id="Path_261825" data-name="Path 261825" d="M1495.865,3981.163c0,6.336-43.666,11.469-97.531,11.469s-97.53-5.133-97.53-11.469,43.666-11.468,97.53-11.468,97.531,5.134,97.531,11.468" transform="translate(-1192.858 -3640.274)" fill="#e3e8ea"/>
    <path id="Path_261826" data-name="Path 261826" d="M1533.1,3984.187c0,6.184-42.589,11.2-95.127,11.2s-95.125-5.011-95.125-11.2,42.59-11.195,95.125-11.195,95.127,5.012,95.127,11.195" transform="translate(-1231.417 -3643.297)" fill="#e2e7e9"/>
    <path id="Path_261827" data-name="Path 261827" d="M1570.341,3987.21c0,6.033-41.513,10.921-92.722,10.921s-92.721-4.888-92.721-10.921,41.513-10.921,92.721-10.921,92.722,4.889,92.722,10.921" transform="translate(-1269.974 -3646.321)" fill="#e1e6e8"/>
    <path id="Path_261828" data-name="Path 261828" d="M1607.58,3990.233c0,5.882-40.436,10.648-90.318,10.648s-90.316-4.765-90.316-10.648,40.437-10.647,90.316-10.647,90.318,4.767,90.318,10.647" transform="translate(-1308.533 -3649.344)" fill="#e0e6e8"/>
    <path id="Path_261829" data-name="Path 261829" d="M1644.817,3993.257c0,5.731-39.36,10.374-87.913,10.374s-87.912-4.643-87.912-10.374,39.36-10.374,87.912-10.374,87.913,4.645,87.913,10.374" transform="translate(-1347.089 -3652.368)" fill="#dfe5e7"/>
    <path id="Path_261830" data-name="Path 261830" d="M1682.056,3996.28c0,5.58-38.283,10.1-85.509,10.1s-85.507-4.521-85.507-10.1,38.284-10.1,85.507-10.1,85.509,4.522,85.509,10.1" transform="translate(-1385.648 -3655.391)" fill="#dfe4e6"/>
    <path id="Path_261831" data-name="Path 261831" d="M1719.294,3999.3c0,5.429-37.207,9.827-83.1,9.827s-83.1-4.4-83.1-9.827,37.207-9.827,83.1-9.827,83.1,4.4,83.1,9.827" transform="translate(-1424.206 -3658.416)" fill="#dee3e5"/>
    <path id="Path_261832" data-name="Path 261832" d="M1756.532,4002.328c0,5.278-36.13,9.553-80.7,9.553s-80.7-4.276-80.7-9.553,36.13-9.553,80.7-9.553,80.7,4.277,80.7,9.553" transform="translate(-1462.764 -3661.439)" fill="#dde2e4"/>
    <path id="Path_261833" data-name="Path 261833" d="M1793.77,4005.352c0,5.127-35.054,9.28-78.3,9.28s-78.294-4.153-78.294-9.28,35.054-9.279,78.294-9.279,78.3,4.154,78.3,9.279" transform="translate(-1501.322 -3664.463)" fill="#dce2e4"/>
    <path id="Path_261834" data-name="Path 261834" d="M1831.009,4008.375c0,4.976-33.977,9.006-75.891,9.006s-75.889-4.031-75.889-9.006,33.977-9.006,75.889-9.006,75.891,4.032,75.891,9.006" transform="translate(-1539.88 -3667.486)" fill="#dbe1e3"/>
    <path id="Path_261835" data-name="Path 261835" d="M1868.246,4011.4c0,4.824-32.9,8.732-73.486,8.732s-73.485-3.908-73.485-8.732,32.9-8.732,73.485-8.732,73.486,3.909,73.486,8.732" transform="translate(-1578.437 -3670.509)" fill="#dae0e2"/>
    <path id="Path_261836" data-name="Path 261836" d="M1905.485,4014.422c0,4.674-31.824,8.459-71.082,8.459s-71.08-3.785-71.08-8.459,31.824-8.459,71.08-8.459,71.082,3.787,71.082,8.459" transform="translate(-1616.996 -3673.533)" fill="#d9dfe1"/>
    <path id="Path_261837" data-name="Path 261837" d="M1942.723,4017.445c0,4.522-30.747,8.185-68.677,8.185s-68.676-3.663-68.676-8.185,30.748-8.185,68.676-8.185,68.677,3.665,68.677,8.185" transform="translate(-1655.554 -3676.556)" fill="#d8dee1"/>
    <path id="Path_261838" data-name="Path 261838" d="M1979.961,4020.468c0,4.371-29.671,7.912-66.273,7.912s-66.271-3.541-66.271-7.912,29.671-7.911,66.271-7.911,66.273,3.542,66.273,7.911" transform="translate(-1694.111 -3679.579)" fill="#d7dde0"/>
    <path id="Path_261839" data-name="Path 261839" d="M2017.2,4023.492c0,4.22-28.595,7.638-63.868,7.638s-63.867-3.418-63.867-7.638,28.595-7.638,63.867-7.638,63.868,3.42,63.868,7.638" transform="translate(-1732.67 -3682.603)" fill="#d6dddf"/>
    <path id="Path_261840" data-name="Path 261840" d="M2054.437,4026.515c0,4.069-27.518,7.364-61.464,7.364s-61.462-3.3-61.462-7.364,27.518-7.364,61.462-7.364,61.464,3.3,61.464,7.364" transform="translate(-1771.227 -3685.626)" fill="#d5dcde"/>
    <path id="Path_261841" data-name="Path 261841" d="M2091.676,4029.539c0,3.918-26.442,7.091-59.059,7.091s-59.057-3.173-59.057-7.091,26.442-7.091,59.057-7.091,59.059,3.175,59.059,7.091" transform="translate(-1809.786 -3688.65)" fill="#d4dbde"/>
    <path id="Path_261842" data-name="Path 261842" d="M2128.912,4032.562c0,3.767-25.365,6.817-56.655,6.817s-56.653-3.05-56.653-6.817,25.365-6.817,56.653-6.817,56.655,3.052,56.655,6.817" transform="translate(-1848.342 -3691.673)" fill="#d3dadd"/>
    <path id="Path_261843" data-name="Path 261843" d="M2166.152,4035.585c0,3.616-24.288,6.543-54.25,6.543s-54.249-2.928-54.249-6.543,24.289-6.543,54.249-6.543,54.25,2.93,54.25,6.543" transform="translate(-1886.901 -3694.697)" fill="#d2d9dc"/>
    <path id="Path_261844" data-name="Path 261844" d="M2203.388,4038.609c0,3.465-23.212,6.27-51.845,6.27s-51.844-2.805-51.844-6.27,23.212-6.27,51.844-6.27,51.845,2.807,51.845,6.27" transform="translate(-1925.458 -3697.72)" fill="#d1d9db"/>
    <path id="Path_261845" data-name="Path 261845" d="M2240.627,4041.632c0,3.313-22.135,6-49.441,6s-49.439-2.683-49.439-6,22.135-6,49.439-6,49.441,2.685,49.441,6" transform="translate(-1964.017 -3700.743)" fill="#d0d8db"/>
    <path id="Path_261846" data-name="Path 261846" d="M2277.865,4044.656c0,3.162-21.059,5.723-47.036,5.723s-47.035-2.56-47.035-5.723,21.059-5.723,47.035-5.723,47.036,2.562,47.036,5.723" transform="translate(-2002.574 -3703.767)" fill="#cfd7da"/>
    <path id="Path_261847" data-name="Path 261847" d="M2315.1,4047.679c0,3.011-19.982,5.449-44.632,5.449s-44.63-2.438-44.63-5.449,19.982-5.449,44.63-5.449,44.632,2.44,44.632,5.449" transform="translate(-2041.132 -3706.79)" fill="#ced6d9"/>
    <path id="Path_261848" data-name="Path 261848" d="M2352.341,4050.7c0,2.86-18.906,5.176-42.228,5.176s-42.226-2.315-42.226-5.176,18.906-5.176,42.226-5.176,42.228,2.317,42.228,5.176" transform="translate(-2079.69 -3709.814)" fill="#cdd5d8"/>
    <path id="Path_261849" data-name="Path 261849" d="M2389.58,4053.726c0,2.709-17.829,4.9-39.823,4.9s-39.821-2.193-39.821-4.9,17.829-4.9,39.821-4.9,39.823,2.195,39.823,4.9" transform="translate(-2118.249 -3712.837)" fill="#ccd4d8"/>
    <path id="Path_261850" data-name="Path 261850" d="M2426.818,4056.749c0,2.558-16.753,4.628-37.418,4.628s-37.417-2.07-37.417-4.628,16.753-4.628,37.417-4.628,37.418,2.072,37.418,4.628" transform="translate(-2156.806 -3715.86)" fill="#cbd4d7"/>
    <path id="Path_261851" data-name="Path 261851" d="M2464.056,4059.773c0,2.407-15.676,4.354-35.014,4.354s-35.012-1.948-35.012-4.354,15.676-4.355,35.012-4.355,35.014,1.95,35.014,4.355" transform="translate(-2195.364 -3718.884)" fill="#cad3d6"/>
    <path id="Path_261852" data-name="Path 261852" d="M2501.294,4062.8c0,2.256-14.6,4.081-32.609,4.081s-32.608-1.825-32.608-4.081,14.6-4.081,32.608-4.081,32.609,1.827,32.609,4.081" transform="translate(-2233.922 -3721.907)" fill="#c9d2d5"/>
    <path id="Path_261853" data-name="Path 261853" d="M2538.532,4065.819c0,2.1-13.523,3.807-30.2,3.807s-30.2-1.7-30.2-3.807,13.523-3.808,30.2-3.808,30.2,1.705,30.2,3.808" transform="translate(-2272.48 -3724.93)" fill="#c8d1d5"/>
    <path id="Path_261854" data-name="Path 261854" d="M2575.77,4068.843c0,1.954-12.447,3.534-27.8,3.534s-27.8-1.58-27.8-3.534,12.447-3.534,27.8-3.534,27.8,1.583,27.8,3.534" transform="translate(-2311.038 -3727.954)" fill="#c7d0d4"/>
    <path id="Path_261855" data-name="Path 261855" d="M2613.008,4071.866c0,1.8-11.37,3.26-25.4,3.26s-25.394-1.458-25.394-3.26,11.37-3.26,25.394-3.26,25.4,1.46,25.4,3.26" transform="translate(-2349.595 -3730.977)" fill="#c6d0d3"/>
    <path id="Path_261856" data-name="Path 261856" d="M2650.247,4074.89c0,1.651-10.293,2.987-22.991,2.987s-22.99-1.335-22.99-2.987,10.293-2.987,22.99-2.987,22.991,1.338,22.991,2.987" transform="translate(-2388.154 -3734)" fill="#c5cfd2"/>
    <path id="Path_261857" data-name="Path 261857" d="M2687.484,4077.913c0,1.5-9.217,2.713-20.587,2.713s-20.585-1.213-20.585-2.713,9.217-2.713,20.585-2.713,20.587,1.215,20.587,2.713" transform="translate(-2426.711 -3737.024)" fill="#c4ced2"/>
    <path id="Path_261858" data-name="Path 261858" d="M2724.723,4080.938c0,1.349-8.14,2.439-18.182,2.439s-18.181-1.09-18.181-2.439,8.14-2.439,18.181-2.439,18.182,1.093,18.182,2.439" transform="translate(-2465.27 -3740.049)" fill="#c3cdd1"/>
    <path id="Path_261859" data-name="Path 261859" d="M2761.96,4083.961c0,1.2-7.064,2.166-15.778,2.166s-15.776-.968-15.776-2.166,7.064-2.166,15.776-2.166,15.778.97,15.778,2.166" transform="translate(-2503.826 -3743.072)" fill="#c2ccd0"/>
    <path id="Path_261860" data-name="Path 261860" d="M2799.2,4086.984c0,1.047-5.987,1.892-13.373,1.892s-13.372-.845-13.372-1.892,5.987-1.892,13.372-1.892,13.373.848,13.373,1.892" transform="translate(-2542.385 -3746.095)" fill="#c1cbcf"/>
    <path id="Path_261861" data-name="Path 261861" d="M2836.436,4090.008c0,.9-4.911,1.618-10.969,1.618s-10.967-.723-10.967-1.618,4.911-1.619,10.967-1.619,10.969.725,10.969,1.619" transform="translate(-2580.942 -3749.119)" fill="#c0cbcf"/>
    <path id="Path_261862" data-name="Path 261862" d="M2873.675,4093.031c0,.745-3.834,1.345-8.564,1.345s-8.563-.6-8.563-1.345,3.834-1.345,8.563-1.345,8.564.6,8.564,1.345" transform="translate(-2619.501 -3752.142)" fill="#bfcace"/>
    <path id="Path_261863" data-name="Path 261863" d="M2910.913,4096.054c0,.594-2.758,1.071-6.159,1.071s-6.158-.478-6.158-1.071,2.758-1.071,6.158-1.071,6.159.48,6.159,1.071" transform="translate(-2658.059 -3755.166)" fill="#bec9cd"/>
    <g id="Group_146591" data-name="Group 146591">
      <path id="Path_261865" data-name="Path 261865" d="M1784.208,923.019l11.393,42.695,31.3-31.3-11.395-42.695Z" transform="translate(-1636.147 -817.718)" fill="#e6ebed"/>
      <path id="Path_261867" data-name="Path 261867" d="M2849.449,3119.88l11.394,42.7,31.3-31.3-11.394-42.7Z" transform="translate(-2612.991 -2832.276)" fill="#e6ebed"/>
      <path id="Path_261868" data-name="Path 261868" d="M652.578,2054.649l11.393,42.7,31.3-31.3-11.393-42.7Z" transform="translate(-598.424 -1855.442)" fill="#e6ebed"/>
      <path id="Path_261869" data-name="Path 261869" d="M2024.122,31.3l11.392,42.7,31.3-31.3L2055.423,0Z" transform="translate(-1856.152 0.003)" fill="#e6ebed"/>
      <path id="Path_261870" data-name="Path 261870" d="M3705.7,958.472l11.393,42.7,31.3-31.3L3737,927.169Z" transform="translate(-3398.189 -850.229)" fill="#e6ebed"/>
      <path id="Path_261871" data-name="Path 261871" d="M0,1402.072l11.394,42.7,31.3-31.3L31.3,1370.77Z" transform="translate(0 -1257.018)" fill="#e6ebed"/>
      <path id="Path_261872" data-name="Path 261872" d="M1544.307,1814.721l11.392,42.7,31.3-31.3-11.393-42.7Z" transform="translate(-1416.154 -1635.423)" fill="#e6ebed"/>
      <path id="Path_261873" data-name="Path 261873" d="M648.421,101.842l42.7,11.393-31.3,31.3-42.7-11.394Z" transform="translate(-565.908 -93.391)" fill="#e6ebed"/>
      <path id="Path_261874" data-name="Path 261874" d="M408.5,993.568l42.7,11.394-31.3,31.3-42.7-11.394Z" transform="translate(-345.897 -911.118)" fill="#e6ebed"/>
      <path id="Path_261876" data-name="Path 261876" d="M3089.363,2228.161l11.393,42.7,31.3-31.3-11.394-42.7Z" transform="translate(-2832.996 -2014.555)" fill="#e6ebed"/>
      <path id="Path_261877" data-name="Path 261877" d="M2606.146,2059.574l42.7,11.392-31.3,31.3-42.7-11.394Z" transform="translate(-2361.174 -1888.663)" fill="#e6ebed"/>
      <path id="Path_261883" data-name="Path 261883" d="M1921.506,1783.417l11.457,42.761,42.7,11.393-11.458-42.76Zm17.71,36.482-6.871-25.641,25.6,6.833,6.871,25.641Z" transform="translate(-1762.052 -1635.422)" fill="#e6ebed"/>
      <g id="Group_146595" data-name="Group 146595">
        <path id="Path_261864" data-name="Path 261864" d="M2845.286,1167.083l42.7,11.393-31.3,31.3-42.7-11.395Z" transform="translate(-2580.468 -1070.233)" fill="#e6ebed"/>
        <path id="Path_261866" data-name="Path 261866" d="M2675.925,683.105l11.393,42.7,31.3-31.3L2707.226,651.8Z" transform="translate(-2453.866 -597.714)" fill="#e6ebed"/>
      </g>
      <path id="Path_262135" data-name="Path 262135" d="M1474.516,3191.214l42.7,11.393-31.3,31.3-42.7-11.394Z" transform="translate(-1323.45 -2926.396)" fill="#c2d3e0"/>
    </g>
    <g id="Group_146593" data-name="Group 146593">
      <path id="Path_261878" data-name="Path 261878" d="M2868.137,1598.434l-42.7-11.392-11.457-42.761,42.7,11.395Z" transform="translate(-2580.469 -1416.13)" fill="#255be3"/>
      <path id="Path_261879" data-name="Path 261879" d="M1323.84,1185.781l-42.7-11.392-11.457-42.761,42.7,11.395Z" transform="translate(-1164.323 -1037.721)" fill="#255be3"/>
      <path id="Path_261880" data-name="Path 261880" d="M1736.5,2730.078l-42.7-11.393-11.457-42.761,42.7,11.394Z" transform="translate(-1542.742 -2453.866)" fill="#255be3"/>
      <path id="Path_261881" data-name="Path 261881" d="M2161.406,891.717l11.458,42.76,42.7,11.392L2204.1,903.11Zm17.711,36.482-6.871-25.642,25.6,6.833,6.87,25.641Z" transform="translate(-1982.044 -817.719)" fill="#255be3"/>
      <path id="Path_261882" data-name="Path 261882" d="M2401.32,0l11.457,42.76,42.7,11.394-11.458-42.76Zm17.71,36.481-6.871-25.641,25.6,6.833,6.871,25.641Z" transform="translate(-2202.049 0.003)" fill="#255be3"/>
      <path id="Path_261884" data-name="Path 261884" d="M2334.928,3328.5l11.457,42.76,42.7,11.393-11.458-42.76Zm17.71,36.481-6.871-25.641,25.6,6.833,6.87,25.641Z" transform="translate(-2141.167 -3052.289)" fill="#255be3"/>
      <path id="Path_261885" data-name="Path 261885" d="M3190.618,808.069l-6.87-25.642,25.6,6.833,6.871,25.641Z" transform="translate(-2919.549 -717.499)" fill="#255be3"/>
      <path id="Path_261886" data-name="Path 261886" d="M2711.474,2592.354l-6.871-25.642,25.6,6.833,6.871,25.641Z" transform="translate(-2480.164 -2353.717)" fill="#255be3"/>
      <path id="Path_261887" data-name="Path 261887" d="M1167.182,2179.7l-6.871-25.642,25.6,6.833,6.87,25.641Z" transform="translate(-1064.024 -1975.306)" fill="#255be3"/>
      <path id="Path_261888" data-name="Path 261888" d="M3629.567,2360.047l-25.6-6.832-6.871-25.642,25.6,6.833Z" transform="translate(-3298.593 -2134.423)" fill="#255be3"/>
      <path id="Path_261889" data-name="Path 261889" d="M780.214,642.151l-25.6-6.831-6.871-25.643,25.6,6.833Z" transform="translate(-685.689 -559.085)" fill="#255be3"/>
    </g>
    <g id="Group_146594" data-name="Group 146594">
      <path id="Path_261890" data-name="Path 261890" d="M2831.206,1114.537l-31.3,31.3-.334-1.244,31.3-31.3Z" transform="translate(-2567.251 -1020.907)" fill="#0f1632"/>
      <path id="Path_261891" data-name="Path 261891" d="M2817.469,1063.262l-31.3,31.3-.334-1.244,31.3-31.3Z" transform="translate(-2554.654 -973.889)" fill="#0f1632"/>
      <path id="Path_261892" data-name="Path 261892" d="M2803.73,1011.988l-31.3,31.3-.333-1.244,31.3-31.3Z" transform="translate(-2542.056 -926.868)" fill="#0f1632"/>
      <path id="Path_261893" data-name="Path 261893" d="M2789.984,960.71l-31.3,31.3-.334-1.243,31.3-31.3Z" transform="translate(-2529.449 -879.847)" fill="#0f1632"/>
      <path id="Path_261894" data-name="Path 261894" d="M2776.247,909.436l-31.3,31.3-.333-1.244,31.3-31.3Z" transform="translate(-2516.854 -832.827)" fill="#0f1632"/>
      <path id="Path_261895" data-name="Path 261895" d="M2762.508,858.161l-31.3,31.3-.334-1.243,31.3-31.3Z" transform="translate(-2504.254 -785.807)" fill="#0f1632"/>
      <path id="Path_261896" data-name="Path 261896" d="M2748.773,806.883l-31.3,31.3-.333-1.243,31.3-31.3Z" transform="translate(-2491.659 -738.784)" fill="#0f1632"/>
      <path id="Path_261897" data-name="Path 261897" d="M2735.031,755.608l-31.3,31.3-.333-1.243,31.3-31.3Z" transform="translate(-2479.058 -691.765)" fill="#0f1632"/>
      <path id="Path_261898" data-name="Path 261898" d="M2721.286,704.331l-31.3,31.3-.333-1.244,31.3-31.3Z" transform="translate(-2466.453 -644.742)" fill="#0f1632"/>
      <path id="Path_261899" data-name="Path 261899" d="M2707.551,653.047l-31.3,31.3-.333-1.244,31.3-31.3Z" transform="translate(-2453.857 -597.714)" fill="#0f1632"/>
      <path id="Path_261900" data-name="Path 261900" d="M2845.286,1167.083l7.143,1.914-31.3,31.3-7.143-1.915Z" transform="translate(-2580.468 -1070.233)" fill="#0f1632"/>
      <path id="Path_261901" data-name="Path 261901" d="M2955.441,1196.6l-31.3,31.3-7.143-1.914,31.3-31.3Z" transform="translate(-2674.933 -1095.546)" fill="#0f1632"/>
      <path id="Path_261902" data-name="Path 261902" d="M3058.467,1224.2l-31.3,31.3-7.142-1.914,31.3-31.3Z" transform="translate(-2769.409 -1120.86)" fill="#0f1632"/>
      <path id="Path_261903" data-name="Path 261903" d="M3161.481,1251.809l-31.3,31.3-7.142-1.914,31.3-31.3Z" transform="translate(-2863.875 -1146.175)" fill="#0f1632"/>
      <path id="Path_261904" data-name="Path 261904" d="M3233.192,1310.711l-7.143-1.914,31.3-31.3,7.143,1.914Z" transform="translate(-2958.339 -1171.483)" fill="#0f1632"/>
      <path id="Path_261905" data-name="Path 261905" d="M3483.782,1767.117l-31.3,31.3-.333-1.244,31.3-31.3Z" transform="translate(-3165.674 -1619.335)" fill="#0f1632"/>
      <path id="Path_261906" data-name="Path 261906" d="M3470.038,1715.843l-31.3,31.3-.334-1.244,31.3-31.3Z" transform="translate(-3153.07 -1572.315)" fill="#0f1632"/>
      <path id="Path_261907" data-name="Path 261907" d="M3456.3,1664.564l-31.3,31.3-.334-1.244,31.3-31.3Z" transform="translate(-3140.472 -1525.292)" fill="#0f1632"/>
      <path id="Path_261908" data-name="Path 261908" d="M3442.563,1613.278l-31.3,31.3-.333-1.243,31.3-31.3Z" transform="translate(-3127.875 -1478.261)" fill="#0f1632"/>
      <path id="Path_261909" data-name="Path 261909" d="M3428.825,1562.013l-31.3,31.3-.333-1.245,31.3-31.3Z" transform="translate(-3115.277 -1431.251)" fill="#0f1632"/>
      <path id="Path_261910" data-name="Path 261910" d="M3415.083,1510.738l-31.3,31.3-.334-1.244,31.3-31.3Z" transform="translate(-3102.676 -1384.231)" fill="#0f1632"/>
      <path id="Path_261911" data-name="Path 261911" d="M3401.337,1459.463l-31.3,31.3-.334-1.243,31.3-31.3Z" transform="translate(-3090.07 -1337.211)" fill="#0f1632"/>
      <path id="Path_261912" data-name="Path 261912" d="M3387.6,1408.177l-31.3,31.3-.334-1.244,31.3-31.3Z" transform="translate(-3077.476 -1290.181)" fill="#0f1632"/>
      <path id="Path_261913" data-name="Path 261913" d="M3373.864,1356.907l-31.3,31.3-.333-1.244,31.3-31.3Z" transform="translate(-3064.877 -1243.166)" fill="#0f1632"/>
      <path id="Path_261914" data-name="Path 261914" d="M3360.119,1305.624l-31.3,31.3-.334-1.243,31.3-31.3Z" transform="translate(-3052.272 -1196.138)" fill="#0f1632"/>
      <path id="Path_261915" data-name="Path 261915" d="M1286.911,701.88l-31.3,31.3-.333-1.243,31.3-31.3Z" transform="translate(-1151.108 -642.493)" fill="#0f1632"/>
      <path id="Path_261916" data-name="Path 261916" d="M1273.174,650.6l-31.3,31.3-.334-1.243,31.3-31.3Z" transform="translate(-1138.51 -595.469)" fill="#0f1632"/>
      <path id="Path_261917" data-name="Path 261917" d="M1259.429,599.324l-31.3,31.3-.334-1.243,31.3-31.3Z" transform="translate(-1125.905 -548.448)" fill="#0f1632"/>
      <path id="Path_261918" data-name="Path 261918" d="M1245.684,548.049l-31.3,31.3-.335-1.244,31.3-31.3Z" transform="translate(-1113.301 -501.43)" fill="#0f1632"/>
      <path id="Path_261919" data-name="Path 261919" d="M1231.954,496.769l-31.3,31.3-.334-1.244,31.3-31.3Z" transform="translate(-1100.711 -454.405)" fill="#0f1632"/>
      <path id="Path_261920" data-name="Path 261920" d="M1218.215,445.506l-31.3,31.3-.333-1.244,31.3-31.3Z" transform="translate(-1088.112 -407.396)" fill="#0f1632"/>
      <path id="Path_261921" data-name="Path 261921" d="M1204.475,394.22l-31.3,31.3-.333-1.244,31.3-31.3Z" transform="translate(-1075.514 -360.365)" fill="#0f1632"/>
      <path id="Path_261922" data-name="Path 261922" d="M1190.734,342.945l-31.3,31.3L1159.1,373l31.3-31.3Z" transform="translate(-1062.912 -313.346)" fill="#0f1632"/>
      <path id="Path_261923" data-name="Path 261923" d="M1177,291.669l-31.3,31.3-.333-1.243,31.3-31.3Z" transform="translate(-1050.316 -266.325)" fill="#0f1632"/>
      <path id="Path_261924" data-name="Path 261924" d="M1163.254,240.383l-31.3,31.3-.333-1.244,31.3-31.3Z" transform="translate(-1037.713 -219.294)" fill="#0f1632"/>
      <path id="Path_261925" data-name="Path 261925" d="M1300.99,754.42l7.142,1.913-31.3,31.3-7.143-1.914Z" transform="translate(-1164.323 -691.815)" fill="#0f1632"/>
      <path id="Path_261926" data-name="Path 261926" d="M1411.148,783.933l-31.3,31.3-7.143-1.914,31.3-31.3Z" transform="translate(-1258.791 -717.123)" fill="#0f1632"/>
      <path id="Path_261927" data-name="Path 261927" d="M1514.167,811.538l-31.3,31.3-7.142-1.914,31.3-31.3Z" transform="translate(-1353.262 -742.438)" fill="#0f1632"/>
      <path id="Path_261928" data-name="Path 261928" d="M1617.187,839.142l-31.3,31.3-7.143-1.914,31.3-31.3Z" transform="translate(-1447.731 -767.753)" fill="#0f1632"/>
      <path id="Path_261929" data-name="Path 261929" d="M1688.906,898.047l-7.142-1.913,31.3-31.3,7.142,1.914Z" transform="translate(-1542.205 -793.063)" fill="#0f1632"/>
      <path id="Path_261930" data-name="Path 261930" d="M1761.432,474.282l.334,1.243-16.56,16.56-1.245-.334Z" transform="translate(-1599.241 -434.924)" fill="#0f1632"/>
      <path id="Path_261931" data-name="Path 261931" d="M1812.989,530.055,1799.742,543.3l-1.245-.333,14.158-14.158Z" transform="translate(-1649.252 -484.929)" fill="#0f1632"/>
      <path id="Path_261932" data-name="Path 261932" d="M1864.222,584.6l-9.934,9.934-1.243-.334,10.844-10.844Z" transform="translate(-1699.272 -534.944)" fill="#0f1632"/>
      <path id="Path_261933" data-name="Path 261933" d="M1915.439,639.136l-6.621,6.621-1.244-.333,7.532-7.531Z" transform="translate(-1749.276 -584.958)" fill="#0f1632"/>
      <path id="Path_261934" data-name="Path 261934" d="M1963.354,696.976l-1.244-.334,4.219-4.218.333,1.244Z" transform="translate(-1799.287 -634.964)" fill="#0f1632"/>
      <path id="Path_261935" data-name="Path 261935" d="M2016.646,747.858l.906-.906.331,1.237Z" transform="translate(-1849.297 -684.968)" fill="#0f1632"/>
      <path id="Path_261936" data-name="Path 261936" d="M1939.489,1354.46l-31.3,31.3-.333-1.243,31.3-31.3Z" transform="translate(-1749.532 -1240.921)" fill="#0f1632"/>
      <path id="Path_261937" data-name="Path 261937" d="M1925.747,1303.177l-31.3,31.3-.334-1.244,31.3-31.3Z" transform="translate(-1736.93 -1193.893)" fill="#0f1632"/>
      <path id="Path_261938" data-name="Path 261938" d="M1912.005,1251.9l-31.3,31.3-.334-1.244,31.3-31.3Z" transform="translate(-1724.33 -1146.87)" fill="#0f1632"/>
      <path id="Path_261939" data-name="Path 261939" d="M1898.261,1200.624l-31.3,31.3-.334-1.243,31.3-31.3Z" transform="translate(-1711.725 -1099.851)" fill="#0f1632"/>
      <path id="Path_261940" data-name="Path 261940" d="M1884.532,1149.337l-31.3,31.3-.333-1.243,31.3-31.3Z" transform="translate(-1699.135 -1052.819)" fill="#0f1632"/>
      <path id="Path_261941" data-name="Path 261941" d="M1870.789,1098.071l-31.3,31.3-.334-1.243,31.3-31.3Z" transform="translate(-1686.533 -1005.809)" fill="#0f1632"/>
      <path id="Path_261942" data-name="Path 261942" d="M1857.052,1046.8l-31.3,31.3-.333-1.243,31.3-31.3Z" transform="translate(-1673.938 -958.79)" fill="#0f1632"/>
      <path id="Path_261943" data-name="Path 261943" d="M1843.308,995.521l-31.3,31.3-.333-1.244,31.3-31.3Z" transform="translate(-1661.333 -911.769)" fill="#0f1632"/>
      <path id="Path_261944" data-name="Path 261944" d="M1829.57,944.235l-31.3,31.3-.334-1.245,31.3-31.3Z" transform="translate(-1648.734 -864.737)" fill="#0f1632"/>
      <path id="Path_261945" data-name="Path 261945" d="M1815.834,892.961l-31.3,31.3-.333-1.245,31.3-31.3Z" transform="translate(-1636.139 -817.719)" fill="#0f1632"/>
      <path id="Path_261946" data-name="Path 261946" d="M1953.569,1407l7.143,1.913-31.3,31.3-7.143-1.914Z" transform="translate(-1762.75 -1290.238)" fill="#0f1632"/>
      <path id="Path_261947" data-name="Path 261947" d="M2063.725,1436.513l-31.3,31.3-7.142-1.913,31.3-31.3Z" transform="translate(-1857.214 -1315.55)" fill="#0f1632"/>
      <path id="Path_261948" data-name="Path 261948" d="M2166.745,1464.109l-31.3,31.3-7.143-1.914,31.3-31.3Z" transform="translate(-1951.686 -1340.856)" fill="#0f1632"/>
      <path id="Path_261949" data-name="Path 261949" d="M2269.765,1491.721l-31.3,31.3-7.143-1.914,31.3-31.3Z" transform="translate(-2046.156 -1366.177)" fill="#0f1632"/>
      <path id="Path_261950" data-name="Path 261950" d="M2341.478,1550.628l-7.143-1.914,31.3-31.3,7.143,1.913Z" transform="translate(-2140.623 -1391.491)" fill="#0f1632"/>
      <path id="Path_261951" data-name="Path 261951" d="M2414.012,1126.849l.334,1.244-16.56,16.56-1.245-.334Z" transform="translate(-2197.668 -1033.339)" fill="#0f1632"/>
      <path id="Path_261952" data-name="Path 261952" d="M2465.561,1182.624l-13.247,13.246-1.245-.333,14.159-14.158Z" transform="translate(-2247.67 -1083.343)" fill="#0f1632"/>
      <path id="Path_261953" data-name="Path 261953" d="M2516.8,1237.165l-9.933,9.933-1.243-.333,10.844-10.844Z" transform="translate(-2297.696 -1133.359)" fill="#0f1632"/>
      <path id="Path_261954" data-name="Path 261954" d="M2568.014,1291.7l-6.621,6.621-1.244-.333,7.532-7.531Z" transform="translate(-2347.697 -1183.372)" fill="#0f1632"/>
      <path id="Path_261955" data-name="Path 261955" d="M2615.931,1349.542l-1.244-.333,4.219-4.22.333,1.245Z" transform="translate(-2397.711 -1233.376)" fill="#0f1632"/>
      <path id="Path_261956" data-name="Path 261956" d="M2669.217,1400.436l.906-.906.331,1.238Z" transform="translate(-2447.715 -1283.392)" fill="#0f1632"/>
      <path id="Path_261957" data-name="Path 261957" d="M2349.126,1097.231l-1.916-7.141,12.516-12.515,7.145,1.912Z" transform="translate(-2152.43 -988.153)" fill="#0f1632"/>
      <path id="Path_261958" data-name="Path 261958" d="M2321.489,1063.4l-1.917-7.14,6.258-6.258,7.145,1.912Z" transform="translate(-2127.085 -962.864)" fill="#0f1632"/>
      <path id="Path_261959" data-name="Path 261959" d="M2299.092,1024.339l-5.228,5.228-1.916-7.139Z" transform="translate(-2101.754 -937.583)" fill="#0f1632"/>
      <path id="Path_261960" data-name="Path 261960" d="M2592.054,2007.029l-31.3,31.3-.334-1.243,31.3-31.3Z" transform="translate(-2347.944 -1839.337)" fill="#0f1632"/>
      <path id="Path_261961" data-name="Path 261961" d="M2578.323,1955.753l-31.3,31.3-.334-1.245,31.3-31.3Z" transform="translate(-2335.354 -1792.316)" fill="#0f1632"/>
      <path id="Path_261962" data-name="Path 261962" d="M2564.582,1904.478l-31.3,31.3-.333-1.243,31.3-31.3Z" transform="translate(-2322.753 -1745.297)" fill="#0f1632"/>
      <path id="Path_261963" data-name="Path 261963" d="M2550.841,1853.2l-31.3,31.3-.334-1.244,31.3-31.3Z" transform="translate(-2310.151 -1698.278)" fill="#0f1632"/>
      <path id="Path_261964" data-name="Path 261964" d="M2537.108,1801.916l-31.3,31.3-.333-1.244,31.3-31.3Z" transform="translate(-2297.559 -1651.246)" fill="#0f1632"/>
      <path id="Path_261965" data-name="Path 261965" d="M2523.37,1750.661l-31.3,31.3-.333-1.244,31.3-31.3Z" transform="translate(-2284.96 -1604.245)" fill="#0f1632"/>
      <path id="Path_261966" data-name="Path 261966" d="M2509.621,1699.366l-31.3,31.3-.334-1.244,31.3-31.3Z" transform="translate(-2272.353 -1557.204)" fill="#0f1632"/>
      <path id="Path_261967" data-name="Path 261967" d="M2495.886,1648.088l-31.3,31.3-.334-1.244,31.3-31.3Z" transform="translate(-2259.758 -1510.182)" fill="#0f1632"/>
      <path id="Path_261968" data-name="Path 261968" d="M2482.146,1596.812l-31.3,31.3-.333-1.243,31.3-31.3Z" transform="translate(-2247.158 -1463.162)" fill="#0f1632"/>
      <path id="Path_261969" data-name="Path 261969" d="M2468.412,1545.526l-31.3,31.3-.333-1.245,31.3-31.3Z" transform="translate(-2234.563 -1416.13)" fill="#0f1632"/>
      <path id="Path_261970" data-name="Path 261970" d="M2606.146,2059.574l7.143,1.914-31.3,31.3-7.142-1.914Z" transform="translate(-2361.174 -1888.663)" fill="#0f1632"/>
      <path id="Path_261971" data-name="Path 261971" d="M2716.3,2089.082l-31.3,31.3-7.143-1.914,31.3-31.3Z" transform="translate(-2455.636 -1913.966)" fill="#0f1632"/>
      <path id="Path_261972" data-name="Path 261972" d="M2819.321,2116.692l-31.3,31.3-7.142-1.914,31.3-31.3Z" transform="translate(-2550.109 -1939.286)" fill="#0f1632"/>
      <path id="Path_261973" data-name="Path 261973" d="M2922.341,2144.3l-31.3,31.3-7.142-1.914,31.3-31.3Z" transform="translate(-2644.579 -1964.601)" fill="#0f1632"/>
      <path id="Path_261974" data-name="Path 261974" d="M2994.055,2203.2l-7.143-1.914,31.3-31.3,7.143,1.915Z" transform="translate(-2739.047 -1989.912)" fill="#0f1632"/>
      <path id="Path_261975" data-name="Path 261975" d="M3066.584,1779.436l.333,1.243-16.559,16.56-1.245-.333Z" transform="translate(-2796.086 -1631.771)" fill="#0f1632"/>
      <path id="Path_261976" data-name="Path 261976" d="M3118.14,1835.2l-13.247,13.247-1.245-.333,14.158-14.158Z" transform="translate(-2846.096 -1681.767)" fill="#0f1632"/>
      <path id="Path_261977" data-name="Path 261977" d="M3169.369,1889.745l-9.934,9.933-1.244-.333,10.844-10.844Z" transform="translate(-2896.112 -1731.786)" fill="#0f1632"/>
      <path id="Path_261978" data-name="Path 261978" d="M3220.594,1944.281l-6.621,6.621-1.243-.333,7.531-7.531Z" transform="translate(-2946.125 -1781.796)" fill="#0f1632"/>
      <path id="Path_261979" data-name="Path 261979" d="M3268.5,2002.119l-1.245-.334,4.219-4.219.333,1.245Z" transform="translate(-2996.129 -1831.8)" fill="#0f1632"/>
      <path id="Path_261980" data-name="Path 261980" d="M3321.791,2053.013l.906-.906.331,1.237Z" transform="translate(-3046.136 -1881.815)" fill="#0f1632"/>
      <path id="Path_261981" data-name="Path 261981" d="M3001.7,1749.8l-1.916-7.141,12.515-12.515,7.145,1.911Z" transform="translate(-2750.854 -1586.574)" fill="#0f1632"/>
      <path id="Path_261982" data-name="Path 261982" d="M2974.066,1715.977l-1.916-7.14,6.258-6.258,7.144,1.911Z" transform="translate(-2725.51 -1561.292)" fill="#0f1632"/>
      <path id="Path_261983" data-name="Path 261983" d="M2951.661,1676.911l-5.228,5.228-1.917-7.14Z" transform="translate(-2700.169 -1536.001)" fill="#0f1632"/>
      <path id="Path_261984" data-name="Path 261984" d="M3244.635,2659.606l-31.3,31.3-.334-1.244,31.3-31.3Z" transform="translate(-2946.371 -2437.761)" fill="#0f1632"/>
      <path id="Path_261985" data-name="Path 261985" d="M3230.9,2608.33l-31.3,31.3-.334-1.243,31.3-31.3Z" transform="translate(-2933.775 -2390.741)" fill="#0f1632"/>
      <path id="Path_261986" data-name="Path 261986" d="M3217.159,2557.044l-31.3,31.3-.334-1.244,31.3-31.3Z" transform="translate(-2921.176 -2343.71)" fill="#0f1632"/>
      <path id="Path_261987" data-name="Path 261987" d="M3203.418,2505.778l-31.3,31.3-.334-1.242,31.3-31.3Z" transform="translate(-2908.576 -2296.699)" fill="#0f1632"/>
      <path id="Path_261988" data-name="Path 261988" d="M3189.685,2454.493l-31.3,31.3-.333-1.244,31.3-31.3Z" transform="translate(-2895.982 -2249.67)" fill="#0f1632"/>
      <path id="Path_261989" data-name="Path 261989" d="M3175.941,2403.225l-31.3,31.3-.333-1.243,31.3-31.3Z" transform="translate(-2883.379 -2202.657)" fill="#0f1632"/>
      <path id="Path_261990" data-name="Path 261990" d="M3162.2,2351.943l-31.3,31.3-.334-1.244,31.3-31.3Z" transform="translate(-2870.776 -2155.629)" fill="#0f1632"/>
      <path id="Path_261991" data-name="Path 261991" d="M3148.463,2300.664l-31.3,31.3-.333-1.243,31.3-31.3Z" transform="translate(-2858.182 -2108.605)" fill="#0f1632"/>
      <path id="Path_261992" data-name="Path 261992" d="M3134.725,2249.39l-31.3,31.3-.333-1.243,31.3-31.3Z" transform="translate(-2845.583 -2061.587)" fill="#0f1632"/>
      <path id="Path_261993" data-name="Path 261993" d="M3120.988,2198.1l-31.3,31.3-.333-1.243,31.3-31.3Z" transform="translate(-2832.986 -2014.554)" fill="#0f1632"/>
      <path id="Path_261994" data-name="Path 261994" d="M3258.715,2712.151l7.142,1.914-31.3,31.3-7.143-1.915Z" transform="translate(-2959.589 -2487.087)" fill="#0f1632"/>
      <path id="Path_261995" data-name="Path 261995" d="M3368.877,2741.658l-31.3,31.3-7.142-1.914,31.3-31.3Z" transform="translate(-3054.061 -2512.39)" fill="#0f1632"/>
      <path id="Path_261996" data-name="Path 261996" d="M3471.893,2769.264l-31.3,31.3-7.143-1.915,31.3-31.3Z" transform="translate(-3148.527 -2537.704)" fill="#0f1632"/>
      <path id="Path_261997" data-name="Path 261997" d="M3574.9,2796.863l-31.3,31.3-7.144-1.915,31.3-31.3Z" transform="translate(-3242.989 -2563.013)" fill="#0f1632"/>
      <path id="Path_261998" data-name="Path 261998" d="M3646.631,2855.783l-7.142-1.914,31.3-31.3,7.142,1.913Z" transform="translate(-3337.471 -2588.339)" fill="#0f1632"/>
      <path id="Path_261999" data-name="Path 261999" d="M3719.167,2432l.333,1.243-16.56,16.56-1.244-.333Z" transform="translate(-3394.517 -2230.188)" fill="#0f1632"/>
      <path id="Path_262000" data-name="Path 262000" d="M3770.711,2487.778l-13.247,13.247-1.245-.333,14.159-14.158Z" transform="translate(-3444.514 -2280.191)" fill="#0f1632"/>
      <path id="Path_262001" data-name="Path 262001" d="M3821.946,2542.319l-9.934,9.933-1.244-.333,10.844-10.844Z" transform="translate(-3494.536 -2330.207)" fill="#0f1632"/>
      <path id="Path_262002" data-name="Path 262002" d="M3873.163,2596.861l-6.621,6.621-1.245-.333,7.532-7.531Z" transform="translate(-3544.54 -2380.223)" fill="#0f1632"/>
      <path id="Path_262003" data-name="Path 262003" d="M3921.08,2654.7l-1.244-.334,4.219-4.219.333,1.245Z" transform="translate(-3594.553 -2430.228)" fill="#0f1632"/>
      <path id="Path_262004" data-name="Path 262004" d="M3974.371,2705.591l.906-.906.331,1.237Z" transform="translate(-3644.563 -2480.24)" fill="#0f1632"/>
      <path id="Path_262005" data-name="Path 262005" d="M3654.277,2402.385l-1.916-7.14,12.515-12.515,7.144,1.91Z" transform="translate(-3349.275 -2185.001)" fill="#0f1632"/>
      <path id="Path_262006" data-name="Path 262006" d="M3626.643,2368.553l-1.916-7.139,6.258-6.258,7.145,1.911Z" transform="translate(-3323.933 -2159.716)" fill="#0f1632"/>
      <path id="Path_262007" data-name="Path 262007" d="M3604.238,2329.484l-5.228,5.229-1.916-7.14Z" transform="translate(-3298.593 -2134.423)" fill="#0f1632"/>
      <path id="Path_262008" data-name="Path 262008" d="M408.5,993.568l7.143,1.914-31.3,31.3-7.143-1.914Z" transform="translate(-345.897 -911.118)" fill="#0f1632"/>
      <path id="Path_262009" data-name="Path 262009" d="M518.663,1023.084l-31.3,31.3-7.143-1.913,31.3-31.3Z" transform="translate(-440.367 -936.429)" fill="#0f1632"/>
      <path id="Path_262010" data-name="Path 262010" d="M621.682,1050.69l-31.3,31.3-7.143-1.914,31.3-31.3Z" transform="translate(-534.838 -961.745)" fill="#0f1632"/>
      <path id="Path_262011" data-name="Path 262011" d="M724.7,1078.285l-31.3,31.3-7.143-1.913,31.3-31.3Z" transform="translate(-629.304 -987.05)" fill="#0f1632"/>
      <path id="Path_262012" data-name="Path 262012" d="M796.416,1137.187l-7.143-1.913,31.3-31.3,7.143,1.915Z" transform="translate(-723.776 -1012.358)" fill="#0f1632"/>
      <path id="Path_262013" data-name="Path 262013" d="M868.954,713.421l.333,1.244-16.56,16.56-1.243-.333Z" transform="translate(-780.824 -654.218)" fill="#0f1632"/>
      <path id="Path_262014" data-name="Path 262014" d="M920.5,769.2l-13.247,13.246-1.244-.334L920.17,767.95Z" transform="translate(-830.828 -704.223)" fill="#0f1632"/>
      <path id="Path_262015" data-name="Path 262015" d="M971.726,823.735l-9.934,9.934-1.245-.333,10.845-10.845Z" transform="translate(-880.838 -754.237)" fill="#0f1632"/>
      <path id="Path_262016" data-name="Path 262016" d="M1022.949,878.279l-6.621,6.62-1.244-.333,7.532-7.531Z" transform="translate(-930.849 -804.255)" fill="#0f1632"/>
      <path id="Path_262017" data-name="Path 262017" d="M1070.87,936.116l-1.244-.334,4.218-4.219.333,1.244Z" transform="translate(-980.864 -854.259)" fill="#0f1632"/>
      <path id="Path_262018" data-name="Path 262018" d="M1124.164,987.01l.9-.9.332,1.236Z" transform="translate(-1030.877 -904.274)" fill="#0f1632"/>
      <path id="Path_262019" data-name="Path 262019" d="M1047,1593.6l-31.3,31.3-.333-1.244,31.3-31.3Z" transform="translate(-931.109 -1460.216)" fill="#0f1632"/>
      <path id="Path_262020" data-name="Path 262020" d="M1033.262,1542.319l-31.3,31.3-.334-1.244,31.3-31.3Z" transform="translate(-918.508 -1413.19)" fill="#0f1632"/>
      <path id="Path_262021" data-name="Path 262021" d="M1019.52,1491.037l-31.3,31.3-.334-1.243,31.3-31.3Z" transform="translate(-905.906 -1366.164)" fill="#0f1632"/>
      <path id="Path_262022" data-name="Path 262022" d="M1005.777,1439.764l-31.3,31.3-.333-1.243,31.3-31.3Z" transform="translate(-893.302 -1319.146)" fill="#0f1632"/>
      <path id="Path_262023" data-name="Path 262023" d="M992.04,1388.489l-31.3,31.3-.334-1.245,31.3-31.3Z" transform="translate(-880.706 -1272.126)" fill="#0f1632"/>
      <path id="Path_262024" data-name="Path 262024" d="M978.3,1337.213l-31.3,31.3-.334-1.243,31.3-31.3Z" transform="translate(-868.107 -1225.106)" fill="#0f1632"/>
      <path id="Path_262025" data-name="Path 262025" d="M964.559,1285.94l-31.3,31.3-.334-1.243,31.3-31.3Z" transform="translate(-855.506 -1178.087)" fill="#0f1632"/>
      <path id="Path_262026" data-name="Path 262026" d="M950.827,1234.663l-31.3,31.3-.333-1.245,31.3-31.3Z" transform="translate(-842.913 -1131.066)" fill="#0f1632"/>
      <path id="Path_262027" data-name="Path 262027" d="M937.079,1183.384l-31.3,31.3-.333-1.244,31.3-31.3Z" transform="translate(-830.307 -1084.042)" fill="#0f1632"/>
      <path id="Path_262028" data-name="Path 262028" d="M923.343,1132.108l-31.3,31.3-.333-1.244,31.3-31.3Z" transform="translate(-817.71 -1037.022)" fill="#0f1632"/>
      <path id="Path_262029" data-name="Path 262029" d="M1061.078,1646.146l7.143,1.913-31.3,31.3-7.142-1.914Z" transform="translate(-944.322 -1509.542)" fill="#0f1632"/>
      <path id="Path_262030" data-name="Path 262030" d="M1171.234,1675.652l-31.3,31.3-7.143-1.913,31.3-31.3Z" transform="translate(-1038.786 -1534.845)" fill="#0f1632"/>
      <path id="Path_262031" data-name="Path 262031" d="M1274.259,1703.267l-31.3,31.3-7.142-1.914,31.3-31.3Z" transform="translate(-1133.262 -1560.169)" fill="#0f1632"/>
      <path id="Path_262032" data-name="Path 262032" d="M1377.273,1730.859l-31.3,31.3-7.143-1.913,31.3-31.3Z" transform="translate(-1227.727 -1585.471)" fill="#0f1632"/>
      <path id="Path_262033" data-name="Path 262033" d="M1448.99,1789.764l-7.142-1.913,31.3-31.3,7.142,1.915Z" transform="translate(-1322.197 -1610.782)" fill="#0f1632"/>
      <path id="Path_262034" data-name="Path 262034" d="M1521.525,1366l.333,1.243L1505.3,1383.8l-1.244-.335Z" transform="translate(-1379.242 -1252.643)" fill="#0f1632"/>
      <path id="Path_262035" data-name="Path 262035" d="M1573.08,1421.771l-13.247,13.247-1.243-.334,14.158-14.157Z" transform="translate(-1429.252 -1302.646)" fill="#0f1632"/>
      <path id="Path_262036" data-name="Path 262036" d="M1624.3,1476.313l-9.934,9.934-1.245-.333,10.845-10.844Z" transform="translate(-1479.263 -1352.663)" fill="#0f1632"/>
      <path id="Path_262037" data-name="Path 262037" d="M1675.526,1530.855l-6.621,6.62-1.244-.333,7.532-7.531Z" transform="translate(-1529.272 -1402.679)" fill="#0f1632"/>
      <path id="Path_262038" data-name="Path 262038" d="M1723.441,1588.693l-1.245-.334,4.219-4.219.334,1.244Z" transform="translate(-1579.282 -1452.683)" fill="#0f1632"/>
      <path id="Path_262039" data-name="Path 262039" d="M1776.738,1639.575l.905-.906.332,1.238Z" transform="translate(-1629.298 -1502.687)" fill="#0f1632"/>
      <path id="Path_262040" data-name="Path 262040" d="M1456.636,1336.374l-1.916-7.141,12.515-12.516,7.145,1.912Z" transform="translate(-1334.001 -1207.45)" fill="#0f1632"/>
      <path id="Path_262041" data-name="Path 262041" d="M1429,1302.536l-1.916-7.139,6.259-6.259,7.143,1.912Z" transform="translate(-1308.663 -1182.16)" fill="#0f1632"/>
      <path id="Path_262042" data-name="Path 262042" d="M1406.6,1263.478l-5.228,5.228-1.916-7.139Z" transform="translate(-1283.324 -1156.878)" fill="#0f1632"/>
      <path id="Path_262043" data-name="Path 262043" d="M1699.575,2246.177l-31.3,31.3-.333-1.243,31.3-31.3Z" transform="translate(-1529.527 -2058.64)" fill="#0f1632"/>
      <path id="Path_262044" data-name="Path 262044" d="M1685.834,2194.894l-31.3,31.3-.334-1.244,31.3-31.3Z" transform="translate(-1516.926 -2011.612)" fill="#0f1632"/>
      <path id="Path_262045" data-name="Path 262045" d="M1672.092,2143.615l-31.3,31.3-.334-1.243,31.3-31.3Z" transform="translate(-1504.324 -1964.588)" fill="#0f1632"/>
      <path id="Path_262046" data-name="Path 262046" d="M1658.356,2092.341l-31.3,31.3-.334-1.244,31.3-31.3Z" transform="translate(-1491.729 -1917.569)" fill="#0f1632"/>
      <path id="Path_262047" data-name="Path 262047" d="M1644.617,2041.065l-31.3,31.3-.333-1.244,31.3-31.3Z" transform="translate(-1479.131 -1870.55)" fill="#0f1632"/>
      <path id="Path_262048" data-name="Path 262048" d="M1630.879,1989.779l-31.3,31.3-.334-1.245,31.3-31.3Z" transform="translate(-1466.531 -1823.519)" fill="#0f1632"/>
      <path id="Path_262049" data-name="Path 262049" d="M1617.127,1938.5l-31.3,31.3-.335-1.243,31.3-31.3Z" transform="translate(-1453.921 -1776.499)" fill="#0f1632"/>
      <path id="Path_262050" data-name="Path 262050" d="M1603.4,1887.237l-31.3,31.3-.334-1.244,31.3-31.3Z" transform="translate(-1441.328 -1729.487)" fill="#0f1632"/>
      <path id="Path_262051" data-name="Path 262051" d="M1589.658,1835.961l-31.3,31.3-.333-1.243,31.3-31.3Z" transform="translate(-1428.732 -1682.466)" fill="#0f1632"/>
      <path id="Path_262052" data-name="Path 262052" d="M1575.92,1784.674l-31.3,31.3-.333-1.243,31.3-31.3Z" transform="translate(-1416.134 -1635.435)" fill="#0f1632"/>
      <path id="Path_262053" data-name="Path 262053" d="M1713.652,2298.723l7.143,1.913-31.3,31.3-7.143-1.913Z" transform="translate(-1542.742 -2107.966)" fill="#0f1632"/>
      <path id="Path_262054" data-name="Path 262054" d="M1823.815,2328.23l-31.3,31.3-7.143-1.914,31.3-31.3Z" transform="translate(-1637.213 -2133.269)" fill="#0f1632"/>
      <path id="Path_262055" data-name="Path 262055" d="M1926.836,2355.835l-31.3,31.3-7.142-1.913,31.3-31.3Z" transform="translate(-1731.687 -2158.584)" fill="#0f1632"/>
      <path id="Path_262056" data-name="Path 262056" d="M2029.851,2383.439l-31.3,31.3-7.142-1.914,31.3-31.3Z" transform="translate(-1826.151 -2183.897)" fill="#0f1632"/>
      <path id="Path_262057" data-name="Path 262057" d="M2101.567,2442.341l-7.142-1.913,31.3-31.3,7.142,1.914Z" transform="translate(-1920.621 -2209.206)" fill="#0f1632"/>
      <path id="Path_262058" data-name="Path 262058" d="M2174.1,2018.575l.333,1.244-16.56,16.56-1.244-.334Z" transform="translate(-1977.663 -1851.065)" fill="#0f1632"/>
      <path id="Path_262059" data-name="Path 262059" d="M2225.648,2074.35,2212.4,2087.6l-1.244-.333,14.158-14.157Z" transform="translate(-2027.667 -1901.071)" fill="#0f1632"/>
      <path id="Path_262060" data-name="Path 262060" d="M2276.878,2128.89l-9.934,9.933-1.245-.333,10.845-10.844Z" transform="translate(-2077.684 -1951.087)" fill="#0f1632"/>
      <path id="Path_262061" data-name="Path 262061" d="M2328.1,2183.422l-6.62,6.619-1.244-.332,7.532-7.533Z" transform="translate(-2127.693 -2001.091)" fill="#0f1632"/>
      <path id="Path_262062" data-name="Path 262062" d="M2376.018,2241.271l-1.244-.333,4.219-4.219.333,1.244Z" transform="translate(-2177.706 -2051.106)" fill="#0f1632"/>
      <path id="Path_262063" data-name="Path 262063" d="M2429.322,2292.164l.9-.9.332,1.236Z" transform="translate(-2227.728 -2101.122)" fill="#0f1632"/>
      <path id="Path_262064" data-name="Path 262064" d="M2109.212,1988.944l-1.916-7.141,12.516-12.516,7.145,1.912Z" transform="translate(-1932.425 -1805.868)" fill="#0f1632"/>
      <path id="Path_262065" data-name="Path 262065" d="M2081.575,1955.1l-1.916-7.14,6.258-6.259,7.144,1.913Z" transform="translate(-1907.081 -1780.575)" fill="#0f1632"/>
      <path id="Path_262066" data-name="Path 262066" d="M2059.17,1916.047l-5.227,5.229-1.917-7.14Z" transform="translate(-1881.74 -1755.293)" fill="#0f1632"/>
      <path id="Path_262067" data-name="Path 262067" d="M2352.143,2898.747l-31.3,31.3-.334-1.244,31.3-31.3Z" transform="translate(-2127.942 -2657.058)" fill="#0f1632"/>
      <path id="Path_262068" data-name="Path 262068" d="M2338.411,2847.47l-31.3,31.3-.334-1.244,31.3-31.3Z" transform="translate(-2115.35 -2610.035)" fill="#0f1632"/>
      <path id="Path_262069" data-name="Path 262069" d="M2324.669,2796.192l-31.3,31.3-.334-1.244,31.3-31.3Z" transform="translate(-2102.748 -2563.013)" fill="#0f1632"/>
      <path id="Path_262070" data-name="Path 262070" d="M2310.933,2744.918l-31.3,31.3-.333-1.243,31.3-31.3Z" transform="translate(-2090.152 -2515.993)" fill="#0f1632"/>
      <path id="Path_262071" data-name="Path 262071" d="M2297.194,2693.632l-31.3,31.3-.333-1.244,31.3-31.3Z" transform="translate(-2077.554 -2468.962)" fill="#0f1632"/>
      <path id="Path_262072" data-name="Path 262072" d="M2283.455,2642.355l-31.3,31.3-.334-1.244,31.3-31.3Z" transform="translate(-2064.955 -2421.942)" fill="#0f1632"/>
      <path id="Path_262073" data-name="Path 262073" d="M2269.71,2591.082l-31.3,31.3-.334-1.243,31.3-31.3Z" transform="translate(-2052.351 -2374.923)" fill="#0f1632"/>
      <path id="Path_262074" data-name="Path 262074" d="M2255.973,2539.814l-31.3,31.3-.334-1.243,31.3-31.3Z" transform="translate(-2039.752 -2327.911)" fill="#0f1632"/>
      <path id="Path_262075" data-name="Path 262075" d="M2242.228,2488.529l-31.3,31.3-.334-1.244,31.3-31.3Z" transform="translate(-2027.148 -2280.881)" fill="#0f1632"/>
      <path id="Path_262076" data-name="Path 262076" d="M2228.5,2437.254l-31.3,31.3-.333-1.243,31.3-31.3Z" transform="translate(-2014.559 -2233.861)" fill="#0f1632"/>
      <path id="Path_262077" data-name="Path 262077" d="M2366.23,2951.291l7.142,1.914-31.3,31.3-7.142-1.914Z" transform="translate(-2141.166 -2706.381)" fill="#0f1632"/>
      <path id="Path_262078" data-name="Path 262078" d="M2476.392,2980.807l-31.3,31.3-7.143-1.914,31.3-31.3Z" transform="translate(-2235.637 -2731.692)" fill="#0f1632"/>
      <path id="Path_262079" data-name="Path 262079" d="M2579.415,3008.412l-31.3,31.3-7.142-1.913,31.3-31.3Z" transform="translate(-2330.111 -2757.007)" fill="#0f1632"/>
      <path id="Path_262080" data-name="Path 262080" d="M2682.427,3036l-31.3,31.3-7.142-1.914,31.3-31.3Z" transform="translate(-2424.575 -2782.31)" fill="#0f1632"/>
      <path id="Path_262081" data-name="Path 262081" d="M2754.144,3094.919l-7.142-1.913,31.3-31.3,7.142,1.914Z" transform="translate(-2519.045 -2807.631)" fill="#0f1632"/>
      <path id="Path_262082" data-name="Path 262082" d="M2826.676,2671.144l.333,1.244-16.559,16.561-1.244-.335Z" transform="translate(-2576.087 -2449.481)" fill="#0f1632"/>
      <path id="Path_262083" data-name="Path 262083" d="M2878.238,2726.928l-13.248,13.247-1.243-.333,14.157-14.157Z" transform="translate(-2626.103 -2499.497)" fill="#0f1632"/>
      <path id="Path_262084" data-name="Path 262084" d="M2929.454,2781.459l-9.933,9.934-1.244-.333,10.844-10.844Z" transform="translate(-2676.106 -2549.502)" fill="#0f1632"/>
      <path id="Path_262085" data-name="Path 262085" d="M2980.677,2836l-6.621,6.62-1.244-.332,7.532-7.533Z" transform="translate(-2726.117 -2599.515)" fill="#0f1632"/>
      <path id="Path_262086" data-name="Path 262086" d="M3028.592,2893.849l-1.244-.333,4.218-4.218.333,1.244Z" transform="translate(-2776.127 -2649.533)" fill="#0f1632"/>
      <path id="Path_262087" data-name="Path 262087" d="M3081.883,2944.729l.905-.9.332,1.238Z" transform="translate(-2826.137 -2699.534)" fill="#0f1632"/>
      <path id="Path_262088" data-name="Path 262088" d="M2761.789,2641.531l-1.915-7.14,12.515-12.515,7.145,1.91Z" transform="translate(-2530.85 -2404.303)" fill="#0f1632"/>
      <path id="Path_262089" data-name="Path 262089" d="M2734.15,2607.681l-1.917-7.14,6.258-6.259,7.144,1.913Z" transform="translate(-2505.502 -2378.999)" fill="#0f1632"/>
      <path id="Path_262090" data-name="Path 262090" d="M2711.747,2568.625l-5.229,5.228-1.916-7.14Z" transform="translate(-2480.164 -2353.718)" fill="#0f1632"/>
      <path id="Path_262091" data-name="Path 262091" d="M564.918,1576.278l-1.916-7.14,12.515-12.517,7.145,1.912Z" transform="translate(-516.282 -1427.446)" fill="#0f1632"/>
      <path id="Path_262092" data-name="Path 262092" d="M537.281,1542.452l-1.916-7.14,6.258-6.258,7.144,1.912Z" transform="translate(-490.938 -1402.167)" fill="#0f1632"/>
      <path id="Path_262093" data-name="Path 262093" d="M514.881,1503.392l-5.228,5.229-1.916-7.14Z" transform="translate(-465.604 -1376.882)" fill="#0f1632"/>
      <path id="Path_262094" data-name="Path 262094" d="M821.939,2538.614l7.142,1.916-31.3,31.3-7.143-1.914Z" transform="translate(-725.027 -2327.95)" fill="#0f1632"/>
      <path id="Path_262095" data-name="Path 262095" d="M932.094,2568.146l-31.3,31.3-7.143-1.915,31.3-31.3Z" transform="translate(-819.491 -2353.276)" fill="#0f1632"/>
      <path id="Path_262096" data-name="Path 262096" d="M1035.108,2595.745l-31.3,31.3-7.143-1.914,31.3-31.3Z" transform="translate(-913.955 -2378.585)" fill="#0f1632"/>
      <path id="Path_262097" data-name="Path 262097" d="M1138.131,2623.351l-31.3,31.3-7.142-1.915,31.3-31.3Z" transform="translate(-1008.43 -2403.9)" fill="#0f1632"/>
      <path id="Path_262098" data-name="Path 262098" d="M1209.845,2682.258l-7.143-1.914,31.3-31.3,7.143,1.913Z" transform="translate(-1102.897 -2429.214)" fill="#0f1632"/>
      <path id="Path_262099" data-name="Path 262099" d="M1282.379,2258.489l.334,1.243-16.56,16.559-1.244-.333Z" transform="translate(-1159.941 -2071.071)" fill="#0f1632"/>
      <path id="Path_262100" data-name="Path 262100" d="M1333.936,2314.263l-13.246,13.248-1.245-.335,14.158-14.157Z" transform="translate(-1209.952 -2121.075)" fill="#0f1632"/>
      <path id="Path_262101" data-name="Path 262101" d="M1385.161,2368.793l-9.933,9.934-1.244-.333,10.844-10.845Z" transform="translate(-1259.965 -2171.08)" fill="#0f1632"/>
      <path id="Path_262102" data-name="Path 262102" d="M1436.386,2423.334l-6.622,6.621-1.243-.334,7.532-7.531Z" transform="translate(-1309.978 -2221.096)" fill="#0f1632"/>
      <path id="Path_262103" data-name="Path 262103" d="M1484.3,2481.183l-1.245-.334,4.218-4.218.334,1.243Z" transform="translate(-1359.982 -2271.11)" fill="#0f1632"/>
      <path id="Path_262104" data-name="Path 262104" d="M1537.593,2532.075l.9-.9.333,1.237Z" transform="translate(-1409.997 -2321.124)" fill="#0f1632"/>
      <path id="Path_262105" data-name="Path 262105" d="M1217.49,2228.855l-1.917-7.14,12.516-12.516,7.145,1.912Z" transform="translate(-1114.7 -2025.871)" fill="#0f1632"/>
      <path id="Path_262106" data-name="Path 262106" d="M1189.859,2195.028l-1.916-7.141,6.258-6.258,7.144,1.912Z" transform="translate(-1089.362 -2000.589)" fill="#0f1632"/>
      <path id="Path_262107" data-name="Path 262107" d="M1167.456,2155.969l-5.228,5.229-1.916-7.14Z" transform="translate(-1064.024 -1975.306)" fill="#0f1632"/>
      <path id="Path_262108" data-name="Path 262108" d="M1460.436,3138.67l-31.3,31.3-.333-1.244,31.3-31.3Z" transform="translate(-1310.233 -2877.071)" fill="#0f1632"/>
      <path id="Path_262109" data-name="Path 262109" d="M1446.7,3087.384l-31.3,31.3-.333-1.244,31.3-31.3Z" transform="translate(-1297.637 -2830.04)" fill="#0f1632"/>
      <path id="Path_262110" data-name="Path 262110" d="M1432.949,3036.109l-31.3,31.3-.334-1.244,31.3-31.3Z" transform="translate(-1285.027 -2783.021)" fill="#0f1632"/>
      <path id="Path_262111" data-name="Path 262111" d="M1419.214,2984.833l-31.3,31.3-.334-1.243,31.3-31.3Z" transform="translate(-1272.432 -2736.001)" fill="#0f1632"/>
      <path id="Path_262112" data-name="Path 262112" d="M1405.472,2933.553l-31.3,31.3-.334-1.244,31.3-31.3Z" transform="translate(-1259.829 -2688.976)" fill="#0f1632"/>
      <path id="Path_262113" data-name="Path 262113" d="M1391.74,2882.279l-31.3,31.3-.333-1.243,31.3-31.3Z" transform="translate(-1247.237 -2641.956)" fill="#0f1632"/>
      <path id="Path_262114" data-name="Path 262114" d="M1377.994,2830.993l-31.3,31.3-.334-1.245,31.3-31.3Z" transform="translate(-1234.632 -2594.925)" fill="#0f1632"/>
      <path id="Path_262115" data-name="Path 262115" d="M1364.256,2779.718l-31.3,31.3-.334-1.244,31.3-31.3Z" transform="translate(-1222.034 -2547.905)" fill="#0f1632"/>
      <path id="Path_262116" data-name="Path 262116" d="M1350.516,2728.443l-31.3,31.3-.333-1.244,31.3-31.3Z" transform="translate(-1209.436 -2500.886)" fill="#0f1632"/>
      <path id="Path_262117" data-name="Path 262117" d="M1336.776,2677.167l-31.3,31.3-.334-1.243,31.3-31.3Z" transform="translate(-1196.834 -2453.866)" fill="#0f1632"/>
      <path id="Path_262118" data-name="Path 262118" d="M1474.516,3191.2l7.141,1.914-31.3,31.3-7.142-1.914Z" transform="translate(-1323.45 -2926.386)" fill="#0f1632"/>
      <path id="Path_262119" data-name="Path 262119" d="M1584.672,3220.72l-31.3,31.3-7.143-1.914,31.3-31.3Z" transform="translate(-1417.915 -2951.697)" fill="#0f1632"/>
      <path id="Path_262120" data-name="Path 262120" d="M1687.685,3248.322l-31.3,31.3-7.143-1.914,31.3-31.3Z" transform="translate(-1512.38 -2977.009)" fill="#0f1632"/>
      <path id="Path_262121" data-name="Path 262121" d="M1790.712,3275.928l-31.3,31.3-7.142-1.915,31.3-31.3Z" transform="translate(-1606.856 -3002.323)" fill="#0f1632"/>
      <path id="Path_262122" data-name="Path 262122" d="M1862.422,3334.832l-7.143-1.913,31.3-31.3,7.143,1.914Z" transform="translate(-1701.32 -3027.636)" fill="#0f1632"/>
      <path id="Path_262123" data-name="Path 262123" d="M1934.959,2911.066l.333,1.242-16.559,16.562-1.244-.334Z" transform="translate(-1758.368 -2669.495)" fill="#0f1632"/>
      <path id="Path_262124" data-name="Path 262124" d="M1986.51,2966.843l-13.247,13.247-1.244-.333,14.158-14.157Z" transform="translate(-1808.373 -2719.502)" fill="#0f1632"/>
      <path id="Path_262125" data-name="Path 262125" d="M2037.737,3021.372l-9.933,9.934-1.244-.334,10.845-10.845Z" transform="translate(-1858.389 -2769.506)" fill="#0f1632"/>
      <path id="Path_262126" data-name="Path 262126" d="M2088.963,3075.913l-6.621,6.622-1.243-.333,7.531-7.531Z" transform="translate(-1908.401 -2819.522)" fill="#0f1632"/>
      <path id="Path_262127" data-name="Path 262127" d="M2136.871,3133.75l-1.245-.333,4.218-4.22.334,1.245Z" transform="translate(-1958.403 -2869.524)" fill="#0f1632"/>
      <path id="Path_262128" data-name="Path 262128" d="M2190.17,3184.646l.9-.905.331,1.237Z" transform="translate(-2008.421 -2919.542)" fill="#0f1632"/>
      <path id="Path_262129" data-name="Path 262129" d="M1870.067,2881.435l-1.916-7.14,12.516-12.515,7.144,1.912Z" transform="translate(-1713.124 -2624.298)" fill="#0f1632"/>
      <path id="Path_262130" data-name="Path 262130" d="M1842.433,2847.6l-1.916-7.14,6.259-6.259,7.144,1.913Z" transform="translate(-1687.783 -2599.004)" fill="#0f1632"/>
      <path id="Path_262131" data-name="Path 262131" d="M1820.031,2808.546l-5.228,5.23-1.916-7.14Z" transform="translate(-1662.445 -2573.73)" fill="#0f1632"/>
      <path id="Path_262132" data-name="Path 262132" d="M3414.36,3294.1l-1.916-7.14,12.516-12.515,7.144,1.912Z" transform="translate(-3129.267 -3002.717)" fill="#0f1632"/>
      <path id="Path_262133" data-name="Path 262133" d="M3386.727,3260.259l-1.917-7.14,6.259-6.259,7.144,1.912Z" transform="translate(-3103.926 -2977.423)" fill="#0f1632"/>
      <path id="Path_262134" data-name="Path 262134" d="M3364.322,3221.2l-5.228,5.228-1.917-7.14Z" transform="translate(-3078.586 -2952.142)" fill="#0f1632"/>
    </g>
  </g>
</svg>
