<svg xmlns="http://www.w3.org/2000/svg" width="380" height="372.168" viewBox="0 0 380 372.168">
  <g id="Group_146669" data-name="Group 146669" transform="translate(0 -0.001)">
    <g id="Group_146603" data-name="Group 146603" transform="translate(0 0.001)">
      <path id="Path_262279" data-name="Path 262279" d="M2055.74,2320.271c-14.669-14.669-32.274-20.846-39.324-13.8l-30.043,30.044c-7.049,7.049-.873,24.653,13.8,39.323s32.274,20.845,39.323,13.8l30.043-30.043c7.05-7.05.873-24.654-13.8-39.324m-3.734,3.733c10.378,10.379,14.749,22.833,9.76,27.821s-17.441.616-27.819-9.761-14.748-22.833-9.761-27.82,17.442-.617,27.819,9.76" transform="translate(-1801.927 -2092.732)" fill="#255be3"/>
      <path id="Path_262280" data-name="Path 262280" d="M1333.079,29.882C1322.7,19.5,1318.33,7.05,1323.317,2.063s17.441-.616,27.819,9.761,14.748,22.833,9.761,27.819-17.441.618-27.82-9.761" transform="translate(-1200.331 -0.001)" fill="#e6ebed"/>
      <path id="Path_262281" data-name="Path 262281" d="M1405.96,2054.332c-7.05,7.048-.873,24.654,13.8,39.322s32.275,20.846,39.324,13.8.872-24.655-13.8-39.323-32.274-20.848-39.323-13.8" transform="translate(-1274.635 -1863.666)" fill="#e6ebed"/>
      <path id="Path_262282" data-name="Path 262282" d="M566.633,1487.739c-14.669-14.669-32.274-20.846-39.323-13.8l-30.044,30.044c-7.048,7.049-.872,24.653,13.8,39.323s32.274,20.846,39.323,13.8l30.044-30.044c7.048-7.048.871-24.655-13.8-39.324" transform="translate(-449.107 -1336.395)" fill="#255be3"/>
      <path id="Path_262283" data-name="Path 262283" d="M2138.09,1015.436c-14.669-14.669-32.274-20.846-39.323-13.8l-36.256,36.255c-7.049,7.049-.872,24.654,13.8,39.323s32.274,20.846,39.323,13.8l36.255-36.255c7.049-7.049.872-24.654-13.8-39.324m-3.734,3.734c10.378,10.378,14.749,22.833,9.762,27.82s-17.442.617-27.821-9.761-14.747-22.833-9.76-27.819,17.442-.617,27.819,9.761" transform="translate(-1871.097 -907.319)" fill="#255be3"/>
      <path id="Path_262284" data-name="Path 262284" d="M685.865,2838.619c4.552-5.165.159-17.344-10.024-27.527s-22.363-14.577-27.527-10.024l-.015-.015-20.206,20.208c-4.987,4.987-.618,17.441,9.761,27.819s22.832,14.748,27.819,9.761l20.208-20.207Z" transform="translate(-568.735 -2543.077)" fill="#255be3"/>
      <path id="Path_262285" data-name="Path 262285" d="M2406.016,3337.717c4.552-5.166.159-17.342-10.023-27.527s-22.362-14.577-27.528-10.024l-.015-.015-28.872,28.873h0c-4.986,4.987-.617,17.441,9.76,27.82s22.833,14.747,27.82,9.76l28.872-28.872Z" transform="translate(-2123.582 -2996.497)" fill="#255be3"/>
      <path id="Path_262286" data-name="Path 262286" d="M59.928,988.492c-10.379-10.378-22.832-14.748-27.819-9.761L2.062,1008.777c-4.987,4.987-.616,17.442,9.761,27.82s22.833,14.748,27.82,9.761l30.046-30.046c4.988-4.987.618-17.442-9.761-27.82" transform="translate(0 -887.281)" fill="#e6ebed"/>
      <path id="Path_262287" data-name="Path 262287" d="M1975.6,110.853c-10.378-10.378-22.832-14.749-27.82-9.761l-44.056,44.057c-4.987,4.987-.617,17.441,9.761,27.819s22.833,14.748,27.819,9.761l44.057-44.056c4.987-4.988.617-17.442-9.761-27.82m-14.933,14.935c-6.787-6.788-9.644-14.931-6.383-18.193s11.4-.4,18.191,6.383,9.644,14.93,6.383,18.191-11.406.4-18.191-6.381" transform="translate(-1727.616 -89.966)" fill="#e6ebed"/>
      <path id="Path_262288" data-name="Path 262288" d="M137.825,2233.616c-10.377-10.378-22.832-14.747-27.819-9.76L94.659,2239.2c-4.987,4.987-.617,17.442,9.761,27.82s22.833,14.748,27.82,9.76l15.348-15.347c4.987-4.987.616-17.441-9.762-27.819M107.546,2263.9c-6.787-6.787-9.645-14.93-6.383-18.192s11.4-.4,18.191,6.383,9.644,14.93,6.383,18.192-11.406.4-18.191-6.383" transform="translate(-84.123 -2018.45)" fill="#e6ebed"/>
      <path id="Path_262289" data-name="Path 262289" d="M1257,1266.879c-10.377-10.379-14.748-22.833-9.76-27.82s17.441-.617,27.819,9.761,14.747,22.833,9.761,27.819-17.441.617-27.82-9.761" transform="translate(-1131.219 -1123.784)" fill="#e6ebed"/>
      <path id="Path_262290" data-name="Path 262290" d="M2749.24,1943.84c-10.378-10.377-22.833-14.748-27.82-9.76l-15.347,15.346c-4.987,4.987-.616,17.442,9.761,27.82s22.833,14.748,27.821,9.76L2759,1971.66c4.987-4.987.616-17.442-9.761-27.82" transform="translate(-2456.533 -1755.194)" fill="#e6ebed"/>
      <path id="Path_262291" data-name="Path 262291" d="M1483.832,3066.2c-10.378-10.378-22.832-14.748-27.82-9.761l-29.767,29.768c-4.988,4.987-.617,17.441,9.761,27.819s22.832,14.749,27.819,9.761l29.768-29.768c4.987-4.986.617-17.441-9.762-27.819m-44.7,44.7c-6.785-6.787-9.643-14.93-6.382-18.192s11.4-.4,18.191,6.383,9.643,14.93,6.383,18.191-11.405.4-18.192-6.383" transform="translate(-1293.838 -2774.832)" fill="#e6ebed"/>
      <path id="Path_262292" data-name="Path 262292" d="M944.992,2258.965l9.027-9.027c.27,2.926-.357,5.382-2,7.026s-4.1,2.271-7.026,2" transform="translate(-858.504 -2044.018)" fill="#0f1632"/>
      <path id="Path_262293" data-name="Path 262293" d="M896.868,2188.571a21.9,21.9,0,0,1,1.352,5.194l-9.4,9.4a22.035,22.035,0,0,1-5.2-1.352Z" transform="translate(-802.751 -1988.267)" fill="#0f1632"/>
      <path id="Path_262294" data-name="Path 262294" d="M847.533,2136.76a35.683,35.683,0,0,1,1.994,4.042l-13.669,13.669a35.56,35.56,0,0,1-4.042-1.994Z" transform="translate(-755.687 -1941.198)" fill="#0f1632"/>
      <path id="Path_262295" data-name="Path 262295" d="M802.6,2090.354q1.213,1.657,2.214,3.31l-16.1,16.1c-1.1-.667-2.2-1.406-3.31-2.216Z" transform="translate(-713.523 -1899.039)" fill="#0f1632"/>
      <path id="Path_262296" data-name="Path 262296" d="M761.006,2048.013q1.208,1.354,2.279,2.734l-17.478,17.478c-.916-.713-1.831-1.474-2.734-2.279Z" transform="translate(-675.064 -1860.574)" fill="#0f1632"/>
      <path id="Path_262297" data-name="Path 262297" d="M704.272,2027.237l18.023-18.022c.426.4.851.811,1.271,1.231q.5.5.987,1.011L706.514,2029.5q-.509-.484-1.012-.987-.63-.63-1.23-1.272" transform="translate(-639.815 -1825.327)" fill="#0f1632"/>
      <path id="Path_262298" data-name="Path 262298" d="M686.4,1973.866q1.1.855,2.19,1.8l-17.868,17.868q-.945-1.087-1.8-2.189Z" transform="translate(-607.701 -1793.213)" fill="#0f1632"/>
      <path id="Path_262299" data-name="Path 262299" d="M653.678,1942.4q1.046.647,2.1,1.38l-16.943,16.943q-.734-1.049-1.381-2.1Z" transform="translate(-579.111 -1764.622)" fill="#0f1632"/>
      <path id="Path_262300" data-name="Path 262300" d="M624.935,1915.8c.66.292,1.328.614,2,.965l-15.115,15.117q-.528-1.01-.966-2Z" transform="translate(-554.948 -1740.457)" fill="#0f1632"/>
      <path id="Path_262301" data-name="Path 262301" d="M602.511,1896.927c.638.127,1.294.292,1.963.49l-12,11.995c-.2-.669-.363-1.326-.489-1.963Z" transform="translate(-537.81 -1723.316)" fill="#0f1632"/>
      <path id="Path_262302" data-name="Path 262302" d="M592.415,1896.793a7.176,7.176,0,0,1,3.58-1.638l-5.774,5.774a7.175,7.175,0,0,1,1.637-3.58Z" transform="translate(-536.203 -1721.706)" fill="#0f1632"/>
      <path id="Path_262303" data-name="Path 262303" d="M526.189,1234.677l60.09-60.089c-2.317,2.316-3.2,5.772-2.833,9.89l-47.367,47.367c-4.117-.372-7.574.517-9.89,2.832" transform="translate(-478.031 -1067.087)" fill="#0f1632"/>
      <path id="Path_262304" data-name="Path 262304" d="M685.873,1287.28a28.2,28.2,0,0,0,1.173,5.374L644.26,1335.44a28.235,28.235,0,0,0-5.374-1.173Z" transform="translate(-580.413 -1169.465)" fill="#0f1632"/>
      <path id="Path_262305" data-name="Path 262305" d="M747.945,1354.048a42.345,42.345,0,0,0,1.737,4.3l-39.728,39.728a42.281,42.281,0,0,0-4.3-1.736Z" transform="translate(-641.072 -1230.122)" fill="#0f1632"/>
      <path id="Path_262306" data-name="Path 262306" d="M802.825,1412.019q.877,1.771,1.944,3.578l-37.569,37.57q-1.8-1.066-3.58-1.943Z" transform="translate(-693.732 -1282.787)" fill="#0f1632"/>
      <path id="Path_262307" data-name="Path 262307" d="M853.425,1464.74q.937,1.5,2,3.011l-36.069,36.07q-1.508-1.062-3.012-2Z" transform="translate(-741.632 -1330.683)" fill="#0f1632"/>
      <path id="Path_262308" data-name="Path 262308" d="M900.836,1513.56q.944,1.267,1.972,2.528L867.7,1551.2q-1.259-1.027-2.528-1.972Z" transform="translate(-785.985 -1375.035)" fill="#0f1632"/>
      <path id="Path_262309" data-name="Path 262309" d="M945.6,1559.142q.915,1.058,1.888,2.1l-34.636,34.636q-1.044-.973-2.1-1.888Z" transform="translate(-827.4 -1416.445)" fill="#0f1632"/>
      <path id="Path_262310" data-name="Path 262310" d="M953.416,1636.368l34.566-34.567.22.224q.765.763,1.538,1.5l-34.606,34.606c-.489-.515-.986-1.028-1.5-1.538-.073-.074-.149-.147-.223-.22" transform="translate(-866.157 -1455.2)" fill="#0f1632"/>
      <path id="Path_262311" data-name="Path 262311" d="M1028.09,1641.682q.8.7,1.6,1.368l-35.021,35.021q-.668-.8-1.367-1.6Z" transform="translate(-902.391 -1491.431)" fill="#0f1632"/>
      <path id="Path_262312" data-name="Path 262312" d="M1065.844,1678.693q.706.535,1.412,1.043l-35.908,35.909q-.509-.707-1.044-1.411Z" transform="translate(-936.007 -1525.055)" fill="#0f1632"/>
      <path id="Path_262313" data-name="Path 262313" d="M1100.98,1712.495q.6.383,1.2.747l-37.331,37.33q-.362-.6-.746-1.2Z" transform="translate(-966.712 -1555.763)" fill="#0f1632"/>
      <path id="Path_262314" data-name="Path 262314" d="M1133.036,1742.518c.321.164.639.32.956.474l-39.391,39.391c-.154-.317-.311-.636-.474-.956Z" transform="translate(-993.99 -1583.038)" fill="#0f1632"/>
      <path id="Path_262315" data-name="Path 262315" d="M1160.971,1767.5q.343.123.682.238l-42.307,42.306c-.075-.226-.154-.454-.237-.681Z" transform="translate(-1016.686 -1605.732)" fill="#0f1632"/>
      <path id="Path_262316" data-name="Path 262316" d="M1135.981,1830.673l46.3-46.3c.12.017.24.033.36.047l-46.612,46.611c-.015-.12-.029-.239-.047-.36" transform="translate(-1032.013 -1621.065)" fill="#0f1632"/>
      <path id="Path_262317" data-name="Path 262317" d="M1601.559,1602.4l9.027-9.027c.27,2.926-.358,5.382-2,7.026s-4.1,2.27-7.025,2" transform="translate(-1454.98 -1447.539)" fill="#0f1632"/>
      <path id="Path_262318" data-name="Path 262318" d="M1553.442,1532a21.935,21.935,0,0,1,1.352,5.195l-9.4,9.4a21.992,21.992,0,0,1-5.2-1.352Z" transform="translate(-1399.232 -1391.791)" fill="#0f1632"/>
      <path id="Path_262319" data-name="Path 262319" d="M1504.1,1480.2a35.512,35.512,0,0,1,2,4.042l-13.67,13.668a35.505,35.505,0,0,1-4.042-1.994Z" transform="translate(-1352.163 -1344.725)" fill="#0f1632"/>
      <path id="Path_262320" data-name="Path 262320" d="M1459.181,1433.784q1.213,1.657,2.214,3.31l-16.1,16.1q-1.649-1-3.309-2.216Z" transform="translate(-1310.01 -1302.561)" fill="#0f1632"/>
      <path id="Path_262321" data-name="Path 262321" d="M1417.578,1391.454q1.206,1.354,2.278,2.733l-17.477,17.478q-1.376-1.07-2.733-2.279Z" transform="translate(-1271.546 -1264.104)" fill="#0f1632"/>
      <path id="Path_262322" data-name="Path 262322" d="M1360.833,1370.674l18.022-18.022q.64.6,1.272,1.23.5.5.986,1.012l-18.037,18.038q-.51-.484-1.013-.987-.63-.63-1.231-1.271" transform="translate(-1236.286 -1228.854)" fill="#0f1632"/>
      <path id="Path_262323" data-name="Path 262323" d="M1342.973,1317.3q1.1.855,2.19,1.8l-17.869,17.868q-.945-1.087-1.8-2.189Z" transform="translate(-1204.182 -1196.739)" fill="#0f1632"/>
      <path id="Path_262324" data-name="Path 262324" d="M1310.24,1285.822q1.045.648,2.1,1.38l-16.944,16.944q-.733-1.05-1.381-2.1Z" transform="translate(-1175.581 -1168.14)" fill="#0f1632"/>
      <path id="Path_262325" data-name="Path 262325" d="M1281.492,1259.231q.988.439,2,.965l-15.117,15.117c-.35-.674-.672-1.343-.966-2Z" transform="translate(-1151.414 -1143.983)" fill="#0f1632"/>
      <path id="Path_262326" data-name="Path 262326" d="M1259.067,1240.355c.639.127,1.3.291,1.964.491l-11.995,11.994c-.2-.669-.363-1.326-.49-1.964Z" transform="translate(-1134.276 -1126.834)" fill="#0f1632"/>
      <path id="Path_262327" data-name="Path 262327" d="M1248.968,1240.227a7.184,7.184,0,0,1,3.582-1.638l-5.776,5.774a7.2,7.2,0,0,1,1.638-3.58Z" transform="translate(-1132.666 -1125.23)" fill="#0f1632"/>
      <path id="Path_262328" data-name="Path 262328" d="M778.318,2143.363l60.09-60.09c-2.317,2.317-3.2,5.773-2.833,9.891l-47.367,47.368c-4.118-.373-7.574.514-9.891,2.832" transform="translate(-707.084 -1892.606)" fill="#0f1632"/>
      <path id="Path_262329" data-name="Path 262329" d="M938,2195.961a28.179,28.179,0,0,0,1.173,5.374l-42.788,42.787a28.2,28.2,0,0,0-5.373-1.171Z" transform="translate(-809.461 -1994.981)" fill="#0f1632"/>
      <path id="Path_262330" data-name="Path 262330" d="M1000.063,2262.735a42.669,42.669,0,0,0,1.736,4.3l-39.727,39.728a42.66,42.66,0,0,0-4.3-1.737Z" transform="translate(-870.114 -2055.644)" fill="#0f1632"/>
      <path id="Path_262331" data-name="Path 262331" d="M1054.953,2320.708q.876,1.772,1.943,3.58l-37.567,37.569q-1.805-1.066-3.581-1.943Z" transform="translate(-922.784 -2108.311)" fill="#0f1632"/>
      <path id="Path_262332" data-name="Path 262332" d="M1105.56,2373.441q.938,1.5,2,3.01l-36.07,36.069q-1.507-1.063-3.01-2Z" transform="translate(-970.691 -2156.217)" fill="#0f1632"/>
      <path id="Path_262333" data-name="Path 262333" d="M1152.958,2422.254q.942,1.266,1.972,2.528l-35.112,35.113q-1.261-1.028-2.529-1.973Z" transform="translate(-1015.032 -2200.563)" fill="#0f1632"/>
      <path id="Path_262334" data-name="Path 262334" d="M1197.733,2467.836q.915,1.057,1.887,2.1l-34.635,34.635q-1.043-.973-2.1-1.887Z" transform="translate(-1056.454 -2241.973)" fill="#0f1632"/>
      <path id="Path_262335" data-name="Path 262335" d="M1205.533,2545.061l34.567-34.567c.073.075.146.149.221.224q.764.764,1.537,1.5l-34.607,34.606q-.731-.773-1.494-1.538l-.224-.22" transform="translate(-1095.199 -2280.727)" fill="#0f1632"/>
      <path id="Path_262336" data-name="Path 262336" d="M1280.214,2550.38q.8.7,1.6,1.368l-35.022,35.022q-.668-.8-1.367-1.6Z" transform="translate(-1131.439 -2316.963)" fill="#0f1632"/>
      <path id="Path_262337" data-name="Path 262337" d="M1317.965,2587.392q.7.535,1.411,1.043l-35.91,35.909q-.507-.7-1.044-1.41Z" transform="translate(-1165.052 -2350.587)" fill="#0f1632"/>
      <path id="Path_262338" data-name="Path 262338" d="M1353.109,2621.188c.4.255.8.5,1.2.746l-37.331,37.33c-.241-.4-.491-.8-.746-1.2Z" transform="translate(-1195.765 -2381.29)" fill="#0f1632"/>
      <path id="Path_262339" data-name="Path 262339" d="M1385.16,2651.216c.321.164.639.321.957.474l-39.391,39.391c-.154-.317-.311-.636-.475-.957Z" transform="translate(-1223.038 -2408.57)" fill="#0f1632"/>
      <path id="Path_262340" data-name="Path 262340" d="M1413.1,2676.188q.343.123.681.239l-42.3,42.3c-.076-.225-.156-.453-.238-.681Z" transform="translate(-1245.735 -2431.257)" fill="#0f1632"/>
      <path id="Path_262341" data-name="Path 262341" d="M1388.1,2739.358l46.3-46.3c.121.017.24.033.361.048l-46.611,46.612c-.015-.121-.031-.24-.049-.361" transform="translate(-1261.057 -2446.584)" fill="#0f1632"/>
      <path id="Path_262342" data-name="Path 262342" d="M1853.688,2511.088l9.027-9.027c.269,2.926-.357,5.382-2,7.025s-4.1,2.271-7.025,2" transform="translate(-1684.034 -2273.066)" fill="#0f1632"/>
      <path id="Path_262343" data-name="Path 262343" d="M1805.564,2440.7a21.962,21.962,0,0,1,1.353,5.195l-9.4,9.4a21.96,21.96,0,0,1-5.195-1.352Z" transform="translate(-1628.28 -2217.318)" fill="#0f1632"/>
      <path id="Path_262344" data-name="Path 262344" d="M1756.229,2388.886a35.617,35.617,0,0,1,1.994,4.042l-13.669,13.669a35.484,35.484,0,0,1-4.042-1.995Z" transform="translate(-1581.217 -2170.249)" fill="#0f1632"/>
      <path id="Path_262345" data-name="Path 262345" d="M1711.3,2342.483c.809,1.1,1.549,2.211,2.216,3.31l-16.1,16.1q-1.65-1-3.311-2.215Z" transform="translate(-1539.052 -2128.093)" fill="#0f1632"/>
      <path id="Path_262346" data-name="Path 262346" d="M1669.7,2300.139q1.208,1.357,2.279,2.735l-17.478,17.477q-1.377-1.068-2.734-2.278Z" transform="translate(-1500.595 -2089.624)" fill="#0f1632"/>
      <path id="Path_262347" data-name="Path 262347" d="M1612.962,2279.355l18.022-18.022q.641.6,1.272,1.231.5.5.987,1.012l-18.038,18.038q-.509-.483-1.012-.986-.63-.633-1.231-1.272" transform="translate(-1465.34 -2054.37)" fill="#0f1632"/>
      <path id="Path_262348" data-name="Path 262348" d="M1595.1,2226q1.1.855,2.189,1.8l-17.869,17.867q-.943-1.088-1.8-2.189Z" transform="translate(-1433.231 -2022.266)" fill="#0f1632"/>
      <path id="Path_262349" data-name="Path 262349" d="M1562.372,2194.521q1.047.647,2.1,1.38l-16.944,16.944q-.732-1.05-1.379-2.1Z" transform="translate(-1404.64 -1993.673)" fill="#0f1632"/>
      <path id="Path_262350" data-name="Path 262350" d="M1533.631,2167.921c.66.292,1.329.615,2,.965L1520.516,2184q-.526-1.009-.965-2Z" transform="translate(-1380.478 -1969.507)" fill="#0f1632"/>
      <path id="Path_262351" data-name="Path 262351" d="M1511.207,2149.056c.638.125,1.294.29,1.964.49l-11.995,12q-.3-1.005-.49-1.965Z" transform="translate(-1363.339 -1952.369)" fill="#0f1632"/>
      <path id="Path_262352" data-name="Path 262352" d="M1501.1,2148.92a7.186,7.186,0,0,1,3.581-1.638l-5.775,5.774a7.187,7.187,0,0,1,1.638-3.581Z" transform="translate(-1361.721 -1950.757)" fill="#0f1632"/>
      <path id="Path_262353" data-name="Path 262353" d="M1200.151,3164.628l9.028-9.026c.269,2.925-.358,5.38-2,7.024s-4.1,2.271-7.026,2" transform="translate(-1090.31 -2866.793)" fill="#0f1632"/>
      <path id="Path_262354" data-name="Path 262354" d="M1152.033,3094.225a21.945,21.945,0,0,1,1.352,5.2l-9.4,9.4a21.953,21.953,0,0,1-5.194-1.353Z" transform="translate(-1034.562 -2811.033)" fill="#0f1632"/>
      <path id="Path_262355" data-name="Path 262355" d="M1102.693,3042.423a35.416,35.416,0,0,1,1.994,4.042l-13.669,13.668a35.67,35.67,0,0,1-4.042-1.994Z" transform="translate(-987.494 -2763.973)" fill="#0f1632"/>
      <path id="Path_262356" data-name="Path 262356" d="M1057.768,2996.005q1.211,1.658,2.214,3.31l-16.1,16.1c-1.1-.667-2.206-1.406-3.31-2.215Z" transform="translate(-945.334 -2721.803)" fill="#0f1632"/>
      <path id="Path_262357" data-name="Path 262357" d="M1016.17,2953.675q1.207,1.355,2.278,2.733l-17.477,17.478q-1.376-1.069-2.734-2.278Z" transform="translate(-906.876 -2683.347)" fill="#0f1632"/>
      <path id="Path_262358" data-name="Path 262358" d="M959.425,2932.895l18.023-18.023q.64.6,1.272,1.232.5.5.985,1.011l-18.037,18.037q-.509-.483-1.011-.986c-.421-.421-.831-.845-1.232-1.272" transform="translate(-871.616 -2648.095)" fill="#0f1632"/>
      <path id="Path_262359" data-name="Path 262359" d="M941.566,2879.522q1.1.856,2.189,1.8l-17.867,17.868q-.945-1.089-1.8-2.191Z" transform="translate(-839.513 -2615.981)" fill="#0f1632"/>
      <path id="Path_262360" data-name="Path 262360" d="M908.832,2848.048q1.046.646,2.1,1.379l-16.944,16.944q-.734-1.048-1.38-2.1Z" transform="translate(-810.911 -2587.388)" fill="#0f1632"/>
      <path id="Path_262361" data-name="Path 262361" d="M880.094,2821.458q.99.438,2,.965l-15.117,15.117q-.525-1.01-.965-2Z" transform="translate(-786.754 -2563.23)" fill="#0f1632"/>
      <path id="Path_262362" data-name="Path 262362" d="M857.67,2802.587q.959.19,1.965.491l-11.995,11.995q-.3-1-.49-1.964Z" transform="translate(-769.617 -2546.087)" fill="#0f1632"/>
      <path id="Path_262363" data-name="Path 262363" d="M847.563,2802.455a7.205,7.205,0,0,1,3.581-1.637l-5.774,5.774a7.2,7.2,0,0,1,1.638-3.582Z" transform="translate(-767.999 -2544.48)" fill="#0f1632"/>
      <path id="Path_262364" data-name="Path 262364" d="M2510.238,1854.538l9.027-9.027c.27,2.925-.357,5.381-2,7.026s-4.1,2.27-7.027,2" transform="translate(-2280.494 -1676.605)" fill="#0f1632"/>
      <path id="Path_262365" data-name="Path 262365" d="M2462.115,1784.138a21.958,21.958,0,0,1,1.353,5.2l-9.405,9.4a22.021,22.021,0,0,1-5.194-1.352Z" transform="translate(-2224.741 -1620.849)" fill="#0f1632"/>
      <path id="Path_262366" data-name="Path 262366" d="M2412.777,1732.336a35.4,35.4,0,0,1,1.993,4.042l-13.668,13.669a35.435,35.435,0,0,1-4.042-1.994Z" transform="translate(-2177.675 -1573.788)" fill="#0f1632"/>
      <path id="Path_262367" data-name="Path 262367" d="M2367.86,1685.924c.809,1.1,1.549,2.211,2.215,3.309l-16.1,16.1c-1.1-.667-2.206-1.407-3.31-2.215Z" transform="translate(-2135.524 -1531.624)" fill="#0f1632"/>
      <path id="Path_262368" data-name="Path 262368" d="M2326.257,1643.589q1.208,1.355,2.279,2.734l-17.477,17.478q-1.377-1.07-2.734-2.279Z" transform="translate(-2097.06 -1493.164)" fill="#0f1632"/>
      <path id="Path_262369" data-name="Path 262369" d="M2269.518,1622.8l18.023-18.023c.426.4.851.811,1.272,1.232q.5.5.985,1.012l-18.036,18.037q-.508-.484-1.011-.986-.632-.631-1.232-1.271" transform="translate(-2061.806 -1457.904)" fill="#0f1632"/>
      <path id="Path_262370" data-name="Path 262370" d="M2251.648,1569.434q1.1.857,2.188,1.8l-17.867,17.868q-.944-1.087-1.8-2.189Z" transform="translate(-2029.691 -1425.795)" fill="#0f1632"/>
      <path id="Path_262371" data-name="Path 262371" d="M2218.924,1537.962q1.046.647,2.1,1.38l-16.944,16.943q-.732-1.049-1.379-2.1Z" transform="translate(-2001.101 -1397.204)" fill="#0f1632"/>
      <path id="Path_262372" data-name="Path 262372" d="M2190.181,1511.368q.991.439,2,.965l-15.117,15.117q-.527-1.009-.965-2Z" transform="translate(-1976.938 -1373.044)" fill="#0f1632"/>
      <path id="Path_262373" data-name="Path 262373" d="M2167.757,1492.5c.638.126,1.294.291,1.965.49l-12,12c-.2-.67-.363-1.326-.49-1.964Z" transform="translate(-1959.8 -1355.901)" fill="#0f1632"/>
      <path id="Path_262374" data-name="Path 262374" d="M2157.65,1492.366a7.207,7.207,0,0,1,3.58-1.637l-5.773,5.774a7.2,7.2,0,0,1,1.638-3.581Z" transform="translate(-1958.183 -1354.294)" fill="#0f1632"/>
      <path id="Path_262375" data-name="Path 262375" d="M684.676,1342.079l9.027-9.027c.269,2.926-.358,5.381-2,7.025s-4.1,2.271-7.025,2" transform="translate(-622.013 -1211.048)" fill="#0f1632"/>
      <path id="Path_262376" data-name="Path 262376" d="M636.553,1271.679a21.968,21.968,0,0,1,1.352,5.195l-9.4,9.4a21.983,21.983,0,0,1-5.194-1.352Z" transform="translate(-566.259 -1155.292)" fill="#0f1632"/>
      <path id="Path_262377" data-name="Path 262377" d="M587.216,1219.871a35.689,35.689,0,0,1,1.993,4.041l-13.668,13.67a35.6,35.6,0,0,1-4.042-1.995Z" transform="translate(-519.195 -1108.225)" fill="#0f1632"/>
      <path id="Path_262378" data-name="Path 262378" d="M542.286,1173.47q1.212,1.656,2.214,3.309l-16.1,16.1q-1.649-1-3.31-2.214Z" transform="translate(-477.031 -1066.071)" fill="#0f1632"/>
      <path id="Path_262379" data-name="Path 262379" d="M500.695,1131.129q1.208,1.354,2.279,2.734L485.5,1151.341q-1.376-1.069-2.733-2.279Z" transform="translate(-438.578 -1027.605)" fill="#0f1632"/>
      <path id="Path_262380" data-name="Path 262380" d="M443.95,1110.359l18.022-18.021q.641.6,1.271,1.23c.335.335.664.673.986,1.011l-18.038,18.038q-.508-.483-1.011-.985c-.421-.421-.83-.846-1.231-1.272" transform="translate(-403.319 -992.364)" fill="#0f1632"/>
      <path id="Path_262381" data-name="Path 262381" d="M426.084,1056.977q1.1.855,2.19,1.8l-17.867,17.868q-.946-1.089-1.8-2.19Z" transform="translate(-371.209 -960.24)" fill="#0f1632"/>
      <path id="Path_262382" data-name="Path 262382" d="M393.361,1025.505q1.046.646,2.1,1.38l-16.944,16.943q-.732-1.049-1.379-2.1Z" transform="translate(-342.619 -931.648)" fill="#0f1632"/>
      <path id="Path_262383" data-name="Path 262383" d="M364.617,998.908q.99.438,2,.964L351.5,1014.99q-.528-1.01-.965-2Z" transform="translate(-318.455 -907.485)" fill="#0f1632"/>
      <path id="Path_262384" data-name="Path 262384" d="M342.184,980.038c.638.127,1.294.291,1.964.49l-11.995,12q-.3-1.006-.49-1.965Z" transform="translate(-301.307 -890.343)" fill="#0f1632"/>
      <path id="Path_262385" data-name="Path 262385" d="M332.087,979.907a7.2,7.2,0,0,1,3.58-1.637l-5.774,5.775a7.2,7.2,0,0,1,1.638-3.582Z" transform="translate(-299.7 -888.735)" fill="#0f1632"/>
      <path id="Path_262386" data-name="Path 262386" d="M1434.891,1486.8l60.089-60.09c-2.316,2.316-3.2,5.773-2.832,9.891l-47.367,47.367c-4.117-.372-7.574.516-9.89,2.832" transform="translate(-1303.566 -1296.129)" fill="#0f1632"/>
      <path id="Path_262387" data-name="Path 262387" d="M1594.558,1539.394a28.362,28.362,0,0,0,1.172,5.374l-42.786,42.787a28.259,28.259,0,0,0-5.374-1.173Z" transform="translate(-1405.932 -1398.505)" fill="#0f1632"/>
      <path id="Path_262388" data-name="Path 262388" d="M1656.635,1606.166a42.559,42.559,0,0,0,1.737,4.3l-39.728,39.727a42.441,42.441,0,0,0-4.3-1.736Z" transform="translate(-1466.595 -1459.166)" fill="#0f1632"/>
      <path id="Path_262389" data-name="Path 262389" d="M1711.521,1664.141q.876,1.772,1.944,3.58l-37.569,37.568q-1.8-1.065-3.579-1.943Z" transform="translate(-1519.261 -1511.835)" fill="#0f1632"/>
      <path id="Path_262390" data-name="Path 262390" d="M1762.12,1716.868q.938,1.5,2,3.011l-36.069,36.069q-1.507-1.062-3.01-2Z" transform="translate(-1567.162 -1559.736)" fill="#0f1632"/>
      <path id="Path_262391" data-name="Path 262391" d="M1809.532,1765.689q.942,1.266,1.971,2.528l-35.112,35.112q-1.259-1.028-2.528-1.972Z" transform="translate(-1611.514 -1604.089)" fill="#0f1632"/>
      <path id="Path_262392" data-name="Path 262392" d="M1854.3,1811.271q.915,1.056,1.887,2.1l-34.635,34.635q-1.043-.973-2.1-1.887Z" transform="translate(-1652.93 -1645.499)" fill="#0f1632"/>
      <path id="Path_262393" data-name="Path 262393" d="M1862.1,1888.488l34.567-34.567c.073.075.146.149.221.224q.765.765,1.538,1.5l-34.607,34.606q-.733-.774-1.495-1.537c-.074-.074-.149-.147-.224-.221" transform="translate(-1691.676 -1684.245)" fill="#0f1632"/>
      <path id="Path_262394" data-name="Path 262394" d="M1936.786,1893.81q.8.7,1.6,1.367l-35.023,35.023q-.666-.8-1.366-1.6Z" transform="translate(-1727.92 -1720.484)" fill="#0f1632"/>
      <path id="Path_262395" data-name="Path 262395" d="M1974.545,1930.824q.706.534,1.412,1.043l-35.909,35.909q-.507-.7-1.042-1.411Z" transform="translate(-1761.542 -1754.11)" fill="#0f1632"/>
      <path id="Path_262396" data-name="Path 262396" d="M2009.676,1964.624c.4.255.8.5,1.2.745l-37.33,37.331q-.363-.6-.746-1.2Z" transform="translate(-1792.241 -1784.817)" fill="#0f1632"/>
      <path id="Path_262397" data-name="Path 262397" d="M2041.732,1994.646c.32.164.639.321.956.475l-39.391,39.391c-.154-.318-.311-.636-.474-.957Z" transform="translate(-1819.52 -1812.091)" fill="#0f1632"/>
      <path id="Path_262398" data-name="Path 262398" d="M2069.658,2019.624c.228.082.456.162.681.239l-42.306,42.3q-.114-.339-.238-.682Z" transform="translate(-1842.206 -1834.783)" fill="#0f1632"/>
      <path id="Path_262399" data-name="Path 262399" d="M2044.672,2082.8l46.3-46.3.362.048-46.612,46.612c-.015-.121-.03-.24-.047-.361" transform="translate(-1857.538 -1850.112)" fill="#0f1632"/>
      <path id="Path_262400" data-name="Path 262400" d="M274.066,325.977l60.09-60.09c-2.317,2.316-3.2,5.774-2.832,9.891l-47.367,47.368c-4.117-.372-7.574.515-9.891,2.831" transform="translate(-248.983 -241.552)" fill="#0f1632"/>
      <path id="Path_262401" data-name="Path 262401" d="M433.745,378.586a28.259,28.259,0,0,0,1.172,5.373l-42.786,42.788a28.238,28.238,0,0,0-5.374-1.173Z" transform="translate(-351.36 -343.937)" fill="#0f1632"/>
      <path id="Path_262402" data-name="Path 262402" d="M495.817,445.346a42.611,42.611,0,0,0,1.736,4.3l-39.727,39.727a42.242,42.242,0,0,0-4.3-1.737Z" transform="translate(-412.018 -404.587)" fill="#0f1632"/>
      <path id="Path_262403" data-name="Path 262403" d="M550.707,503.328q.876,1.771,1.944,3.58l-37.569,37.568q-1.8-1.066-3.58-1.944Z" transform="translate(-464.688 -457.262)" fill="#0f1632"/>
      <path id="Path_262404" data-name="Path 262404" d="M601.308,556.056q.937,1.5,2,3.011l-36.069,36.07q-1.507-1.064-3.011-2Z" transform="translate(-512.589 -505.164)" fill="#0f1632"/>
      <path id="Path_262405" data-name="Path 262405" d="M648.716,604.861q.943,1.268,1.973,2.529L615.577,642.5q-1.261-1.027-2.528-1.971Z" transform="translate(-556.941 -549.503)" fill="#0f1632"/>
      <path id="Path_262406" data-name="Path 262406" d="M693.476,650.444q.915,1.057,1.887,2.1l-34.635,34.635q-1.045-.974-2.1-1.888Z" transform="translate(-598.347 -590.914)" fill="#0f1632"/>
      <path id="Path_262407" data-name="Path 262407" d="M701.287,727.677l34.566-34.567.221.223q.764.764,1.538,1.5l-34.607,34.605q-.733-.772-1.5-1.537c-.074-.074-.149-.147-.223-.22" transform="translate(-637.103 -629.675)" fill="#0f1632"/>
      <path id="Path_262408" data-name="Path 262408" d="M775.973,733q.8.7,1.6,1.366l-35.022,35.022q-.668-.8-1.366-1.6Z" transform="translate(-673.348 -665.914)" fill="#0f1632"/>
      <path id="Path_262409" data-name="Path 262409" d="M813.726,770.009q.706.534,1.412,1.043l-35.911,35.909q-.507-.706-1.042-1.411Z" transform="translate(-706.963 -699.536)" fill="#0f1632"/>
      <path id="Path_262410" data-name="Path 262410" d="M848.864,803.811q.6.383,1.2.745L812.73,841.886c-.24-.4-.49-.8-.745-1.2Z" transform="translate(-737.67 -730.244)" fill="#0f1632"/>
      <path id="Path_262411" data-name="Path 262411" d="M880.907,833.822c.32.165.64.322.957.476l-39.391,39.392c-.154-.319-.311-.637-.475-.958Z" transform="translate(-764.936 -757.509)" fill="#0f1632"/>
      <path id="Path_262412" data-name="Path 262412" d="M908.844,858.805q.343.124.682.239l-42.306,42.3q-.114-.339-.238-.681Z" transform="translate(-787.634 -780.205)" fill="#0f1632"/>
      <path id="Path_262413" data-name="Path 262413" d="M883.853,921.983l46.3-46.3.36.047L883.9,922.344c-.015-.12-.031-.24-.048-.361" transform="translate(-802.96 -795.54)" fill="#0f1632"/>
      <path id="Path_262414" data-name="Path 262414" d="M3006.313,3663.711l9.026-9.027c.27,2.926-.356,5.382-2,7.026s-4.1,2.271-7.025,2" transform="translate(-2731.167 -3320.198)" fill="#0f1632"/>
      <path id="Path_262415" data-name="Path 262415" d="M2958.19,3593.323a21.919,21.919,0,0,1,1.352,5.194l-9.4,9.4a21.936,21.936,0,0,1-5.194-1.352Z" transform="translate(-2675.414 -3264.453)" fill="#0f1632"/>
      <path id="Path_262416" data-name="Path 262416" d="M2908.855,3541.506a35.556,35.556,0,0,1,1.994,4.043l-13.669,13.667a35.53,35.53,0,0,1-4.041-1.994Z" transform="translate(-2628.351 -3217.378)" fill="#0f1632"/>
      <path id="Path_262417" data-name="Path 262417" d="M2863.924,3495.108c.809,1.105,1.549,2.212,2.215,3.309l-16.1,16.1q-1.649-1-3.31-2.215Z" transform="translate(-2586.187 -3175.227)" fill="#0f1632"/>
      <path id="Path_262418" data-name="Path 262418" d="M2822.328,3452.773q1.207,1.354,2.278,2.733l-17.477,17.477q-1.377-1.068-2.734-2.278Z" transform="translate(-2547.729 -3136.766)" fill="#0f1632"/>
      <path id="Path_262419" data-name="Path 262419" d="M2765.593,3431.987l18.023-18.023q.639.6,1.271,1.231.5.5.985,1.012l-18.036,18.037q-.508-.484-1.012-.986-.632-.632-1.231-1.272" transform="translate(-2512.478 -3101.509)" fill="#0f1632"/>
      <path id="Path_262420" data-name="Path 262420" d="M2747.722,3378.62q1.1.855,2.19,1.8l-17.868,17.869q-.946-1.088-1.8-2.189Z" transform="translate(-2480.365 -3069.4)" fill="#0f1632"/>
      <path id="Path_262421" data-name="Path 262421" d="M2714.989,3347.146q1.047.647,2.1,1.38l-16.943,16.943q-.733-1.049-1.381-2.1Z" transform="translate(-2451.763 -3040.806)" fill="#0f1632"/>
      <path id="Path_262422" data-name="Path 262422" d="M2686.256,3320.546q.99.438,2,.964l-15.117,15.118q-.525-1.011-.964-2Z" transform="translate(-2427.611 -3016.641)" fill="#0f1632"/>
      <path id="Path_262423" data-name="Path 262423" d="M2663.832,3301.679c.639.127,1.295.291,1.964.491l-11.995,11.995c-.2-.671-.364-1.327-.49-1.964Z" transform="translate(-2410.474 -2999.5)" fill="#0f1632"/>
      <path id="Path_262424" data-name="Path 262424" d="M2653.725,3301.553a7.184,7.184,0,0,1,3.58-1.637l-5.774,5.774a7.186,7.186,0,0,1,1.638-3.581Z" transform="translate(-2408.856 -2997.899)" fill="#0f1632"/>
      <path id="Path_262425" data-name="Path 262425" d="M2595.709,2647.625l60.09-60.088c-2.316,2.315-3.2,5.772-2.832,9.89l-47.367,47.367c-4.117-.372-7.574.516-9.89,2.832" transform="translate(-2358.143 -2350.718)" fill="#0f1632"/>
      <path id="Path_262426" data-name="Path 262426" d="M2755.383,2700.221a28.259,28.259,0,0,0,1.173,5.374l-42.788,42.787a28.138,28.138,0,0,0-5.374-1.172Z" transform="translate(-2460.515 -2453.09)" fill="#0f1632"/>
      <path id="Path_262427" data-name="Path 262427" d="M2817.454,2767a42.878,42.878,0,0,0,1.737,4.3l-39.727,39.728a42.365,42.365,0,0,0-4.3-1.737Z" transform="translate(-2521.174 -2513.754)" fill="#0f1632"/>
      <path id="Path_262428" data-name="Path 262428" d="M2872.345,2824.966q.877,1.772,1.944,3.579l-37.569,37.568q-1.8-1.066-3.579-1.943Z" transform="translate(-2573.844 -2566.418)" fill="#0f1632"/>
      <path id="Path_262429" data-name="Path 262429" d="M2922.945,2877.7q.938,1.5,2,3.01l-36.069,36.07q-1.507-1.062-3.01-2Z" transform="translate(-2621.745 -2614.327)" fill="#0f1632"/>
      <path id="Path_262430" data-name="Path 262430" d="M2970.356,2926.5q.943,1.266,1.97,2.528l-35.112,35.112q-1.259-1.028-2.527-1.973Z" transform="translate(-2666.097 -2658.662)" fill="#0f1632"/>
      <path id="Path_262431" data-name="Path 262431" d="M3015.114,2972.094q.915,1.056,1.887,2.1l-34.635,34.635q-1.045-.972-2.1-1.887Z" transform="translate(-2707.502 -2700.08)" fill="#0f1632"/>
      <path id="Path_262432" data-name="Path 262432" d="M3022.925,3049.31l34.566-34.568.222.224q.763.764,1.537,1.495l-34.606,34.606q-.731-.774-1.5-1.538c-.073-.074-.149-.146-.223-.22" transform="translate(-2746.259 -2738.825)" fill="#0f1632"/>
      <path id="Path_262433" data-name="Path 262433" d="M3097.61,3054.641q.8.7,1.6,1.367l-35.022,35.022q-.667-.8-1.366-1.6Z" transform="translate(-2782.503 -2775.072)" fill="#0f1632"/>
      <path id="Path_262434" data-name="Path 262434" d="M3135.364,3091.641q.706.536,1.411,1.044l-35.908,35.909q-.508-.7-1.043-1.411Z" transform="translate(-2816.119 -2808.686)" fill="#0f1632"/>
      <path id="Path_262435" data-name="Path 262435" d="M3170.5,3125.449c.4.254.8.5,1.2.746l-37.331,37.33q-.361-.6-.745-1.2Z" transform="translate(-2846.826 -2839.399)" fill="#0f1632"/>
      <path id="Path_262436" data-name="Path 262436" d="M3202.552,3155.469c.32.164.638.321.955.475l-39.39,39.392c-.154-.319-.312-.637-.475-.958Z" transform="translate(-2874.097 -2866.672)" fill="#0f1632"/>
      <path id="Path_262437" data-name="Path 262437" d="M3230.492,3180.452c.227.082.455.161.681.238L3188.868,3223c-.076-.226-.156-.454-.237-.682Z" transform="translate(-2896.799 -2889.369)" fill="#0f1632"/>
      <path id="Path_262438" data-name="Path 262438" d="M3205.49,3243.616l46.3-46.3c.121.018.24.033.361.048l-46.611,46.611c-.016-.12-.032-.24-.048-.361" transform="translate(-2912.115 -2904.69)" fill="#0f1632"/>
      <path id="Path_262439" data-name="Path 262439" d="M1182.751,578.128l60.089-60.089c-2.317,2.316-3.2,5.773-2.832,9.89L1192.641,575.3c-4.117-.373-7.574.515-9.89,2.831" transform="translate(-1074.502 -470.627)" fill="#0f1632"/>
      <path id="Path_262440" data-name="Path 262440" d="M1342.418,630.723a28.218,28.218,0,0,0,1.173,5.374L1300.8,678.884a28.282,28.282,0,0,0-5.375-1.173Z" transform="translate(-1176.869 -572.998)" fill="#0f1632"/>
      <path id="Path_262441" data-name="Path 262441" d="M1404.5,697.5a42.168,42.168,0,0,0,1.736,4.3L1366.5,741.525a42.334,42.334,0,0,0-4.3-1.737Z" transform="translate(-1237.532 -633.661)" fill="#0f1632"/>
      <path id="Path_262442" data-name="Path 262442" d="M1459.385,755.469q.875,1.771,1.944,3.58l-37.569,37.568q-1.8-1.066-3.58-1.944Z" transform="translate(-1290.202 -686.327)" fill="#0f1632"/>
      <path id="Path_262443" data-name="Path 262443" d="M1509.985,808.2q.938,1.5,2,3.01l-36.07,36.069q-1.507-1.062-3.01-2Z" transform="translate(-1338.104 -734.227)" fill="#0f1632"/>
      <path id="Path_262444" data-name="Path 262444" d="M1557.39,857.01q.944,1.267,1.972,2.528l-35.112,35.113q-1.26-1.029-2.527-1.973Z" transform="translate(-1382.45 -778.574)" fill="#0f1632"/>
      <path id="Path_262445" data-name="Path 262445" d="M1602.153,902.593q.914,1.057,1.887,2.1l-34.635,34.635q-1.044-.973-2.1-1.889Z" transform="translate(-1423.86 -819.985)" fill="#0f1632"/>
      <path id="Path_262446" data-name="Path 262446" d="M1609.954,979.817l34.567-34.567c.074.074.146.15.221.223q.764.765,1.538,1.5l-34.606,34.606q-.733-.773-1.5-1.537c-.074-.075-.148-.147-.224-.221" transform="translate(-1462.607 -858.738)" fill="#0f1632"/>
      <path id="Path_262447" data-name="Path 262447" d="M1684.635,985.143q.8.7,1.6,1.367l-35.022,35.021q-.667-.8-1.367-1.6Z" transform="translate(-1498.846 -894.98)" fill="#0f1632"/>
      <path id="Path_262448" data-name="Path 262448" d="M1722.39,1022.149q.7.534,1.41,1.044l-35.909,35.909q-.507-.706-1.044-1.412Z" transform="translate(-1532.462 -928.599)" fill="#0f1632"/>
      <path id="Path_262449" data-name="Path 262449" d="M1757.531,1055.957c.4.255.8.5,1.2.745l-37.33,37.331q-.363-.6-.746-1.2Z" transform="translate(-1563.173 -959.313)" fill="#0f1632"/>
      <path id="Path_262450" data-name="Path 262450" d="M1789.586,1085.97c.32.164.64.321.957.474l-39.391,39.392q-.231-.476-.474-.957Z" transform="translate(-1590.45 -986.579)" fill="#0f1632"/>
      <path id="Path_262451" data-name="Path 262451" d="M1817.523,1110.954c.227.082.455.161.681.238L1775.9,1153.5q-.115-.339-.237-.681Z" transform="translate(-1613.148 -1009.277)" fill="#0f1632"/>
      <path id="Path_262452" data-name="Path 262452" d="M1792.531,1174.124l46.3-46.3c.12.017.24.033.36.048l-46.611,46.611c-.015-.119-.03-.24-.048-.36" transform="translate(-1628.474 -1024.604)" fill="#0f1632"/>
      <path id="Path_262453" data-name="Path 262453" d="M2105.816,3419.79l9.027-9.026c.269,2.925-.359,5.38-2,7.025s-4.1,2.271-7.025,2" transform="translate(-1913.086 -3098.601)" fill="#0f1632"/>
      <path id="Path_262454" data-name="Path 262454" d="M2057.693,3349.39a21.965,21.965,0,0,1,1.351,5.195l-9.4,9.4a22,22,0,0,1-5.195-1.352Z" transform="translate(-1857.333 -3042.845)" fill="#0f1632"/>
      <path id="Path_262455" data-name="Path 262455" d="M2008.357,3297.588a35.536,35.536,0,0,1,1.994,4.04l-13.669,13.671a35.577,35.577,0,0,1-4.041-1.995Z" transform="translate(-1810.269 -2995.784)" fill="#0f1632"/>
      <path id="Path_262456" data-name="Path 262456" d="M1963.433,3251.173c.808,1.1,1.548,2.211,2.214,3.309l-16.1,16.1c-1.1-.667-2.2-1.406-3.308-2.215Z" transform="translate(-1768.111 -2953.617)" fill="#0f1632"/>
      <path id="Path_262457" data-name="Path 262457" d="M1921.818,3208.832q1.208,1.356,2.279,2.734l-17.477,17.478q-1.377-1.07-2.735-2.278Z" transform="translate(-1729.637 -2915.151)" fill="#0f1632"/>
      <path id="Path_262458" data-name="Path 262458" d="M1865.085,3188.056l18.022-18.022c.427.4.851.81,1.272,1.231q.5.5.985,1.011l-18.037,18.039c-.338-.323-.677-.653-1.012-.987q-.63-.632-1.231-1.273" transform="translate(-1694.388 -2879.904)" fill="#0f1632"/>
      <path id="Path_262459" data-name="Path 262459" d="M1847.214,3134.68q1.1.856,2.19,1.8l-17.868,17.868q-.943-1.087-1.8-2.191Z" transform="translate(-1662.273 -2847.785)" fill="#0f1632"/>
      <path id="Path_262460" data-name="Path 262460" d="M1814.492,3103.213q1.045.646,2.1,1.38l-16.944,16.943q-.734-1.05-1.381-2.1Z" transform="translate(-1633.683 -2819.199)" fill="#0f1632"/>
      <path id="Path_262461" data-name="Path 262461" d="M1785.753,3076.622q.989.437,2,.964l-15.117,15.117q-.528-1.011-.965-2Z" transform="translate(-1609.525 -2795.042)" fill="#0f1632"/>
      <path id="Path_262462" data-name="Path 262462" d="M1763.324,3057.746q.958.19,1.964.49l-12,11.994c-.2-.669-.363-1.326-.49-1.964Z" transform="translate(-1592.382 -2777.893)" fill="#0f1632"/>
      <path id="Path_262463" data-name="Path 262463" d="M1753.229,3057.615a7.183,7.183,0,0,1,3.58-1.637l-5.774,5.774a7.183,7.183,0,0,1,1.638-3.58Z" transform="translate(-1590.776 -2776.286)" fill="#0f1632"/>
      <path id="Path_262464" data-name="Path 262464" d="M1687.014,2395.5l60.09-60.089c-2.317,2.317-3.2,5.771-2.832,9.89l-47.367,47.368c-4.118-.373-7.574.516-9.89,2.832" transform="translate(-1532.614 -2121.665)" fill="#0f1632"/>
      <path id="Path_262465" data-name="Path 262465" d="M1846.686,2448.087a28.268,28.268,0,0,0,1.174,5.374l-42.787,42.787a28.249,28.249,0,0,0-5.374-1.174Z" transform="translate(-1634.986 -2224.032)" fill="#0f1632"/>
      <path id="Path_262466" data-name="Path 262466" d="M1908.77,2514.867a42.281,42.281,0,0,0,1.736,4.3l-39.728,39.729a42.612,42.612,0,0,0-4.3-1.737Z" transform="translate(-1695.655 -2284.7)" fill="#0f1632"/>
      <path id="Path_262467" data-name="Path 262467" d="M1963.648,2572.837q.875,1.771,1.944,3.579l-37.569,37.569c-1.2-.711-2.4-1.361-3.579-1.944Z" transform="translate(-1748.314 -2337.364)" fill="#0f1632"/>
      <path id="Path_262468" data-name="Path 262468" d="M2014.239,2625.553q.937,1.5,2,3.012l-36.069,36.069q-1.507-1.063-3.011-2Z" transform="translate(-1796.205 -2385.256)" fill="#0f1632"/>
      <path id="Path_262469" data-name="Path 262469" d="M2061.659,2674.373q.943,1.268,1.972,2.529l-35.112,35.113q-1.261-1.03-2.528-1.973Z" transform="translate(-1840.567 -2429.607)" fill="#0f1632"/>
      <path id="Path_262470" data-name="Path 262470" d="M2106.418,2719.956q.914,1.058,1.886,2.1l-34.635,34.635q-1.043-.972-2.1-1.887Z" transform="translate(-1881.973 -2471.019)" fill="#0f1632"/>
      <path id="Path_262471" data-name="Path 262471" d="M2114.229,2797.189l34.566-34.566.222.223q.763.765,1.537,1.5l-34.605,34.606q-.734-.773-1.5-1.537c-.074-.075-.15-.147-.224-.222" transform="translate(-1920.729 -2509.781)" fill="#0f1632"/>
      <path id="Path_262472" data-name="Path 262472" d="M2188.9,2802.5q.795.7,1.6,1.367l-35.021,35.022q-.667-.8-1.367-1.6Z" transform="translate(-1956.963 -2546.008)" fill="#0f1632"/>
      <path id="Path_262473" data-name="Path 262473" d="M2226.667,2839.521q.706.535,1.413,1.045l-35.91,35.907q-.509-.705-1.043-1.411Z" transform="translate(-1990.589 -2579.641)" fill="#0f1632"/>
      <path id="Path_262474" data-name="Path 262474" d="M2261.807,2873.321q.6.381,1.2.745l-37.331,37.33q-.362-.6-.745-1.2Z" transform="translate(-2021.296 -2610.347)" fill="#0f1632"/>
      <path id="Path_262475" data-name="Path 262475" d="M2293.851,2903.334c.32.164.639.321.957.476l-39.391,39.391q-.23-.477-.476-.957Z" transform="translate(-2048.563 -2637.613)" fill="#0f1632"/>
      <path id="Path_262476" data-name="Path 262476" d="M2321.792,2928.329c.228.081.456.161.682.237l-42.305,42.305c-.077-.226-.157-.454-.238-.682Z" transform="translate(-2071.265 -2660.321)" fill="#0f1632"/>
      <path id="Path_262477" data-name="Path 262477" d="M2296.794,2991.493l46.3-46.3c.12.018.24.032.361.049l-46.611,46.611c-.017-.12-.031-.24-.049-.361" transform="translate(-2086.585 -2675.643)" fill="#0f1632"/>
      <path id="Path_262478" data-name="Path 262478" d="M2762.384,2763.219l9.027-9.027c.269,2.926-.358,5.381-2,7.025s-4.1,2.271-7.024,2" transform="translate(-2509.563 -2502.121)" fill="#0f1632"/>
      <path id="Path_262479" data-name="Path 262479" d="M2714.261,2692.826a21.948,21.948,0,0,1,1.352,5.194l-9.4,9.4a21.985,21.985,0,0,1-5.194-1.352Z" transform="translate(-2453.811 -2446.372)" fill="#0f1632"/>
      <path id="Path_262480" data-name="Path 262480" d="M2664.925,2641.015a35.34,35.34,0,0,1,1.994,4.041l-13.669,13.669a35.4,35.4,0,0,1-4.041-1.994Z" transform="translate(-2406.747 -2399.303)" fill="#0f1632"/>
      <path id="Path_262481" data-name="Path 262481" d="M2620,2594.612q1.211,1.656,2.214,3.309l-16.1,16.1c-1.1-.667-2.206-1.406-3.308-2.215Z" transform="translate(-2364.592 -2357.146)" fill="#0f1632"/>
      <path id="Path_262482" data-name="Path 262482" d="M2578.391,2552.259q1.208,1.356,2.279,2.734l-17.479,17.478q-1.375-1.069-2.733-2.279Z" transform="translate(-2326.118 -2318.67)" fill="#0f1632"/>
      <path id="Path_262483" data-name="Path 262483" d="M2521.658,2531.492l18.022-18.022q.641.6,1.272,1.231c.334.334.664.672.985,1.011L2523.9,2533.75q-.508-.484-1.011-.986c-.421-.421-.83-.844-1.231-1.272" transform="translate(-2290.869 -2283.431)" fill="#0f1632"/>
      <path id="Path_262484" data-name="Path 262484" d="M2503.787,2478.115q1.1.854,2.19,1.8l-17.869,17.868q-.943-1.087-1.8-2.189Z" transform="translate(-2258.755 -2251.312)" fill="#0f1632"/>
      <path id="Path_262485" data-name="Path 262485" d="M2471.061,2446.646q1.046.647,2.1,1.38l-16.944,16.944q-.733-1.05-1.379-2.1Z" transform="translate(-2230.162 -2222.722)" fill="#0f1632"/>
      <path id="Path_262486" data-name="Path 262486" d="M2442.327,2420.049q.989.438,2,.964l-15.117,15.118q-.527-1.011-.965-2Z" transform="translate(-2206.008 -2198.56)" fill="#0f1632"/>
      <path id="Path_262487" data-name="Path 262487" d="M2419.892,2401.185q.957.189,1.964.491l-11.994,11.994c-.2-.67-.364-1.326-.49-1.964Z" transform="translate(-2188.859 -2181.422)" fill="#0f1632"/>
      <path id="Path_262488" data-name="Path 262488" d="M2409.8,2401.049a7.178,7.178,0,0,1,3.581-1.638l-5.774,5.773a7.19,7.19,0,0,1,1.638-3.58Z" transform="translate(-2187.252 -2179.811)" fill="#0f1632"/>
      <path id="Path_262489" data-name="Path 262489" d="M901.7,236.959c-7.049,7.048-.872,24.654,13.8,39.323s32.274,20.847,39.322,13.8.873-24.654-13.8-39.323-32.275-20.846-39.324-13.8" transform="translate(-816.523 -212.624)" fill="#255be3"/>
      <path id="Path_262490" data-name="Path 262490" d="M3288.968,2284.928c-14.669-14.669-32.274-20.847-39.323-13.8l-26.31,26.311c-7.048,7.048-.872,24.653,13.8,39.322s32.273,20.847,39.322,13.8l26.31-26.31c7.049-7.049.872-24.655-13.8-39.324m-48.1,48.1c-10.378-10.378-14.748-22.833-9.762-27.82s17.442-.617,27.821,9.762,14.747,22.831,9.76,27.818-17.441.618-27.818-9.759" transform="translate(-2925.68 -2060.623)" fill="#e6ebed"/>
    </g>
  </g>
</svg>
