<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="194.244" height="157.718" viewBox="0 0 194.244 157.718">
  <defs>
    <clipPath id="clip-path">
      <path id="Path_268749" data-name="Path 268749" d="M378,24.158V0h5.929l12.908,0a18.177,18.177,0,0,1,7.773,1.636c6.045,2.829,9.457,9.006,9.619,15.97,0,.018,0,.035,0,.053,0,.094,0,.188,0,.283,0,.059,0,.117,0,.176v6.036h0a18.122,18.122,0,0,1-18.117,18.118h0A18.125,18.125,0,0,1,378,24.158" transform="translate(-377.997)" fill="#0f1632"/>
    </clipPath>
    <filter id="Path_268747" x="0" y="55.363" width="126.511" height="102.355" filterUnits="userSpaceOnUse">
      <feOffset dy="3" input="SourceAlpha"/>
      <feGaussianBlur stdDeviation="8" result="blur"/>
      <feFlood flood-opacity="0.161"/>
      <feComposite operator="in" in2="blur"/>
      <feComposite in="SourceGraphic"/>
    </filter>
    <clipPath id="clip-path-2">
      <path id="Path_268749-2" data-name="Path 268749" d="M0,17.926V0H4.4l9.578,0a13.488,13.488,0,0,1,5.768,1.214c4.486,2.1,7.018,6.683,7.138,11.851,0,.013,0,.026,0,.039,0,.07,0,.14,0,.21,0,.044,0,.087,0,.131v4.479h0A13.447,13.447,0,0,1,13.445,31.37h0A13.449,13.449,0,0,1,0,17.926" fill="#0f1632"/>
    </clipPath>
    <filter id="Path_268747-2" x="87.986" y="0" width="106.258" height="88.334" filterUnits="userSpaceOnUse">
      <feOffset dy="3" input="SourceAlpha"/>
      <feGaussianBlur stdDeviation="8" result="blur-2"/>
      <feFlood flood-opacity="0.161"/>
      <feComposite operator="in" in2="blur-2"/>
      <feComposite in="SourceGraphic"/>
    </filter>
  </defs>
  <g id="Group_147334" data-name="Group 147334" transform="translate(4.286 0.463)">
    <g id="Group_147332" data-name="Group 147332" transform="translate(19.714 75.9)" style="isolation: isolate">
      <g id="Group_147315" data-name="Group 147315" transform="translate(42.275 36.236)" style="isolation: isolate">
        <g id="Group_147314" data-name="Group 147314" transform="translate(0)">
          <g id="Group_147318" data-name="Group 147318">
            <g id="Group_147312" data-name="Group 147312">
              <g id="Group_147311" data-name="Group 147311" clip-path="url(#clip-path)">
                <path id="Path_268750" data-name="Path 268750" d="M0,48.368H36.265V0H0Z" transform="translate(-0.014 -6.102)" fill="#0f1632"/>
              </g>
            </g>
          </g>
        </g>
      </g>
      <g transform="matrix(1, 0, 0, 1, -24, -76.36)" filter="url(#Path_268747)">
        <path id="Path_268747-3" data-name="Path 268747" d="M18.117,215.994h41.19a19.2,19.2,0,0,1,19.2,19.2v35.151c0-.058,0-.116,0-.174,0-.094,0-.189,0-.283,0-.018,0-.035,0-.053-.162-6.965-3.574-13.141-9.619-15.97a18.185,18.185,0,0,0-7.773-1.638H18.118A18.122,18.122,0,0,1,0,234.113H0a18.125,18.125,0,0,1,18.117-18.118" transform="translate(24 -139.63)" fill="#255be3"/>
      </g>
    </g>
    <g id="Group_147333" data-name="Group 147333" transform="translate(165.958 60.871) rotate(180)" style="isolation: isolate">
      <g id="Group_147315-2" data-name="Group 147315" transform="translate(31.37 26.889)" style="isolation: isolate">
        <g id="Group_147314-2" data-name="Group 147314">
          <g id="Group_147318-2" data-name="Group 147318">
            <g id="Group_147312-2" data-name="Group 147312">
              <g id="Group_147311-2" data-name="Group 147311" clip-path="url(#clip-path-2)">
                <path id="Path_268750-2" data-name="Path 268750" d="M0,35.891H26.91V0H0Z" transform="translate(-0.01 -4.528)" fill="#0f1632"/>
              </g>
            </g>
          </g>
        </g>
      </g>
      <g transform="matrix(-1, 0, 0, -1, 170.24, 61.33)" filter="url(#Path_268747-2)">
        <path id="Path_268747-4" data-name="Path 268747" d="M13.444,0H44.008a14.25,14.25,0,0,1,14.25,14.25V40.334c0-.043,0-.086,0-.129,0-.07,0-.14,0-.21,0-.013,0-.026,0-.039-.12-5.168-2.652-9.751-7.138-11.851a13.494,13.494,0,0,0-5.768-1.215h-31.9A13.447,13.447,0,0,1,0,13.445H0A13.449,13.449,0,0,1,13.444,0" transform="translate(170.24 61.33) rotate(180)" fill="#255be3"/>
      </g>
    </g>
  </g>
</svg>
