<template>
    <q-page class="overflow-hidden">
        <!-- A11y INTRO -->
        <div class="sr-only q-py-md">
            <p
                class="q-mb-md"
                v-html="t('ui.pageLoaded')"
                id="a11yIntro"
                style="outline: 0"
                tabindex="0"
            ></p>
            <p
                v-html="
                    (store.getPagesCompleted ? store.getPagesCompleted : 0) +
                    $t('ui.of') +
                    store.manifest.content.length +
                    t('ui.pagesCompleted')
                "
            ></p>
        </div>


        <!-- --------------------------------------------------------------------------------- -->
        <!-- --------------------------------------------------------------------------------- -->
        <div class="container q-pt-xl q-mt-md-lg q-mt-xl-xl" data-aos="zoom-out">
            <div
                class="row items-start"
                style="position: relative"
                :class="$q.screen.lt.md ? 'justify-center' : 'justify-start'"
            >
                <div
                    class="col-10 col-md-6"
                    :style="
                        $q.screen.lt.md && locale != 'ar'
                            ? 'position: absolute; left: 5%;'
                            : $q.screen.md && locale != 'ar'
                            ? 'position: absolute; right: 0%'
                            : $q.screen.lt.md && locale == 'ar'
                            ? 'position: absolute; left: 5%;'
                            : $q.screen.md && locale == 'ar'
                            ? 'position: absolute; left: 0%'
                            : $q.screen.gt.md && locale == 'ar'
                            ? 'position: absolute; right:50%'
                            : 'position: absolute; left: 50%'
                    "
                    data-aos="fade-right"
                >
                    <Vue3Lottie
                        :auto-play="store.aosEnable"
                        :pause-animation="!store.aosEnable"
                        class="lottie"
                        :loop="true"
                        :scale="1"
                        :animationData="check2"
                    />
                </div>
                <div class="order-last order-md-first col-md-5 offset-md-1 q-pr-md-md">
                    <h1 class="q-mb-lg" v-html="tm('page6.sectionTop.title1')"></h1>
                    <h2
                        class="text-secondary text-bold q-pb-lg q-mb-sm"
                        v-html="t('page6.section1.title1')"
                    ></h2>
                    <p
                        class="text-secondary text-bold q-pb-md"
                        v-html="t('page6.section1.text1')"
                    ></p>
                    <p class="q-pb-md" v-html="t('page6.section1.text2')"></p>
                    <p class="q-pb-md" v-html="t('page6.section1.text3')"></p>
                </div>
                <div
                    class="order-first order-md-last col-10 col-md-6 q-mb-md q-mb-md-none"
                    style="z-index: 1"
                >
                    <q-img
                        loading="eager"
                        no-transition
                        src="~assets/page6/greta.png"
                        spinner-color="white"
                    ></q-img>
                </div>
            </div>
        </div>


        <!-- --------------------------------------------------------------------------------- -->
        <!-- --------------------------------------------------------------------------------- -->
        <div style="background-color: #f7fafb" class="q-py-md q-mb-md-xl" data-aos="fade">
            <div class="container q-py-xl">
                <div class="row justify-center">
                    <div class="col-md-6 q-pb-xl">
                        <p
                            class="text-secondary text-bold text-center"
                            v-html="t('page6.section1.text4')"
                        ></p>
                    </div>
                </div>
                <div class="row justify-center">
                    <div class="col-10 col-md-6 q-pb-xl">
                        <div
                            style="
                                display: flex;
                                align-items: center;
                                justify-content: center;
                            "
                        >
                            <div>
                                <q-img
                                    loading="eager"
                                    no-transition
                                    src="~assets/global/scroll.svg"
                                    width="50px"
                                    height="50px"
                                    spinner-color="white"
                                ></q-img>
                            </div>

                            <p class="q-ml-md text-center" v-html="t('ui.scroll')"></p>
                        </div>
                    </div>
                </div>
                <div
                    class="row items-center q-pt-lg"
                    :class="$q.screen.lt.md ? 'justify-center' : 'justify-start'"
                >
                    <div
                        class="col-10 offset-md-1 col-md-4 q-mb-lg q-mb-md-none"
                        data-aos="fade-right"
                    >
                        <q-img
                            loading="eager"
                            no-transition
                            src="~assets/page6/1.png"
                            spinner-color="white"
                        ></q-img>
                    </div>
                    <div class="col-md-5 offset-md-1 q-pr-md-lg" data-aos="fade-left">
                        <p class="" v-html="t('page6.section1.text6')"></p>
                    </div>
                </div>

                <!-- --------------------------------------------------------------------------------- -->
                <!-- --------------------------------------------------------------------------------- -->
                <div
                    class="row items-center q-pt-xxl"
                    :class="$q.screen.lt.md ? 'justify-center' : 'justify-start'"
                >
                    <div
                        class="col-md-5 offset-md-1 q-pr-md-lg order-last order-md-first"
                        data-aos="fade-right"
                    >
                        <p class="q-pb-lg" v-html="t('page6.section1.text7')"></p>
                        <p class="" v-html="t('page6.section1.text8')"></p>
                        <ul>
                            <li
                                v-for="(item, index) in tm('page6.section1.list1')"
                                class="q-pb-md last-in-list"
                                :key="index"
                                v-html="item"
                            ></li>
                        </ul>
                    </div>
                    <div
                        class="offset-md-1 col-10 col-md-4 order-first order-md-last q-mb-lg q-mb-md-none"
                        data-aos="fade-left"
                    >
                        <q-img
                            loading="eager"
                            no-transition
                            src="~assets/page6/2.png"
                            spinner-color="white"
                        ></q-img>
                    </div>
                </div>

                <!-- --------------------------------------------------------------------------------- -->
                <!-- --------------------------------------------------------------------------------- -->
                <div
                    class="row items-center q-pt-xl q-pt-md-xxl"
                    :class="$q.screen.lt.md ? 'justify-center' : 'justify-start'"
                >
                    <div
                        class="offset-md-1 col-10 col-md-4 q-mb-lg q-mb-md-none"
                        data-aos="fade-right"
                    >
                        <q-img
                            loading="eager"
                            no-transition
                            src="~assets/page6/3.png"
                            spinner-color="white"
                        ></q-img>
                    </div>
                    <div class="col-md-5 offset-md-1 q-pr-md-lg" data-aos="fade-left">
                        <p class="" v-html="t('page6.section1.text9')"></p>
                    </div>
                </div>

                <!-- --------------------------------------------------------------------------------- -->
                <!-- --------------------------------------------------------------------------------- -->
                <div
                    class="row items-center q-pt-xxl"
                    :class="$q.screen.lt.md ? 'justify-center' : 'justify-start'"
                >
                    <div class="col-md-5 offset-md-1 q-pr-md-lg" data-aos="fade-right">
                        <p class="" v-html="t('page6.section1.text10')"></p>
                    </div>
                    <div
                        class="offset-md-1 col-10 col-md-4 order-first order-md-last q-mb-lg q-mb-md-none"
                        data-aos="fade-left"
                    >
                        <q-img
                            loading="eager"
                            no-transition
                            src="~assets/page6/4.png"
                            spinner-color="white"
                        ></q-img>
                    </div>
                </div>

                <!-- --------------------------------------------------------------------------------- -->
                <!-- --------------------------------------------------------------------------------- -->
                <div
                    data-aos="fade"
                    class="container q-pt-xl q-pt-sm-xxl q-pb-sm-xl q-pb-md-none q-pb-lg-xl"
                >
                    <div
                        class="row justify-start items-center q-pb-sm-xl"
                        :class="$q.screen.gt.md ? 'bg-greta' : null"
                        :style="$q.screen.gt.md ? 'height: 450px;' : ''"
                    >
                        <div class="col-lg-11">
                            <div
                                class="row"
                                :class="
                                    locale == 'ar' ? 'justify-start' : 'justify-center'
                                "
                            >
                                <div
                                    class="col-lg-5 q-py-xl shadow-1"
                                    :class="
                                        locale == 'ar' ? 'offset-lg-2' : 'offset-lg-5'
                                    "
                                    style="
                                        background: #f0f5f7;
                                        border-radius: 8px;
                                        opacity: 0.98;
                                    "
                                >
                                    <p
                                        class="q-px-md q-mx-md q-mb-md"
                                        v-html="t('page6.section1.text12')"
                                    ></p>
                                    <p
                                        class="q-px-md q-mx-md"
                                        v-html="t('page6.section1.text13')"
                                    ></p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>


        <!-- --------------------------------------------------------------------------------- -->
        <!-- --------------------------------------------------------------------------------- -->
        <div class="container q-pt-xl q-mt-md-lg q-mt-xl-xl q-pb-lg q-pb-md-none" data-aos="zoom-out" >
            <div
                class="row items-center"
                style="position: relative"
                :class="$q.screen.lt.md ? 'justify-center' : 'justify-start'"
            >
                <div
                    class="col-10 col-md-6"
                    :style="
                        $q.screen.lt.md && locale != 'ar'
                            ? 'position: absolute; top:0%; left: 5%;'
                            : $q.screen.md && locale != 'ar'
                            ? 'position: absolute; right: 0%'
                            : $q.screen.lt.md && locale == 'ar'
                            ? 'position: absolute; left: 5%; top:0%'
                            : $q.screen.md && locale == 'ar'
                            ? 'position: absolute; left: 0%'
                            : $q.screen.gt.md && locale == 'ar'
                            ? 'position: absolute; right:50%'
                            : 'position: absolute; left: 50%'
                    "
                    data-aos="fade-right"
                >
                    <Vue3Lottie
                        :auto-play="store.aosEnable"
                        :pause-animation="!store.aosEnable"
                        class="lottie"
                        :loop="true"
                        :scale="1"
                        :animationData="check2"
                    />
                </div>
                <div class="order-last order-md-first col-md-5 offset-md-1 q-pr-md-md">
                    <h2
                        class="text-secondary text-bold q-pb-lg q-mb-sm"
                        v-html="t('page6.section1.title2')"
                    ></h2>
                    <p v-html="t('page6.section1.text14')"></p>
                </div>
                <div
                    class="order-first order-md-last col-10 col-md-6 q-mb-md q-mb-md-none"
                    style="z-index: 1"
                >
                    <q-img
                        loading="eager"
                        no-transition
                        src="~assets/page6/lorenzo.png"
                        spinner-color="white"
                    ></q-img>
                </div>
            </div>
        </div>


        <!-- --------------------------------------------------------------------------------- -->
        <!-- --------------------------------------------------------------------------------- -->
        <div style="background-color: #f7fafb" class="q-py-md q-mb-xl" data-aos="fade">

            <!-- Thinking of Greta’s experience, let’s revisit our client, Lorenzo. -->
            <div class="container q-py-xl">

                <!-- Thinking of Greta’s experience, let’s revisit our client, Lorenzo. -->
                <div class="row justify-center">
                    <div class="col-md-6 q-pb-xl">
                        <p v-html="t('page6.section1.text15')" class="text-secondary text-bold text-center" ></p>
                    </div>
                </div>

                <!-- scroll down to continue -->
                <div class="row justify-center">
                    <div class="col-10 col-md-6 q-pb-xl">
                        <div style=" display: flex; align-items: center; justify-content: center; " >
                            <div>
                                <q-img
                                    loading="eager"
                                    no-transition
                                    src="~assets/global/scroll.svg"
                                    width="50px"
                                    height="50px"
                                    spinner-color="white"
                                ></q-img>
                            </div>
                            <p class="q-ml-md text-center" v-html="t('ui.scroll')"></p>
                        </div>
                    </div>
                </div>

                <!-- --------------------------------------------------------------------------------- -->
                <!-- lorenzo 1 -->
                <!-- --------------------------------------------------------------------------------- -->
                <div class="row items-center q-pt-lg" :class="$q.screen.lt.md ? 'justify-center' : 'justify-start'" >
                    <div class="col-10 offset-md-1 col-md-4 q-mb-lg q-mb-md-none" data-aos="fade-right" >
                        <q-img
                            loading="eager"
                            no-transition
                            src="~assets/page6/lorenzos_journey/5.png"
                            spinner-color="white"
                        ></q-img>
                    </div>
                    <div class="col-md-5 offset-md-1 q-pr-md-lg" data-aos="fade-left">
                        <p v-html="t('page6.section1.text17')" class="text-secondary q-pb-lg" ></p>

                        <p
                            v-for="(item, index) in tm('page6.section1.lorenzos_journey_paragraphs').slice(0, 3)" :key="index"
                            v-html="item"
                            class="q-mb-md last-in-list"
                        ></p>

                    </div>
                </div>


                <!-- --------------------------------------------------------------------------------- -->
                <!-- lorenzo 2 -->
                <!-- --------------------------------------------------------------------------------- -->
                <div class="row items-center q-pt-xxl" :class="$q.screen.lt.md ? 'justify-center' : 'justify-start'" >

                    <div class="col-md-5 offset-md-1 q-pr-md-lg" data-aos="fade-right">
                        <p
                            v-for="(item, index) in tm('page6.section1.lorenzos_journey_paragraphs').slice(3, 4)" :key="index"
                            v-html="item"
                            class="q-mb-md last-in-list"
                        ></p>
                    </div>

                    <div class="col-10 offset-md-1 col-md-4 order-first order-md-last q-mb-lg q-mb-md-none" data-aos="fade-left" >
                        <q-img
                            loading="eager"
                            no-transition
                            src="~assets/page6/lorenzos_journey/6.png"
                            spinner-color="white"
                        ></q-img>
                    </div>

                </div>


                <!-- --------------------------------------------------------------------------------- -->
                <!-- lorenzo 3 -->
                <!-- --------------------------------------------------------------------------------- -->
                <div class="row items-center q-pt-lg" :class="$q.screen.lt.md ? 'justify-center' : 'justify-start'" >
                    <div class="col-10 offset-md-1 col-md-4 q-mb-lg q-mb-md-none" data-aos="fade-right" >
                        <q-img
                            loading="eager"
                            no-transition
                            src="~assets/page6/lorenzos_journey/7.png"
                            spinner-color="white"
                        ></q-img>
                    </div>
                    <div class="col-md-5 offset-md-1 q-pr-md-lg" data-aos="fade-left">
                        <p
                            v-for="(item, index) in tm('page6.section1.lorenzos_journey_paragraphs').slice(4, 5)" :key="index"
                            v-html="item"
                            class="q-mb-md last-in-list"
                        ></p>

                    </div>
                </div>


                <!-- --------------------------------------------------------------------------------- -->
                <!-- lorenzo 4 -->
                <!-- --------------------------------------------------------------------------------- -->
                <div class="row items-center q-pt-xxl" :class="$q.screen.lt.md ? 'justify-center' : 'justify-start'" >

                    <div class="col-md-5 offset-md-1 q-pr-md-lg" data-aos="fade-right">
                        <p
                            v-for="(item, index) in tm('page6.section1.lorenzos_journey_paragraphs').slice(5, 6)" :key="index"
                            v-html="item"
                            class="q-mb-md last-in-list"
                        ></p>
                    </div>

                    <div class="col-10 offset-md-1 col-md-4 order-first order-md-last q-mb-lg q-mb-md-none" data-aos="fade-left" >
                        <q-img
                            loading="eager"
                            no-transition
                            src="~assets/page6/lorenzos_journey/8.png"
                            spinner-color="white"
                        ></q-img>
                    </div>

                </div>
                <q-separator aria-hidden="true" :size="$q.screen.gt.sm ? '100px' : '50px'" color="transparent" ></q-separator>




                <!-- --------------------------------------------------------------------------------- -->
                <!-- Let’s learn more about how the Process Owner team used CEAM artifacts... -->
                <!-- --------------------------------------------------------------------------------- -->
                <div class="container">
                    <div class="row justify-center">
                        <div class="col-11 col-md-8">
                            <p v-html="t('page6.section1.lorenzos_journey_paragraphs[6]')" class="Citi-Sans-Display-Regular balance-this-text" ></p>
                        </div>
                    </div>
                </div>
                <q-separator aria-hidden="true" :size="$q.screen.gt.sm ? '100px' : '50px'" color="transparent" ></q-separator>




                <!-- --------------------------------------------------------------------------------- -->
                <!-- Technology Simplification Applied -->
                <!-- --------------------------------------------------------------------------------- -->
                <div class="row items-center" :class="$q.screen.lt.md ? 'justify-center' : 'justify-start'" >

                    <div class="col-10 offset-md-1 col-md-4 q-mb-lg q-mb-md-none" data-aos="fade-right" >
                        <q-img
                            loading="eager"
                            no-transition
                            src="~assets/page6/7.png"
                            spinner-color="white"
                        ></q-img>
                    </div>

                    <div class="col-md-5 offset-md-1 q-pr-md-lg" data-aos="fade-left">
                        <p v-html="tm('page6.section1.text21')" class="text-secondary text-bold q-pb-lg" ></p>
                        <p class="q-pb-md" v-html="t('page6.section1.text22')"></p>
                    </div>

                </div>


                <!-- --------------------------------------------------------------------------------- -->
                <!-- --------------------------------------------------------------------------------- -->
                <div class="row items-center q-pt-xl q-pt-md-xxl" :class="$q.screen.lt.md ? 'justify-center' : 'justify-start'" >
                    <div class="col-md-5 offset-md-1 q-pr-md-lg" data-aos="fade-right">
                        <p class="q-pb-md" v-html="t('page6.section1.text23')"></p>
                    </div>
                    <div
                        class="col-10 offset-md-1 col-md-4 order-first order-md-last q-mb-lg q-mb-md-none"
                        data-aos="fade-left"
                    >
                        <q-img
                            loading="eager"
                            no-transition
                            src="~assets/page6/8.png"
                            spinner-color="white"
                        ></q-img>
                    </div>
                </div>



                <!-- --------------------------------------------------------------------------------- -->
                <!-- Now that Lorenzo’s credit check is more streamlined, they won’t have... -->
                <!-- --------------------------------------------------------------------------------- -->
                <div data-aos="fade" class="container q-pt-xl q-pt-sm-xxl q-pb-md-xl">
                    <div class="row justify-start items-center bg-lorenzo q-pb-lg q-pb-sm-xl"
                        :style="$q.screen.gt.xs ? 'height: 450px;' : ''"
                    >
                        <div class="col-md-11">
                            <div class="row" :class=" locale == 'ar' ? 'justify-endr' : 'justify-center' " >
                                <div
                                    class="col-md-7 q-py-xl shadow-1"
                                    :class=" locale == 'ar' ? 'offset-md-2' : 'offset-md-8 offset-lg-6' "
                                    style=" background: #f0f5f7; border-radius: 8px; opacity: 0.98; "
                                >
                                    <p v-html="t('page6.section1.text27')" class="q-px-md q-mx-md q-mb-md" ></p>
                                    <p v-html="t('page6.section1.text28')" class="q-px-md q-mx-md" ></p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>



            </div>
        </div>


        <!-- --------------------------------------------------------------------------------------------------- -->
        <!-- continue -->
        <!-- --------------------------------------------------------------------------------------------------- -->
        <div class="q-py-none q-pb-md-xl bg-footer">
            <div class="q-pt-lg q-pb-xl container row justify-center">
                <div class="col-12 col-lg text-center q-pb-xl q-mb-xl q-mb-xl-lg">
                    <h3 class="q-mb-lg" v-html="tm('page6.footer.text1')"></h3>
                    <q-btn
                        @click="continueClicked()"
                        :label="t('ui.continue')"
                        style="border-radius: 7.5px"
                        no-caps
                        outline
                        padding="sm xl"
                        class="btn-fixed-width bg-white"
                        color="secondary"
                    ></q-btn>
                </div>
            </div>
        </div>
    </q-page>
</template>

<script>
import { defineComponent, ref, onMounted } from "vue";
import { useQuasar } from "quasar";
import { useI18n } from "vue-i18n";
import { Vue3Lottie } from "vue3-lottie";
import { useStore } from "stores/store";
import { useCore } from "stores/composables/core";
import { useAos } from "stores/composables/aos";
import graphic1 from "../assets/global/graphic_callout1.json";
import check1 from "../assets/global/graphic_1_1_v3.json";
import check2 from "../assets/global/graphic3.json";
import globe from "../assets/global/graphic_globe.json";
import greta from "../assets/page6/greta.json";
export default defineComponent({
    name: "PageSix",

    components: {
        Vue3Lottie,
    },

    setup() {
        const store = useStore();
        const core = { ...useCore() };
        const aos = { ...useAos() };

        const { locale } = useI18n({ useScope: "global" });
        const { t, tm } = useI18n();
        const $q = useQuasar();

        const openTranscript = ref(false);

        const continueClicked = () => {
            core.checkNetwork(() => continueClickedCallback());
        };
        const continueClickedCallback = () => {
            core.completePageAndRoute("page6", "/page7");
        };

        onMounted(() => {
            console.log("***------ PageSix - onMounted ------***");
            store.rte_load("page6");

            core.setFocusById("a11yIntro", 400);
            aos.refreshAOS();
        });

        return {
            icon: ref(false),
            bar: ref(false),
            bar2: ref(false),
            toolbar: ref(false),
            store,
            check1,
            graphic1,
            ...useCore(),
            ...useAos(),
            locale,
            t,
            tm,
            animations: ref(false),
            openTranscript,
            continueClicked,
            globe,
            greta,
            check2,
        };
    },
});
</script>

<style scoped lang="scss">
@import "../css/quasar.variables.scss";

/* for parallax containers, couldn't do inline src background tag? using classes instead */

.bg-footer {
    background-image: url("../assets/page1/footer.svg");
    background-position: bottom center;
    background-repeat: no-repeat;
    background-size: auto;
}
body.screen--xs {
    .bg-greta {
        background-image: none;
    }
    .bg-lorenzo {
        background-image: none;
    }
}

.bg-greta {
    background-image: url("../assets/page6/bg_greta.png");
    background-position: left center;
    background-repeat: no-repeat;
    background-size: auto;
}

.bg-lorenzo {
    background-image: url("../assets/page6/bg_lorenzo.png");
    background-position: left center;
    background-repeat: no-repeat;
    background-size: auto;
}

.bg-tax1 {
    background-image: url("../assets/page3/bg_tax1.png");
    background-position: 44% center;
    background-repeat: no-repeat;
    background-size: auto;
}

.bg-tax2 {
    background-image: url("../assets/page3/bg_tax2.png");
    background-position: 44% center;
    background-repeat: no-repeat;
    background-size: auto;
}

.bg-tax3 {
    background-image: url("../assets/page3/bg_tax3.png");
    background-position: 44% center;
    background-repeat: no-repeat;
    background-size: auto;
}

.bg-world {
    background-image: url("../assets/page1/3.svg");
    background-position: 3% center;
    background-repeat: no-repeat;
    background-size: contain;
}

.li-style {
    list-style-image: url("../assets/global/licheck.svg");
}
</style>
