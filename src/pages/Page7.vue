<template>
    <q-page class="overflow-hidden">

        <!-- A11y INTRO -->
        <div class="sr-only q-py-md">
            <p
                class="q-mb-md"
                v-html="t('ui.pageLoaded')"
                id="a11yIntro"
                style="outline: 0"
                tabindex="0"
            ></p>
            <p
                v-html="
                    (store.getPagesCompleted ? store.getPagesCompleted : 0) +
                    $t('ui.of') +
                    store.manifest.content.length +
                    t('ui.pagesCompleted')
                "
            ></p>
        </div>


        <!-- --------------------------------------------------------------------------------------------------- -->
        <!-- Who is Responsiblw for Applying Enterprise Architecture and Process Governance -->
        <!-- --------------------------------------------------------------------------------------------------- -->
        <div class="container q-pb-xl" data-aos="zoom-out">

            <div class="row justify-start q-pb-sm-xl q-pb-md-none">
                <div class="col-lg-8 q-pt-xl offset-md-1">
                    <h1 v-html="tm('page7.sectionTop.title1')" class="q-mb-lg q-pt-lg q-mt-md" ></h1>
                </div>
            </div>


            <!-- --------------------------------------------------------------------------------------------------- -->
            <!-- Remember, the purpose of Enterprise Architecture is to align... -->
            <!-- --------------------------------------------------------------------------------------------------- -->
            <div
                class="row justify-end items-center q-py-md-xxl q-mt-xl q-mt-md-none q-mb-sm-xl q-mb-md-none q-pb-lg"
                style="position: relative"
            >

                <!-- lottie - globe -->
                <div
                    class="col-md-4"
                    :style="
                        $q.screen.gt.xs && locale != 'ar'
                            ? 'position: absolute; left: 2%;'
                            : $q.screen.gt.xs && locale == 'ar'
                            ? 'position: absolute; right: 2%;'
                            : 'position: absolute; top:-50px;'
                    "
                    data-aos="fade-right"
                >
                    <Vue3Lottie
                        :auto-play="store.aosEnable"
                        :pause-animation="!store.aosEnable"
                        class="lottie"
                        :loop="true"
                        :scale="1"
                        :animationData="globe"
                    />
                </div>

                <!-- text -->
                <div
                    class="col-12 col-md-10"
                    style="position: relative"
                    data-aos="fade-left"
                >
                    <div
                        class="shadow-1 q-py-lg q-px-md-xl q-my-lg"
                        style="background: #255be3; border-radius: 7.5px"
                    >
                        <h3
                            class="text-white q-pt-lg q-pb-lg q-mx-xl"
                            v-html="t('page7.section1.text1')"
                        ></h3>
                    </div>
                </div>

            </div>


            <!-- --------------------------------------------------------------------------------------------------- -->
            <!-- Simplifying and managing our processes won't... -->
            <!-- --------------------------------------------------------------------------------------------------- -->
            <div class="row justify-center items-center q-pt-sm-lg q-pt-md-none">
                <div class="col-md-5 q-mr-md-xl" data-aos="fade-right">
                    <p class="q-mb-lg" v-html="t('page7.section1.text2')"></p>
                    <ul>
                        <li
                            v-for="(item, index) in tm('page7.section1.list1')"
                            :key="index"
                            v-html="item"
                            class="q-pb-md last-in-list"
                        ></li>
                    </ul>
                </div>
                <div
                    class="col-10 col-md-5 order-first order-md-last q-mb-md q-mb-md-none"
                    data-aos="fade-left"
                >
                    <q-img
                        loading="eager"
                        no-transition
                        src="~assets/page7/1.png"
                        :style="$q.screen.gt.xs ? 'width: 85%;' : ''"
                        spinner-color="white"
                    ></q-img>
                </div>
            </div>


            <!-- --------------------------------------------------------------------------------------------------- -->
            <!-- --------------------------------------------------------------------------------------------------- -->
            <div data-aos="fade" class="row q-pt-xl items-center">

                <div v-if="$q.screen.gt.sm" class="col-4 col-md-2 offset-3 offset-md-1">
                    <Vue3Lottie
                        :auto-play="store.aosEnable"
                        :pause-animation="!store.aosEnable"
                        class="lottie"
                        :loop="true"
                        :scale="0.7"
                        :animationData="check1"
                    />
                </div>

                <div
                    class="col-md-8 q-pa-xl"
                    :style="
                        $q.screen.gt.sm && locale != 'ar'
                            ? 'background-color: #F0F5F7; margin-left: -4.5rem;'
                            : $q.screen.gt.sm && locale == 'ar'
                            ? 'background-color: #F0F5F7; margin-right: -4.5rem;'
                            : 'background-color: #F0F5F7;'
                    "
                >
                    <h3
                        class="q-pb-md q-px-md-xl q-mx-lg-md"
                        v-html="tm('page7.section1.text3')"
                    ></h3>
                    <h3
                        class="q-px-md-xl q-mx-lg-md"
                        v-html="tm('page7.section1.text3b')"
                    ></h3>
                </div>
            </div>


            <!-- --------------------------------------------------------------------------------------------------- -->
            <!-- --------------------------------------------------------------------------------------------------- -->
            <div class="row justify-center q-pt-xxl">

                <div class="col-md-7 q-pb-xl">
                    <p v-html="tm('page7.section1.text4')" class="text-secondary text-center text-bold" ></p>
                </div>

                <div class="col-6 col-md-6 q-pb-lg">
                    <div style=" display: flex; align-items: center; justify-content: center; " >
                        <q-img
                            loading="eager"
                            no-transition
                            src="~assets/global/scroll.svg"
                            width="50px"
                            height="50px"
                            spinner-color="white"
                        ></q-img>
                    </div>
                </div>

            </div>
        </div>


        <!-- --------------------------------------------------------------------------------------------------- -->
        <!-- --------------------------------------------------------------------------------------------------- -->
        <div class="q-py-xl" style="background: #eeeeee">

            <div class="container">

                <div class="row justify-center" data-aos="fade">
                    <div class="col-lg-8">
                        <h2
                            class="q-pb-lg q-mb-xs q-mb-xl-md text-center"
                            v-html="tm('page7.section1.title1')"
                        ></h2>
                        <p
                            class="text-center q-pb-xl q-mb-xs"
                            v-html="t('page7.section1.text5')"
                        ></p>
                    </div>
                </div>

                <!-- --------------------------------------------------------------------------------------------------- -->
                <!-- pop-up modal cards -->
                <!-- --------------------------------------------------------------------------------------------------- -->
                <div class="row justify-evenly">
                    <div v-for="(item, index) in tm('page7.section1.cards1')" :key="index"
                        v-show="$q.screen.lt.md ? isCardVisible(index) : true"
                        data-aos="zoom-out"
                        class="col-lg-3"
                    >
                        <div
                            class="bg-white q-pt-lg q-pb-md shadow-1"
                            :style="$q.screen.lt.lg ? 'width:330px' : 'height:100%'"
                            style="
                                display: flex;
                                flex-direction: column;
                                justify-content: center;
                                align-items: center;
                                border-radius: 8px;
                                position: relative;
                            "
                        >
                            <q-img
                                loading="eager"
                                class="q-mt-md"
                                no-transition
                                :src="getImageUrl(index)"
                                width="8rem"
                                spinner-color="white"
                            ></q-img>
                            <p
                                style="min-height: 100px"
                                class="text-center q-px-lg q-mx-xs q-pt-lg q-mt-xs"
                                v-html="item.title"
                            ></p>
                            <div>
                                <q-btn
                                    @click="item.id = true"
                                    flat
                                    round
                                    icon="add_circle_outline"
                                    size="1.5rem"
                                    color="secondary"
                                    :aria-label="t('ui.learn')"
                                ></q-btn>
                            </div>
                            <Vue3Lottie
                                class="lottie"
                                v-if="
                                    store.getInteractionSubmitted(
                                        'modal' + parseInt(index + 1)
                                    ).submitted
                                "
                                style="
                                    position: absolute;
                                    top: 3px;
                                    right: 5px;
                                    cursor: default;
                                "
                                :loop="false"
                                :scale="1"
                                width="44.56px"
                                height="44.56px"
                                :animationData="check"
                            />
                        </div>

                        <div class="q-pa-md q-gutter-sm">
                            <q-dialog
                                @hide="
                                    store.setInteraction(
                                        'modal' + parseInt(index + 1),
                                        null,
                                        null,
                                        true
                                    )
                                "
                                v-model="item.id"
                                transition-show="scale"
                                transition-hide="fade"
                            >
                                <!-- :style="index == 1 ?'background:linear-gradient(to bottom, #255BE3 100px, #ffffff 0%)' : 'background:linear-gradient(to bottom, #255BE3 105px, #ffffff 0%)'" -->
                                <q-card
                                    class="q-pb-lg"
                                    style="
                                        width: 700px;
                                        border-radius: 8px;
                                        border-radius: 8px;
                                    "
                                >
                                    <q-card-section
                                        class="row q-py-lg q-px-lg items-center bg-secondary"
                                    >
                                        <div class="col-lg-12">
                                            <h3
                                                style="
                                                    font-size: 1.6rem;
                                                    line-height: 1.9rem;
                                                "
                                                class="text-white"
                                                v-html="item.title"
                                            ></h3>
                                        </div>
                                        <q-space />
                                        <!-- <q-btn icon="close" flat round dense v-close-popup /> -->
                                    </q-card-section>

                                    <q-card-section class="q-pt-lg q-mt-md q-mx-md-lg">
                                        <p
                                            v-for="(item, index) in item.text"
                                            :key="index"
                                            class="q-pb-md-md last-in-list"
                                            v-html="item"
                                        ></p>
                                        <ol v-if="item.list">
                                            <li
                                                v-for="(item, index) in item.list"
                                                class="q-pb-sm q-pb-md-md last-in-list"
                                                :key="index"
                                                v-html="item"
                                            ></li>
                                        </ol>
                                        <p
                                            v-if="item.extra"
                                            class="q-pt-md-lg"
                                            v-html="item.extra"
                                        ></p>
                                        <div
                                            class="q-pt-xl"
                                            style="display: flex; justify-content: center"
                                        >
                                            <q-btn
                                                v-close-popup
                                                :label="t('ui.continue')"
                                                style="border-radius: 7.5px"
                                                no-caps
                                                outline
                                                padding="sm xl"
                                                class="btn-fixed-width bg-white"
                                                color="secondary"
                                            ></q-btn>
                                        </div>
                                    </q-card-section>
                                </q-card>
                            </q-dialog>
                        </div>
                    </div>
                </div>

            </div>

        </div>



        <!-- --------------------------------------------------------------------------------------------------- -->
        <!-- To learn more about how different areas at Citi... -->
        <!-- --------------------------------------------------------------------------------------------------- -->
        <div v-if="store.getInteractionsCompleted(['modal1', 'modal2', 'modal3'])" class="container" >


            <div
                class="row justify-end items-center q-mt-xxl q-mt-sm-none q-mt-md-xl q-py-xxl"
                style="position: relative"
            >
                <div
                    class="col-md-4"
                    :style="
                        $q.screen.gt.xs && locale != 'ar'
                            ? 'position: absolute; left: 2%;'
                            : $q.screen.gt.xs && locale == 'ar'
                            ? 'position: absolute; right: 2%;'
                            : 'position: absolute; top:-50px;'
                    "
                    data-aos="fade-right"
                >
                    <Vue3Lottie
                        :auto-play="store.aosEnable"
                        :pause-animation="!store.aosEnable"
                        class="lottie"
                        :loop="true"
                        :scale="1"
                        :animationData="globe"
                    />
                </div>

                <div
                    class="col-12 col-md-10"
                    style="position: relative"
                    data-aos="fade-left"
                >
                    <div
                        class="shadow-1 q-py-lg q-px-lg-xl q-my-lg"
                        style="background: #255be3; border-radius: 7.5px"
                    >
                        <h3
                            class="text-white q-pt-lg q-pb-lg q-mx-xl"
                            v-html="tm('page7.section1.text6')"
                        ></h3>
                    </div>
                </div>
            </div>


            <div data-aos="fade" class="row q-pt-md-lg q-pb-xl items-center q-pb-md-lg">
                <div v-if="$q.screen.gt.sm" class="col-4 col-md-2 offset-3 offset-md-1">
                    <Vue3Lottie
                        :auto-play="store.aosEnable"
                        :pause-animation="!store.aosEnable"
                        class="lottie"
                        :loop="true"
                        :scale="0.7"
                        :animationData="check1"
                    />
                </div>

                <div
                    class="col-md-8 q-pa-xl"
                    :style="
                        $q.screen.gt.sm && locale != 'ar'
                            ? 'background-color: #F0F5F7; margin-left: -4.5rem;'
                            : $q.screen.gt.sm && locale == 'ar'
                            ? 'background-color: #F0F5F7; margin-right: -4.5rem;'
                            : 'background-color: #F0F5F7;'
                    "
                >
                    <h3
                        class="q-px-md-xl q-mx-lg-md"
                        v-html="t('page7.section1.text7')"
                    ></h3>
                </div>
            </div>

        </div>



        <!-- --------------------------------------------------------------------------------------------------- -->
        <!-- continue -->
        <!-- --------------------------------------------------------------------------------------------------- -->
        <div
            class="q-py-none q-pb-lg-xl bg-footer"
            v-if="store.getInteractionsCompleted(['modal1', 'modal2', 'modal3'])"
        >
            <div class="q-pt-lg q-pb-xl container row justify-center">
                <div class="col-12 col-lg-9 text-center q-pb-xl q-mb-xl q-mb-xl-lg">
                    <h3 class="q-mb-lg" v-html="t('page7.footer.text1')"></h3>
                    <q-btn
                        @click="continueClicked()"
                        :label="t('ui.continue')"
                        style="border-radius: 7.5px"
                        no-caps
                        outline
                        padding="sm xl"
                        class="btn-fixed-width bg-white"
                        color="secondary"
                    ></q-btn>
                </div>
            </div>
        </div>
    </q-page>
</template>

<script>
import { defineComponent, ref, onMounted } from "vue";
import { useQuasar } from "quasar";
import { useI18n } from "vue-i18n";
import { Vue3Lottie } from "vue3-lottie";
import { useStore } from "stores/store";
import { useCore } from "stores/composables/core";
import { useAos } from "stores/composables/aos";
import graphic1 from "../assets/global/graphic_callout1.json";
import check1 from "../assets/global/graphic_1_1_v3.json";
import globe from "../assets/global/graphic_globe.json";
import check from "../assets/page3/modalcheckciti.json";
export default defineComponent({
    name: "PageSeven",

    components: {
        Vue3Lottie,
    },

    setup() {
        const store = useStore();
        const core = { ...useCore() };
        const aos = { ...useAos() };

        const { locale } = useI18n({ useScope: "global" });
        const { t, tm } = useI18n();
        const $q = useQuasar();

        const openTranscript = ref(false);

        const continueClicked = () => {
            core.checkNetwork(() => continueClickedCallback());
        };
        const continueClickedCallback = () => {
            core.completePageAndRoute("page7", "/page8");
        };

        function getImageUrl(index) {
            // This path must be correct for your file
            return new URL(
                `../assets/page7/card${parseInt(index + 1)}.png`,
                import.meta.url
            ).toString();
        }

        function isCardVisible(index) {
            // First card is always visible
            if (index === 0) {
                return true;
            }

            // For subsequent cards, check if the previous card's modal has been visited
            const previousModalId = 'modal' + index; // modal1, modal2, etc.
            return store.getInteractionSubmitted(previousModalId).submitted;
        }

        onMounted(() => {
            console.log("***------ PageSeven - onMounted ------***");
            store.rte_load("page7");

            core.setFocusById("a11yIntro", 400);
            aos.refreshAOS();
        });

        return {
            check,
            store,
            check1,
            graphic1,
            ...useCore(),
            ...useAos(),
            locale,
            t,
            tm,
            animations: ref(false),
            openTranscript,
            continueClicked,
            globe,
            getImageUrl,
            isCardVisible,
        };
    },
});
</script>

<style scoped lang="scss">
@import "../css/quasar.variables.scss";

/* for parallax containers, couldn't do inline src background tag? using classes instead */

.bg-footer {
    background-image: url("../assets/page1/footer.svg");
    background-position: bottom center;
    background-repeat: no-repeat;
    background-size: auto;
}

.bg-lorenzo {
    background-image: url("../assets/page3/bg_lorenzo.png");
    background-position: 44% center;
    background-repeat: no-repeat;
    background-size: auto;
}

.bg-tax1 {
    background-image: url("../assets/page3/bg_tax1.png");
    background-position: 44% center;
    background-repeat: no-repeat;
    background-size: auto;
}

.bg-tax2 {
    background-image: url("../assets/page3/bg_tax2.png");
    background-position: 44% center;
    background-repeat: no-repeat;
    background-size: auto;
}

.bg-tax3 {
    background-image: url("../assets/page3/bg_tax3.png");
    background-position: 44% center;
    background-repeat: no-repeat;
    background-size: auto;
}

.bg-world {
    background-image: url("../assets/page1/3.svg");
    background-position: 3% center;
    background-repeat: no-repeat;
    background-size: contain;
}

.li-style {
    list-style-image: url("../assets/global/licheck.svg");
}
</style>
