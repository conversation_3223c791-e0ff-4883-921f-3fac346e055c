<template>
    <q-page class="overflow-hidden">
        <!-- A11y INTRO -->
        <div class="sr-only q-py-md">
            <p
                class="q-mb-md"
                v-html="t('ui.pageLoaded')"
                id="a11yIntro"
                style="outline: 0"
                tabindex="0"
            ></p>
            <p
                v-html="
                    (store.getPagesCompleted ? store.getPagesCompleted : 0) +
                    $t('ui.of') +
                    store.manifest.content.length +
                    t('ui.pagesCompleted')
                "
            ></p>
        </div>

        <div class="container" data-aos="zoom-out">


            <!-- Components of CEAM -->
            <div class="row justify-start">
                <div class="col-lg-8 q-pt-xl offset-md-1">
                    <h1 v-html="tm('page5.sectionTop.title1')" class="q-mb-lg q-pt-lg q-mt-md" ></h1>
                </div>
            </div>


            <!-- MOVED FROM BELOW - July 2025 -->
            <div class="row justify-center q-mt-xl">

                <div class="row justify-center">
                    <div style="border-radius: 8px; " class="col-lg-10 shadow-1 q-px-lg q-px-md-none"
                        :style="
                            $q.screen.gt.sm
                            ? 'background: linear-gradient( to bottom, #255be3 130px, #ffffff 0% );'
                            : 'background: linear-gradient( to bottom, #255be3 200px, #ffffff 0% );' "
                    >

                        <!-- Application Mapping Functions, Designations & Mapping -->
                        <div class="row justify-center">

                            <div class="col-md-10 q-py-lg q-mb-xl q-mb-sm-none">
                                <h2 v-html="tm('page5.section2.title1')" class="text-white" ></h2>
                            </div>

                            <q-separator aria-hidden="true" :size="$q.screen.gt.xs ? '150px' : '0px'" ></q-separator>

                            <div class="row justify-center q-mt-lg q-mt-lg-xl">
                                <div class="col-md-6 q-pr-md-md">
                                    <!-- <h3 v-html="t('page5.section2.text1')" class="q-mb-md" ></h3> -->
                                    <p v-html="t('page5.section2.text2')"></p>
                                </div>
                                <div class="col-10 col-md-4 q-pt-lg q-pt-md-none">
                                    <q-img
                                        loading="eager"
                                        no-transition
                                        src="~assets/page5/4.png"
                                        spinner-color="white"
                                    ></q-img>
                                </div>
                            </div>

                        </div>

                        <!-- ------------------------------------------------------ -->
                        <!-- What Is an Application Function Designation? -->
                        <!-- ------------------------------------------------------ -->
                        <div class="row justify-center q-pt-lg q-pt-md-xxl">

                            <div class="col-10 col-md-4 q-mb-md q-mb-md-lg q-pb-md order-last order-md-first">
                                <q-img
                                    loading="eager"
                                    no-transition
                                    src="~assets/page5/5.png"
                                    spinner-color="white"
                                ></q-img>
                            </div>

                            <div class="col-md-6 q-pl-md-xl q-mb-lg q-pb-sm-md order-first order-md-last">
                                <h3
                                    class="q-mb-md"
                                    v-html="tm('page5.section2.text5')"
                                ></h3>
                                <p
                                    class="q-mb-md"
                                    v-html="tm('page5.section2.text6')"
                                ></p>
                                <p class="q-mb-md" v-html="t('page5.section2.text7')"></p>
                            </div>

                        </div>


                        <!-- ------------------------------------------------------ -->
                        <!-- Strategic, Maintain, Deprecate -->
                        <!-- ------------------------------------------------------ -->
                        <div class="row justify-center">

                            <div class="col-md-5 q-pr-md-md q-pb-lg q-pb-md-none">
                                <div :style=" $q.screen.gt.sm ? locale == 'ru' ? 'height:450px' : 'height:325px' : null "
                                    style=" background-color: #f7fafb; border-radius: 8px; ; "
                                    class="q-pa-lg shadow-1"
                                >
                                    <div class="q-pa-lg-md">
                                        <p
                                            class="q-mb-xs text-secondary"
                                            v-html="t('page5.section2.text8')"
                                        ></p>
                                        <p
                                            class=""
                                            v-html="t('page5.section2.list2[0]')"
                                        ></p>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-5 q-pl-md-md">
                                <div :style=" $q.screen.gt.sm ? locale == 'ru' ? 'height:450px' : 'height:325px' : null "
                                    style="background-color: #f7fafb; border-radius: 8px"
                                    class="q-pa-lg shadow-1"
                                >
                                    <div class="q-pa-lg-md">
                                        <p
                                            class="q-mb-xs text-secondary"
                                            v-html="t('page5.section2.text9')"
                                        ></p>
                                        <p
                                            class=""
                                            v-html="t('page5.section2.list3[0]')"
                                        ></p>
                                    </div>
                                </div>
                            </div>


                            <!-- Decommission -->
                            <div class="col-md-10 q-pt-lg">
                                <div style="background-color: #f7fafb; border-radius: 8px" class="q-pa-lg shadow-1" >
                                    <div class="q-pa-lg-md">
                                        <p
                                            class="q-mb-xs text-secondary"
                                            v-html="t('page5.section2.text10')"
                                        ></p>
                                        <p
                                            class=""
                                            v-html="t('page5.section2.list4[0]')"
                                        ></p>
                                    </div>
                                </div>
                            </div>

                            <!-- AFTS designations are the inputs needed to evaluate our progress in driving... -->
                            <div class="q-py-lg q-mt-md q-mb-lg col-md-10">
                                <div v-for="(item, index) in tm('page5.section2.paragraphs_11')" :key="index" class="q-mb-md">
                                    <p v-if="typeof item === 'string'" v-html="item" class="last-in-list"></p>
                                    <div v-if="typeof item === 'object' && Array.isArray(item)" class="q-mt-sm">
                                        <ul class="list-unstyled">
                                            <li v-for="(listItem, subIndex) in item" :key="subIndex" v-html="listItem" class="q-mb-sm last-in-list"></li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                        </div>

                    </div>
                </div>

                <!-- ---------------------------------------------------- -->
                <!-- CALLOUT - Process Owners are responsible for the accuracy and... -->
                <!-- ---------------------------------------------------- -->
                <div class="row justify-center items-center q-py-xl q-my-md">

                    <div class="col-md-5 q-pr-lg-lg" data-aos="fade-right">
                        <h3 v-html="t('page5.section2.text12')" class="q-mb-lg q-mb-md-md" ></h3>
                    </div>

                    <div class="offset-md-1 col-md-4" data-aos="fade-left">
                        <Vue3Lottie
                            :auto-play="store.aosEnable"
                            :pause-animation="!store.aosEnable"
                            class="lottie"
                            :loop="true"
                            :scale="1"
                            :animationData="graphic1"
                        />
                    </div>

                </div>


            </div>


            <!-- Process Models -->
            <div :class="$q.screen.lt.md ? 'justify-center' : 'justify-start'" class="row items-start q-pt-xl" >
                <div class="col-10 col-md-4 offset-md-1 q-pb-lg q-pb-md-none" data-aos="fade-rigbt" >
                    <q-img
                        loading="eager"
                        no-transition
                        src="~assets/page5/1.png"
                        width="100%"
                        spinner-color="white"
                    ></q-img>
                </div>
                <div class="col-md-5 q-pl-lg-xl" data-aos="fade-left">
                    <h2 class="q-mb-md" v-html="tm('page5.section1.title1')"></h2>
                    <p class="q-mb-md" v-html="t('page5.section1.text1')"></p>
                    <p class="q-mb-md" v-html="t('page5.section1.text2')"></p>
                </div>
            </div>


            <!-- These models visually describe the steps of the Process by using the Function Taxonomy -->
            <div
                class="row justify-start items-center bg-tax4 q-mt-lg q-mt-sm-xxl q-mb-lg q-pb-sm-xl"
                :style="$q.screen.gt.xs ? 'height: 460px;' : ''"
                data-aos="fade"
            >
                <div class="col-md-10">
                    <div
                        class="row"
                        :class="locale == 'ar' ? 'justify-center' : 'justify-end'"
                    >
                        <div
                            class="col-md-6 q-pa-xl shadow-1"
                            style="background: #f0f5f7; border-radius: 8px; opacity: 0.98"
                        >
                            <p class="" v-html="t('page5.section1.text3')"></p>
                        </div>
                    </div>
                </div>
            </div>


        </div>
        <q-separator
            aria-hidden="true"
            :size="$q.screen.gt.sm ? '100px' : '50px'"
            color="transparent"
        ></q-separator>



        <!-- --------------------------------------------------------------------------------------------------- -->
        <!-- Process Owners are accountable for the accuracy of the Process Models for their Process. -->
        <!-- --------------------------------------------------------------------------------------------------- -->
        <div class="container">

            <q-separator
                aria-hidden="true"
                :size="$q.screen.gt.sm ? '75px' : '25px'"
                color="transparent"
            ></q-separator>

            <div class="row justify-center items-center q-mb-md-xl" style="position: relative" >
                <div class="col-md-4"
                    :style="
                        $q.screen.gt.xs && locale != 'ar'
                            ? 'position: absolute; left: 2%;'
                            : $q.screen.gt.xs && locale == 'ar'
                            ? 'position: absolute; right: 2%;'
                            : 'position: absolute; top:-50px;'
                    "
                    data-aos="fade-right"
                >
                    <Vue3Lottie :auto-play="store.aosEnable"
                        :pause-animation="!store.aosEnable"
                        class="lottie"
                        :loop="true"
                        :scale="1"
                        :animationData="globe"
                    />
                </div>

                <div class="col-12 col-md-10 col-lg-8" style="position: relative" data-aos="fade-left" >
                    <div
                        class="shadow-1 q-py-lg q-px-lg q-px-md-xl q-my-lg"
                        style="background: #255be3; border-radius: 7.5px"
                    >
                        <p class="text-white Citi-Sans-Regular" v-html="t('page5.section1.callout1')"></p>
                    </div>
                </div>
            </div>

            <q-separator
                aria-hidden="true"
                :size="$q.screen.gt.sm ? '75px' : '175px'"
                color="transparent"
            ></q-separator>

        </div>



        <div class="container">


            <!-- ---------------------------------------------------------------------------------------- -->
            <!-- Let’s take a closer look at Process Models by exploring the different components of an... -->
            <!-- ---------------------------------------------------------------------------------------- -->
            <div class="row items-center q-py-lg">
                <div class="offset-md-1 col-md-5 q-pr-lg-lg" data-aos="fade-right">
                    <p class="q-mb-md" v-html="t('page5.section1.text7')"></p>
                </div>
                <div class="col-md-4" data-aos="fade-left">
                    <Vue3Lottie
                        :auto-play="store.aosEnable"
                        :pause-animation="!store.aosEnable"
                        class="lottie"
                        :loop="true"
                        :scale="1"
                        :animationData="graphic1"
                    />
                </div>
            </div>



            <!-- ---------------------------------------------------------------------------------------- -->
            <!-- TABS - Select each of the four tabs (Full Overview, Functions, Data Concepts, and Manual Inputs) -->
            <!-- ---------------------------------------------------------------------------------------- -->
            <div class="row justify-start">
                <div class="offset-md-1 col-md-10 q-pb-xl" data-aos="fade">
                    <p class="text-bold" v-html="t('page5.section1.cta_tabs')"></p>
                </div>
                <div class="offset-lg-1 col-12 col-lg-11">
                    <tabs-c
                        interactionId="p5_tabs_1"
                        :contentArray="tm('page5.section1.tabs1')"
                    ></tabs-c>
                </div>
            </div>


        </div>


        <!-- --------------------------------------------------------------------------------------------------- -->
        <!-- Application Mapping / Why Do We Perform Application Mapping? -->
        <!-- --------------------------------------------------------------------------------------------------- -->
        <div v-if="store.getInteractionSubmitted('p5_tabs_1').submitted" style="background-color: #f7fafb" class="q-pt-md q-mt-xl" data-aos="fade" >
            <div class="container q-pt-xl q-pb-xs">


                <!-- ---------------------------------------------------- -->
                <!-- Technology Simplification / What is Technology Simplification and why is it important? -->
                <!-- ---------------------------------------------------- -->
                <div class="row justify-center">
                    <div style=" background: linear-gradient( to bottom, #255be3 100px, #ffffff 0% ); border-radius: 8px; "
                        class="col-md-10 shadow-1 q-px-lg q-px-md-none"
                        data-aos="fade"
                    >
                        <div class="row justify-center">

                            <div class="col-md-10 q-py-lg q-mb-lg q-mb-sm-xl q-mb-sm-none" >
                                <h2 v-html="t('page5.section3.title')" class="text-white" ></h2>
                            </div>

                            <q-separator
                                aria-hidden="true"
                                :size="$q.screen.gt.xs ? '150px' : '0px'"
                            ></q-separator>

                            <div class="col-md-6 q-pr-md-lg q-pb-lg q-mb-md">
                                <h3 v-html="t('page5.section3.subtitle')" class="q-pb-lg Citi-Sans-Display-Regular" ></h3>
                                <p v-html="t('page5.section3.paragraphs[0]')" class="q-pb-md Citi-Sans-Display-Regular" ></p>
                                <p v-html="t('page5.section3.paragraphs[1]')" class=" Citi-Sans-Display-Regular" ></p>
                            </div>

                            <div class="col-10 col-md-4 q-pb-lg q-mb-md">
                                <q-img
                                    loading="eager"
                                    no-transition
                                    src="~assets/page5/6.png"
                                    spinner-color="white"
                                ></q-img>
                            </div>

                        </div>
                    </div>
                </div>



                <!-- ---------------------------------------------------- -->
                <!-- By reducing Citi’s inherent complexities, through technology... -->
                <!-- ---------------------------------------------------- -->
                <div class="row items-center q-py-xl q-my-md">
                    <div class="offset-md-1 col-md-4" data-aos="fade-right">
                        <Vue3Lottie
                            :auto-play="store.aosEnable"
                            :pause-animation="!store.aosEnable"
                            class="lottie"
                            :loop="true"
                            :scale="1"
                            :animationData="graphic1"
                        />
                    </div>
                    <div class="col-md-5 q-pl-lg-lg" data-aos="fade-left">

                        <div class="q-py-lg q-mt-md q-mb-lg col-md-10">
                            <div v-for="(item, index) in tm('page5.section4.paragraphs')" :key="index" class="q-mb-md Citi-Sans-Display-Regular">
                                <p v-if="typeof item === 'string'" v-html="item" class="last-in-list"></p>
                                <div v-if="typeof item === 'object' && Array.isArray(item)" class="q-mt-sm">
                                    <ul class="list-unstyled">
                                        <li v-for="(listItem, subIndex) in item" :key="subIndex" v-html="listItem" class="q-mb-sm last-in-list"></li>
                                    </ul>
                                </div>
                            </div>
                        </div>

                    </div>
                </div>


            </div>
        </div>


        <!-- --------------------------------------------------------------------------------------------------- -->
        <!-- continue -->
        <!-- --------------------------------------------------------------------------------------------------- -->
        <div v-if="store.getInteractionSubmitted('p5_tabs_1').submitted" class="q-py-none q-pb-lg-xl bg-footer q-pt-xl" >

            <div class="q-pt-lg q-pb-xl container row justify-center">
                <div class="col-12 col-lg-10 text-center q-pb-xl q-mb-xl q-mb-xl-lg">
                    <h3 class="q-mb-lg" v-html="tm('page5.footer.text1')"></h3>
                    <q-btn
                        @click="continueClicked()"
                        :label="t('ui.continue')"
                        style="border-radius: 7.5px"
                        no-caps
                        outline
                        padding="sm xl"
                        class="btn-fixed-width bg-white"
                        color="secondary"
                    ></q-btn>
                </div>
            </div>

        </div>



    </q-page>
</template>

<script>
import { defineComponent, ref, onMounted } from "vue";
import { useQuasar } from "quasar";
import TabsC from "components/TabsC.vue";

import { useI18n } from "vue-i18n";
import { Vue3Lottie } from "vue3-lottie";
import { useStore } from "stores/store";
import { useCore } from "stores/composables/core";
import { useAos } from "stores/composables/aos";

import graphic1 from "../assets/global/graphic_callout1.json";
import check1 from "../assets/global/graphic_1_1_v3.json";
import globe from "../assets/global/graphic_globe.json";

export default defineComponent({
    name: "PageFive",

    components: {
        Vue3Lottie,
        TabsC,
    },

    setup() {
        const store = useStore();
        const core = { ...useCore() };
        const aos = { ...useAos() };

        const { locale } = useI18n({ useScope: "global" });
        const { t, tm } = useI18n();
        const $q = useQuasar();

        const openTranscript = ref(false);

        const continueClicked = () => {
            core.checkNetwork(() => continueClickedCallback());
        };
        const continueClickedCallback = () => {
            core.completePageAndRoute("page5", "/page6");
        };

        onMounted(() => {
            console.log("***------ PageFive - onMounted ------***");
            store.rte_load("page5");

            core.setFocusById("a11yIntro", 400);
            aos.refreshAOS();
        });

        return {
            icon: ref(false),
            bar: ref(false),
            bar2: ref(false),
            toolbar: ref(false),
            store,
            check1,
            graphic1,
            globe,

            ...useCore(),
            ...useAos(),
            locale,
            t,
            tm,
            animations: ref(false),
            openTranscript,
            continueClicked,
        };
    },
});
</script>

<style scoped lang="scss">
@import "../css/quasar.variables.scss";

/* for parallax containers, couldn't do inline src background tag? using classes instead */

.bg-footer {
    background-image: url("../assets/page1/footer.svg");
    background-position: bottom center;
    background-repeat: no-repeat;
    background-size: auto;
}

.bg-lorenzo {
    background-image: url("../assets/page3/bg_lorenzo.png");
    background-position: 44% center;
    background-repeat: no-repeat;
    background-size: auto;
}

body.screen--xs {
    .bg-tax4 {
        background-image: none;
    }
}

.bg-tax4 {
    background-image: url("../assets/page5/bg_tax4.png");
    background-position: 44% center;
    background-repeat: no-repeat;
    background-size: auto;
}

.bg-world {
    background-image: url("../assets/page1/3.svg");
    background-position: 3% center;
    background-repeat: no-repeat;
    background-size: contain;
}

.li-style {
    list-style-image: url("../assets/global/licheck.svg");
}
</style>
