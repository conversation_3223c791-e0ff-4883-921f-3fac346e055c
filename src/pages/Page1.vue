<template>
    <q-page class="overflow-hidden">

        <!-- A11y INTRO -->
        <div class="sr-only q-py-md">
            <p class="q-mb-md" v-html="t('ui.pageLoaded')" id="a11yIntro" style="outline: 0;" tabindex="0"></p>
            <p
                v-html="(store.getPagesCompleted ? store.getPagesCompleted : 0) + $t('ui.of') + (store.manifest.content.length) + t('ui.pagesCompleted')">
            </p>
        </div>

        <div class="container" data-aos="zoom-out">
            <div class="row text-center justify-center">
                <div class="col-md-8 q-pt-md">
                    <q-img class="q-mt-xl q-mb-lg" src="~assets/logo-blue-red.svg" spinner-color="white"
                        style="width: 55px;"></q-img>
                    <h1 class="q-mb-md q-pt-lg q-mt-md" style="border-top: 1px solid #255BE3;"
                        v-html="tm('page1.sectionTop.title1')"></h1>
                    <p v-html="t('page1.sectionTop.title2')"></p>
                    <p class="text-bold q-pt-xl q-mt-lg-lg" v-html="t('ui.scrollc')"></p>
                </div>
            </div>
        </div>

        <q-separator aria-hidden="true" :size="$q.screen.gt.md ? '150px' : '100px'" color="transparent"></q-separator>

        <!-- --------------------------------------------------------------------------------------------------- -->
        <!-- How to Navigate This Course -->
        <!-- --------------------------------------------------------------------------------------------------- -->
        <div class="">

            <div class="container">
                <div class="row">
                    <div class="q-mb-xl col-12 col-lg-7">
                        <h2 class="q-pb-lg" v-html="tm('page1.section1.title1')" data-aos="fade-right"> </h2>

                        <p class="" v-html="tm('page1.section1.instr1')" data-aos="fade-right">
                        </p>
                    </div>
                </div>
                <div class="row q-col-gutter-xl">
                    <div class="col-12 col-md-6" v-for="(item, index) in tm('page1.section1.list1')" :key="index"
                        data-aos="zoom-in">
                        <div class="q-px-lg q-pt-lg shadow-1" :style="$q.screen.gt.md ? 'min-height:310px' : $q.screen.md ? 'min-height:400px' :'min-height:350px'"
                            style="background-color: #F0F5F7; border-radius: 25px;">
                            <q-icon class="q-mb-md text-secondary" size="55px" :name="item.icon1"></q-icon>
                            <p v-html="item.text1"></p>
                            <div v-if="index===3" class="q-pt-sm row justify-between">
                                <q-toggle v-model="store.aosEnable"
                                    :label="store.aosEnable ? t('ui.on') : t('ui.off')"
                                    @update:model-value="initAOS(); refreshAOS();"></q-toggle>
                                <!-- <q-icon class="q-ml-md" size="35px" name="animation"></q-icon> -->
                            </div>
                        </div>
                    </div>

                </div>

            </div>

        </div>
        <q-separator :size="$q.screen.gt.md ? '100px' : '75px'" aria-hidden="true" color="transparent" ></q-separator>


        <div class="trigger"></div>


        <!-- --------------------------------------------------------------------------------------------------- -->
        <!-- Citi’s Enterprise Architecture and Process Governance Training /  -->
        <!-- Why, Why, Why  ---  What's the win? - (2-components) -->
        <!-- --------------------------------------------------------------------------------------------------- -->
        <div class="bg-grey">

            <div class="container" >
                <q-separator :size="$q.screen.gt.md ? '100px' : '75px'" aria-hidden="true" color="transparent" ></q-separator>

                <div class="row">
                    <div class="col-12 col-lg-7">
                        <h2 class="q-mb-lg" v-html="tm('page1.sectionWhyWhyWhyWhy.title')" data-aos="fade-right"> </h2>
                    </div>
                </div>

                <!-- ----------------------------------------------------------------------------------------------------- -->
                <!-- 🚨🚨🚨🚨🚨 box-list-component  -->
                <!-- Why, Why, Why? -->
                <!-- ----------------------------------------------------------------------------------------------------- -->
                <box-list-component
                    :list-items="tm('page1.sectionWhyWhyWhyWhy.list').slice(0, 3)"
                    :background-class="undefined"
                    :text-class="undefined"
                />
                <q-separator size="2rem" color="transparent" />
                <!-- ----------------------------------------------------------------------------------------------------- -->
                <!-- 🚨🚨🚨🚨🚨 image-box-text-box -->
                <!-- What's the Win? -->
                <!-- ----------------------------------------------------------------------------------------------------- -->
                <image-box-text-box
                    image-path="assets/page1/whats_the_win.jpg"
                    :title="tm('page1.sectionWhyWhyWhyWhy.list[3].title')"
                    :paragraphs="tm('page1.sectionWhyWhyWhyWhy.list[3].paragraphs')"
                    :text-right="undefined"
                    :image-always-top-on-mobile="undefined"
                    :title-class="undefined"
                    :background-class="undefined"
                    :text-class="undefined"
                />

                <q-separator :size="$q.screen.gt.md ? '100px' : '75px'" aria-hidden="true" color="transparent" ></q-separator>
            </div>

        </div>


        <!-- --------------------------------------------------------------------------------------------------- -->
        <!-- bg and cube banner background -->
        <!-- --------------------------------------------------------------------------------------------------- -->
        <div class=" bg-banner-img">
            <q-separator aria-hidden="true" size="250px" color="transparent"></q-separator>

            <div class="bg-split">

                <div aria-hidden="true" fluid class="px-0 bg-waves"
                    style="display: flex; justify-content: center;">

                    <q-img class="slice" loading="eager" width="250px" style="margin-top: -0rem" no-transition
                        src="~assets/page1/cube.svg"
                    ></q-img>

                </div>

            </div>
        </div>


        <!-- --------------------------------------------------------------------------------------------------- -->
        <!-- This training will help you understand: / After completing this course, you’ll be able to: -->
        <!-- --------------------------------------------------------------------------------------------------- -->
        <div class="container" style="margin-top: -1rem">

            <!-- This training will help you understand:  -->
            <div data-aos="fade" class="row justify-center items-center q-mb-md-xl q-pb-xl">

                <div class="col-10 col-md-4 q-mb-xs-xl" data-aos="fade-right">
                    <q-img loading="eager" no-transition src="~assets/page1/1.svg" spinner-color="white"></q-img>
                </div>

                <div class="col-md-5 offset-md-1" data-aos="fade-left">
                    <div class="q-my-lg" style="border-top: 1px solid #255BE3; width:10%"></div>

                    <p v-html="t('page1.section2.text1')"></p>
                    <ul>
                        <li v-for="(item, index) in tm('page1.section2.list1')"
                            :key="index"
                            v-html="item"
                            class="q-mb-md last-in-list li-style"
                        ></li>
                    </ul>

                </div>
            </div>

            <!-- After completing this course, you’ll be able to: -->
            <div data-aos="fade" class="row justify-center items-center">

                <div class="col-md-5 order-last order-md-first" data-aos="fade-right">
                    <h3 class="q-mb-lg" v-html="t('page1.section2.text2')"></h3>
                    <ul>
                        <li v-for="(item, index) in tm('page1.section2.list2')" :key="index" v-html="item"
                            class="q-mb-md last-in-list li-style"></li>
                    </ul>
                </div>
                <div class="col-6 col-10 col-md-5 order-first order-md-last " data-aos="fade-left">
                    <q-img loading="eager" no-transition src="~assets/page1/2.png" class="q-mb-lg q-mb-md-none" spinner-color="white"
                     :style="$q.screen.gt.xs ? 'width:85%; margin-left:20px' : ''"></q-img>
                </div>

            </div>

        </div>
        <q-img loading="eager" no-transition src="~assets/page1/wavePositive.png" spinner-color="white" ></q-img>
        <q-separator aria-hidden="true" :size="$q.screen.gt.xs ? '100px' : '25px'" color="transparent"></q-separator>




        <div class="container" >


<!-- --------------------------------------------------------------------------------------------------- -->
<!-- Please note this course contains a final assessment. -->
<!-- --------------------------------------------------------------------------------------------------- -->
<!-- <div data-aos="fade" class="row justify-center items-center q-mb-lg q-mb-lg-xl">
    <div  class="col-4 col-md-2 offset-lg-1">
        <Vue3Lottie v-if="$q.screen.gt.xs"  :auto-play="store.aosEnable" :pause-animation="!store.aosEnable" class="lottie" :scale="0.7"  :animationData="check1" />
    </div>

    <div class="col-md-8 q-pa-xl q-mt-lg q-mt-sm-none" :style= "$q.screen.gt.sm && locale != 'ar' ? 'background-color: #F0F5F7; margin-left: -4.5rem;' : $q.screen.gt.sm && locale == 'ar' ? 'background-color: #F0F5F7; margin-right: -4.5rem;' :'background-color: #F0F5F7;'">
        <h3 class="q-px-md-xl" v-html="t('page1.section2.text3')"></h3>
    </div>
</div> -->


            <!-- --------------------------------------------------------------------------------------------------- -->
            <!-- Citi has grown into many businesses and markets over the years, inheriting... -->
            <!-- --------------------------------------------------------------------------------------------------- -->
            <div data-aos="fade" class="row justify-center items-center q-pt-lg q-pb-xl q-mb-sm">

                <div class="col-11 col-md-10">
                    <h2 class="Citi-Sans-Display-Bold q-mb-xl" v-html="t('page1.section3.title')"></h2>
                </div>

                <div class="col-6 col-10 col-md-5 q-pb-lg q-mb-md-none" data-aos="fade-right">
                    <q-img loading="eager" no-transition src="~assets/page1/3.png" :style="$q.screen.gt.xs ? 'width: 85%;' : ''" spinner-color="white"></q-img>
                </div>

                <div class="col-md-5 q-pa-md-md" data-aos="fade-left">
                    <!-- <h2 class="Citi-Sans-Display-Bold q-pb-lg" v-html="t('page1.section3.title')"></h2> -->
                    <ListComponent
                        class="Citi-Sans-Display-Regular q-mb-md"
                        propFirstLevel="p"
                        :propListData="tm('page1.section3.paragraphs')"
                        propMarginFirstLevel="1rem"
                        propMarginSecondLevel="0.5rem"
                        :prop-animation="false"
                    />
                </div>
            </div>


            <!-- --------------------------------------------------------------------------------------------------- -->
            <!-- This complexity has presented itself in various ways, such as:... -->
            <!-- --------------------------------------------------------------------------------------------------- -->
            <div data-aos="fade" class="row justify-center items-center q-pb-xl q-mb-lg">
                <div class="col-md-5 q-pa-md-md order-last order-md-first" data-aos="fade-right">
                    <ListComponent
                        class="Citi-Sans-Display-Regular q-mb-md"
                        propFirstLevel="p"
                        :propListData="tm('page1.section4.paragraphs')"
                        propMarginFirstLevel="1rem"
                        propMarginSecondLevel="0.5rem"
                        :prop-animation="false"
                    />
                </div>
                <div class="col-6 col-10 col-md-5 order-first order-md-last q-mb-lg q-mb-md-none" data-aos="fade-left">
                    <q-img loading="eager" no-transition src="~assets/page1/4.png" :style="$q.screen.gt.xs ? 'width: 85%;' : ''" spinner-color="white"></q-img>
                </div>


            </div>


            <!-- --------------------------------------------------------------------------------------------------- -->
            <!-- To address this, Citi has established the Citi Enterprise Architecture Methodology, better known as... -->
            <!-- --------------------------------------------------------------------------------------------------- -->
            <div class="row q-pb-xxl justify-center" data-aos="fade">
                <div class="col-lg-10">
                    <ListComponent
                        class="Citi-Sans-Display-Regular text-h3 text-secondary q-mb-md"
                        propFirstLevel="p"
                        :propListData="tm('page1.section5.paragraphs')"
                        propMarginFirstLevel="1rem"
                        propMarginSecondLevel="0.5rem"
                        :prop-animation="false"
                    />

                    <ListComponent
                        class="Citi-Sans-Display-Regular q-mb-md last-in-list-no-margin"
                        propFirstLevel="p"
                        :propListData="tm('page1.section6.paragraphs')"
                        propMarginFirstLevel="1rem"
                        propMarginSecondLevel="0.5rem"
                        :prop-animation="false"
                    />
                </div>
            </div>


            <!-- --------------------------------------------------------------------------------------------------- -->
            <!-- The foundation of Enterprise Architecture is the Business Process Taxonomy, which is... -->
            <!-- --------------------------------------------------------------------------------------------------- -->
            <div data-aos="fade" class="row justify-center items-center q-mb-xl q-pb-xl">
                <div class="col-6 col-10 col-md-4 q-mb-md q-mb-md-none" data-aos="fade-right">
                    <Vue3Lottie :auto-play="store.aosEnable" :pause-animation="!store.aosEnable" class="lottie" :loop="true" :scale="1" :animationData="graphic1" />
                </div>

                <div class="col-md-5 offset-md-1 " data-aos="fade-left">
                    <ListComponent
                        class="Citi-Sans-Display-Regular q-mb-md last-in-list-no-margin"
                        propFirstLevel="p"
                        :propListData="tm('page1.section7.paragraphs')"
                        propMarginFirstLevel="1rem"
                        propMarginSecondLevel="0.5rem"
                        :prop-animation="false"
                    />

                    <ListComponent
                        class="Citi-Sans-Display-Regular q-mb-md last-in-list-no-margin"
                        propFirstLevel="p"
                        :propListData="tm('page1.section8.paragraphs')"
                        propMarginFirstLevel="1rem"
                        propMarginSecondLevel="0.5rem"
                        :prop-animation="false"
                    />
                </div>
            </div>



        </div>
        <!-- <q-separator aria-hidden="true" :size="$q.screen.gt.xs ? '100px' : '25px'" color="transparent"></q-separator> -->


        <!-- --------------------------------------------------------------------------------------------------- -->
        <!-- course level testout - UPDATE apr 2025 -->
        <!-- --------------------------------------------------------------------------------------------------- -->
        <AssessmentCourseLevelTestOut
            v-if="
                !store.getInteractionSubmitted(`page1_testout_complete_passed`).submitted &&
                !store.getInteractionSubmitted(`page1_testout_complete_failed`).submitted &&
                !store.getInteractionSubmitted(`page1_testout_complete_skiped`).submitted
            "
            pageId="page1"
            nextRoute="/page2"
            :pagesToCompleteWhenPassed="['page1', 'page2', 'page3', 'page4', 'page5', 'page6', 'page7', 'page8', 'page11']"
            :testoutQuestions="tm('assessmentFinal.questionsSection')"
            :numberQuestions="10"
        ></AssessmentCourseLevelTestOut>


        <!-- --------------------------------------------------------------------------------------------------- -->
        <!-- continue -->
        <!-- --------------------------------------------------------------------------------------------------- -->
        <div
            v-if="
                store.getInteractionSubmitted(`page1_testout_complete_passed`).submitted ||
                store.getInteractionSubmitted(`page1_testout_complete_failed`).submitted ||
                store.getInteractionSubmitted(`page1_testout_complete_skiped`).submitted
            "
            class="bg-footer"
        >
            <div class="q-py-xl container row justify-center">
                <div class="col-12 col-lg text-center q-mb-xxl">
                    <h3 class="q-mb-xl" v-html="tm('page1.footer.text1')"></h3>
                    <q-btn @click="continueClicked()" :label="t('ui.continue')" style="border-radius: 7.5px;" no-caps  outline
                        padding="sm xl" class="btn-fixed-width bg-white" color="secondary"></q-btn>
                </div>
            </div>
        </div>


    </q-page>

</template>

<script>
    import { defineComponent, ref, onMounted } from 'vue'
    import { useQuasar } from 'quasar'
    import { useI18n } from 'vue-i18n'

    import { useStore } from 'stores/store';
    import { useCore } from "stores/composables/core";
    import { useAos } from "stores/composables/aos";

    import { gsap } from "gsap";
    import { ScrollTrigger } from "gsap/ScrollTrigger";
    import { Vue3Lottie } from 'vue3-lottie'
    import check1 from '../assets/global/graphic_1_1_v3.json'
    import graphic1 from '../assets/global/graphic_callout1.json'
    import globe from '../assets/global/graphic_globe.json'

    import BoxListComponent from "components/BoxListComponent.vue";
    import ImageBoxTextBox from "components/ImageBoxTextBox.vue";

    import AssessmentCourseLevelTestOut from "components/AssessmentCourseLevelTestOut.vue";

    import ListComponent from 'components/ListComponent.vue'

    gsap.registerPlugin(ScrollTrigger);

    export default defineComponent({
        name: 'PageOne',

        components: {
            Vue3Lottie,
            BoxListComponent,
            ImageBoxTextBox,

            AssessmentCourseLevelTestOut,

            ListComponent,
        },

        setup() {
            const store = useStore();
            const core = { ...useCore() };
            const aos = { ...useAos() };
            const { locale } = useI18n({ useScope: 'global' })
            const { t, tm } = useI18n();
            const $q = useQuasar();

            const openTranscript = ref(false);

            const continueClicked = (testOutOption) => {
                if (testOutOption) {
                    store.setInteraction(testOutOption, null, null, true);
                }
                core.checkNetwork(() => continueClickedCallback());
            };
            const continueClickedCallback = () => {
                core.completePageAndRoute("page1", "/page2");
            };


            onMounted(() => {
                console.log(locale.value)
                const tl = gsap.timeline({
                    scrollTrigger: {
                        trigger: ".trigger",
                        scrub: 1,
                        start: 'center center',
                        end: '+=500',
                        markers: false,
                        once:true,
                    }
                });

                tl.fromTo(
                    (".slice"),
                    1,
                    { scale: 0.7, y: -50 },
                    { scale: 1, y: -100, ease: "Power1.easeInOut", }
                )
                tl.from(
                    (".bg-banner-img"),
                    1,
                    { backgroundPosition: "50% 25%" },
                    { ease: "Power1.easeInOut", }
                )
                console.log("***------ PageOne - onMounted ------***");
                store.rte_load("page1");

                // set focus to the a11yIntro - if page 1 is completed it will focus the top message, otherwise it will read the mainlayout message intro SR message
                if (store.getPageStatusById('page1') == 1) {
                    core.setFocusById("a11yIntro", 400);
                }
                aos.refreshAOS();
            });

            return {
                store,
                check1,
                graphic1,
                ...useCore(),
                ...useAos(),
                locale, t, tm,
                animations: ref(false),
                openTranscript,
                continueClicked,
                globe,

            }
        }

    })
</script>




<style scoped lang="scss">
    @import '../css/quasar.variables.scss';

    .bg-footer {
          background-image: url("../assets/page1/footer.svg");
          background-position: bottom center;
        background-repeat: no-repeat;
        background-size: auto;
    }

    .bg-world{
         background-image: url("../assets/page1/3.svg");
          background-position: 13% center;
        background-repeat: no-repeat;
        background-size: contain;
    }

    .li-style {
        list-style-image: url("../assets/global/licheck.svg");
        ;
    }

    .bg-banner-img {

        background-image: url("../assets/page1/bg_cover_2.png");

        /* Create the parallax scrolling effect */
        background-position: top center;
        background-repeat: no-repeat;
        background-size: auto;

    }

    .bg-waves {
        background-image: url("../assets/page1/waveNegative.png");
        background-position: 50% 90%;
        background-size: auto;
        background-repeat: no-repeat;

    }

    @media (max-width: 768px) {
        .bg-waves {
            background-image: url("../assets/page1/waveNegative.png");
            background-position: bottom center;
            background-size: auto;
            background-repeat: no-repeat;
        }
    }

    .bg-split {
        background: linear-gradient(to bottom, transparent 80%, #ffffff 20%);
    }


    .bg-1_1 {
        background-position: top center;
        background-image: url("../assets/page1/1_1.jpg");
        background-repeat: no-repeat;
        background-size: auto;
    }

    .bg-1_8 {
        background-position: center center;
        background-image: url("../assets/page1/1_8.jpg");
        background-repeat: no-repeat;
        background-size: cover;
    }
</style>
