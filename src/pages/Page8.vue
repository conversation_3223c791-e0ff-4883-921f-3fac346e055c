<template>
    <q-page class="overflow-hidden">

        <!-- A11y INTRO -->
        <div class="sr-only q-py-md">
            <p
                class="q-mb-md"
                v-html="t('ui.pageLoaded')"
                id="a11yIntro"
                style="outline: 0"
                tabindex="0"
            ></p>
            <p
                v-html="
                    (store.getPagesCompleted ? store.getPagesCompleted : 0) +
                    $t('ui.of') +
                    store.manifest.content.length +
                    t('ui.pagesCompleted')
                "
            ></p>
        </div>


        <!-- —------------------------------------------------ -->
        <!-- Course Summary /  We’ve covered the following in this training: -->
        <!-- —------------------------------------------------ -->
        <div class="container q-pb-lg" data-aos="zoom-out">
            <div class="row justify-start">
                <div class="col-lg-6 q-pt-xl offset-md-1">
                    <h1
                        class="q-mb-lg q-pt-lg q-mt-md"
                        v-html="t('page8.sectionTop.title1')"
                    ></h1>
                    <p v-html="t('page8.section1.text1')"></p>
                </div>
            </div>
        </div>



        <div class="container q-pt-lg q-pt-sm-xl q-pb-sm-xl">


            <!-- —------------------------------------------------ -->
            <!-- image bg / text x __ -->
            <!-- —------------------------------------------------ -->
            <div v-for="(item, index) in tm('page8.section1.cards1')" :key="index"
                data-aos="zoom-out"
                class="row justify-start items-center q-pb-sm-xl q-mb-xl q-mt-lg"
                :class="'bg' + parseInt(index + 1)"
                :style="$q.screen.gt.xs ? 'height: 450px;' : ''"
            >
                <div class="col-md-11">

                    <div class="row" :class="locale == 'ar' ? 'justify-start' : 'justify-start'" >
                        <div
                            :data-aos="index % 2 ? 'fade-left' : 'fade-right'"
                            class="col-md-5 q-py-lg shadow-1"
                            :class="
                                index % 2 && locale == 'ar'
                                    ? 'offset-md-4'
                                    : locale == 'ar'
                                    ? 'offset-md-3'
                                    : index % 2
                                    ? 'offset-md-2'
                                    : 'offset-md-7'
                            "
                            style="background: #f0f5f7; border-radius: 8px; opacity: 0.98"
                        >
                            <div class="q-px-md q-mx-md">
                                <p
                                    v-if="item.text"
                                    v-html="item.text"
                                    :class="item.list ? 'q-mb-md' : ''"
                                ></p>
                                <ul v-if="item.list" class="" style="margin-bottom: 0">
                                    <li
                                        v-for="(item, index) in item.list" :key="index"
                                        v-html="item"
                                        class="q-mb-sm last-in-list"
                                    ></li>
                                </ul>
                                <p
                                    v-for="(item, index) in item.paragraphs" :key="index"
                                    v-html="item"
                                    class="q-mb-md last-in-list"
                                ></p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>


            <!-- --------------------------------------------------------------------------------------------------- -->
            <!-- Review the enterprise architecture and process governance policy... -->
            <!-- --------------------------------------------------------------------------------------------------- -->
            <div data-aos="fade" class="row items-center q-pt-md-xl q-pb-lg q-pb-sm-xl q-pb-md-xxl" >
                <div v-if="$q.screen.gt.sm" class="col-4 col-md-2 offset-4 offset-md-1">
                    <Vue3Lottie
                        :auto-play="store.aosEnable"
                        :pause-animation="!store.aosEnable"
                        class="lottie"
                        :loop="true"
                        :scale="0.7"
                        :animationData="check1"
                    />
                </div>

                <div
                    class="col-md-8 q-pa-xl"
                    :style="
                        $q.screen.gt.sm && locale != 'ar'
                            ? 'background-color: #F0F5F7; margin-left: -4.5rem;'
                            : $q.screen.gt.sm && locale == 'ar'
                            ? 'background-color: #F0F5F7; margin-right: -4.5rem;'
                            : 'background-color: #F0F5F7;'
                    "
                >
                    <h3
                        class="q-px-md-xl q-mx-sm-md"
                        v-html="t('page8.section1.text2')"
                    ></h3>
                </div>
            </div>


            <!-- --------------------------------------------------------------------------------------------------- -->
            <!-- Remember, applying enterprise architecture, and process governance results in simplification... -->
            <!-- --------------------------------------------------------------------------------------------------- -->
            <div class="row justify-end items-center q-mt-xxl q-mt-sm-none q-py-sm-xxl q-py-xl" style="position: relative" >
                <div
                    class="col-md-4"
                    :style="
                        $q.screen.gt.xs && locale != 'ar'
                            ? 'position: absolute; left: 2%;'
                            : $q.screen.gt.xs && locale == 'ar'
                            ? 'position: absolute; right: 2%;'
                            : 'position: absolute; top:-50px;'
                    "
                    data-aos="fade-right"
                >
                    <Vue3Lottie
                        :auto-play="store.aosEnable"
                        :pause-animation="!store.aosEnable"
                        class="lottie"
                        :loop="true"
                        :scale="1"
                        :animationData="globe"
                    />
                </div>

                <div
                    class="q-mt-sm-xl q-mt-md-none col-12 col-md-10"
                    style="position: relative"
                    data-aos="fade-left"
                >
                    <div
                        class="shadow-1 q-py-lg q-px-lg-xl q-my-lg"
                        style="background: #255be3; border-radius: 7.5px"
                    >
                        <h3
                            class="text-white q-pt-lg q-pb-lg q-mx-xl"
                            v-html="t('page8.section1.text3')"
                        ></h3>
                    </div>
                </div>
            </div>


        </div>



        <!-- --------------------------------------------------------------------------------------------------- -->
        <!-- continue -->
        <!-- --------------------------------------------------------------------------------------------------- -->
        <div class="q-py-none q-py-lg-xl bg-footer">
            <div class="q-pt-lg q-pb-xl container row justify-center">
                <div class="col-12 col-lg-9 text-center q-pb-xl q-mb-xl q-mb-xl-lg">
                    <h3 class="q-mb-lg" v-html="t('page8.footer.text1')"></h3>
                    <q-btn
                        @click="continueClicked()"
                        :label="t('ui.continue')"
                        style="border-radius: 7.5px"
                        no-caps
                        outline
                        padding="sm xl"
                        class="btn-fixed-width bg-white q-mt-xs"
                        color="secondary"
                    ></q-btn>
                </div>
            </div>
        </div>


    </q-page>
</template>

<script>
import { defineComponent, ref, onMounted } from "vue";
import { useQuasar } from "quasar";
import { useI18n } from "vue-i18n";
import { Vue3Lottie } from "vue3-lottie";
import { useStore } from "stores/store";
import { useCore } from "stores/composables/core";
import { useAos } from "stores/composables/aos";
import graphic1 from "../assets/global/graphic_callout1.json";
import check1 from "../assets/global/graphic_1_1_v3.json";
import globe from "../assets/global/graphic_globe.json";
import check from "../assets/page3/modalcheckciti.json";
export default defineComponent({
    name: "PageEight",

    components: {
        Vue3Lottie,
    },

    setup() {
        const store = useStore();
        const core = { ...useCore() };
        const aos = { ...useAos() };

        const { locale } = useI18n({ useScope: "global" });
        const { t, tm } = useI18n();
        const $q = useQuasar();

        const openTranscript = ref(false);

        const continueClicked = () => {
            core.checkNetwork(() => continueClickedCallback());
        };
        const continueClickedCallback = () => {
            core.completePageAndRoute("page8", "/page11");
        };

        function getImageUrl(index) {
            // This path must be correct for your file
            return new URL(
                `../assets/page7/card${parseInt(index + 1)}.png

                `,
                import.meta.url
            );
        }

        onMounted(() => {
            console.log("***------ PageEight - onMounted ------***");
            store.rte_load("page8");

            core.setFocusById("a11yIntro", 400);
            aos.refreshAOS();
        });

        return {
            check,
            store,
            check1,
            graphic1,
            ...useCore(),
            ...useAos(),
            locale,
            t,
            tm,
            animations: ref(false),
            openTranscript,
            continueClicked,
            globe,
            getImageUrl,
        };
    },
});
</script>

<style scoped lang="scss">
@import "../css/quasar.variables.scss";

/* for parallax containers, couldn't do inline src background tag? using classes instead */

.bg-greta {
    background-image: url("../assets/page6/bg_greta.png");
    background-position: right center;
    background-repeat: no-repeat;
    background-size: contain;
}

.bg-footer {
    background-image: url("../assets/page1/footer.svg");
    background-position: bottom center;
    background-repeat: no-repeat;
    background-size: auto;
}

.bg-lorenzo {
    background-image: url("../assets/page3/bg_lorenzo.png");
    background-position: 44% center;
    background-repeat: no-repeat;
    background-size: auto;
}

body.screen--xs {
    .bg-lorenzo {
        background-image: none;
    }

    .bg1 {
        background-image: none;
    }

    .bg2 {
        background-image: none;
    }
    .bg3 {
        background-image: none;
    }
    .bg4 {
        background-image: none;
    }
}

.bg1 {
    background-image: url("../assets/page8/bg1.png");
    background-position: 33% center;
    background-repeat: no-repeat;
    background-size: auto;
}

.bg2 {
    background-image: url("../assets/page8/bg2.png");
    background-position: 33% center;
    background-repeat: no-repeat;
    background-size: auto;
}

.bg3 {
    background-image: url("../assets/page8/bg3.png");
    background-position: 33% center;
    background-repeat: no-repeat;
    background-size: auto;
}
.bg4 {
    background-image: url("../assets/page8/bg4.png");
    background-position: 33% center;
    background-repeat: no-repeat;
    background-size: auto;
}
.bg5 {
    background-image: url("../assets/page8/bg5.png");
    background-position: 33% center;
    background-repeat: no-repeat;
    background-size: auto;
}
.bg-world {
    background-image: url("../assets/page1/3.svg");
    background-position: 3% center;
    background-repeat: no-repeat;
    background-size: contain;
}

.li-style {
    list-style-image: url("../assets/global/licheck.svg");
}
</style>
