<template>
    <q-page class="overflow-hidden">


        <!-- A11y INTRO -->
        <div class="sr-only q-py-md">
            <p
                class="q-mb-md"
                v-html="t('ui.pageLoaded')"
                id="a11yIntro"
                style="outline: 0"
                tabindex="0"
            ></p>
            <p
                v-html="
                    (store.getPagesCompleted ? store.getPagesCompleted : 0) +
                    $t('ui.of') +
                    store.manifest.content.length +
                    t('ui.pagesCompleted')
                "
            ></p>
        </div>


        <!-- --------------------------------------------------------------------------------------------------- -->
        <!-- Foundation of Enterprise Architecture: Process & Function Taxonomies / Let’s start with a payment scenario that... -->
        <!-- --------------------------------------------------------------------------------------------------- -->
        <div class="container q-pb-xl" data-aos="zoom-out">
            <div class="row justify-start">
                <div class="col-lg-8 q-pt-xl offset-md-1">
                    <h1 v-html="tm('page3.sectionTop.title1')" class="q-pb-md q-pb-sm-none q-mb-lg q-pt-lg q-mt-md" ></h1>
                </div>
            </div>

            <div data-aos="fade" class="row items-center q-mt-sm-lg q-mt-md-none">
                <div class="q-pt-xl offset-md-1 col-11 col-md-10 col-lg-7">
                    <h2
                        class="Citi-Sans-Display-Bold q-mb-lg"
                        v-html="t('page3.section1.title')"
                    ></h2>
                    <p
                        class="Citi-Sans-Display-Regular"
                        v-html="t('page3.section1.text1')"
                    ></p>
                </div>
            </div>

        </div>


        <!-- --------------------------------------------------------------------------------------------------- -->
        <!-- Meet Lorenzo, they are a current Citi client who runs a small... -->
        <!-- --------------------------------------------------------------------------------------------------- -->
        <div data-aos="fade" class="container q-pb-lg q-pb-sm-xl">
            <div
                class="row justify-start items-center bg-lorenzo q-pb-xl"
                :style="$q.screen.gt.xs ? 'height:450px' : ''"
            >
                <div class="col-lg-10 col-md-11">
                    <div class="row" :class="locale == 'ar' ? 'justify-center' : 'justify-end'" >
                        <div
                            class="col-md-5 col-lg-6 q-pa-lg q-pa-lg-xl shadow-1"
                            style="background: #f0f5f7; border-radius: 8px; opacity: 0.98"
                        >
                            <p
                                class="Citi-Sans-Display-Regular q-mb-md"
                                v-html="t('page3.section1.example_lorenzo[0]')"
                            ></p>
                            <p
                                class="Citi-Sans-Display-Regular"
                                v-html="t('page3.section1.example_lorenzo[1]')"
                            ></p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <q-separator :size="$q.screen.gt.sm ? '100px' : '25px'" aria-hidden="true" color="transparent" />



        <!-- --------------------------------------------------------------------------------------------------- -->
        <!-- Why? - Behind the scenes, Citi’s payment technology is complex. -->
        <!-- --------------------------------------------------------------------------------------------------- -->
        <div class="container">
            <div class="row justify-center items-center q-mb-xl">
                <div
                    data-aos="fade-right"
                    style="background: #f0f5f7; border-radius: 8px"
                    class="order-last order-md-first q-mt-md q-mt-md-none shadow-1 col-md-5 col-lg-4 q-pa-lg q-pa-lg-xl q-mr-md-xl"
                >
                    <p
                        class="Citi-Sans-Display-Bold text-h3 q-mb-md"
                        v-html="t('page3.section1.example_lorenzo[2]')"
                    ></p>
                    <p
                        class="Citi-Sans-Display-Regular"
                        v-html="t('page3.section1.example_lorenzo[3]')"
                    ></p>
                </div>
                <div class="col-10 col-md-4" data-aos="fade-left">
                    <q-img
                        loading="eager"
                        no-transition
                        src="~assets/page3/1.png"
                        spinner-color="white"
                    ></q-img>
                </div>
            </div>

            <!-- As a result, Lorenzo has to log into and navigate three different payment platforms just to pay their suppliers. -->
            <div class="row justify-center items-center q-mb-xl">
                <div class="col-10 col-md-4 q-mb-md q-mb-md-none" data-aos="fade-right">
                    <q-img
                        loading="eager"
                        no-transition
                        src="~assets/page3/2.png"
                        spinner-color="white"
                    ></q-img>
                </div>
                <div
                    data-aos="fade-left"
                    style="background: #f0f5f7; border-radius: 8px"
                    class="q-ml-md-xl shadow-1 col-md-6 col-lg-4 q-pa-lg q-pa-lg-xl"
                >
                    <p
                        class="Citi-Sans-Display-Regular"
                        v-html="t('page3.section1.example_lorenzo[4]')"
                    ></p>
                </div>
            </div>



            <!-- Lorenzo's situation highlights where enterprise architecture... -->
            <div class="row justify-center items-center q-pt-lg q-mb-xl">

                <div class="col-md-5" data-aos="fade-down">
                    <p
                        class="Citi-Sans-Display-Regular q-mb-md"
                        v-html="t('page3.section1.example_lorenzo[5]')"
                    ></p>
                    <p
                        class="Citi-Sans-Display-Regular"
                        v-html="t('page3.section1.example_lorenzo[6]')"
                    ></p>
                </div>

                <div class="col-10 col-md-5 q-mb-md q-mb-md-none q-ml-md-xl" data-aos="fade-up">
                    <q-img
                        loading="eager"
                        no-transition
                        src="~assets/page3/3.png"
                        spinner-color="white"
                        :style="$q.screen.gt.xs ? 'width: 85%;' : ''"
                    ></q-img>
                </div>

            </div>


        </div>


        <!-- --------------------------------------------------------------------------------------------------- -->
        <!-- ACCORDION - Now let’s further explore how Enterprise Taxonomies, most notably the Process and... -->
        <!-- --------------------------------------------------------------------------------------------------- -->
        <div style="background-color: #f7fafb" class="q-py-md q-mb-xl" data-aos="fade">
            <div class="container q-py-xl">

                <!-- intro / instructions -->
                <div class="row">
                    <div class="offset-md-1 col-11 col-md-10 col-lg-6 q-pb-xl">
                        <p
                            class="Citi-Sans-Display-Regular q-mb-lg balance-this-text"
                            v-html="t('page3.section1.text8')"
                        ></p>
                        <p
                            class="Citi-Sans-Display-Regular text-bold"
                            v-html="t('page3.section1.cta_accordion')"
                        ></p>
                    </div>
                </div>
                <AccordionComponent
                    interactionId="p3_accordion_1"
                    :contentArray="tm('page3.section1.accordion1')"
                ></AccordionComponent>

                <!-- --------------------------------------------------------------------------------------------------- -->
                <!-- Business Architecture is responsible for the Enterprise Architecture... -->
                <!-- --------------------------------------------------------------------------------------------------- -->
                <div
                    v-if="store.getInteractionSubmitted('p3_accordion_1').submitted"
                    data-aos="fade"
                    class="row items-center q-pt-md-xl"
                >
                    <div
                        v-if="$q.screen.gt.xs"
                        class="col-4 col-md-2 offset-3 offset-md-1"
                    >
                        <Vue3Lottie
                            :auto-play="store.aosEnable"
                            :pause-animation="!store.aosEnable"
                            class="lottie"
                            :loop="true"
                            :scale="0.7"
                            :animationData="check1"
                        />
                    </div>

                    <div
                        class="col-md-8 q-pa-xl q-my-md q-my-sm-none"
                        :style="
                            $q.screen.gt.sm && locale != 'ar'
                                ? 'background-color: #F0F5F7; margin-left: -3.5rem;'
                                : $q.screen.gt.sm && locale == 'ar'
                                ? 'background-color: #F0F5F7; margin-right: -3.5rem;'
                                : 'background-color: #F0F5F7;'
                        "
                    >
                        <h3
                            v-html="tm('page3.section1.callout1')"
                            class="q-px-md-dxl q-mx-md"
                        ></h3>
                    </div>
                </div>

                <!-- --------------------------------------------------------------------------------------------------- -->
                <!-- Let’s explore each of these Enterprise Taxonomies further. -->
                <!-- --------------------------------------------------------------------------------------------------- -->
                <div
                    v-if="store.getInteractionSubmitted('p3_accordion_1').submitted"
                    class="row justify-center"
                >
                    <div class="col-11 col-md-6 q-pt-xl">
                        <p
                            class="text-secondary text-bold"
                            v-html="t('page3.section1.text9')"
                        ></p>
                    </div>
                </div>

            </div>
        </div>

        <!-- --------------------------------------------------------------------------------------------------- -->
        <!-- Enterprise Process Taxonomy  / This taxonomy describes and organizes Business Processes. -->
        <!-- --------------------------------------------------------------------------------------------------- -->
        <div v-if="store.getInteractionSubmitted('p3_accordion_1').submitted"
            class="container q-pt-sm q-pb-xl"
        >
            <div
                class="row justify-start items-center bg-tax1 q-pb-lg q-pb-sm-xl"
                :style="$q.screen.gt.xs ? 'height: 460px;' : ''"
                data-aos="fade"
            >
                <div class="col-md-11">
                    <div
                        class="row"
                        :class="locale == 'ar' ? 'justify-center' : 'justify-end'"
                    >
                        <div
                            class="col-md-6 q-pa-xl shadow-1"
                            style="background: #f0f5f7; border-radius: 8px; opacity: 0.98"
                        >
                            <h3
                                style="font-size: 32px; line-height: 36px"
                                class="q-pb-lg"
                                v-html="tm('page3.section2.title1')"
                            ></h3>
                            <p v-html="t('page3.section2.text1')"></p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- --------------------------------------------------------------------------------------------------- -->
            <!-- Please note: / A process is a collection of / they receive one or more inputs -->
            <!-- --------------------------------------------------------------------------------------------------- -->
            <div class="row justify-center items-center q-pt-xl">
                <div class="col-10 col-md-5 q-mb-md q-mb-md-none" data-aos="fade-right">
                    <q-img
                        loading="eager"
                        no-transition
                        src="~assets/page3/4.png"
                        :style="$q.screen.gt.xs ? 'width: 85%;' : ''"
                        spinner-color="white"
                    ></q-img>
                </div>
                <div class="col-md-5 q-pl-md-lg" data-aos="fade-left">
                    <p class="q-mb-md" v-html="t('page3.section2.text2')"></p>
                    <ol>
                        <li
                            v-for="(item, index) in tm('page3.section2.list1')"
                            v-html="item"
                            class="q-pb-md last-in-list"
                            :key="index"
                        ></li>
                    </ol>
                </div>

                <div class="col-12 col-md-10 offset-md-1 q-mt-xl" data-aos="fade-left">
                    <p class="q-mb-md" v-html="t('page3.section2.text3')"></p>
                </div>
            </div>
        </div>

        <!-- --------------------------------------------------------------------------------------------------- -->
        <!-- CALLOUT???? - Each Process has a Process Owner who is a Citi executive... -->
        <!-- --------------------------------------------------------------------------------------------------- -->
        <div
            class="container"
            v-if="store.getInteractionSubmitted('p3_accordion_1').submitted"
        >
            <q-separator
                aria-hidden="true"
                :size="$q.screen.gt.sm ? '75px' : '25px'"
                color="transparent"
            ></q-separator>

            <div
                class="row justify-end items-center q-mb-md-xl"
                style="position: relative"
            >
                <div
                    class="col-md-4"
                    :style="
                        $q.screen.gt.xs && locale != 'ar'
                            ? 'position: absolute; left: 2%;'
                            : $q.screen.gt.xs && locale == 'ar'
                            ? 'position: absolute; right: 2%;'
                            : 'position: absolute; top:-50px;'
                    "
                    data-aos="fade-right"
                >
                    <Vue3Lottie
                        :auto-play="store.aosEnable"
                        :pause-animation="!store.aosEnable"
                        class="lottie"
                        :loop="true"
                        :scale="1"
                        :animationData="globe"
                    />
                </div>

                <div
                    class="col-12 col-md-10"
                    style="position: relative"
                    data-aos="fade-left"
                >
                    <div
                        class="shadow-1 q-py-lg q-px-md-xl q-my-lg"
                        style="background: #255be3; border-radius: 7.5px"
                    >
                        <h3
                            v-html="tm('page3.section2.text6')"
                            class="text-white q-pt-lg q-pb-lg q-mx-xl"
                        ></h3>
                    </div>
                </div>
            </div>

            <q-separator
                aria-hidden="true"
                :size="$q.screen.gt.sm ? '75px' : '25px'"
                color="transparent"
            ></q-separator>
        </div>

        <!-- --------------------------------------------------------------------------------------------------- -->
        <!-- You’ll learn more about Process Owners later in this training. -->
        <!-- --------------------------------------------------------------------------------------------------- -->
        <div
            v-if="store.getInteractionSubmitted('p3_accordion_1').submitted"
            class="container"
        >
            <div class="row justify-center">
                <div class="col-11 col-md-10">
                    <p v-html="tm('page3.section2.text7')" class=""></p>
                </div>
            </div>
        </div>

        <!-- --------------------------------------------------------------------------------------------------- -->
        <!-- Enterprise Business Function Taxonomy / For example, the Level 3 Process: Payments Process consists of multiple steps. -->
        <!-- --------------------------------------------------------------------------------------------------- -->
        <div
            v-if="store.getInteractionSubmitted('p3_accordion_1').submitted"
            class="container"
        >
            <q-separator
                aria-hidden="true"
                :size="$q.screen.gt.sm ? '50px' : '25px'"
                class="q-pt-lg"
                color="transparent"
                style="border-bottom: 1px solid #97999b"
            ></q-separator>

            <!-- --------------------------------------------------------------------------------------------------- -->
            <!-- Enterprise Business Function Taxonomy -->
            <!-- --------------------------------------------------------------------------------------------------- -->
            <div
                class="row justify-start items-center bg-tax2 q-pb-lg q-pb-sm-xl"
                :style="$q.screen.gt.xs ? 'height: 460px;' : ''"
                data-aos="fade"
            >
                <div class="col-md-11">
                    <div
                        class="row"
                        :class="locale == 'ar' ? 'justify-center' : 'justify-end'"
                    >
                        <div
                            class="col-md-6 q-pa-lg shadow-1"
                            style="background: #f0f5f7; border-radius: 8px; opacity: 0.98"
                        >
                            <h3
                                v-html="t('page3.section2.title2')"
                                style="font-size: 32px; line-height: 36px"
                                class="q-mb-lg Citi-Sans-Display-Regular"
                            ></h3>
                            <p
                                class="Citi-Sans-Display-Regular q-mb-md"
                                v-html="tm('page3.section2.text8')"
                            ></p>
                            <p
                                class="Citi-Sans-Display-Regular"
                                v-html="t('page3.section2.text9')"
                            ></p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- --------------------------------------------------------------------------------------------------- -->
            <!-- For example, the Level 3 Process: Payments Process consists of multiple steps. -->
            <!-- --------------------------------------------------------------------------------------------------- -->
            <div class="row justify-center items-center q-pt-xl">
                <div
                    class="col-md-5 q-mr-md-xl order-last order-md-first q-mt-md q-mt-md-none"
                    data-aos="fade-right"
                >
                    <p class="q-mb-lg" v-html="t('page3.section2.text10')"></p>
                    <ul>
                        <li
                            v-for="(item, index) in tm('page3.section2.list3')"
                            :key="index"
                            v-html="item"
                            class="q-pb-md last-in-list"
                        ></li>
                    </ul>
                </div>
                <div class="col-10 col-md-5" data-aos="fade-left">
                    <q-img
                        loading="eager"
                        no-transition
                        src="~assets/page3/7.png"
                        :style="$q.screen.gt.xs ? 'width: 85%;' : ''"
                        spinner-color="white"
                    ></q-img>
                </div>
            </div>
        </div>

        <div
            v-if="store.getInteractionSubmitted('p3_accordion_1').submitted"
            class="container q-pb-lg"
        >
            <q-separator
                aria-hidden="true"
                size="20px"
                color="transparent"
                style="border-bottom: 1px solid #97999b"
            ></q-separator>
        </div>

        <!-- --------------------------------------------------------------------------------------------------- -->
        <!-- Enterprise Data Concept Controlled Vocabulary -->
        <!-- --------------------------------------------------------------------------------------------------- -->
        <div
            v-if="store.getInteractionSubmitted('p3_accordion_1').submitted"
            class="container q-pt-lg q-pb-xl"
        >
            <!-- --------------------------------------------------------------------------------------------------- -->
            <!-- Enterprise Data Concept Controlled Vocabulary -->
            <!-- --------------------------------------------------------------------------------------------------- -->
            <div
                class="row justify-start items-center bg-tax3 q-pb-lg q-pb-sm-xl"
                :style="$q.screen.gt.xs ? 'height: 490px;' : ''"
                data-aos="fade"
            >
                <div class="col-md-11">
                    <div
                        class="row"
                        :class="locale == 'ar' ? 'justify-center' : 'justify-end'"
                    >
                        <div
                            class="col-md-6 q-pa-xl shadow-1"
                            style="background: #f0f5f7; border-radius: 8px; opacity: 0.98"
                            data-aos="fade"
                        >
                            <h3
                                style="font-size: 32px; line-height: 36px"
                                class="q-pb-lg Citi-Sans-Display-Regular"
                                v-html="t('page3.section2.title3')"
                            ></h3>
                            <p
                                class="Citi-Sans-Display-Regular"
                                v-html="t('page3.section2.text11')"
                            ></p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- --------------------------------------------------------------------------------------------------- -->
            <!-- A Data Concept is a representation of a collection of data elements... -->
            <!-- --------------------------------------------------------------------------------------------------- -->
            <div class="row justify-center items-center q-pt-xl">
                <div class="col-10 col-md-4 q-mb-md q-mb-md-none" data-aos="fade-right">
                    <q-img
                        loading="eager"
                        no-transition
                        src="~assets/page3/8.png"
                        width="100%"
                        spinner-color="white"
                    ></q-img>
                </div>

                <div class="col-md-5 q-ml-md-lg" data-aos="fade-left">
                    <p class="q-mb-md" v-html="t('page3.section2.text12')"></p>
                    <p class="" v-html="t('page3.section2.text13')"></p>
                </div>
            </div>
            <q-separator
                aria-hidden="true"
                :size="$q.screen.gt.sm ? '75px' : '25px'"
                color="transparent"
            ></q-separator>

            <!-- --------------------------------------------------------------------------------------------------- -->
            <!-- To learn more about the Enterprise... -->
            <!-- --------------------------------------------------------------------------------------------------- -->
            <div class="row justify-center items-center q-py-xl">
                <div
                    class="col-md-5 q-pr-lg-lg order-last order-md-first q-mt-md q-mt-md-none"
                    data-aos="fade-right"
                >
                    <h3 class="q-mb-md" v-html="t('page3.section2.text14')"></h3>
                </div>
                <div class="q-ml-lg-lg col-md-4" data-aos="fade-left">
                    <Vue3Lottie
                        :auto-play="store.aosEnable"
                        :pause-animation="!store.aosEnable"
                        class="lottie"
                        :loop="true"
                        :scale="1"
                        :animationData="graphic1"
                    />
                </div>
            </div>
            <q-separator
                aria-hidden="true"
                :size="$q.screen.gt.sm ? '75px' : '25px'"
                color="transparent"
            ></q-separator>

            <!-- --------------------------------------------------------------------------------------------------- -->
            <!-- Remember, all three of these Enterprise Taxonomies work together to... -->
            <!-- --------------------------------------------------------------------------------------------------- -->
            <div
                data-aos="fade"
                class="row justify-end items-center q-py-lg q-py-sm-xl q-mb-lg-xl"
                style="position: relative"
            >
                <div
                    class="col-md-5"
                    :style="
                        $q.screen.gt.xs && locale != 'ar'
                            ? 'position: absolute; left: 2%;'
                            : $q.screen.gt.xs && locale == 'ar'
                            ? 'position: absolute; right: 2%;'
                            : 'position: absolute; top:-50px;'
                    "
                >
                    <Vue3Lottie
                        :auto-play="store.aosEnable"
                        :pause-animation="!store.aosEnable"
                        class="lottie"
                        :loop="true"
                        :scale="1"
                        :animationData="globe"
                    />
                </div>

                <div
                    class="col-12 col-md-10"
                    style="position: relative"
                    data-aos="fade-left"
                >
                    <div
                        class="shadow-1 q-py-lg q-px-md-xl q-my-md-lg"
                        style="background: #255be3; border-radius: 7.5px"
                    >
                        <h3
                            class="text-white q-pt-lg q-pb-lg q-mx-xl"
                            v-html="t('page3.section2.text15')"
                        ></h3>
                    </div>
                </div>
            </div>
        </div>

        <!-- --------------------------------------------------------------------------------------------------- -->
        <!-- continue -->
        <!-- --------------------------------------------------------------------------------------------------- -->
        <div
            v-if="store.getInteractionSubmitted('p3_accordion_1').submitted"
            class="q-py-none q-pb-lg-xl bg-footer"
        >
            <div class="q-pb-xl container row justify-center">
                <div class="col-12 col-lg text-center q-pb-xl q-mb-xl q-mb-xl-lg">
                    <h3 class="q-mb-lg" v-html="t('page3.footer.text1')"></h3>
                    <q-btn
                        @click="continueClicked()"
                        :label="t('ui.continue')"
                        style="border-radius: 7.5px"
                        no-caps
                        outline
                        padding="sm xl"
                        class="btn-fixed-width bg-white"
                        color="secondary"
                    ></q-btn>
                </div>
            </div>
        </div>
    </q-page>
</template>

<script>
import { defineComponent, ref, onMounted } from "vue";
import { useQuasar } from "quasar";
import { useI18n } from "vue-i18n";
import { Vue3Lottie } from "vue3-lottie";
import { useStore } from "stores/store";
import { useCore } from "stores/composables/core";
import { useAos } from "stores/composables/aos";
import AccordionComponent from "components/AccordionComponent.vue";
import graphic1 from "../assets/global/graphic_callout1.json";
import check1 from "../assets/global/graphic_1_1_v3.json";
import check2 from "../assets/global/graphic2.json";
import globe from "../assets/global/graphic_globe.json";
export default defineComponent({
    name: "PageThree",

    components: {
        AccordionComponent,
        Vue3Lottie,
    },

    setup() {
        const store = useStore();
        const core = { ...useCore() };
        const aos = { ...useAos() };

        const { locale } = useI18n({ useScope: "global" });
        const { t, tm } = useI18n();
        const $q = useQuasar();

        const openTranscript = ref(false);
        function getImageUrl() {
            // This path must be correct for your file
            // return new URL(`../assets/page3/6_${locale.value}.svg`, import.meta.url);
            return new URL(
                `../assets/page3/6_${locale.value}.svg`,
                import.meta.url
            ).toString();
        }
        const continueClicked = () => {
            core.checkNetwork(() => continueClickedCallback());
        };
        const continueClickedCallback = () => {
            core.completePageAndRoute("page3", "/page4");
        };

        onMounted(() => {
            console.log("***------ PageThree - onMounted ------***");
            store.rte_load("page3");

            core.setFocusById("a11yIntro", 400);
            aos.refreshAOS();
        });

        return {
            icon: ref(false),
            bar: ref(false),
            bar2: ref(false),
            toolbar: ref(false),
            store,
            check1,
            check2,
            graphic1,
            getImageUrl,
            ...useCore(),
            ...useAos(),
            locale,
            t,
            tm,
            animations: ref(false),
            openTranscript,
            continueClicked,
            globe,
        };
    },
});
</script>

<style scoped lang="scss">
@import "../css/quasar.variables.scss";

/* for parallax containers, couldn't do inline src background tag? using classes instead */

.bg-footer {
    background-image: url("../assets/page1/footer.svg");
    background-position: bottom center;
    background-repeat: no-repeat;
    background-size: auto;
}

.bg-lorenzo {
    background-image: url("../assets/page3/bg_lorenzo.png");
    background-position: 44% center;
    background-repeat: no-repeat;
    background-size: auto;
}

body.screen--xs {
    .bg-lorenzo {
        background-image: none;
    }

    .bg-tax1 {
        background-image: none;
    }

    .bg-tax2 {
        background-image: none;
    }

    .bg-tax3 {
        background-image: none;
    }
}

.bg-tax1 {
    background-image: url("../assets/page3/bg_tax1.png");
    background-position: 44% center;
    background-repeat: no-repeat;
    background-size: auto;
}

.bg-tax2 {
    background-image: url("../assets/page3/bg_tax2.png");
    background-position: 44% center;
    background-repeat: no-repeat;
    background-size: auto;
}

.bg-tax3 {
    background-image: url("../assets/page3/bg_tax3.png");
    background-position: 44% center;
    background-repeat: no-repeat;
    background-size: auto;
}

.bg-world {
    background-image: url("../assets/page1/3.svg");
    background-position: 3% center;
    background-repeat: no-repeat;
    background-size: contain;
}

.li-style {
    list-style-image: url("../assets/global/licheck.svg");
}
</style>
