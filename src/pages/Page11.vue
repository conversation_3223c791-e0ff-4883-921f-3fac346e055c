<template>
    <transition
        mode="out-in"
        appear
        enter-active-class="animated fadeIn slower"
        leave-active-class="animated fadeOut slower"
    >
        <q-page v-if="pageReady" class="overflow-hidden">


            <!-- A11y INTRO -->
            <div class="sr-only q-py-md">
                <p
                    class="q-mb-md"
                    v-html="t('ui.pageLoaded')"
                    id="a11yIntro"
                    style="outline: 0"
                    tabindex="0"
                ></p>
                <p
                    v-html="
                        (store.getPagesCompleted ? store.getPagesCompleted : 0) +
                        $t('ui.of') +
                        store.manifest.content.length +
                        t('ui.pagesCompleted')
                    "
                ></p>
            </div>


            <!-- --------------------------------------------------------------------------------------------------- -->
            <!-- Top Section -->
            <!-- --------------------------------------------------------------------------------------------------- -->
            <div class="bg-11_1" data-aos="zoom-out">
                <div class="container">
                    <div
                        class="row justify-center items-center q-py-lg"
                        style="min-height: 299px"
                    >
                        <div class="col-8">
                            <h1
                                class="text-center Citi-Sans-Display-Regular"
                                v-html="t('page11.topSection.title')"
                            ></h1>
                        </div>
                    </div>
                </div>
            </div>


            <!-- --------------------------------------------------------------------------------------------------- -->
            <!-- Assessmet if not passed the test-out -->
            <!-- --------------------------------------------------------------------------------------------------- -->
                <!-- v-if="!store.  getInteractionSubmitted('page1_testout_end').submitted" -->
            <AssessmentFinal
                v-if="!store.getInteractionSubmitted('page1_testout_end').submitted"
                :pagesToCompleteWhenPassed="[
                    'page1',
                    'page2',
                    'page3',
                    'page4',
                    'page5',
                    'page6',
                    'page7',
                    'page8',
                    'page11'
                ]"
                :assessmentQuestions="tm('assessmentFinal.questionsSection')"
            ></AssessmentFinal>


            <!-- --------------------------------------------------------------------------------------------------- -->
            <!-- Content if passed the test-out  -->
            <!-- --------------------------------------------------------------------------------------------------- -->
                <!-- v-if="store.getInteractionSubmitted('page1_testout_end').submitted" -->
            <section
                v-if="store.getInteractionSubmitted('page1_testout_end').submitted"
            >
                <div class="bg-passed-assessment">
                    <div class="container">
                        <div class="row items-center" style="min-height: 496px">
                            <div class="offset-md-5 col-md-6" data-aos="zoom-in">
                                <h3
                                    class="q-mb-lg text-white"
                                    v-html="t('page11.topSection.text1')"
                                ></h3>
                                <h4
                                    class="q-mb-lg text-white Citi-Sans-Display-Regular"
                                    v-html="t('page11.topSection.text2')"
                                ></h4>
                            </div>
                        </div>
                    </div>
                </div>
            </section>


        </q-page>
    </transition>
</template>

<script>
import { defineComponent, ref, onMounted } from "vue";
import { useQuasar } from "quasar";
import { useI18n } from "vue-i18n";

import { useStore } from "stores/store";
import { useCore } from "stores/composables/core";
import { useAos } from "stores/composables/aos";

import AssessmentFinal from "components/AssessmentFinal.vue";

export default defineComponent({
    name: "PageEleven",

    components: {
        AssessmentFinal,
    },

    setup() {
        const store = useStore();
        const core = { ...useCore() };
        const aos = { ...useAos() };

        const { locale } = useI18n({ useScope: "global" });
        const { t, tm } = useI18n();
        const $q = useQuasar();

        const pageReady = ref(false);

        const continueClicked = () => {
            core.checkNetwork(() => continueClickedCallback());
        };
        const continueClickedCallback = () => {
            core.completePageAndRoute("page2", "/page1");
        };

        onMounted(() => {
            console.log("***------ PageEleven - onMounted ------***");
            store.rte_load("page11");

            // this is needed to refresh AOS and make it work properly
            setTimeout(() => {
                pageReady.value = true;
                aos.refreshAOS();
            }, 100);

            // set focus to the a11yIntro
            core.setFocusById("a11yIntro", 400);
        });

        return {
            pageReady,

            store,
            ...useCore(),
            ...useAos(),
            locale,
            t,
            tm,

            continueClicked,
        };
    },
});
</script>

<style lang="scss">
@import "../css/quasar.variables.scss";

.bg-11_1 {
    background-image: url("../assets/global/assessmentbg.svg");
    background-position: 5%, 100%;
    background-repeat: no-repeat;
    background-size: auto;
}

.bg-11_1:lang(ar) {
    background-image: url("../assets/global/assessmentbg.svg");
    background-position: 100%, 100%;
    background-repeat: no-repeat;
    background-size: auto;
}

@media (max-width: 600px) {
    .bg-11_1 {
        background-image: url("../assets/global/assessmentbgm.svg");
        background-position: 20%, 50%;
        background-repeat: no-repeat;
        background-size: auto;
    }
    .bg-11_1:lang(ar) {
        background-image: url("../assets/global/assessmentbgm.svg");
        background-position: center center;
        background-repeat: no-repeat;
        background-size: auto;
    }
}

@media (max-width: 1024px) {
    .bg-11_1 {
        background-image: url("../assets/global/assessmentbgm.svg");
        background-position: 20%, 50%;
        background-repeat: no-repeat;
        background-size: auto;
    }
    .bg-11_1:lang(ar) {
        background-image: url("../assets/global/assessmentbgm.svg");
        background-position: center center;
        background-repeat: no-repeat;
        background-size: auto;
    }
}

.bg-passed-assessment {
    background-position: center;
    background-image: url("../assets/page11/2_2.jpg");
    background-repeat: no-repeat;
    background-size: cover;

    &:lang(ar) {
        background-image: url("../assets/page11/2_2_rtl.jpg");
    }
}
</style>
