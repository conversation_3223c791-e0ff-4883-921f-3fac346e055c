<template>
    <q-page class="overflow-hidden">

        <!-- A11y INTRO -->
        <div class="sr-only q-py-md">
            <p class="q-mb-md" v-html="t('ui.pageLoaded')" id="a11yIntro" style="outline: 0;" tabindex="0"></p>
            <p
                v-html="(store.getPagesCompleted ? store.getPagesCompleted : 0) + $t('ui.of') + (store.manifest.content.length) + t('ui.pagesCompleted')">
            </p>
        </div>

        <!-- --------------------------------------------------------------------------------------------------- -->
        <!-- Who Are Process Owners? / Now that... / This would...-->
        <!-- --------------------------------------------------------------------------------------------------- -->
        <div class="container q-pb-xl" data-aos="zoom-out">

            <!-- Who Are Process Owners? -->
            <div class="row  justify-start">
                <div class="col-lg-7 q-pt-xl offset-md-1" >
                    <h1 class="q-mb-lg q-pt-lg q-mt-md" v-html="t('page4.sectionTop.title1')"></h1>
                </div>
            </div>

            <div data-aos="fade" class="row items-center justify-start q-pb-md">
                <div v-if="$q.screen.gt.sm" class="col-4 col-md-2 offset-4 offset-md-1" data-aos="fade-right">
                    <q-img loading="eager" no-transition src="~assets/page4/1.svg" :width="$q.screen.gt.sm ? '75%': '50%'" spinner-color="white"></q-img>
                </div>

                <!-- --------------------------------------------------------------------------------------------------- -->
                <!-- Now that we have a defined set of Business Processes, who provides oversight and uses the... -->
                <!-- --------------------------------------------------------------------------------------------------- -->
                <div class="col-md-8  q-pa-xl q-mt-lg q-mt-sm-none" :style= "$q.screen.gt.sm && locale != 'ar' ? 'background-color: #F0F5F7; margin-left: -4.5rem;' : $q.screen.gt.sm && locale == 'ar' ? 'background-color: #F0F5F7; margin-right: -4.5rem;' :'background-color: #F0F5F7;'">
                    <h3 class="q-px-lg-xl q-mx-md text-bold" v-html="t('page4.section1.text1')"></h3>
                </div>

                <div class="col-md-3"></div>

                <div  data-aos="flip-down" :class="locale == 'ar' ? 'q-mt-md-md col-md-8' : 'col-md-8 q-mt-md-xl'" style="position: relative;">
                    <div  :style="$q.screen.gt.sm && !$q.screen.lg ? 'border-left: 1px solid black; position: absolute; height: 54px; top: -54px; left: 38%;' : $q.screen.lg ? 'border-left: 1px solid black; position: absolute; height: 48.5px; top: -48.5px; left: 38%;' : 'border-left: 1px solid black; position: absolute; height: 48px; left: 165px;'">
                    </div>
                </div>

                <div :class="locale == 'ar' ? 'col-md-2' : 'col-md-3'"></div>

                <!-- --------------------------------------------------------------------------------------------------- -->
                <!-- This would be Process Owners. -->
                <!-- --------------------------------------------------------------------------------------------------- -->
                <div data-aos="fade-up" class=" q-mt-xl q-mt-md-none col-md-8 q-pa-xl" :style="$q.screen.gt.sm ? 'background-color: #F0F5F7; margin-left: -4.5rem;' : 'background-color: #F0F5F7'">
                    <p class="q-pb-md" v-html="t('page4.section1.text2')"></p>
                    <p v-html="t('page4.section1.text3')"></p>
                </div>

            </div>

        </div>

        <!-- --------------------------------------------------------------------------------------------------- -->
        <!-- Process Owner Responsibilities -->
        <!-- --------------------------------------------------------------------------------------------------- -->
        <div style="background-color: #F7FAFB;" class="q-py-md q-mb-xl">
            <div class="container q-py-xl">
                <div class="row justify-start items-center q-pt-md-xl">


                    <div class="col-md-10 offset-md-1 col-lg-8 q-mb-md q-mb-md-none q-pb-lg q-pb-sm-none" data-aos="fade-right">
                        <h2 class="q-mb-md" v-html="t('page4.section1.text4')"></h2>
                        <p v-html="t('page4.section1.text5')"></p>
                    </div>


                    <!-- <div class="col-12 col-md-6 q-mb-lg q-mt-md-none " data-aos="fade-left">
                        <q-img loading="eager" no-transition :src="getImageUrl(0)" :style="$q.screen.gt.xs ? 'width: 95%;' : 'width:100%'"
                            spinner-color="white"></q-img>

                        <p class="sr-only" v-html="t('page4.section1.text5AltText')"></p>
                    </div> -->
                </div>


                <!-- ---------------------------------------------------------------------------------------- -->
                <!-- Process Owners are responsible for: -->
                <!-- ---------------------------------------------------------------------------------------- -->
                <div class="row justify-center items-center q-pt-md-xl q-mt-xl">


                    <div class="col-12 col-md-5 q-mb-lg q-mt-md q-mt-md-none" :style="$q.screen.lt.md ? 'display: flex; justify-content: center' : ''" data-aos="fade-right">
                        <q-img :src="getImageUrl(2)"
                            loading="eager"
                            no-transition
                            :style="$q.screen.gt.xs ? 'width: 80%;' : 'width:90%'"
                            :alt="t('page4.section1.text7AltText')"
                            spinner-color="white"
                        ></q-img>
                    </div>

                    <div class="col-md-5 order-first order-md-last" data-aos="fade-left">
                        <p class="q-mb-md" v-html="t('page4.section1.text7')"></p>
                        <ul>
                            <div v-for="(item,index) in tm('page4.section1.list1')" :key="index">
                                <li v-if="typeof item === 'string'" v-html="item" class="q-py-sm q-my-sm "></li>
                                <ul class="" v-if="typeof item === 'object'">
                                    <li v-for="(item,index) in item" :key="index" v-html="item" class="q-pb-sm last-in-list"></li>
                                </ul>
                            </div>
                        </ul>
                    </div>
                </div>


            </div>
        </div>


        <!-- --------------------------------------------------------------------------------------------------- -->
        <!-- To learn more about Process Ownership, review the... -->
        <!-- --------------------------------------------------------------------------------------------------- -->
        <div class="container  q-pb-xl">

            <div class="row justify-center items-center q-pt-md">

                <div class="col-md-5"  data-aos="fade-down">
                    <h3 class="q-mb-md" v-html="t('page4.section1.text8')"></h3>
                </div>

                <div class="q-ml-lg-lg col-10 col-md-5 q-pt-lg q-pt-md-none" data-aos="fade-up">
                    <q-img loading="eager" no-transition src="~assets/page4/3.png" :style="$q.screen.gt.xs ? 'width: 85%;' : ''"
                        spinner-color="white"></q-img>
                </div>

            </div>

        </div>



        <!-- --------------------------------------------------------------------------------------------------- -->
        <!-- Process Ownership Assessment (POA) and Process Risk Assessment Score. -->
        <!-- --------------------------------------------------------------------------------------------------- -->
        <div class="container">

            <q-separator
                aria-hidden="true"
                :size="$q.screen.gt.sm ? '75px' : '25px'"
                color="transparent"
            ></q-separator>

            <div class="row justify-center items-center q-mb-md-xl" style="position: relative" >
                <div class="col-md-4"
                    :style="
                        $q.screen.gt.xs && locale != 'ar'
                            ? 'position: absolute; left: 2%;'
                            : $q.screen.gt.xs && locale == 'ar'
                            ? 'position: absolute; right: 2%;'
                            : 'position: absolute; top:-50px;'
                    "
                    data-aos="fade-right"
                >
                    <Vue3Lottie :auto-play="store.aosEnable"
                        :pause-animation="!store.aosEnable"
                        class="lottie"
                        :loop="true"
                        :scale="1"
                        :animationData="globe"
                    />
                </div>

                <div class="col-12 col-md-10 col-lg-8" style="position: relative" data-aos="fade-left" >
                    <div
                        class="shadow-1 q-py-lg q-py-md-xl q-px-lg q-px-md-xl q-my-lg"
                        style="background: #255be3; border-radius: 7.5px"
                    >
                        <h3 class="text-white Citi-Sans-Display-Regular text-bold q-mb-md" v-html="t('page4.section_poa.title')"></h3>
                        <p class="text-white Citi-Sans-Display-Regular q-mb-md" v-html="t('page4.section_poa.paragraphs[0]')"></p>
                        <p class="text-white Citi-Sans-Display-Regular" v-html="t('page4.section_poa.paragraphs[1]')"></p>
                    </div>
                </div>
            </div>

            <q-separator
                aria-hidden="true"
                :size="$q.screen.gt.sm ? '75px' : '25px'"
                color="transparent"
            ></q-separator>

        </div>

        <!-- --------------------------------------------------------------------------------------------------- -->
        <!-- continue -->
        <!-- --------------------------------------------------------------------------------------------------- -->
        <div class="q-py-none q-pb-lg-xl bg-footer">
            <div class="q-pt-lg q-pb-xl container row justify-center">
                <div class="col-12 col-lg text-center q-pb-xl q-mb-xl q-mb-xl-lg">
                    <h3 class="q-mb-lg" v-html="tm('page4.footer.text1')"></h3>
                    <q-btn @click="continueClicked()" :label="t('ui.continue')" style="border-radius: 7.5px;"
                        no-caps outline padding="sm xl" class="btn-fixed-width bg-white" color="secondary"></q-btn>
                </div>
            </div>
        </div>


    </q-page>

</template>

<script>
    import { defineComponent, ref, onMounted } from 'vue'
    import { useQuasar } from 'quasar'
    import { useI18n } from 'vue-i18n'
    import { useStore } from 'stores/store';
    import { useCore } from "stores/composables/core";
    import { useAos } from "stores/composables/aos";

    import { Vue3Lottie } from "vue3-lottie";
    import graphic1 from '../assets/global/graphic_callout1.json'
    import check1 from '../assets/global/graphic_1_1_v3.json'
    import globe from "../assets/global/graphic_globe.json";

    export default defineComponent({
        name: 'PageFour',

        components: {
            Vue3Lottie,
        },

        setup() {
            const store = useStore();
            const core = { ...useCore() };
            const aos = { ...useAos() };

            const { locale } = useI18n({ useScope: 'global' })
            const { t, tm } = useI18n();
            const $q = useQuasar();

            const openTranscript = ref(false);
            function getImageUrl(img) {
                // This path must be correct for your file
                return (new URL(`../assets/page4/${img}_${locale.value}.png`, import.meta.url)).toString();
            }
            const continueClicked = () => {
                core.checkNetwork(() => continueClickedCallback());
            };
            const continueClickedCallback = () => {
                core.completePageAndRoute('page4', '/page5');
            };

            onMounted(() => {

                console.log("***------ PageFour - onMounted ------***");
                store.rte_load("page4");

                core.setFocusById("a11yIntro", 400);
                aos.refreshAOS();
            });

            return {
                icon: ref(false),
                bar: ref(false),
                bar2: ref(false),
                toolbar: ref(false),
                store,
                check1,
                graphic1,
                globe,
                ...useCore(),
                ...useAos(),
                locale, t, tm,
                animations: ref(false),
                openTranscript,
                continueClicked,
                getImageUrl
            }
        }

    })
</script>




<style scoped lang="scss">
    @import '../css/quasar.variables.scss';

    /* for parallax containers, couldn't do inline src background tag? using classes instead */

    .bg-footer {
        background-image: url("../assets/page1/footer.svg");
        background-position: bottom center;
        background-repeat: no-repeat;
        background-size: auto;
    }

    .bg-lorenzo {
        background-image: url("../assets/page3/bg_lorenzo.png");
        background-position: 44% center;
        background-repeat: no-repeat;
        background-size: auto;
    }

    .bg-tax1 {
        background-image: url("../assets/page3/bg_tax1.png");
        background-position: 44% center;
        background-repeat: no-repeat;
        background-size: auto;
    }

    .bg-tax2 {
        background-image: url("../assets/page3/bg_tax2.png");
        background-position: 44% center;
        background-repeat: no-repeat;
        background-size: auto;
    }

    .bg-tax3 {
        background-image: url("../assets/page3/bg_tax3.png");
        background-position: 44% center;
        background-repeat: no-repeat;
        background-size: auto;
    }

    .bg-world {
        background-image: url("../assets/page1/3.svg");
        background-position: 3% center;
        background-repeat: no-repeat;
        background-size: contain;
    }

    .li-style {
        list-style-image: url("../assets/global/licheck.svg");
        ;
    }
</style>
