<template>
    <q-page class="overflow-hidden">


        <!-- A11y INTRO -->
        <div class="sr-only q-py-md">
            <p
                class="q-mb-md"
                v-html="t('ui.pageLoaded')"
                id="a11yIntro"
                style="outline: 0"
                tabindex="0"
            ></p>
            <p
                v-html="
                    (store.getPagesCompleted ? store.getPagesCompleted : 0) +
                    $t('ui.of') +
                    store.manifest.content.length +
                    t('ui.pagesCompleted')
                "
            ></p>
        </div>

        <!-- --------------------------------------------------------------------------------------------------- -->
        <!-- Foundation of Enterprise Architecture: Process & Function Taxonomies -->
        <!-- --------------------------------------------------------------------------------------------------- -->
        <div class="container q-pb-xl q-mb-lg" data-aos="zoom-out">
            <div class="row justify-start">
                <div class="col-11 col-md-10 col-lg-6 q-pt-xl offset-md-1">
                    <h1 class="q-mb-lg q-pt-lg q-mt-md" v-html="t('page2.sectionTop.title1')" ></h1>
                    <p class="q-pb-md" v-html="t('page2.section1.text1')"></p>
                    <p v-html="tm('page2.section1.text2')"></p>
                </div>
            </div>
        </div>


        <!-- --------------------------------------------------------------------------------------------------- -->
        <!-- Parallax -->
        <!-- --------------------------------------------------------------------------------------------------- -->
        <div
            v-for="(item, index) in tm('page2.section1.list1')"
            :key="index"
            :style="
                $q.screen.gt.md
                    ? 'background-image: linear-gradient(to right, #255BE3 100%, transparent 0%)'
                    : 'background:#255BE3; margin-bottom:-0.5rem'
            "
        >
            <div
                class=""
                :class="$q.screen.lt.md ? 'bg-secondary' : `bg-para${index + 1}`"
            >
                <div class="container">
                    <div class="row">
                        <div
                            class="col-12 col-md-4 offset-lg-1"
                            :style="
                                $q.screen.xl
                                    ? 'padding: 20em 0em 20em 0em'
                                    : $q.screen.lg
                                    ? 'padding: 15em 0em 15em 0em'
                                    : $q.screen.md
                                    ? 'padding: 10em 0em 10em 0em'
                                    : 'padding: 5em 2em 5em 2em'
                            "
                        >
                            <h3
                                class="text-white"
                                :class="locale == 'ar' ? 'q-pr-md' : null"
                                data-aos="fade-up"
                                v-html="item"
                            ></h3>

                        </div>
                        <div class="p-0 col-12 col-md-7">
                            <!-- <b-img v-if="$q.platform.is.mobile" aria-hidden="true" :src="require(`@/${item.img}`)"
                        data-aos="zoom-in" fluid class="d-none d-md-block"> </b-img> -->
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <q-separator v-if="$q.screen.gt.sm" aria-hidden="true" size="100px" color="transparent" ></q-separator>


        <!-- --------------------------------------------------------------------------------------------------- -->
        <!-- Enterprise Architecture and Process Governance -->
        <!-- --------------------------------------------------------------------------------------------------- -->
        <div class="container q-mt-xl q-mt-sm-none q-py-lg q-py-md-none">
            <div
                data-aos="fade"
                class="row justify-end items-center q-py-sm-xl q-mt-lg q-mt-md-none q-mb-md-xl"
                style="position: relative"
            >
                <div
                    class="col-md-4"
                    :style="
                        $q.screen.gt.xs && locale != 'ar'
                            ? 'position: absolute; left: 2%;'
                            : $q.screen.gt.xs && locale == 'ar'
                            ? 'position: absolute; right: 2%;'
                            : 'position: absolute; top:-50px;'
                    "
                >
                    <Vue3Lottie
                        :auto-play="store.aosEnable"
                        :pause-animation="!store.aosEnable"
                        class="lottie"
                        :loop="true"
                        :scale="1"
                        :animationData="globe"
                    />
                </div>

                <div class="col-12 col-md-10" style="position: relative">
                    <div
                        class="shadow-1 q-py-lg q-px-md-xl q-my-lg"
                        style="background: #255be3; border-radius: 7.5px"
                    >
                        <h3
                            class="text-white q-pa-lg q-mx-xs"
                            v-html="tm('page2.section1.text3')"
                        ></h3>
                    </div>
                </div>
            </div>
        </div>

        <!-- --------------------------------------------------------------------------------------------------- -->
        <!-- continue -->
        <!-- --------------------------------------------------------------------------------------------------- -->
        <div class="q-py-none q-py-lg-xl bg-footer">
            <div class="q-py-xl container row justify-center">
                <div class="col-12 col-lg text-center q-pb-xl q-mb-xl q-mb-xl-lg">
                    <h3 class="q-mb-lg" v-html="tm('page2.footer.text1')"></h3>
                    <q-btn
                        @click="continueClicked()"
                        :label="t('page2.footer.btnText1')"
                        style="border-radius: 7.5px"
                        no-caps
                        outline
                        padding="sm xl"
                        class="btn-fixed-width bg-white"
                        color="secondary"
                    ></q-btn>
                </div>
            </div>
        </div>


    </q-page>
</template>

<script>
import { defineComponent, ref, onMounted } from "vue";
import { useQuasar } from "quasar";
import { useI18n } from "vue-i18n";

import { useStore } from "stores/store";
import { useCore } from "stores/composables/core";
import { useAos } from "stores/composables/aos";
import globe from "../assets/global/graphic_globe.json";
import { Vue3Lottie } from "vue3-lottie";
export default defineComponent({
    name: "PageTwo",

    components: {
        Vue3Lottie,
    },

    setup() {
        const store = useStore();
        const core = { ...useCore() };
        const aos = { ...useAos() };

        const { locale } = useI18n({ useScope: "global" });
        const { t, tm } = useI18n();
        const $q = useQuasar();

        const openTranscript = ref(false);

        const continueClicked = () => {
            core.checkNetwork(() => continueClickedCallback());
        };
        const continueClickedCallback = () => {
            core.completePageAndRoute("page2", "/page3");
        };
        function getImageUrl(index) {
            // This path must be correct for your file
            return new URL(
                `../assets/page2/bg${parseInt(index + 1)}.png`,
                import.meta.url
            );
        }
        onMounted(() => {
            console.log("***------ PageTwo - onMounted ------***");
            store.rte_load("page2");

            core.setFocusById("a11yIntro", 400);
            aos.refreshAOS();
        });

        return {
            store,
            ...useCore(),
            ...useAos(),
            locale,
            t,
            tm,
            animations: ref(false),
            openTranscript,
            continueClicked,
            globe,
            getImageUrl,
        };
    },
});
</script>

<style lang="scss">
@import "../css/quasar.variables.scss";
/* for parallax containers, couldn't do inline src background tag? using classes instead */

.bg-para1:lang(ar) {
    background-image: url("../assets/page2/bg1_ar.png");
    background-position: 50% 80%;
    background-repeat: no-repeat;
    background-size: auto 90%;
    background-attachment: fixed;
}

.bg-para2:lang(ar) {
    background-image: url("../assets/page2/bg2_ar.png");
    background-position: 50% 80%;
    background-repeat: no-repeat;
    background-size: auto 90%;
    background-attachment: fixed;
}

.bg-para3:lang(ar) {
    background-image: url("../assets/page2/bg3_ar.png");
    background-position: 50% 80%;
    background-repeat: no-repeat;
    background-size: auto 90%;
    background-attachment: fixed;
}

.bg-para4:lang(ar) {
    background-image: url("../assets/page2/bg4_ar.png");
    background-position: 50% 80%;
    background-repeat: no-repeat;
    background-size: auto 90%;
    background-attachment: fixed;
}

.bg-para5:lang(ar) {
    background-image: url("../assets/page2/bg5_ar.png");
    background-position: 50% 80%;
    background-repeat: no-repeat;
    background-size: auto 90%;
    background-attachment: fixed;
}

.bg-para1 {
    background-image: url("../assets/page2/bg1.png");
    background-position: 50% 80%;
    background-repeat: no-repeat;
    background-size: auto 90%;
    background-attachment: fixed;

    @media (max-width: $breakpoint-md) {
        background-size: auto 75%;
        background-position: 55% 80%;
    }
}

.bg-para2 {
    background-image: url("../assets/page2/bg2.png");
    background-position: 50% 80%;
    background-repeat: no-repeat;
    background-size: auto 90%;
    background-attachment: fixed;

    @media (max-width: $breakpoint-md) {
        background-size: auto 75%;
        background-position: 55% 80%;
    }
}

.bg-para3 {
    background-image: url("../assets/page2/bg3.png");
    background-position: 50% 80%;
    background-repeat: no-repeat;
    background-size: auto 90%;
    background-attachment: fixed;

    @media (max-width: $breakpoint-md) {
        background-size: auto 75%;
        background-position: 55% 80%;
    }
}

.bg-para4 {
    background-image: url("../assets/page2/bg4.png");
    background-position: 50% 80%;
    background-repeat: no-repeat;
    background-size: auto 90%;
    background-attachment: fixed;

    @media (max-width: $breakpoint-md) {
        background-size: auto 75%;
        background-position: 55% 80%;
    }
}

.bg-para5 {
    background-image: url("../assets/page2/bg5.png");
    background-position: 50% 80%;
    background-repeat: no-repeat;
    background-size: auto 90%;
    background-attachment: fixed;

    @media (max-width: $breakpoint-md) {
        background-size: auto 75%;
        background-position: 55% 80%;
    }
}

.bg-footer {
    background-image: url("../assets/page1/footer.svg");
    background-position: bottom center;
    background-repeat: no-repeat;
    background-size: auto;
}

.bg-world {
    background-image: url("../assets/page1/3.svg");
    background-position: 13% center;
    background-repeat: no-repeat;
    background-size: contain;
}

.li-style {
    list-style-image: url("../assets/global/licheck.svg");
}
</style>
