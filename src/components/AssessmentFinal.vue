<template>
    <section>


        <!-- --------------------------------------------------------------------------------------------------- -->
        <!-- Intro Test Your Knowledge -->
        <!-- --------------------------------------------------------------------------------------------------- -->
        <div class="bg-primary text-white">
            <div class="container">
                <div class="row q-py-xxl justify-center">
                    <div class="col-12 col-lg-6">
                        <p class="Citi-Sans-Text-Regular q-mb-md" v-html="t('assessmentFinal.text1a')"></p>
                        <p
                            class="Citi-Sans-Text-Regular q-mb-md"
                            v-html="t('assessmentFinal.text1b', { numberOfQuestions: selectedQuestions ? selectedQuestions.length : 0 })"
                        ></p>
                        <ListComponent
                            propFirstLevel="p"
                            :propListData="tm('assessmentFinal.list1')"
                            propMarginFirstLevel="1rem"
                            propMarginSecondLevel="0.5rem"
                            propFirstLevelPClass="Citi-Sans-Text-Regular"
                        />
                        <p class="Citi-Sans-Text-Bold" v-html="tm('assessmentFinal.text2')" data-aos="fade-right"></p>
                    </div>
                </div>
            </div>
        </div>


        <!-- --------------------------------------------------------------------------------------------------- -->
        <!-- Testout Questions -->
        <!-- --------------------------------------------------------------------------------------------------- -->
        <section v-if="selectedQuestions">


            <!-- Testout Questions -->
            <div class="bg-grey">
                <q-separator aria-hidden="true" size="60px" color="transparent"></q-separator>

                <div class="container">
                    <div class="row justify-center">
                        <div class="col-lg-11">
                            <template v-for="(item, index) in selectedQuestions" :key="index">
                                <QuestionCard
                                    class="q-mb-xl"
                                    :key="locale + '-' + item + assessmentAttempts"
                                    v-if="index == 0 || store.getInteractionSubmitted(assessmentAttempts + selectedQuestions[index - 1]).submitted"
                                    :questionId="item"
                                    :questionIdAttempt="assessmentAttempts"
                                    :questionsArray="assessmentQuestions"
                                    :alternativeTitle="t('ui.questionTitleTemplate', { a: index + 1, b: selectedQuestions.length })"
                                    :submitButtonText="t('ui.submit')"
                                    defaultColor="secondary"
                                    feedbackType="correct_incorrect"
                                    :headerInsideQuestionCard="false"
                                />
                            </template>
                        </div>
                    </div>
                </div>
            </div>


            <!-- --------------------------------------------------------------------------------------------------- -->
            <!-- Testout results -->
            <!-- --------------------------------------------------------------------------------------------------- -->
            <section v-if="store.getInteractionsCompleted(selectedQuestions, assessmentAttempts)">


                <!-- --------------------------------------------------------------------------------------------------- -->
                <!-- perfect / passed -->
                <!-- --------------------------------------------------------------------------------------------------- -->
                <div v-if="assessmentScore === 100" class="bg-testout_assessment_pass">
                    <div class="container">
                        <div class="row items-center">

                            <div class="col-12 col-md-4 q-py-xl">
                                <q-img loading="eager" no-transition src="assets/global/course_testout_pass.png" spinner-color="white" data-aos="fade-right"></q-img>
                            </div>

                            <div class="q-py-xl q-py-md-xxl col-12 offset-md-1 col-md-7">
                                <h3
                                    class="text-h4 q-mb-lg text-white Citi-Sans-Display-Bold"
                                    v-html="tm('assessmentFinal.finalPassSection.title')"
                                ></h3>
                                <p
                                    class="text-h5 q-mb-lg text-white Citi-Sans-Text-Bold"
                                    v-html="
                                        locale == 'sk' || locale == 'fr' || locale == 'es' || locale == 'cs' || locale == 'de-DE'
                                            ? t('assessmentFinal.finalPassSection.text1') + ' ' + assessmentScore + ' % '
                                            : locale == 'hu'
                                            ? t('assessmentFinal.finalPassSection.text1') + ' ' + assessmentScore + '%, '
                                            : locale == 'tr'
                                            ? t('assessmentFinal.finalPassSection.text1') + ' %' + assessmentScore + ', '
                                            : t('assessmentFinal.finalPassSection.text1') + ' ' + assessmentScore + '% '
                                    "
                                ></p>

                                <p class="text-h5 q-mb-lg text-white Citi-Sans-Text-Regular" v-html="tm('assessmentFinal.finalPassSection.text2')"></p>
                                <p class="text-h5 q-mb-lg text-white Citi-Sans-Text-Regular" v-html="tm('assessmentFinal.finalPassSection.text3')"></p>
                                <p class="text-h5 text-white Citi-Sans-Text-Regular" v-html="tm('assessmentFinal.finalPassSection.text4')"></p>

                            </div>
                        </div>
                    </div>
                </div>


                <!-- --------------------------------------------------------------------------------------------------- -->
                <!-- 80%-99% /passed -->
                <!-- --------------------------------------------------------------------------------------------------- -->
                <div v-if="assessmentScore >= 80 && assessmentScore < 100" class="bg-testout_assessment_pass">
                    <div class="container">
                        <div class="row items-center">

                            <div class="col-12 col-md-4 q-py-xl">
                                <q-img loading="eager" no-transition src="assets/global/course_testout_pass.png" spinner-color="white" data-aos="fade-right"></q-img>
                            </div>

                            <div class="q-py-xl q-py-md-xxl col-12 offset-md-1 col-md-7">
                                <h3
                                    class="text-h4 q-mb-lg text-white Citi-Sans-Display-Bold"
                                    v-html="tm('assessmentFinal.finalPassSection.title')"
                                ></h3>
                                <p
                                    class="text-h5 q-mb-lg text-white Citi-Sans-Text-Bold"
                                    v-html="
                                        locale == 'sk' || locale == 'fr' || locale == 'es' || locale == 'cs' || locale == 'de-DE'
                                            ? t('assessmentFinal.finalPassSection.text1') + ' ' + assessmentScore + ' % '
                                            : locale == 'hu'
                                            ? t('assessmentFinal.finalPassSection.text1') + ' ' + assessmentScore + '%, '
                                            : locale == 'tr'
                                            ? t('assessmentFinal.finalPassSection.text1') + ' %' + assessmentScore + ', '
                                            : t('assessmentFinal.finalPassSection.text1') + ' ' + assessmentScore + '% '
                                    "
                                ></p>

                                <p class="text-h5 q-mb-lg text-white Citi-Sans-Text-Regular" v-html="tm('assessmentFinal.finalPassSection.text2')"></p>
                                <p class="text-h5 q-mb-lg text-white Citi-Sans-Text-Regular" v-html="tm('assessmentFinal.finalPassSection.text3')"></p>
                                <p class="text-h5 text-white Citi-Sans-Text-Regular" v-html="tm('assessmentFinal.finalPassSection.text4')"></p>

                            </div>
                        </div>
                    </div>
                </div>


                <!-- --------------------------------------------------------------------------------------------------- -->
                <!-- list of incorrect questions showing correct/incorrect answers -->
                <!-- --------------------------------------------------------------------------------------------------- -->
                <div v-if="assessmentScore >= 80 && assessmentScore < 100" class="bg-white">
                    <div class="container">
                        <div class="row justify-center q-py-xxl">
                            <div class="col-lg-11">

                                <template v-for="(item, index) in selectedQuestions" :key="index">
                                    <div
                                        class="q-pa-lg q-pa-md-xxl q-mb-xl shadow-3 rounded-borders bg-white"
                                        v-if="store.getInteractionSubmitted(assessmentAttempts + selectedQuestions[index]).result == 'incorrect'"
                                        data-aos="flip-right"
                                    >
                                        <!-- ----------------------------------------------------------- -->
                                        <!-- Title. Ex: Question 1 of XX -->
                                        <!-- ----------------------------------------------------------- -->
                                        <div class="row">
                                            <div class="col-12">
                                                <h5 class="text-h4 q-mb-lg Citi-Sans-Display-Regular" v-html="findQuestionById(item, assessmentQuestions).title"></h5>
                                            </div>
                                        </div>

                                        <div class="row q-col-gutter-lg">
                                            <div class="col-lg-6">
                                                <!-- ----------------------------------------------------------- -->
                                                <!-- Question text -->
                                                <!-- ----------------------------------------------------------- -->
                                                <p class="text-h5 Citi-Sans-Display-Regular q-mb-lg">
                                                    <ListComponent
                                                        propFirstLevel="p"
                                                        :propListData="findQuestionById(item, assessmentQuestions).text"
                                                        propMarginFirstLevel="1rem"
                                                        propMarginSecondLevel="0.5rem"
                                                    />
                                                </p>
                                            </div>

                                            <div class="col-lg-6">
                                                <!-- ----------------------------------------------------------- -->
                                                <!-- Options -->
                                                <!-- ----------------------------------------------------------- -->
                                                <template v-for="(answerOption, index) in findQuestionById(item, assessmentQuestions).options" :key="index">
                                                    <div class="row items-center q-mb-md no-wrap">
                                                        <q-icon
                                                            v-if="findQuestionById(item, assessmentQuestions).correctResponse.includes(answerOption.value)"
                                                            class="text-positive q-mr-sm"
                                                            size="30px"
                                                            name="bi-check-circle"
                                                        ></q-icon>
                                                        <q-icon v-else class="text-negative q-mr-sm" size="30px" name="bi-x-circle"></q-icon>

                                                        <p v-html="answerOption.label"></p>
                                                    </div>
                                                </template>

                                                <!-- feedback -->
                                                <q-separator class="q-my-lg" aria-hidden="true" size="2px" data-aos="zoom-in"></q-separator>
                                                <ListComponent
                                                    propFirstLevel="p"
                                                    :propListData="findQuestionById(item, assessmentQuestions).correctFeedbackText"
                                                    propMarginFirstLevel="1rem"
                                                    propMarginSecondLevel="0.5rem"
                                                />
                                                <q-separator class="q-my-lg" aria-hidden="true" size="2px" data-aos="zoom-in"></q-separator>
                                            </div>
                                        </div>
                                    </div>
                                </template>
                            </div>
                        </div>
                    </div>
                </div>


                <!-- --------------------------------------------------------------------------------------------------- -->
                <!-- failed -->
                <!-- --------------------------------------------------------------------------------------------------- -->
                <div v-if="assessmentScore < 80" class="bg-testout_assessment_fail">
                    <div class="container">
                        <div class="row items-center">
                            <div class="col-12 col-md-4 q-py-xl">
                                <q-img loading="eager" no-transition src="assets/global/course_testout_fail.png" spinner-color="white" data-aos="fade-right"></q-img>
                            </div>

                            <div class="q-py-xl q-py-md-xxl col-12 offset-md-1 col-md-7">
                                <h4 class="q-mb-lg text-white Citi-Sans-Display-Bold" v-html="tm('assessmentFinal.finalFailSection.title')"></h4>
                                <h5
                                    class="q-mb-lg text-white Citi-Sans-Display-Bold"
                                    v-html="
                                        locale == 'sk' || locale == 'fr' || locale == 'es' || locale == 'cs' || locale == 'de-DE'
                                            ? t('assessmentFinal.finalFailSection.text1') + ' ' + assessmentScore + ' % '
                                            : locale == 'hu'
                                            ? t('assessmentFinal.finalFailSection.text1') + ' ' + assessmentScore + '%, '
                                            : locale == 'tr'
                                            ? t('assessmentFinal.finalFailSection.text1') + ' %' + assessmentScore + '. '
                                            : t('assessmentFinal.finalFailSection.text1') + ' ' + assessmentScore + '% '
                                    "
                                ></h5>
                                <p class="q-mb-lg text-white Citi-Sans-Text-Regular" v-html="tm('assessmentFinal.finalFailSection.text2')"></p>
                                <p class="q-mb-lg text-white Citi-Sans-Text-Regular" v-html="tm('assessmentFinal.finalFailSection.text3')"></p>
                                <p class="q-mb-lg text-white Citi-Sans-Text-Regular" v-html="tm('assessmentFinal.finalFailSection.text4')"></p>

                                <q-btn
                                    class="bg-white"
                                    padding="sm xl"
                                    unelevated
                                    no-caps
                                    rounded
                                    outline
                                    color="primary"
                                    :label="t('assessmentFinal.finalFailSection.btnRetry')"
                                    @click="
                                        mountQuestions();
                                        scrollTop();
                                        setFocusById('a11yIntro', 400);
                                        refreshAOS();
                                    "
                                ></q-btn>
                                <q-btn
                                    class="bg-white"
                                    padding="sm xl"
                                    unelevated
                                    no-caps
                                    rounded
                                    outline
                                    color="primary"
                                    :label="t('assessmentFinal.finalFailSection.btnReturn')"
                                    @click="routerTo('/page1')"
                                ></q-btn>
                            </div>
                        </div>
                    </div>
                </div>


            </section>
        </section>


    </section>
</template>

<script>
import { defineComponent, onMounted, ref, computed, watch } from "vue";

import { useI18n } from "vue-i18n";
import { useStore } from "stores/store";
import { useCore } from "stores/composables/core";
import { useAos } from "stores/composables/aos";
import { getRandomArray } from "stores/composables/utils";

import QuestionCard from "components/QuestionCard.vue";
import ListComponent from "components/ListComponent.vue";

export default defineComponent({
    name: "AssessmentFinal",
    components: {
        QuestionCard,
        ListComponent,
    },
    props: {
        pagesToCompleteWhenPassed: {
            type: Array,
        },
        assessmentQuestions: {
            type: Array,
        },
    },

    setup(props) {
        const store = useStore();
        const core = { ...useCore() };
        const aos = { ...useAos() };

        const { locale } = useI18n({ useScope: "global" });
        const { t, tm } = useI18n();

        const selectedQuestions = ref();
        const assessmentAttempts = ref();
        const showAllQuestions = ref(false);

        const assessmentScore = computed(() => {
            return store.getInteractionsScore(selectedQuestions.value, assessmentAttempts.value);
        });

        const passedAndCompleted = computed(() => {
            if (!selectedQuestions.value || !assessmentAttempts.value) {
                return false;
            }

            if (assessmentScore.value >= 80 && store.getInteractionsCompleted(selectedQuestions.value, assessmentAttempts.value)) {
                return true;
            } else {
                return false;
            }
        });

        // check the course if passed and answered all the questions complete all pages and save the course_complete_passed interaction
        watch(passedAndCompleted, (newValue, oldValue) => {
            console.log("passedAndCompleted - newValue: ", newValue);
            console.log("passedAndCompleted - oldValue: ", oldValue);
            if (newValue) {
                props.pagesToCompleteWhenPassed.forEach((element) => {
                    core.completePage(element);
                });

                store.setInteraction("course_complete_passed", null, null, true);
            }
        });

        const mountQuestions = () => {
            // if NOT completed/passed - creates the array
            if (!store.getInteractionSubmitted("course_complete_passed").submitted) {
                // ---------------------------------------------------------------------------------------------------
                // create the ramdom selected array
                // ---------------------------------------------------------------------------------------------------

                // sets the bank to filter the questions based on testout results and number of attempts
                let filterBank;

                // if the learner chooses to take the testout and doesn't pass
                if (store.getInteractionSubmitted("page2_testout_take").submitted && !store.getInteractionSubmitted("page2_testout_end").submitted) {
                    if (Number(store.getInteractionSubmitted("assessment_attempts").learner_response) % 2 == 0) {
                        // filterBank = "bankB";
                        filterBank = "bankA";
                    } else {
                        filterBank = "bankA";
                    }
                } else {
                    if (Number(store.getInteractionSubmitted("assessment_attempts").learner_response) % 2 == 0) {
                        filterBank = "bankA";
                    } else {
                        // filterBank = "bankB";
                        filterBank = "bankA";
                    }
                }

                // ********* ALL QUESTIONS - NO FILTER/ NOT RANDOMIZED
                // const arraySelectedQuestions = props.assessmentQuestions;

                // ********* ALL QUESTIONS - NO FILTER/ RANDOMIZED
                // const arraySelectedQuestions = getRandomArray(props.assessmentQuestions, props.assessmentQuestions.length);

                // ********* FILTERED NOT RANDOMIZED - Uncomment below
                // const arrayFilter = props.assessmentQuestions.filter(item => item.bank === filterBank);
                // const arraySelectedQuestions = arrayFilter;

                // ********* FILTERED RANDOMIZED - Uncomment below
                // const arrayFilter = props.assessmentQuestions.filter(item => item.bank === filterBank);
                // const arraySelectedQuestions  = getRandomArray(arrayFilter, 10);

                const kc_bank1 = props.assessmentQuestions.filter((item) => item.bank === "bankA");
                // const kc_bank2 = props.assessmentQuestions.filter((item) => item.bank === "bankB");
                // const kc_bank3 = props.assessmentQuestions.filter((item) => item.bank === "bankC");

                let concat_array = [];
                {
                    {
                        console.log("test qs --------------------, ", store.testQuestions);
                    }
                }
                if (store.testQuestions) {
                    const kc_bank1_sample = getRandomArray(kc_bank1, 10);
                    // const kc_bank2_sample = getRandomArray(kc_bank2, 1);

                    concat_array = concat_array.concat(kc_bank1_sample);
                } else {
                    const kc_bank1_sample = getRandomArray(kc_bank1, 10);
                    // const kc_bank3_sample = getRandomArray(kc_bank3, 1);

                    concat_array = concat_array.concat(kc_bank1_sample);
                }
                store.testQuestions = !store.testQuestions;
                console.log("concat_array", concat_array);
                const arraySelectedQuestions = getRandomArray(concat_array, 10);
                // ---------------------------------------------------------------------------------------------------

                selectedQuestions.value = arraySelectedQuestions.map((question) => question.id);
                console.log(selectedQuestions.value);

                // save the set of questions to an interaction
                store.setInteraction("assessment_final_questions", selectedQuestions.value.join(","), null, true);

                //save the number of attempts to an interaction
                const assessment_attempts = Number(store.getInteractionSubmitted("assessment_attempts").learner_response) + 1;
                assessmentAttempts.value = assessment_attempts.toString() + "_";
                store.setInteraction("assessment_attempts", assessment_attempts, null, true);
            }

            // if completed/passed - loads the array of the last attempt
            else {
                const previousSelectedQuestions = store.getInteractionSubmitted("assessment_final_questions");
                selectedQuestions.value = previousSelectedQuestions.learner_response.split(",");

                //gets the number of attempts to an interaction
                const assessment_attempts = Number(store.getInteractionSubmitted("assessment_attempts").learner_response);
                assessmentAttempts.value = assessment_attempts.toString() + "_";
            }
        };

        onMounted(() => {
            mountQuestions();
        });

        return {
            store,
            ...useCore(),
            ...useAos(),
            locale,
            t,
            tm,

            assessmentScore,

            selectedQuestions,
            assessmentAttempts,

            mountQuestions,
        };
    },
});
</script>

<style lang="scss">
</style>
