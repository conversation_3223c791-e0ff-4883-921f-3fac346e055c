<template>

    <!-- -------------------------------------------------------------------- -->
    <!-- tombstone-component -->
    <!-- -------------------------------------------------------------------- -->
    <div class="card-container" :ref="interactionId" :id="interactionId">

        <!-- -------------------------------------------------------------------- -->
        <!-- the back card / behind the tombstone - click me -->
        <!-- -------------------------------------------------------------------- -->
        <div class="content-container">
            <transition
                mode="out-in"
                leave-active-class="expand-out"
                enter-active-class="expand-in"
            >

                <q-card
                    @click="runTombstone();"
                    class="flex justify-center no-border-radius"
                    :class="cardBackClass"
                    style="cursor: pointer; height: 100%;"
                    :style="
                        bgBack
                        ? `background-image: url('${bgBack}');`
                        : cardBackStyle
                        ? cardBackStyle
                        : null
                    "
                >
                    <q-card-section class="">
                        <div class="row justify-center">

                            <!-- --------------------------------------- -->
                            <!-- visitedTombstones checkmark -->
                            <!-- --------------------------------------- -->
                            <q-img v-if="visitedTombstones"
                                src="~assets/global/check-neon-green-circle.svg"
                                style="height: 30px; width: 30px; position: absolute; right: .25rem; top: .25rem;"
                                class=""
                                data-aos="flip-left"
                                data-aos-delay="500"
                                data-aos-duration="750"
                                data-aos-once="true"
                            ></q-img>

                            <div class="column col-11 flex-center">
                                <q-icon
                                    class="q-mb-md card-icon"
                                    name="style"
                                    size="56px"
                                    style="cursor: pointer;"
                                />
                            </div>

                            <div class="column col-11">

                                <!-- if there's a background image I put a solid behind the text -->
                                <div :class=" bgBack ? 'q-mt-xs bg-white q-pa-md q-pa-md-lg rounded-borders' : null " >
                                    <h3 class="q-mb-md text-weight-bold" v-html="cardBackTitle"></h3>
                                    <p
                                        v-html="cardBackText"
                                        :class="cardBackTextClass"
                                        :style="cardBackTextStyle"
                                    ></p>
                                </div>

                            </div>
                        </div>
                    </q-card-section>
                </q-card>

            </transition>

        </div>


        <!-- -------------------------------------------------------------------- -->
        <!-- the front tombstone / on top of the card - I animate up -->
        <!-- -------------------------------------------------------------------- -->
        <div class="tombstone-container" :class=" { raised: tombstone } " @click="runTombstone();" >
            <div
                class="row justify-center"
                :class="tombstoneFrontClass"
                style="cursor: pointer; height: 100%;"
                :style="
                    bgFront
                    ? `background-image: url('${bgFront}');`
                    : tombstoneFrontStyle
                    ? tombstoneFrontStyle
                    : null
                "
            >
                <div class="col-10">

                    <q-separator size="25px" color="transparent"></q-separator>

                    <!-- if there's a background image I put a solid behind the text -->
                    <div :class=" bgFront ? 'bg-white q-pa-md q-pa-md-lg rounded-borders' : '' " >
                        <h3 class="q-mb-md text-weight-bold" v-html="tombstoneFrontTitle"></h3>
                        <p
                            v-html="tombstoneFrontText"
                            :class="tombstoneFrontTextClass"
                            :style="tombstoneFrontTextStyle"
                        ></p>
                    </div>

                </div>
            </div>
        </div>


    </div>
</template>

<script>
import { defineComponent, onMounted, ref } from "vue";
import { useStore } from "stores/store";

export default defineComponent({
    name: "TombstonesComponent",
    props: {
        interactionId: {
            type: String,
            default: null,
        },

        iconVisited: {
            type: String,
            default: null
        },
        bgFront: {
            type: String,
            default: null,
        },
        bgBack: {
            type: String,
            default: null,
        },


        // back
        cardBackClass: {
            type: String,
            default: null,
        },
        cardBackStyle: {
            type: String,
            default: null,
        },
        cardBackTitle: {
            type: String,
            default: null,
        },
        cardBackText: {
            type: String,
            default: null,
        },
        cardBackTextClass: {
            type: String,
            default: null,
        },
        cardBackTextStyle: {
            type: Object,
            default: null,
        },

        // front
        tombstoneFrontClass: {
            type: String,
            default: null,
        },
        tombstoneFrontStyle: {
            type: String,
            default: null,
        },
        tombstoneFrontTitle: {
            type: String,
            default: null,
        },
        tombstoneFrontText: {
            type: String,
            default: null,
        },
        tombstoneFrontTextClass: {
            type: String,
            default: null,
        },
        tombstoneFrontTextStyle: {
            type: Object,
            default: null,
        },
    },
    setup(props) {
        const store = useStore();
        const tombstone = ref(false);
        const visitedTombstones = ref(false);

        const runTombstone = () => {
            tombstone.value = !tombstone.value;
            store.setInteraction(props.interactionId, null, null, true);

            visitedTombstones.value = true;
        };

        onMounted(() => {
            tombstone.value = store.getInteractionSubmitted(props.interactionId).submitted;
        });

        return {
            tombstone,
            runTombstone,
            visitedTombstones,
        };
    },
});
</script>

<style>
.card-container {
    position: relative;
    height: 550px;
    overflow: hidden;
    transition: transform 0.3s ease, opacity 0.3s ease, box-shadow 0.3s ease;
}
.card-container:hover {
    transform: scale(.99) rotate(1deg);
    opacity: 1;
    box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
}

.content-container {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

.tombstone-container {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 0; /* Adjust the initial height of the tombstone */
  transition: height 0.5s ease-in-out;
}

.tombstone-container.raised {
  height: 450px;
}

.card-icon {
  transition: transform 0.15s ease;
}
.card-icon:hover {
  transform: scale(1.1);
}
</style>
