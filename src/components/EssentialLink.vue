<template>
    <q-item 
        :disable="propIsDisabled"
        clickable 
        tag="a" 
        :to="'/' + propLink"
        active-class = "text-secondary"
    >
        <q-item-section v-if="propIcon" avatar>
            <q-icon :class="propIconColor" :name="propIcon" />
        </q-item-section>

        <q-item-section>
            <q-item-label aria-hidden="true"><span v-html="propTitle"></span></q-item-label>
            <q-item-label class="sr-only">{{ propA11yTitle }}</q-item-label>
        </q-item-section>
    </q-item>
</template>

<script>
import { defineComponent } from 'vue'

export default defineComponent({
    name: 'EssentialLink',
    props: {
        propTitle: {
            type: String,
            required: true
        },
        propA11yTitle: {
            type: String,
            required: true
        },

        propLink: {
            type: String,
            default: '#'
        },

        propIcon: {
            type: String,
            default: ''
        },

        propIconColor: {
            type: String,
            default: ''
        },

        propIsDisabled: {
            type: Boolean,
            default: false
        }
    }
})
</script>
