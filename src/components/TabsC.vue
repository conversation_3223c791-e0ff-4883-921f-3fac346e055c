<template>
    <div class="q-pa-md">
        <div class="q-gutter-y-md">
            <q-card>
                <q-tabs
                    no-caps
                    dense
                    :align="'justify'"
                    v-model="tab"
                    class="text-grey"
                    active-color="primary"
                    indicator-color="secondary"
                    @update:model-value="checkComplete"
                >
                    <div
                        v-for="(item, index) in contentArray" :key="index"
                        class="q-px-md q-px-sm-none"
                        style="width: 325px;"
                    >
                        <Vue3Lottie
                            class="lottie"
                            :style="
                                locale == 'ar' && !$q.screen.xs
                                    ? 'margin-left:60px'
                                    : $q.screen.xs
                                    ? 'margin-left:10px'
                                    : 'margin-left:8px;'
                            "
                            style="cursor: default"
                            v-if="visited['card_' + index]"
                            :loop="false"
                            :scale="1"
                            width="44.56px"
                            height="44.56px"
                            :animationData="check"
                        />
                        <div
                            v-if="!visited['card_' + index]"
                            style="width: 44.56px; height: 44.56px"
                        ></div>
                        <q-tab
                            class="q-mr-md q-mr-lg-xl"
                            :name="index"
                            :label="$q.screen.xs ? index + 1 : item.title"
                        />
                    </div>
                </q-tabs>

                <q-separator aria-hidden="true" />

                <q-tab-panels v-model="tab" animated>
                    <q-tab-panel
                        class="q-py-xl"
                        :name="index"
                        v-for="(item, index) in contentArray"
                        :key="index"
                    >
                        <h3 class="" v-html="item.title_full"></h3>
                        <ul>
                            <li
                                v-for="(item, index) in item.text"
                                :key="index"
                                v-html="item"
                                class="q-pb-sm last-in-list"
                            ></li>
                        </ul>
                        <q-img
                            loading="eager"
                            no-transition
                            :src="getImageUrl(index)"
                            width="100%"
                            spinner-color="white"
                        ></q-img>

                        <p class="sr-only" v-html="item.alt"></p>
                    </q-tab-panel>
                </q-tab-panels>
            </q-card>
        </div>
    </div>
</template>
<style scoped>
.q-card {
    box-shadow: none;
    border-radius: 0;
}

.q-tab--inactive {
    color: #a4acaf;
}

.q-tab {
    /* text-transform: capitalize; */
    padding-left: 0;
    padding-right: 0;
}
</style>
<script>
import { defineComponent, onMounted, ref } from "vue";
import { useStore } from "stores/store";
import { Vue3Lottie } from "vue3-lottie";
import check from "../assets/page3/modalcheckciti.json";
import check1 from "../assets/global/graphic_1_1_v3.json";
import { useI18n } from "vue-i18n";
export default defineComponent({
    name: "AccordionComponent",
    components: {
        Vue3Lottie,
    },
    data() {
        return {
            check,
            check1,
            tab: ref(0),
        };
    },
    props: {
        interactionId: {
            type: String,
            default: null,
        },
        contentArray: {
            type: Array,
        },
    },
    setup(props) {
        const { locale } = useI18n({ useScope: "global" });
        const store = useStore();
        const visited = {
            card_0: true,
        };

        function getImageUrl(index) {
            const path = new URL(
                `../assets/page5/tab${parseInt(index + 1)}_${locale.value}.svg`,
                import.meta.url
            );
            return `${path}`;
            // This path must be correct for your file
        }
        // check if the passed arrays length matches visited object length
        const checkComplete = (index) => {
            console.log(index);
            visited["card_" + index] = true;
            console.log(visited);
            if (Object.keys(visited).length == props.contentArray.length) {
                if (store.getInteractionSubmitted(props.interactionId).submitted) {
                    return true;
                } else {
                    store.setInteraction(props.interactionId, null, null, true);
                }
            }
        };
        onMounted(() => {});

        return {
            visited,
            checkComplete,
            getImageUrl,
            locale,
        };
    },
});
</script>
