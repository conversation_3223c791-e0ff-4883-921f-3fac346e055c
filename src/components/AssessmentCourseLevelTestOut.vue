<template>
    <section>
        <!--
            ************************ NOTE
            data/interactions for Testout:
            - page#_testout_take - when the learner chooses to take the testout, so the course shows the questions
            - page#_testout_complete_passed - when the learner passed the test and clicked the continue button
            - page#_testout_complete_failed - when the learner failed the test and clicked the continue button
            - page#_testout_complete_skiped - when the learner skiped the test
            - page#_testout_end - automatically saved if the leaner passes the testout
            - page#_selected_questions - set of questions previously selected
        -->

        <!-- --------------------------------------------------------------------------------------------------- -->
        <!-- Intro Test Your Knowledge -->
        <!-- --------------------------------------------------------------------------------------------------- -->
        <div class="bg-large_continue">
            <q-separator aria-hidden="true" size="60px" color="transparent"></q-separator>

            <div class="container">
                <div class="row justify-center items-center">
                    <div class="col-12 col-md-11 q-pa-md-xl q-pa-lg">

                        <div class="row justify-center items-center q-mb-lg q-col-gutter-xl">

                            <div class="col-12 col-lg-auto text-center">
                                <img
                                    aria-hidden="true"
                                    src="assets/global/hat_callout_assessment.svg"
                                    :style="
                                        $q.screen.gt.sm
                                        ? 'width:155px;'
                                        : 'width:105px; padding-top: 1rem;'
                                    "
                                />
                            </div>

                            <div class="col-12 col-lg-8 q-py-md q-px-lg">
                                <ListComponent
                                    propFirstLevel="p"
                                    :propListData="tm('assessment.course_level_test_out.this_course_contains_a_test_out_and_a_final_assessment')"
                                    propMarginFirstLevel="1rem"
                                    propMarginSecondLevel="0.5rem"
                                    propFirstLevelPClass=""
                                />
                            </div>

                        </div>

                        <q-separator aria-hidden="true" size="100px" color="transparent"></q-separator>

                        <!-- Take Test-Out / Skip Test-Out - buttons -->
                        <div class="row justify-center items-center q-col-gutter-md-md q-mb-lg">

                            <!-- Take Test-Out -->
                            <div class="col-12 col-md-6">
                                <q-btn
                                    :disable="store.getInteractionSubmitted(pageId + '_testout_take').submitted"
                                    class="q-mb-sm q-mr-md-lg bg-white full-width"
                                    padding="sm xl"
                                    unelevated
                                    no-caps
                                    rounded
                                    outline
                                    color="primary"
                                    :label="t('assessment.course_level_test_out.btnTakeTestOut')"
                                    @click="
                                        store.setInteraction(pageId + '_testout_take', null, null, true);
                                        scrollDown(600);
                                    "
                                ></q-btn>
                            </div>

                            <!-- Skip Test-Out -->
                            <!-- save the page#_testout_complete_skiped -->
                            <div class="col-12 col-md-6">
                                <q-btn
                                    :disable="store.getInteractionSubmitted(pageId + '_testout_take').submitted || store.getInteractionSubmitted(pageId + '_testout_end').submitted"
                                    class="q-mb-sm bg-white full-width"
                                    padding="sm xl"
                                    unelevated
                                    no-caps
                                    rounded
                                    outline
                                    color="primary"
                                    :label="t('assessment.course_level_test_out.btnSkipTestOut')"
                                    @click="
                                        store.setInteraction(pageId + '_testout_complete_skiped', null, null, true);
                                        scrollTop();
                                        setFocusById('a11yIntro', 400);
                                        refreshAOS();
                                        continueClicked();
                                    "
                                ></q-btn>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- <q-separator
            v-if="!(store.getInteractionSubmitted(pageId + '_testout_take').submitted || store.getInteractionSubmitted(pageId + '_testout_complete_skiped').submitted)"
            aria-hidden="true"
            size="80px"
            color="transparent"
        ></q-separator> -->



        <!-- --------------------------------------------------------------------------------------------------- -->
        <!-- Testout Questions -->
        <!-- --------------------------------------------------------------------------------------------------- -->
        <section v-if="store.getInteractionSubmitted(pageId + '_testout_take').submitted && selectedQuestions">
            <div class="container">
                <div class="row q-py-xl justify-center">
                    <div class="col-12 col-lg-10">
                        <div class="q-pa-lg q-pa-lg-xxl bg-white" data-aos="zoom-out">
                            <h3 class="Citi-Sans-Display-Regular q-mb-xl" v-html="tm('assessment.course_level_test_out.title1')"></h3>
                            <p class="q-mb-md Citi-Sans-Text-Regular" v-html="tm('assessment.course_level_test_out.text1')"></p>
                            <p
                                class="Citi-Sans-Text-Regular q-mb-md text-bold"
                                v-html="t('assessment.course_level_test_out.text2', { numberOfQuestions: selectedQuestions ? selectedQuestions.length : 0 })"
                            ></p>

                            <p class="q-mb-md Citi-Sans-Text-Regular" v-html="tm('assessment.course_level_test_out.text3')"></p>

                            <ListComponent
                                propFirstLevel="ul"
                                :propListData="tm('assessment.course_level_test_out.list1')"
                                propMarginFirstLevel="1rem"
                                propMarginSecondLevel="0.5rem"
                                propFirstLevelPClass="q-mb-md"
                            />

                            <p class="q-mb-md Citi-Sans-Text-Bold" v-html="tm('assessment.course_level_test_out.text4')"></p>
                        </div>
                    </div>
                </div>
            </div>
            <div class="bg-grey">
                <q-separator aria-hidden="true" size="60px" color="transparent"></q-separator>

                <div class="container">
                    <div class="row justify-center">
                        <div class="col-lg-11">
                            <template v-for="(item, index) in selectedQuestions" :key="index">
                                <QuestionCard
                                    class="q-mb-xl"
                                    :key="locale + '-' + item"
                                    v-if="
                                        index == 0 ||
                                        (store.getInteractionsFailedCount(selectedQuestions) == 2 && !hideFailedFeedback
                                            ? store.getInteractionSubmitted(selectedQuestions[index]).submitted
                                            : store.getInteractionSubmitted(selectedQuestions[index - 1]).submitted)
                                    "
                                    :questionId="item"
                                    :questionsArray="testoutQuestions"
                                    :alternativeTitle="t('ui.questionTitleTemplate', { a: index + 1, b: selectedQuestions.length })"
                                    :submitButtonText="t('ui.submit')"
                                    defaultColor="secondary"
                                    feedbackType="correct_incorrect"
                                    :headerInsideQuestionCard="false"
                                />
                            </template>
                        </div>
                    </div>
                </div>
            </div>

            <!-- --------------------------------------------------------------------------------------------------- -->
            <!-- Testout results -->
            <!-- --------------------------------------------------------------------------------------------------- -->
            <section v-if="store.getInteractionsCompleted(selectedQuestions, assessmentAttempts) || store.getInteractionsFailedCount(selectedQuestions) == 2">
                <div
                    v-if="!hideFailedFeedback || store.getInteractionsCompleted(selectedQuestions, assessmentAttempts)"
                    :class="assessmentScore >= 80 ? 'bg-testout_assessment_pass' : 'bg-testout_assessment_fail'"
                >
                    <div class="container q-py-lg">
                        <div class="container q-py-lg">
                            <div class="row items-center">
                                <div class="col-12 col-md-4 q-py-xl">
                                    <q-img
                                        v-if="assessmentScore >= 80"
                                        loading="eager"
                                        no-transition
                                        src="assets/global/course_testout_pass.png"
                                        spinner-color="white"
                                        data-aos="fade-right"
                                    ></q-img>
                                    <q-img
                                        v-else
                                        loading="eager"
                                        no-transition
                                        src="assets/global/course_testout_fail.png"
                                        spinner-color="white"
                                        data-aos="fade-right"
                                    ></q-img>
                                </div>

                                <!-- perfect -->
                                <div v-if="assessmentScore == 100" class="q-py-xl q-py-md-none col-12 offset-md-1 col-md-7">
                                    <p
                                        class="text-h5 q-mb-lg text-white Citi-Sans-Text-Bold"
                                        v-html="
                                            locale == 'sk' || locale == 'fr' || locale == 'es' || locale == 'cs' || locale == 'de-DE'
                                                ? tm('assessment.course_level_test_out.congrats.text1') + ' ' + assessmentScore + ' % '
                                                : locale == 'hu'
                                                ? tm('assessment.course_level_test_out.congrats.text1') + ' ' + assessmentScore + '%, '
                                                : locale == 'tr'
                                                ? tm('assessment.course_level_test_out.congrats.text1') + ' %' + assessmentScore + ', '
                                                : tm('assessment.course_level_test_out.congrats.text1') + ' ' + assessmentScore + '% '
                                        "
                                    ></p>

                                    <p class="q-mb-lg text-white" v-html="tm('assessment.course_level_test_out.congrats.text2')"></p>
                                    <p class="q-mb-lg text-white" v-html="tm('assessment.course_level_test_out.congrats.text3')"></p>
                                    <p class="q-mb-lg text-white" v-html="tm('assessment.course_level_test_out.congrats.text4')"></p>

                                    <div class="row justify-items-between items-center q-col-gutter-xl-md">
                                        <div class="col-12 col-xl-6">
                                            <q-btn
                                                class="q-mb-sm q-mr-md-lg bg-white full-width"
                                                padding="sm xl"
                                                unelevated
                                                no-caps
                                                rounded
                                                outline
                                                color="primary"
                                                :label="t('assessment.course_level_test_out.btnReviewContent')"
                                                @click="
                                                    store.setInteraction(pageId + '_testout_complete_passed', null, null, false);
                                                    continueClicked();
                                                "
                                            ></q-btn>
                                        </div>
                                    </div>
                                </div>

                                <!-- passed -->
                                <div v-if="assessmentScore >= 80 && assessmentScore < 100" class="q-py-xl q-py-md-none col-12 offset-md-1 col-md-7">
                                    <p
                                        class="text-h5 q-mb-lg text-white Citi-Sans-Text-Bold"
                                        v-html="
                                            locale == 'sk' || locale == 'fr' || locale == 'es' || locale == 'cs' || locale == 'de-DE'
                                                ? tm('assessment.course_level_test_out.passed.text1') + ' ' + assessmentScore + ' % '
                                                : locale == 'hu'
                                                ? tm('assessment.course_level_test_out.passed.text1') + ' ' + assessmentScore + '%, '
                                                : locale == 'tr'
                                                ? tm('assessment.course_level_test_out.passed.text1') + ' %' + assessmentScore + ', '
                                                : tm('assessment.course_level_test_out.passed.text1') + ' ' + assessmentScore + '% '
                                        "
                                    ></p>

                                    <p class="q-mb-lg text-white" v-html="tm('assessment.course_level_test_out.passed.text2')"></p>
                                    <p class="q-mb-lg text-white" v-html="tm('assessment.course_level_test_out.passed.text3')"></p>
                                    <p class="q-mb-lg text-white" v-html="tm('assessment.course_level_test_out.passed.text4')"></p>

                                    <div class="row justify-items-between items-center q-col-gutter-xl-md">
                                        <div class="col-12 col-xl-6">
                                            <q-btn
                                                class="q-mb-sm q-mr-md-lg bg-white full-width"
                                                padding="sm xl"
                                                unelevated
                                                no-caps
                                                rounded
                                                outline
                                                color="primary"
                                                :label="t('assessment.course_level_test_out.btnReviewContent')"
                                                @click="
                                                    store.setInteraction(pageId + '_testout_complete_passed', null, null, false);
                                                    continueClicked();
                                                "
                                            ></q-btn>
                                        </div>
                                    </div>

                                    <div class="col-lg-12 q-py-xxl">
                                        <template v-for="(item, index) in selectedQuestions" :key="index">
                                            <div
                                                class="q-pa-lg q-pa-md-xxl q-mb-xl shadow-3 rounded-borders bg-white"
                                                v-if="store.getInteractionSubmitted(selectedQuestions[index]).result == 'incorrect'"
                                                data-aos="flip-right"
                                            >
                                                <!-- ----------------------------------------------------------- -->
                                                <!-- Title. Ex: Question 1 of XX -->
                                                <!-- ----------------------------------------------------------- -->
                                                <div class="row">
                                                    <div class="col-12">
                                                        <h5 class="text-h4 q-mb-lg Citi-Sans-Display-Regular" v-html="findQuestionById(item, testoutQuestions).title"></h5>
                                                    </div>
                                                </div>

                                                <div class="row q-col-gutter-lg">
                                                    <div class="col-lg-6">
                                                        <!-- ----------------------------------------------------------- -->
                                                        <!-- Question text -->
                                                        <!-- ----------------------------------------------------------- -->
                                                        <p class="text-h5 Citi-Sans-Display-Regular q-mb-lg">
                                                            <ListComponent
                                                                propFirstLevel="p"
                                                                :propListData="findQuestionById(item, testoutQuestions).text"
                                                                propMarginFirstLevel="1rem"
                                                                propMarginSecondLevel="0.5rem"
                                                            />
                                                        </p>
                                                    </div>

                                                    <div class="col-lg-6">
                                                        <!-- ----------------------------------------------------------- -->
                                                        <!-- Options -->
                                                        <!-- ----------------------------------------------------------- -->
                                                        <template v-for="(answerOption, index) in findQuestionById(item, testoutQuestions).options" :key="index">
                                                            <div class="row items-center q-mb-md no-wrap">
                                                                <q-icon
                                                                    v-if="findQuestionById(item, testoutQuestions).correctResponse.includes(answerOption.value)"
                                                                    class="text-positive q-mr-sm"
                                                                    size="30px"
                                                                    name="bi-check-circle"
                                                                ></q-icon>
                                                                <q-icon v-else class="text-negative q-mr-sm" size="30px" name="bi-x-circle"></q-icon>

                                                                <p v-html="answerOption.label"></p>
                                                            </div>
                                                        </template>

                                                        <!-- feedback -->
                                                        <q-separator class="q-my-lg" aria-hidden="true" size="2px" data-aos="zoom-in"></q-separator>
                                                        <ListComponent
                                                            propFirstLevel="p"
                                                            :propListData="findQuestionById(item, testoutQuestions).correctFeedbackText"
                                                            propMarginFirstLevel="1rem"
                                                            propMarginSecondLevel="0.5rem"
                                                        />
                                                        <q-separator class="q-my-lg" aria-hidden="true" size="2px" data-aos="zoom-in"></q-separator>
                                                    </div>
                                                </div>
                                            </div>
                                        </template>
                                    </div>
                                </div>

                                <!-- failed -->
                                <div v-else-if="assessmentScore < 60 && store.getInteractionsFailedCount(selectedQuestions) == 2" class="q-py-xl q-py-md-none col-12 offset-md-1 col-md-7">
                                    <div v-if="!hideFailedFeedback">

                                        <p class="q-mb-lg text-white" v-html="tm('assessment.course_level_test_out.failed.text2')"></p>

                                        <q-btn
                                            class="bg-white q-mx-xs"
                                            padding="sm xl"
                                            unelevated
                                            no-caps
                                            rounded
                                            outline
                                            color="primary"
                                            :label="t('assessment.course_level_test_out.btnContinueCourse')"
                                            @click="
                                                store.setInteraction(pageId + '_testout_complete_failed', null, null, false);
                                                scrollTop();
                                                setFocusById('a11yIntro', 400);
                                                refreshAOS();
                                                continueClicked();
                                            "
                                        ></q-btn>
                                        <!-- <q-btn
                                            class="bg-white q-mx-xs"
                                            padding="sm xl"
                                            unelevated
                                            no-caps
                                            rounded
                                            outline
                                            color="primary"
                                            :label="t('assessment.course_level_test_out.btnContinueTest')"
                                            @click="hideFailedFeedback = true"
                                        ></q-btn> -->
                                    </div>
                                </div>
                                <div v-else-if="assessmentScore < 80" class="q-py-xl q-py-md-none col-12 offset-md-1 col-md-7">
                                    <!-- <p
                                        class="text-h5 q-mb-lg text-white Citi-Sans-Text-Bold"
                                        v-html="
                                            locale == 'sk' || locale == 'fr' || locale == 'es' || locale == 'cs' || locale == 'de-DE'
                                                ? t('assessment.course_level_test_out.failed.text1') + ' ' + assessmentScore + ' % '
                                                : locale == 'hu'
                                                ? t('assessment.course_level_test_out.failed.text1') + ' ' + assessmentScore + '%, '
                                                : locale == 'tr'
                                                ? t('assessment.course_level_test_out.failed.text1') + ' %' + assessmentScore + ', '
                                                : t('assessment.course_level_test_out.failed.text1') + ' ' + assessmentScore + '% '
                                        "
                                    ></p> -->
                                    <p class="q-mb-lg text-white" v-html="tm('assessment.course_level_test_out.failed.text2')"></p>
                                    <q-btn
                                        class="bg-white"
                                        padding="sm xl"
                                        unelevated
                                        no-caps
                                        rounded
                                        outline
                                        color="primary"
                                        :label="t('assessment.course_level_test_out.btnContinueCourse')"
                                        @click="
                                            store.setInteraction(pageId + '_testout_complete_failed', null, null, false);
                                            scrollTop();
                                            setFocusById('a11yIntro', 400);
                                            refreshAOS();
                                            continueClicked();
                                        "
                                    ></q-btn>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </section>
        </section>


    </section>
</template>

<script>
import { defineComponent, onMounted, ref, computed, watch } from "vue";

import { useI18n } from "vue-i18n";
import { useStore } from "stores/store";
import { useCore } from "stores/composables/core";
import { useAos } from "stores/composables/aos";
import { getRandomArray } from "stores/composables/utils";

import QuestionCard from "components/QuestionCard.vue";
import ListComponent from "components/ListComponent.vue";

export default defineComponent({
    name: "AssessmentCourseLevelTestOut",
    components: {
        QuestionCard,
        ListComponent,
    },
    props: {
        pageId: {
            type: String,
            default: null,
        },
        nextRoute: {
            type: String,
            default: null,
        },
        pagesToCompleteWhenPassed: {
            type: Array,
        },
        testoutQuestions: {
            type: Array,
        },
        numberQuestions: {
            type: Number,
        },
    },

    setup(props) {
        const store = useStore();
        const core = { ...useCore() };
        const aos = { ...useAos() };

        const { locale } = useI18n({ useScope: "global" });
        const { t, tm } = useI18n();

        const selectedQuestions = ref();
        const assessmentAttempts = ref();
        const hideFailedFeedback = ref(false);

        const assessmentScore = computed(() => {
            return store.getTestoutScore(selectedQuestions.value);
        });

        const passedAndCompleted = computed(() => {
            if (selectedQuestions.value && assessmentScore.value >= 80 && store.getInteractionsCompleted(selectedQuestions.value, assessmentAttempts.value)) {
                return true;
            } else {
                return false;
            }
        });

        watch(passedAndCompleted, (newValue, oldValue) => {
            console.log("passedAndCompleted - newValue: ", newValue);
            console.log("passedAndCompleted - oldValue: ", oldValue);
            if (newValue) {
                props.pagesToCompleteWhenPassed.forEach((element) => {
                    core.completePage(element);
                });

                store.setInteraction(props.pageId + "_testout_end", null, null, false);
            }
        });

        const continueClicked = () => {
            core.checkNetwork(() => continueClickedCallback());
        };
        const continueClickedCallback = () => {
            core.completePageAndRoute(props.pageId, props.nextRoute);
        };

        onMounted(() => {
            if ( store.getInteractionSubmitted(props.pageId + "_testout_take").submitted ) {
                store.setInteraction(props.pageId + '_testout_complete_skiped', null, null, true);
                return
            }
            console.log("Test-out option not selected yet");

            // check if the learner has already started before. If yes it going to load the previous random selected array of question
            const previousSelectedQuestions = store.getInteractionSubmitted(props.pageId + "_selected_questions");
            if (store.getInteractionSubmitted(props.pageId + "_testout_take").submitted && previousSelectedQuestions.submitted) {
                selectedQuestions.value = previousSelectedQuestions.learner_response.split(",");
            } else {
                // ---------------------------------------------------------------------------------------------------
                // create the ramdom selected array
                // ---------------------------------------------------------------------------------------------------

                // ********* ALL QUESTIONS - NO FILTER/ NOT RANDOMIZED
                // const arraySelectedQuestions = props.testoutQuestions;

                // ********* ALL QUESTIONS - NO FILTER/ RANDOMIZED
                // const arraySelectedQuestions = getRandomArray(props.testoutQuestions, props.testoutQuestions.length);

                // ********* FILTERED QUESTIONS / NOT RANDOMIZED
                // const arraySelectedQuestions = props.testoutQuestions.filter(item => item.bank === "bankA");

                // ********* FILTERED QUESTIONS / RANDOMIZED
                // const arrayFilter = props.testoutQuestions.filter((item) => item.bank === props.pageId);
                const arrayFilter = props.testoutQuestions.filter((item) => item.bank === "bankA");
                const arraySelectedQuestions = getRandomArray(arrayFilter, props.numberQuestions);

                // ---------------------------------------------------------------------------------------------------

                // get just the ids
                selectedQuestions.value = arraySelectedQuestions.map((question) => question.id);

                // save it to an interaction
                store.setInteraction(props.pageId + "_selected_questions", selectedQuestions.value.join(","), null, true);
            }
        });

        return {
            store,
            ...useCore(),
            ...useAos(),
            locale,
            t,
            tm,

            selectedQuestions,
            assessmentScore,
            continueClicked,
            assessmentAttempts,
            hideFailedFeedback,
        };
    },
});
</script>

<style lang="scss">
.bg-large_continue {
    background-image: url('../assets/sectioncontinue/large_continue_bg.jpg');
    background-position: bottom center;
    background-repeat: no-repeat;
    background-size: cover;
}
</style>
