<template>
    <div :ref="interactionId" :id="interactionId">
        <q-carousel
            navigation
            swipeable
            animated
            v-model="slide"
            ref="carousel"
            transition-prev="slide-right"
            transition-next="slide-left"
            transition-duration="500"
            :height="$q.screen.lt.md ? '660px' : '580px'"
            class="bg-transparent"
            control-color="secondary"
        >
            <q-carousel-slide
                class="q-pa-xs q-pa-md-md"
                :name="index"
                v-for="(slide, index) in contentArray" :key="index"
            >
                <q-card class="overflow-hidden" :style="$q.screen.lt.md ? 'height:650px' : 'height:560px'">
                    <!-- top iamge -->
                    <img :src="slide.img">

                    <!-- text content -->
                    <q-card-section class="q-pa-lg col-12 flex flex-center">
                        <ListComponent
                            propFirstLevel="p"
                            :propListData="slide.text"
                            propMarginFirstLevel="1rem"
                            propMarginSecondLevel="0.8rem"
                        />
                    </q-card-section>
                </q-card>
            </q-carousel-slide>

            <template v-slot:control>
                <q-carousel-control
                    position="bottom"
                    :offset="[0, 20]"
                    class="row justify-between q-px-xl"
                >
                    <q-btn
                        :disable="slide==0"
                        push
                        round
                        dense
                        color="white"
                        text-color="secondary"
                        :icon="$q.lang.rtl ? 'arrow_right' : 'arrow_left'"
                        @click="$refs.carousel.previous()"
                    ></q-btn>
                    <q-btn
                        :disable="slide == contentArray.length -1"
                        push
                        round
                        dense
                        color="white"
                        text-color="secondary"
                        :icon="$q.lang.rtl ? 'arrow_left' : 'arrow_right'"
                        @click="$refs.carousel.next()"
                    ></q-btn>
                </q-carousel-control>
            </template>
        </q-carousel>
    </div>
</template>

<script>
import { defineComponent, onMounted, ref, watch } from "vue";
import { useStore } from "stores/store";
import { useCore } from "stores/composables/core";
import ListComponent from 'components/ListComponent.vue'

export default defineComponent({
    name: "CarouselComponent",
    props: {
        interactionId: {
            type: String,
            default: null,
        },
        contentArray: {
            type: Array,
        },
    },
    components: {
        ListComponent,
    },
    setup(props) {
        const store = useStore();

        const slide =  ref(0);
        watch(slide, (newValue) => {
            if (newValue == props.contentArray.length-1) {
                store.setInteraction(props.interactionId, null, null, false)
            }
        });

        onMounted(() => {
        });

        return {
            slide,
        };
    },
});
</script>

<style lang="scss">

</style>