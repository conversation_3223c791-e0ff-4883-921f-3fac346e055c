<template>
    <div class="container">
        <div class="row items-stretch q-col-gutter-lg">
            <!-- Desktop Layout (md and up) -->
            <template v-for="(column, index) in orderedColumns" :key="index">
                <div
                    class="col-12 col-md-6"
                    :class="[
                        // Mobile order classes
                        column.type === 'image'
                            ? `order-${imageAlwaysTopOnMobile ? 'first' : 'none'} order-md-none`
                            : null
                    ]"
                    :data-aos="index === 0 ? 'fade-right' : 'fade-left'"
                >
                    <!-- Image Column -->
                    <q-img
                        v-if="column.type === 'image'"
                        :src="imagePath"
                        spinner-color="primary"
                        class="rounded-borders"
                        fit="cover"
                        position="50% 50%"
                        height="100%"
                        style="min-height: 300px"
                    />
                    <!-- Text Column -->
                    <div
                        v-else
                        class="rounded-borders q-px-lg q-px-xl-xxl q-py-xl full-height"
                        :class="backgroundClass"
                    >
                        <h2
                            class="callout Citi-Sans-Display-Bold q-mb-xl"
                            :class="titleClass"
                            v-html="title"
                        ></h2>
                        <ListComponent
                            class="Citi-Sans-Display-Regular"
                            :class="textClass"
                            propFirstLevel="p"
                            :propListData="paragraphs"
                            propMarginFirstLevel="1rem"
                            propMarginSecondLevel="0.5rem"
                            :prop-animation="false"
                        />
                    </div>
                </div>
            </template>
        </div>
    </div>
</template>

<script>
import ListComponent from 'components/ListComponent.vue'

export default {
    name: 'ImageBoxTextBox',
    components: {
        ListComponent
    },
    props: {
        imagePath: {
            type: String,
            required: true
        },
        title: {
            type: String,
            required: true
        },
        paragraphs: {
            type: [Array, String],
            required: true
        },
        textRight: {
            type: Boolean,
            default: true
        },
        imageAlwaysTopOnMobile: {
            type: Boolean,
            default: true
        },
        titleClass: {
            type: String,
            default: 'text-secondary'
        },
        backgroundClass: {
            type: String,
            default: 'bg-primary'
        },
        textClass: {
            type: String,
            default: 'text-white'
        }
    },
    computed: {
        orderedColumns() {
            const columns = [
                { type: 'image' },
                { type: 'text' }
            ];
            return this.textRight ? columns : columns.reverse();
        }
    }
}
</script>

<style scoped>
/* Add any additional styling here if needed */
</style>