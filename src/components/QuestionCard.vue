<template>
    <div
        v-if="question"
        :ref="questionIdAttempt + questionId"
        :id="questionIdAttempt + questionId"
    >
        <!-- ----------------------------------------------------------- -->
        <!-- Title. Ex: Question 1 of XX -->
        <!-- ----------------------------------------------------------- -->
        <div v-if="!headerInsideQuestionCard" class="row">
            <div class="col-12" data-aos="zoom-out">
                <h3
                    class="text-h4 Citi-Sans-Display-Regular"
                    v-html="question.title"
                ></h3>
                <q-separator
                    class="q-mt-md q-mb-lg"
                    aria-hidden="true"
                    size="2px"
                    :color="defaultColor"
                    data-aos="zoom-in"
                ></q-separator>
            </div>
        </div>
        <div
            v-else
            class="row q-col-gutter-xl q-mb-xl items-center justify-center"
            data-aos="zoom-out"
        >
            <div class="col-12 col-lg-6">
                <div class="rounded-borders bg-primary q-px-xl q-pt-xxl q-pb-xl relative-position" >
                    <img
                        aria-hidden="true"
                        src="assets/global/questioncard_icon.svg"
                        style="
                            width: 96px;
                            position: absolute;
                            top: -47px;
                            left: 50%;
                            transform: translatex(-50%);
                        "
                        data-aos="flip-right"
                    />
                    <h4
                        class="Citi-Sans-Display-Bold text-white text-center"
                        v-html="t('assessment.knowledge_check.title1')"
                    ></h4>
                </div>
            </div>
            <div class="col-12 col-lg-6">
                <h5 class="Citi-Sans-Display-Bold" v-html="question.title"></h5>
            </div>
        </div>

        <div class="row q-col-gutter-xl">
            <div class="col-lg-6" data-aos="fade-right">
                <!-- ----------------------------------------------------------- -->
                <!-- Question text -->
                <!-- ----------------------------------------------------------- -->
                <p class="text-h5 Citi-Sans-Display-Regular text-secondary q-mb-lg">
                    <ListComponent
                        propFirstLevel="p"
                        :propListData="question.text"
                        propMarginFirstLevel="1rem"
                        propMarginSecondLevel="0.5rem"
                    />
                </p>

                <!-- ----------------------------------------------------------- -->
                <!-- Instructions text -->
                <!-- ----------------------------------------------------------- -->
                <p
                    class="q-mt-lg Citi-Sans-Text-Bold"
                    v-html="question.instructions"
                ></p>

                <!-- ----------------------------------------------------------- -->
                <!-- just for testing -->
                <!-- ----------------------------------------------------------- -->
                <p
                    v-if="showCorrectAnswer"
                    class="text-negative"
                    v-html="'Just for testing: ' + question.correctResponse"
                ></p>
                <!-- ----------------------------------------------------------- -->
                <!-- just for testing -->
                <!-- ----------------------------------------------------------- -->
            </div>

            <div class="col-lg-6" data-aos="fade-left">
                <!-- ----------------------------------------------------------- -->
                <!-- Options -->
                <!-- ----------------------------------------------------------- -->
                <!-- checkbox -->
                <q-option-group
                    v-if="typeof question.correctResponse == 'object'"
                    :id="'options_' + (questionIdAttempt + questionId)"
                    :disable="
                        (questionSubmitted && !reSubmit) ||
                        (questionSubmitted && questionResult == 'correct')
                    "
                    v-model="learnerResponse"
                    type="checkbox"
                    :options="question.options"
                >
                    <template v-slot:label="opt">
                        <span v-html="opt.label"></span>
                    </template>
                </q-option-group>
                <!-- radio -->
                <q-option-group
                    v-else
                    :id="'options_' + (questionIdAttempt + questionId)"
                    :disable="
                        (questionSubmitted && !reSubmit) ||
                        (questionSubmitted && questionResult == 'correct')
                    "
                    v-model="learnerResponse"
                    type="radio"
                    :options="question.options"
                >
                    <template v-slot:label="opt">
                        <span v-html="opt.label"></span>
                    </template>
                </q-option-group>

                <!-- ----------------------------------------------------------- -->
                <!-- submit button -->
                <!-- ----------------------------------------------------------- -->
                <q-btn
                    :disable="
                        (questionSubmitted && !reSubmit) ||
                        (questionSubmitted && questionResult == 'correct') ||
                        !learnerResponse ||
                        learnerResponse.length <= 0
                    "
                    @click="submitQuestion()"
                    padding="sm xl"
                    no-caps
                    rounded
                    outline
                    :color="defaultColor"
                    class="q-mt-md q-mb-xl btn-fixed-width"
                    :label="submitButtonText"
                >
                </q-btn>

                <!-- ----------------------------------------------------------- -->
                <!-- Question Feedback -->
                <!-- ----------------------------------------------------------- -->
                <div v-if="feedbackType !== 'none' && questionSubmitted">
                    <section
                        v-if="
                            feedbackType !== 'correct_only' ||
                            (feedbackType == 'correct_only' &&
                                questionResult == 'correct')
                        "
                    >
                        <div class="row items-center">
                            <q-icon
                                class="q-mr-md"
                                size="55px"
                                :name="
                                    questionResult == 'correct'
                                        ? 'bi-check-circle'
                                        : 'bi-x-circle'
                                "
                            ></q-icon>
                            <p
                                class="text-h5"
                                v-html="
                                    questionResult == 'correct'
                                        ? question.correctFeedbackTitle
                                        : question.incorrectFeedbackTitle
                                "
                            ></p>
                        </div>

                        <q-separator
                            class="q-my-lg"
                            aria-hidden="true"
                            size="2px"
                            :color="defaultColor"
                            data-aos="zoom-in"
                        ></q-separator>
                        <ListComponent
                            propFirstLevel="p"
                            :propListData="
                                feedbackType == 'universal'
                                    ? question.universalFeedbackText
                                    : questionResult == 'correct'
                                    ? question.correctFeedbackText
                                    : question.incorrectFeedbackText
                            "
                            propMarginFirstLevel="1rem"
                            propMarginSecondLevel="0.5rem"
                        />
                        <q-separator
                            class="q-my-lg"
                            aria-hidden="true"
                            size="2px"
                            :color="defaultColor"
                            data-aos="zoom-in"
                        ></q-separator>
                    </section>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
import { defineComponent, onMounted, ref, computed } from "vue";

import { useI18n } from "vue-i18n";
import { useStore } from "stores/store";
import { useCore } from "stores/composables/core";
import ListComponent from "components/ListComponent.vue";

export default defineComponent({
    name: "QuestionCard",
    props: {
        questionId: {
            type: String,
            default: null,
        },
        questionIdAttempt: {
            type: String,
            default: "",
        },
        questionsArray: {
            type: Array,
        },
        submitButtonText: {
            type: String,
        },
        defaultColor: {
            type: String,
            default: "secondary",
        },
        alternativeTitle: {
            type: String,
            default: null,
        },
        feedbackType: {
            type: String,
            default: "universal", // options ("none", "universal", "correct_incorrect", "correct_only")
        },
        reSubmit: {
            type: Boolean,
            default: false,
        },
        headerInsideQuestionCard: {
            type: Boolean,
            default: true,
        },
    },
    components: {
        ListComponent,
    },
    setup(props) {
        const store = useStore();
        const core = useCore();

        const { locale } = useI18n({ useScope: "global" });
        const { t, tm } = useI18n();

        const question = ref();
        const learnerResponse = ref();

        const questionSubmitted = computed(() => {
            return store.getInteractionSubmitted(
                props.questionIdAttempt + props.questionId
            ).submitted;
        });
        const questionResult = computed(() => {
            return store.getInteractionSubmitted(
                props.questionIdAttempt + props.questionId
            ).result;
        });

        const submitQuestion = () => {
            store.setInteraction(
                props.questionIdAttempt + props.questionId,
                learnerResponse.value,
                question.value.correctResponse,
                true
            );
            core.scrollDown(300);
        };

        onMounted(() => {
            // get the question from the i18n JSON
            question.value = core.findQuestionById(
                props.questionId,
                props.questionsArray
            );

            // overwrites the title from the JSON key with the alternativeTitle if it is passed as a parameter
            if (props.alternativeTitle) {
                question.value.title = props.alternativeTitle;
            }

            // update the learner_response
            const learner_response = store.getInteractionSubmitted(
                props.questionIdAttempt + props.questionId
            ).learner_response;
            if (learner_response) {
                // check if the learner_response is a string(when coming from the LMS) or already an array(when getting "fresh" from the Store)
                if (
                    typeof question.value.correctResponse == "object" &&
                    !Array.isArray(learner_response)
                ) {
                    learnerResponse.value = learner_response.split(",");
                } else {
                    learnerResponse.value = learner_response;
                }
            } else {
                if (typeof question.value.correctResponse == "object") {
                    learnerResponse.value = [];
                } else {
                    learnerResponse.value = null;
                }
            }
        });

        return {
            locale,
            t,
            tm,

            showCorrectAnswer: ref(true),

            question,
            submitQuestion,
            learnerResponse,
            questionSubmitted,
            questionResult,
        };
    },
});
</script>

<style lang="scss"></style>
