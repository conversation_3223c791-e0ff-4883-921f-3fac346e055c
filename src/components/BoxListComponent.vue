<template>
    <div class="container">
        <template v-for="(item, index) in listItems" :key="index">
            <div
                :class="[ backgroundClass, 'q-mb-md rounded-borders overflow-hidden', ]"
                data-aos="zoom-in"
            >
                <div class="row q-px-xxl q-py-xl items-center q-col-gutter-lg q-col-gutter-xl-xl" >

                    <div class="col-12 col-md-4 col-lg-3">
                        <h4
                            :class="[ textClass, 'Citi-Sans-Display-Bold callout q-mt-sm', ]"
                            :style="{ fontSize: $q.screen.gt.md ? '1.5rem' : null }"
                        >
                            {{ item.title }}
                        </h4>
                    </div>

                    <div class="col-12 col-md-8 col-lg-9">
                        <ListComponent
                            propFirstLevel="p"
                            :propListData="item.paragraphs"
                            propUlClass="q-mb-none"
                            propMarginFirstLevel="1rem"
                            propMarginSecondLevel=""
                            :class="textClass"
                            :prop-animation="false"
                        />
                    </div>

                </div>
            </div>
        </template>
    </div>
</template>

<script>
import { defineComponent } from "vue";
import ListComponent from "components/ListComponent.vue";

export default defineComponent({
    name: "BoxListComponent",

    components: {
        ListComponent,
    },

    props: {
        listItems: {
            type: Array,
            required: true,
        },
        backgroundClass: {
            type: String,
            default: "bg-secondary",
        },
        textClass: {
            type: String,
            default: "text-white",
        },
    },
});
</script>
