<template>

    <!-- -------------------------------------------------------------------- -->
    <!-- tab-component -->
    <!-- -------------------------------------------------------------------- -->
    <div class="q-pa-sm" :ref="interactionId" :id="interactionId">
        <div class="q-gutter-y-md" style="">
            <q-card square>

                <q-tabs
                    v-model="tab"
                    dense
                    class=""
                    active-color="orange"
                    indicator-color="blue"
                    align="justify"
                    @update:model-value="handleTabClick"
                >
                    <q-tab
                        v-for="item in tabData"
                        :key="item.id"
                        :name="item.id"
                        :class="tabButtonClass"
                    >
                        <!-- --------------------------------------- -->
                        <!-- visitedTabs checkmark -->
                        <!-- --------------------------------------- -->
                        <q-img v-if="item.visited === true"
                            src="~assets/global/check-neon-green-circle.svg"
                            style="height: 25px; width: 25px; position: absolute; right: -40px; top: 10px;"
                            class=""
                            data-aos="flip-left"
                            data-aos-delay="500"
                            data-aos-duration="750"
                            data-aos-once="true"
                        ></q-img>

                        <p
                            class="q-my-sm text-bold"
                            style="text-transform: none;"
                            v-html="
                                $q.screen.lt.md
                                ? item.number
                                : item.title
                            "
                        ></p>
                    </q-tab>
                </q-tabs>

                <q-separator class="q-pt-xxs" color="black" ></q-separator>

                <!-- ----------------------------- -->
                <!-- tab panels - bottom -->
                <!-- ----------------------------- -->
                <q-tab-panels
                    v-model="tab"
                    animated
                    :class="tabPanelsClass"
                >

                    <q-tab-panel
                        v-for="(item, index) in tabData" :key="index"
                        :name="item.id"
                        :class="tabPanelClass"
                    >

                        <div class="row">

                            <!-- /////////////////// tab panel text - left -->
                            <div :class="tabPanelCol1Class" :style="tabPanelCol1Style"
                                class="col"
                            >
                                <h3
                                    v-if="item.title"
                                    v-html="item.title"
                                    :class="tabTitleClass"
                                    :style="tabTitleStyle"
                                ></h3>
                                <p
                                    v-if="item.text"
                                    v-html="item.text"
                                    :class="tabTextClass"
                                    :style="tabTextStyle"
                                ></p>
                                <!-- -------------------------------------------------------------------- -->
                                <!-- nested list - will get you two levels of list-items -->
                                <!-- -------------------------------------------------------------------- -->
                                <ul v-if="item.list">
                                    <span v-for="(listItem, index) in item.list" :key="index">
                                        <li class="mb-2" v-if="typeof listItem == 'string'" v-html="listItem"></li>
                                        <ul v-if="typeof listItem == 'object'" class="mb-2">
                                            <li v-for="(item, index) in listItem" :key="index" v-html="item"></li>
                                        </ul>
                                    </span>
                                </ul>
                            </div>

                            <!-- //////////////// tab panel image  - right -->
                            <div :class="tabPanelCol2Class" :style="tabPanelCol2Style"
                                v-if="item.img"
                                class="col flex justify-center"
                            >
                                <img
                                    :src="item.img"
                                    style="width: 100%; height: 100%; object-fit: cover; display: block; overflow: hidden;"
                                >
                            </div>

                        </div>

                    </q-tab-panel>

                </q-tab-panels>

            </q-card>
        </div>
    </div>
</template>

<script>

import { defineComponent, onMounted, ref } from "vue";
import { useStore } from "stores/store";

export default defineComponent ({
    name: "TabsComponent",
    props: {

        /////////////////////////////
        ////////////////// id / data
        tabData: {
            type: Array,
            default: () => [],
        },
        interactionId: {
            type: String,
            default: null,
        },

        /////////////////////////////
        ////////////// clickable tabs
        tabButtonClass: {
            type: String,
            default: null,
        },
        tabPanelsClass: {
            type: String,
            default: null,
        },
        tabPanelClass: {
            type: String,
            default: null,
        },

        /////////////////////////////
        //////////////////// tab text
        tabTitle: {
            type: String,
            default: null,
        },
        tabTitleClass: {
            type: String,
            default: null,
        },
        tabTitleStyle: {
            type: String,
            default: null,
        },
        tabText: {
            type: String,
            default: null,
        },
        tabTextClass: {
            type: String,
            default: null,
        },
        tabTextStyle: {
            type: String,
            default: null,
        },

        /////////////////////////////
        //////////////////// column 1
        tabPanelCol1Class: {
            type: String,
            default: null,
        },
        tabPanelCol1Style: {
            type: String,
            default: null,
        },

        /////////////////////////////
        //////////////////// column 2
        tabPanelCol2Class: {
            type: String,
            default: null,
        },
        tabPanelCol2Style: {
            type: String,
            default: null,
        },

    },

    setup(props) {
        const store = useStore();
        const tab = ref();

        const handleTabClick = (tabId) => {
            console.log(`\x1B[31m Tab ${tabId} clicked`);

            // Find the corresponding tabData item and set its visited property to true
            const clickedTab = props.tabData.find(item => item.id === tabId);
            if (clickedTab) {
                clickedTab.visited = true;
            }

            // saves the interaction for the tabId
            store.setInteraction(tabId, null, null, false);
        };

        onMounted(() => {
            tab.value = props.tabData[0].id;

            ////////////////////////////////////////////////////
            // initialize the visited property for the first tab
            const firstTab = props.tabData.find(item => item.id === tab.value);
            if (firstTab) {
                firstTab.visited = true;
            }

            // saves the interaction for the first tab id
            store.setInteraction(tab.value, null, null, false)
        });

        return {
            tab,
            handleTabClick,
        };

    },

});
</script>

<style scoped></style>
