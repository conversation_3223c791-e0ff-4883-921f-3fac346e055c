<template>

    <!-- -------------------------------------------------------------------- -->
    <!-- stepper-component -->
    <!-- -------------------------------------------------------------------- -->
    <div class="q-pa-md" :ref="interactionId" :id="interactionId">

      <q-stepper
        v-model="step"
        ref="stepper"
        color="primary"
        :class="stepClass"
        animated
        style="border-radius: 0;"
      >

        <!-- ----------------------------------------------------------------- -->
        <!-- iterates over your data object -->
        <!-- ----------------------------------------------------------------- -->
        <q-step v-for="(item, index) in stepData" :key="item.id"
            :name="index"
            :class="bodyClass"
            :title="item.title"
            icon="settings"
            :done="step > index"
        >
            <p v-if="item.text1" v-html="item.text1"></p>
            <p v-if="item.text2" v-html="item.text2"></p>
        </q-step>

        <!-- ----------------------------------------------------------------- -->
        <!-- stepper navigation -->
        <!-- ----------------------------------------------------------------- -->
        <template v-slot:navigation>
          <q-stepper-navigation :class="navClass">

            <q-btn
                @click="$refs.stepper.next(); handleStepClick();"
                class="q-ma-md"
                color="yellow-10"
                :label="
                    step == stepData.length - 1
                    ? btnFinish
                    : btnContinue
                "
            />

            <q-btn v-if="step > 0"
                @click="$refs.stepper.previous(); handleStepClick();"
                color="pink-10"
                :label="btnBack"
                class=""
            />

          </q-stepper-navigation>
        </template>

      </q-stepper>

    </div>
</template>
<script>

import { defineComponent, onMounted, ref } from "vue";
import { useStore } from "stores/store";

export default defineComponent({
    name: "StepperComponent",
    props: {

        /////////////////////////////
        ////////////////// id / data
        stepData: {
            type: Array,
            default: () => [],
        },
        interactionId: {
            type: String,
            default: null,
        },

        /////////////////////////////
        ///////////////////// classes
        stepClass: {
            type: String,
            default: null,
        },
        bodyClass: {
            type: String,
            default: null,
        },
        navClass: {
            type: String,
            default: null,
        },

        /////////////////////////////
        ///////////////////// buttons
        btnContinue: {
            type: String,
            default: null,
        },
        btnFinish: {
            type: String,
            default: null,
        },
        btnBack: {
            type: String,
            default: null,
        },

    },

    data() {
        return {};
    },

    setup(props) {
        const store = useStore();
        const step = ref(0);

        const handleStepClick = () => {

            // saves the interaction
            store.setInteraction(props.interactionId, step.value, null, true);
            console.log(step.value);
        };

        onMounted(() => {
            const savedStep = store.getInteractionSubmitted(props.interactionId).learner_response;
            step.value = savedStep ? savedStep : 0;

        });

        return {
            step,
            handleStepClick,
        };
    },

    // setup(props) {
    //     const store = useStore();
    //     const step = ref(0);

    //     const handleStepClick = (stepId) => {
    //         console.log(`\x1B[31m Step ${stepId} clicked`);

    //         // Find the corresponding stepData item and set its visited property to true
    //         const clickedStep = props.stepData.find(item => item.id === stepId);
    //         if (clickedStep) {
    //             clickedStep.visited = true;
    //         }

    //         // saves the interaction for the stepId
    //         store.setInteraction(stepId, null, null, false);
    //     };

    //     onMounted(() => {
    //         step.value = props.stepData[0].id;

    //         ////////////////////////////////////////////////////
    //         // initialize the visited property for the first step
    //         const firstStep = props.stepData.find(item => item.id === step.value);
    //         if (firstStep) {
    //             firstStep.visited = true;
    //         }

    //         // saves the interaction for the first step id
    //         store.setInteraction(step.value, null, null, false)
    //     });

    //     return {
    //         step,
    //         handleStepClick,
    //     };

    // },

});
</script>

<style scoped></style>
