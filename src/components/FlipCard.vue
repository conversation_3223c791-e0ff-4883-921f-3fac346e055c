<template>
    <div :id="interactionId">

        <transition
            mode="out-in"
            leave-active-class="flip-out-ver-right"
            enter-active-class="flip-in-ver-left"
        >
            <q-btn v-if="!flip" ref="refBtnFront" @click="runFlip" no-caps flat rounded class="text-body1 full-height full-width q-pa-none" :aria-label="t('ui.learn')">
                <q-card class="q-py-lg flex flex-center full-width" :style="`height:527px; background-size: cover; background-image: url('${bgFront}');`">
                    <q-card-section class="flex flex-center">
                        <q-img no-spinner no-transition loading="eager" :src="iconFront" width="160px" height="160px"></q-img>
                    </q-card-section>
                </q-card>
            </q-btn>
            <q-btn v-else ref="refBtnBack" @click="runFlip" no-caps flat rounded class="text-body1 full-height full-width q-pa-none">
                <q-card class="q-py-lg full-width" :style="`height:527px; background-size: cover; background-image: url('${bgBack}');`">
                    <q-card-section class="text-center q-px-md q-px-md-xl q-py-md q-py-md-xl">
                        <q-img no-spinner no-transition loading="eager" class="q-mb-lg" :src="iconBack" width="160px" height="160px"></q-img>
                        <p v-html="textCard"></p>
                    </q-card-section>
                </q-card>
            </q-btn>
        </transition>

    </div>
</template>

<script>
import { defineComponent, onMounted, ref } from 'vue'
import { useStore } from 'stores/store';
import { useI18n } from 'vue-i18n'

export default defineComponent({
    name: 'FlipCard',
    props: {
        interactionId: {
            type: String,
            default: null
        },
        iconFront: {
            type: String,
            default: null
        },
        iconBack: {
            type: String,
            default: null
        },
        bgFront: {
            type: String,
            default: null
        },
        bgBack: {
            type: String,
            default: null
        },
        textCard: {
            type: String,
            default: null
        },
    },
    setup(props) {
        const store = useStore();
        const { t, tm } = useI18n();

        const flip = ref();
        const refBtnFront = ref(null);
        const refBtnBack = ref(null);

        const runFlip = () => {
            flip.value = !flip.value;
            store.setInteraction(props.interactionId, null, null, true)

            setTimeout( () => {
                if (flip.value) {
                    // console.log(refBtnBack.value.$el);
                    refBtnBack.value.$el.focus({ preventScroll: true });
                }
                else {
                    // console.log(refBtnFront.value.$el);
                    refBtnFront.value.$el.focus({ preventScroll: true });
                }
            }, 600);
        };

        onMounted(() => {
            flip.value = store.getInteractionSubmitted(props.interactionId).submitted
		});

        return {
            flip,
            runFlip,
            refBtnFront,
            refBtnBack,
            t,tm
        };
    }
})
</script>
