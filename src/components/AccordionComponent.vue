<template>

    <div class=" row">
        <div class="offset-lg-1 col-12 col-lg-10">
            <q-list class="" >
                <div v-for="(item,index) in contentArray" :key="index" class="cards">

                    <q-expansion-item  :toggle-aria-label="item.title" switch-toggle-side :header-class="'q-py-md'" :header-style="{  borderBottom: '1px solid #0F1632', borderTop: '1px solid #0F1632' }"
                        @show="visited['card_' + index] = true; visible['card_' + index] = true; this.checkComplete()"
                        @hide="visible['card_' + index] = false;">
                        <template v-slot:header >
                            <!-- <q-item-section avatar>
                                <q-avatar icon="bluetooth" color="primary" text-color="white" />
                            </q-item-section> -->
                            <q-item-section  >
                                <p class="Citi-Sans-Display-Regular" style="font-size: 24px; line-height: 28px" v-html="item.title"></p>
                            </q-item-section>
                            <div style="display: flex; justify-content: end;">
                                <Vue3Lottie class="lottie" v-if="visited['card_' + index] || this.checkComplete()"
                                    :loop="false" :scale="1" width="44.56px" height="44.56px" :animationData="check" />


                            </div>
                        </template>

                        <q-card class=" q-py-lg" style="background:none;" >
                            <q-card-section>
                                <div>
                                    <p v-for="(item,index) in item.text" :key="index" v-html="item"
                                        class="q-pb-md last-in-list"></p>
                                </div>
                                <ul v-if="item.list">
                                    <li v-for="(item,index) in item.list" class="q-pb-sm last-in-list" :key="index"
                                        v-html="item"></li>
                                </ul>
                            </q-card-section>
                        </q-card>
                    </q-expansion-item>
                </div>
            </q-list>
        </div>

    </div>
</template>
<style scoped>
    .last-in-list:last-child {
        margin-bottom: 0 !important;
        padding-bottom: 0 !important;
    }

    .cards .q-expansion-item .q-item__section {
        padding-top: 0.5rem;
        padding-bottom: 0.5rem;
    }

        .cards .q-expansion-item .q-card__section {
padding: 0.5rem 0 0.5rem 0rem;
    }

    .cards .q-expansion-item .lottie {
        margin: 0;
    }
</style>
<script>
    import { defineComponent, onMounted, ref } from 'vue'
    import { useStore } from 'stores/store';
    import { Vue3Lottie } from 'vue3-lottie'
    import check from '../assets/page3/modalcheckciti.json'
     import check1 from '../assets/global/graphic_1_1_v3.json'
    export default defineComponent({
        name: 'AccordionComponent',
        components: {
            Vue3Lottie,
        },
        data() {
            return {
                check,
                check1
            }
        },
        props: {
            interactionId: {
                type: String,
                default: null,
            },
            contentArray: {
                type: Array,
            },
        },
        setup(props) {
            const store = useStore();
            const visited = {}
            const visible = {}

            // check if the passed arrays length matches visited object length
            const checkComplete = () => {
                if (Object.keys(visited).length == props.contentArray.length) {
                    if (store.getInteractionSubmitted(props.interactionId).submitted) {
                        return true
                    } else {
                        store.setInteraction(props.interactionId, null, null, true)
                    }
                }
            }
            onMounted(() => {
            });

            return {
                visited,
                visible,
                checkComplete,
            };
        }
    })
</script>
