<?xml version = "1.0" encoding = "UTF-8"?>
<!--
	IPR, License and Distribution Notices
	This machine readable file is derived from IMS specification IMS Simple Sequencing XML Binding Version 1.0 Final Specification 
	found at http://www.imsglobal.org/simplesequencing/ and the original IMS schema binding or code base 
	http://www.imsglobal.org/simplesequencing/ssv1p0/imsss_bindv1p0.html.
	
	Recipients of this document are requested to submit, with their comments, notification of any relevant patent 
	claims or other intellectual property rights of which they may be aware that might be infringed by the schema 
	binding contained in this document.
	
	IMS takes no position regarding the validity or scope of any intellectual property or other rights that might be 
	claimed to pertain to the implementation or use of the technology described in this document or the extent to 
	which any license under such rights might or might not be available; neither does it represent that it has made 
	any effort to identify any such rights. Information on IMS’s procedures with respect to rights in IMS specifications 
	can be found at the IMS Intellectual Property Rights web page: http://www.imsglobal.org/ipr/imsipr_policyFinal.pdf.
	
	Copyright © IMS Global Learning Consortium 1999-2007. All Rights Reserved.
	
	License Notice for Users
	Users of products or services that include this document are hereby granted a worldwide, royalty-free, 
	non-exclusive license to use this document.
	
	Distribution Notice for Developers
	Developers of products or services that provide distribution of this document as is or with modifications are 
	required to register with the IMS community on the IMS website as described in the following two paragraphs:
	
	- If you wish to distribute this document as is, with no modifications, you are hereby granted permission to copy, 
	display and distribute the contents of this document in any medium for any purpose without fee or royalty provided 
	that you include this IPR, License and Distribution notice in its entirety on ALL copies, or portions thereof, that you 
	make and you complete a valid license registration with IMS and receive an email from IMS granting the license. 
	To register, follow the instructions on the IMS website: http://www.imsglobal.org/specificationdownload.cfm. Once 
	registered you are granted permission to transfer unlimited distribution rights of this document for the purposes 
	of third-party or other distribution of your product or service that incorporates this document as long as this IPR, 
	License and Distribution notice remains in place in its entirety.
	
	- If you wish to create and distribute a derived work from this document, you are hereby granted permission to copy, 
	display and distribute the contents of the derived work in any medium for any purpose without fee or royalty provided 
	that you include this IPR, License and Distribution notice in its entirety on ALL copies, or portions thereof, that you 
	make and you complete a valid profile registration with IMS and receive an email from IMS granting the license. To 
	register, follow the instructions on the IMS website: http://www.imsglobal.org/profile/. Once registered you are 
	granted permission to transfer unlimited distribution rights of the derived work for the purposes of third-party or 
	other distribution of your product or service that incorporates the derived work as long as this IPR, License and 
	Distribution notice remains in place in its entirety.
	
	The limited permissions granted above are perpetual and will not be revoked by IMS or its successors or assigns.
	
	THIS SPECIFICATION IS BEING OFFERED WITHOUT ANY WARRANTY WHATSOEVER, AND IN PARTICULAR, 
	ANY WARRANTY OF NONINFRINGEMENT IS EXPRESSLY DISCLAIMED. ANY USE OF THIS SPECIFICATION 
	SHALL BE MADE ENTIRELY AT THE IMPLEMENTER’S OWN RISK, AND NEITHER THE CONSORTIUM, NOR 
	ANY OF ITS MEMBERS OR SUBMITTERS, SHALL HAVE ANY LIABILITY WHATSOEVER TO ANY IMPLEMENTER 
	OR THIRD PARTY FOR ANY DAMAGES OF ANY NATURE WHATSOEVER, DIRECTLY OR INDIRECTLY, ARISING 
	FROM THE USE OF THIS SPECIFICATION.
-->
<!--Generated by Turbo XML *********. Conforms to w3c http://www.w3.org/2001/XMLSchema-->
<xs:schema xmlns = "http://www.imsglobal.org/xsd/imsss"
	 targetNamespace = "http://www.imsglobal.org/xsd/imsss"
	 xmlns:xs = "http://www.w3.org/2001/XMLSchema"
	 elementFormDefault = "qualified"
	 attributeFormDefault = "unqualified">
	
	<xs:complexType name = "limitConditionsType">
		<xs:attribute name = "attemptLimit" type = "xs:nonNegativeInteger">
				<xs:annotation>
					<xs:documentation>Limit Condition Attempt Limit</xs:documentation>
				</xs:annotation>
			</xs:attribute>
			<xs:attribute name = "attemptAbsoluteDurationLimit" type = "xs:duration" >
				<xs:annotation>
					<xs:documentation>Limit Condition Activity Attempt Absolute Duration Limit.  Typed as xs:duration: see http://www.w3.org/TR/xmlschema-2/#duration</xs:documentation>
				</xs:annotation>
			</xs:attribute>
			<xs:attribute name = "attemptExperiencedDurationLimit" type = "xs:duration">
				<xs:annotation>
					<xs:documentation>Limit Condition Activity Attempt Experienced Duration Limit.  Typed as xs:duration: see http://www.w3.org/TR/xmlschema-2/#duration</xs:documentation>
				</xs:annotation>
			</xs:attribute>
			<xs:attribute name = "activityAbsoluteDurationLimit" type = "xs:duration">
				<xs:annotation>
					<xs:documentation>Limit Condition Activity Absolute Duration Limit.  Typed as xs:duration: see http://www.w3.org/TR/xmlschema-2/#duration</xs:documentation>
				</xs:annotation>
			</xs:attribute>
			<xs:attribute name = "activityExperiencedDurationLimit" type = "xs:duration" >
				<xs:annotation>
					<xs:documentation>Limit Condition Activity Experienced Duration Limit.  Typed as xs:duration: see http://www.w3.org/TR/xmlschema-2/#duration</xs:documentation>
				</xs:annotation>
			</xs:attribute>
			<xs:attribute name = "beginTimeLimit" type = "xs:dateTime">
				<xs:annotation>
					<xs:documentation>Limit Condition Begin Time Limit</xs:documentation>
				</xs:annotation>
			</xs:attribute>
			<xs:attribute name = "endTimeLimit" type = "xs:dateTime">
				<xs:annotation>
					<xs:documentation>Limit Condition End Time Limit</xs:documentation>
				</xs:annotation>
			</xs:attribute>
		
	</xs:complexType>
</xs:schema>