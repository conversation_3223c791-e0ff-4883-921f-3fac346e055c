<?xml version = "1.0" encoding = "UTF-8"?>
<!--
	IPR, License and Distribution Notices
	This machine readable file is derived from IMS specification IMS Simple Sequencing XML Binding Version 1.0 Final Specification
	found at http://www.imsglobal.org/simplesequencing/ and the original IMS schema binding or code base
	http://www.imsglobal.org/simplesequencing/ssv1p0/imsss_bindv1p0.html.

	Recipients of this document are requested to submit, with their comments, notification of any relevant patent
	claims or other intellectual property rights of which they may be aware that might be infringed by the schema
	binding contained in this document.

	IMS takes no position regarding the validity or scope of any intellectual property or other rights that might be
	claimed to pertain to the implementation or use of the technology described in this document or the extent to
	which any license under such rights might or might not be available; neither does it represent that it has made
	any effort to identify any such rights. Information on IMS’s procedures with respect to rights in IMS specifications
	can be found at the IMS Intellectual Property Rights web page: http://www.imsglobal.org/ipr/imsipr_policyFinal.pdf.

	Copyright © IMS Global Learning Consortium 1999-2007. All Rights Reserved.

	License Notice for Users
	Users of products or services that include this document are hereby granted a worldwide, royalty-free,
	non-exclusive license to use this document.

	Distribution Notice for Developers
	Developers of products or services that provide distribution of this document as is or with modifications are
	required to register with the IMS community on the IMS website as described in the following two paragraphs:

	- If you wish to distribute this document as is, with no modifications, you are hereby granted permission to copy,
	display and distribute the contents of this document in any medium for any purpose without fee or royalty provided
	that you include this IPR, License and Distribution notice in its entirety on ALL copies, or portions thereof, that you
	make and you complete a valid license registration with IMS and receive an email from IMS granting the license.
	To register, follow the instructions on the IMS website: http://www.imsglobal.org/specificationdownload.cfm. Once
	registered you are granted permission to transfer unlimited distribution rights of this document for the purposes
	of third-party or other distribution of your product or service that incorporates this document as long as this IPR,
	License and Distribution notice remains in place in its entirety.

	- If you wish to create and distribute a derived work from this document, you are hereby granted permission to copy,
	display and distribute the contents of the derived work in any medium for any purpose without fee or royalty provided
	that you include this IPR, License and Distribution notice in its entirety on ALL copies, or portions thereof, that you
	make and you complete a valid profile registration with IMS and receive an email from IMS granting the license. To
	register, follow the instructions on the IMS website: http://www.imsglobal.org/profile/. Once registered you are
	granted permission to transfer unlimited distribution rights of the derived work for the purposes of third-party or
	other distribution of your product or service that incorporates the derived work as long as this IPR, License and
	Distribution notice remains in place in its entirety.

	The limited permissions granted above are perpetual and will not be revoked by IMS or its successors or assigns.

	THIS SPECIFICATION IS BEING OFFERED WITHOUT ANY WARRANTY WHATSOEVER, AND IN PARTICULAR,
	ANY WARRANTY OF NONINFRINGEMENT IS EXPRESSLY DISCLAIMED. ANY USE OF THIS SPECIFICATION
	SHALL BE MADE ENTIRELY AT THE IMPLEMENTER’S OWN RISK, AND NEITHER THE CONSORTIUM, NOR
	ANY OF ITS MEMBERS OR SUBMITTERS, SHALL HAVE ANY LIABILITY WHATSOEVER TO ANY IMPLEMENTER
	OR THIRD PARTY FOR ANY DAMAGES OF ANY NATURE WHATSOEVER, DIRECTLY OR INDIRECTLY, ARISING
	FROM THE USE OF THIS SPECIFICATION.
-->
<!--Generated by Turbo XML *********. Conforms to w3c http://www.w3.org/2001/XMLSchema-->
<xs:schema xmlns = "http://www.imsglobal.org/xsd/imsss"
	 targetNamespace = "http://www.imsglobal.org/xsd/imsss"
	 xmlns:xs = "http://www.w3.org/2001/XMLSchema"
	 elementFormDefault = "qualified"
	 attributeFormDefault = "unqualified">
	<xs:include schemaLocation = "imsss_v1p0util.xsd"/>
	<xs:complexType name = "rollupRuleType">
		<xs:sequence>
			<xs:element name = "rollupConditions"
				 block = "#all">
				<xs:complexType>
					<xs:sequence>
						<xs:element name = "rollupCondition"
							 block = "#all" maxOccurs = "unbounded">
							<xs:complexType>
								<xs:attribute name = "operator" default = "noOp" type = "conditionOperatorType"/>
								<xs:attribute name = "condition" use = "required" type = "rollupRuleConditionType"/>
							</xs:complexType>
						</xs:element>
					</xs:sequence>
					<xs:attribute name = "conditionCombination" default = "any" type = "conditionCombinationType"/>
				</xs:complexType>
			</xs:element>
			<xs:element name = "rollupAction"
				 block = "#all">
				<xs:complexType>
					<xs:attribute name = "action" use = "required" type = "rollupActionType"/>
				</xs:complexType>
			</xs:element>
		</xs:sequence>
		<xs:attribute name = "childActivitySet" default = "all" type = "childActivityType"/>
		<xs:attribute name = "minimumCount" default = "0" type = "xs:nonNegativeInteger"/>
		<xs:attribute name = "minimumPercent" default = "0" type = "percentType"/>
	</xs:complexType>
	<xs:complexType name = "rollupRulesType">
		<xs:sequence>
			<xs:element name = "rollupRule" type = "rollupRuleType"
				 block = "#all" minOccurs = "0" maxOccurs = "unbounded"/>
		</xs:sequence>
		<xs:attribute name = "rollupObjectiveSatisfied" default = "true" type = "xs:boolean"/>
		<xs:attribute name = "rollupProgressCompletion" default = "true" type = "xs:boolean"/>
		<xs:attribute name = "objectiveMeasureWeight" default = "1.0000" type = "weightType"/>
	</xs:complexType>
</xs:schema>
