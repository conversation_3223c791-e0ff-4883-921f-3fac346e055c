<?xml version="1.0" standalone="no" ?>
<!--
Manifest template to demonstrate the proper XML namespace declarations for
SCORM 2004 3rd Edition manifests that do not use metadata.

Provided by Rustici Software - www.scorm.com.
-->
<manifest identifier="CITI_CEAM_TAM_v2_2" version="1"
                xmlns="http://www.imsglobal.org/xsd/imscp_v1p1"
                xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                xmlns:adlcp="http://www.adlnet.org/xsd/adlcp_v1p3"
                xmlns:adlseq="http://www.adlnet.org/xsd/adlseq_v1p3"
                xmlns:adlnav="http://www.adlnet.org/xsd/adlnav_v1p3"
                xmlns:imsss="http://www.imsglobal.org/xsd/imsss"
                xsi:schemaLocation="http://www.imsglobal.org/xsd/imscp_v1p1 imscp_v1p1.xsd
                                    http://www.adlnet.org/xsd/adlcp_v1p3 adlcp_v1p3.xsd
                                    http://www.adlnet.org/xsd/adlseq_v1p3 adlseq_v1p3.xsd
                                    http://www.adlnet.org/xsd/adlnav_v1p3 adlnav_v1p3.xsd
                                    http://www.imsglobal.org/xsd/imsss imsss_v1p0.xsd">
	<metadata>
		<schema>ADL SCORM</schema>
		<schemaversion>2004 3rd Edition</schemaversion>
	</metadata>
	<organizations default="CITI_CEAM">
		<organization identifier="CITI_CEAM" adlseq:objectivesGlobalToSystem="false">
			<title>Introduction to Citi Enterprise Architecture Methodology (CEAM) and Process Governance</title>
			<item identifier="i1" identifierref="resource_1" isvisible="true">
				<title>Introduction to Citi Enterprise Architecture Methodology (CEAM) and Process Governance</title>
			</item>
		</organization>
	</organizations>
	<resources>
		<resource identifier="resource_1" type="webcontent" adlcp:scormType="sco" href="index.html">
			<file href="index.html" />
		</resource>
	</resources>
</manifest>
