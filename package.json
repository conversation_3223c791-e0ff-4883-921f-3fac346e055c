{"name": "citi-ceam", "version": "0.0.1", "description": "Citi-Ceam", "productName": "Citi-Ceam", "author": "", "private": true, "scripts": {"lint": "eslint --ext .js,.vue ./", "format": "prettier --write \"**/*.{js,vue,scss,html,md,json}\" --ignore-path .gitignore", "test": "echo \"No test specified\" && exit 0", "dev": "quasar dev", "build": "quasar build"}, "dependencies": {"@quasar/extras": "^1.16.4", "aos": "^3.0.0-beta.6", "gsap": "^3.12.5", "pinia": "^2.0.11", "pipwerks-scorm-api-wrapper": "^0.1.2", "postcss-rtlcss": "^4.0.8", "quasar": "^2.6.0", "vue": "^3.0.0", "vue-i18n": "^9.0.0", "vue-router": "^4.0.0", "vue3-lottie": "^3.1.0"}, "devDependencies": {"@intlify/vite-plugin-vue-i18n": "^3.3.1", "@quasar/app-vite": "^1.3.0", "autoprefixer": "^10.4.2", "eslint": "^8.10.0", "eslint-config-prettier": "^8.1.0", "eslint-plugin-vue": "^9.0.0", "postcss": "^8.4.14", "prettier": "^2.5.1"}, "engines": {"node": "^18 || ^16 || ^14.19", "npm": ">= 6.13.4", "yarn": ">= 1.21.1"}}