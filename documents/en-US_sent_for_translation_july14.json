{"manifest": {"title": "Introduction to Citi Enterprise Architecture Methodology (CEAM) and Process Governance", "content": [{"title": "Welcome"}, {"title": "What Is Enterprise Architecture Methodology?"}, {"title": "Foundation of Enterprise Architecture"}, {"title": "Who Are Process Owners?"}, {"title": "Components of <span class='sr-only'> C E A M </span><span aria-hidden='true'>CEAM</span>"}, {"title": "Technology Simplification in Action"}, {"title": "Who Is Responsible for Applying Enterprise Architecture and Process Governance?"}, {"title": "Course Summary"}, {"title": "Assessment"}]}, "resources": {"title1": "Resources", "title2": "Acronyms", "title3": "Links", "list1": ["CEAM - Citi Enterprise Architecture Methodology", "DSMT – Data Standards Management ToolGPMP - Global Process MCA Profile ", "EAPGP - Enterprise Architecture and Process Governance Policy", "EUC - End User Computing", "MCA - Manager Control Assessment", "POA - Process Ownership Assessment Platform", "PTS - Project Tracking System", "I@C – Investments@Citi"], "list2": ["<a href='https://citi.sharepoint.com/sites/Enterprise-Architecture/SitePages/Process-Ownership.aspx?csf=1&web=1&e=1oNYIZ' target='_blank'>Business Architecture Process Owner Resources</a>", "<a href='https://citi.sharepoint.com/sites/Enterprise-Architecture/SitePages/ReferenceMaterials.aspx' target='_blank'>Business Architecture Reference Material</a>", "<a href='https://citi.hostedconnectedrisk.com/CitiCPD/react?page=O&CID=sid552268563&DeletedOnly=0&HTMFile=instancePolicyUser_View.htm&InstanceNo=2001487&NodeNo=182&inlineEdit=0&originalRequestNodeNo=182&pageType=42&resetTreeView=0&rightClickContextMenu=0&runOnce=1&scrollableViewer=1&treeViewNode0=253&treeViewNode0OverviewPage=instancePolicyVersionAuthor_Title.htm&treeViewNode1=281&treeViewNode10=371&treeViewNode10OverviewPage=instancePolicySectionLevel10VersionAuthorView.htm&treeViewNode1OverviewPage=instancePolicySectionLevel1VersionAuthor_View.htm&treeViewNode2=282&treeViewNode2OverviewPage=instancePolicySectionLevel2VersionAuthor_View.htm&treeViewNode3=283&treeViewNode3OverviewPage=instancePolicySectionLevel3VersionAuthor_View.htm&treeViewNode4=285&treeViewNode4OverviewPage=instancePolicySectionLevel4VersionAuthor_View.htm&treeViewNode5=284&treeViewNode5OverviewPage=instancePolicySectionLevel5VersionAuthor_View.htm&treeViewNode6=372&treeViewNode6OverviewPage=instancePolicySectionLevel6VersionAuthorView.htm&treeViewNode7=373&treeViewNode7OverviewPage=instancePolicySectionLevel7VersionAuthorView.htm&treeViewNode8=374&treeViewNode8OverviewPage=instancePolicySectionLevel8VersionAuthorView.htm&treeViewNode9=375&treeViewNode9OverviewPage=instancePolicySectionLevel9VersionAuthorView.htm&useCustomFieldForAddChild=0&useOriginalRequestNodeAsCurrent=1&usingpost=1' target='_blank'>Enterprise Architecture and Process Governance Policy (EAPGP)</a>", "<a href='https://www.citigroup.net/clicks/?version=3&utm_campaign=1625863048700868QguJhvH%2fCbsXACX4DonJcmabp9YzQXIRFAJJBbS86kyYTSzXYlBAUFpRk0RutsWxa8CMCIUYkcHJLo6cv6d3YBWMrc7TkgakWaIfQxu2lMf%2bid2Rdit0w40I%2fNZOeMBvDlfOKPiiQtXA5yGrK6rSWg%3d%3d&ctid=1625863048700883DEHnfsoz07WcImMs4XRY3vYrwPekdV1fxVqQQCX3pzC5YN%2bERTNzWzsOqx00Lz6snxX0eMKW%2bpdhG8bC%2fnGLrDr%2fxCAAA1wMNmGYs7aAwh7DHMIPOFne2kz71lbbn6WiTKelhg0XoVQN5%2b5MxQTyVg%3d%3d' target='_blank'>Process Governance Standard (PGrS)</a>"]}, "errors": {"error": "Error", "lostcommunication": "Please close this module and launch it again. An LMS error has occurred. This module will proceed, but it will not track your completion.", "close": "Close"}, "errorLMS": {"text1": "<strong>Attention!</strong> This course is not able to connect to the Learning Management System (LMS). Your previous progress has been saved. However, your current progress is no longer being recorded.", "text2": "This commonly happens when there is an interruption with your network connection. Or, the connection with the LMS has timed out.", "text3": "<strong>To correct this issue:</strong> Close this course window and then close all other browser windows.  Open a new browser window and access the LMS. Locate this training and select the start button.  Your progress in the course will be restored to the last point where there was a recorded connection."}, "ui": {"sr": "Attention screen reader users: this course has been optimized for accessibility and fully supports screen readers.", "langselection": "Language Selection", "chooselang": "Select your language:", "menu": "<PERSON><PERSON>", "menuItems": "Menu Items", "exit": "Exit", "exitCourse": "Exit Course", "logo": "Citi Logo", "notattempted": "Not attempted", "incomplete": "Incomplete", "complete": "Complete", "locked": "Locked", "itemComplete": "Item complete", "itemLocked": "Item locked", "itemUnLocked": "Item unlocked", "pageLoaded": "Page Loaded", "pagesCompleted": " pages completed", "resources": "Resources", "scroll": "Scroll down to continue.", "scrollc": "Scroll down to proceed through the course.", "previous": "Previous", "next": "Next", "continue": "Continue", "close": "Close", "gotit": "Got it!", "submit": "Submit", "learn": "Learn More", "return": "Return to Top", "citigroup": "Citigroup Inc", "selectToBegin": "Select to Begin", "settings": "Setttings", "animations": "Animations", "on": "Animations on", "off": "Animations off", "audio": "Audio", "reportconcern": "Report a Concern", "pressescape": "Press ESCAPE key to close", "of": " of ", "itemscompleted": " items completed", "questionTitleTemplate": "Question {a} of {b}", "correct": "That’s correct.", "incorrect": "Not quite."}, "videoplayer": {"Play": "Play", "Pause": "Pause", "Current Time": "Current Time", "Duration": "Duration", "Remaining Time": "Remaining Time", "Loaded": "Loaded", "Progress": "Progress", "Progress Bar": "Progress Bar", "progress bar timing: currentTime={1} duration={2}": "{1} of {2}", "Fullscreen": "Fullscreen", "Non-Fullscreen": "Non-Fullscreen", "Mute": "Mute", "Unmute": "Unmute", "Volume Level": "Volume Level", "You aborted the media playback": "You terminated the video playback.", "A network error caused the media download to fail part-way.": "A network error caused the media download to fail part-way.", "The media could not be loaded, either because the server or network failed or because the format is not supported.": "The video could not be loaded, either because the server or network failed or because the format is not supported.", "The media playback was aborted due to a corruption problem or because the media used features your browser did not support.": "The video playback was termintated due to a corruption problem or because the video used features your browser did not support.", "No compatible source was found for this media.": "No compatible source was found for this video."}, "bookmark": {"title": "Resume course at bookmark", "message": "Would you like to resume or restart the course?", "cancel": "Restart Course", "ok": "Resume Course"}, "closemodule": {"title": "Exit course", "message": "Are you sure you want to exit the course?", "cancel": "Cancel", "ok": "OK"}, "page1": {"sectionTop": {"title1": "Introduction to Citi’s Enterprise Architecture and Process Governance", "title2": "20-Minute Web-Based Training "}, "section1": {"title1": "How to Navigate This Course", "instr1": "Read through the cards for the best course navigation experience.", "list1": [{"text1": "Select the menu icon to navigate to different sections of the course. Each section will become available once you’ve completed all the requirements of the preceding section.", "text2": null, "icon1": "bi-list"}, {"text1": "Select the resources icon in the top right-hand corner to find additional resources related to this course. This course provides hyperlinks to related procedures and Policy. ", "text2": null, "icon1": "bi-info-circle"}, {"text1": "This course includes <strong><span class='text-secondary' style='text-decoration: underline'>hyperlinks</span></strong> to various resources. If you’re accessing the course from a personal device directly over the internet (outside of the Citi network), some links may not work if they link to content within Citi’s network. This will not impact your ability to complete the course.", "text2": null, "icon1": "bi-link-45deg"}, {"text1": "If you have difficulty viewing animated content, you can choose to turn off this course's animated features now. Animations are designed to enhance the course experience. Turning off animations will not prevent you from accessing any content. ", "text2": null, "icon1": "bi-gear"}, {"text1": "If at any time you need to exit the course before completion, select this icon at the top of the page to save your progress and exit the course.", "text2": null, "icon1": "bi-x-circle"}]}, "sectionWhyWhyWhyWhy": {"title": "Citi’s Enterprise Architecture and Process Governance Training", "list": [{"title": "Why This?", "paragraphs": ["Citi’s growth across diverse businesses and markets has led to a complex ecosystem of digital platforms and applications. While this expansion has brought many benefits, it has also introduced duplication, overlapping functionalities, and a lack of visibility into how our Processes work from end-to-end. Citi’s Enterprise Architecture and Process Governance provides a unified way to assess process health and associated risks, so we can make informed, enterprise-level decisions."]}, {"title": "Why Now?", "paragraphs": ["Enterprise Architecture is critical to Citi’s transformation journey and is essential to maintaining the health and sustainability of the organization in an increasingly competitive market."]}, {"title": "Why Us?", "paragraphs": ["Enterprise Architecture and Process Governance is already part of how we work at Citi. Everyone plays a role in simplifying Processes and linking them across Citi to improve our systems. Using our knowledge of this infrastructure, allows us to reduce risk, improve decisions, and operate more efficiently."]}, {"title": "What’s the win?", "paragraphs": ["Enterprise Architecture and Process Governance provides a process-centric approach used to simplify and reduce risks of Processes resulting in a better client and employee experience. "]}]}, "section2": {"text1": "This training will help you understand: ", "list1": ["The framework to address Enterprise Architecture, commonly known as the Citi Enterprise Architecture Methodology (CEAM).", "How Enterprise Architecture, Process Governance and Technology Simplification help us drive risk reduction by simplifying Citi’s Processes and the supporting applications."], "text2": "After completing this course, you’ll be able to:", "list2": ["Explain the purpose of Enterprise Architecture and Process Governance.", "Describe how Process Governance helps simplify and improve Citi’s operations.", "Recognize the role of taxonomies in supporting consistency across Citi.", "Describe the responsibilities of Process Owners as it relates to Enterprise Architecture and Process Governance.", "Recognize the roles responsible for applying Enterprise Architecture and Process Governance.", "Identify the key components used in CEAM."]}, "section_assessment_disclaimer": {"paragraphs": ["Please note this course contains a final assessment. To receive a completion credit for this training, you must achieve a score of 80% or higher. "]}, "section3": {"title": "Welcome to the Enterprise Architecture and Process Governance Training ", "paragraphs": ["Enterprise Architecture and Process Governance helps Citi focus on consistency and alignment across Processes and supporting applications with a goal to drive risk reduction and Technology Simplification.", "These practices also generate Process Intelligence, allowing a clear understanding of how our Processes, applications, and risks are connected. With this insight, we can identify overlap, reduce complexity, and make more informed decisions about where to simplify, invest, or apply controls."]}, "section4": {"paragraphs": ["This complexity has presented itself in various ways, such as:", ["Multiple applications with similar functionality.", "Applications with too much functionality.", "No standard representation of our key Processes to assess the quality of our Processes’ design."], "As a result, it can be difficult to understand how our Processes work from end to end, the associated risks, and why we have multiple applications that are likely performing the same functionality. "]}, "section5": {"paragraphs": ["To address this, Citi has established standard requirements for Enterprise Architecture & Process Governance."]}, "section6": {"paragraphs": ["These requirements enable us to better understand: ", ["Our Processes and the supporting applications. ", "Where we have opportunities to simplify our application inventory supporting our Processes.", "Where controls are required to reduce the risk of our Processes."]]}, "section7": {"paragraphs": ["<strong>Enterprise Architecture</strong> represents the interconnectedness of our Processes, applications and other key elements that support our operations. "]}, "section8": {"paragraphs": ["The <strong>foundation of Enterprise Architecture is the Process Taxonomy.</strong> The Processes in the Process Taxonomy, along with defined Process Governance, allows us to <strong>consistently view our risks, applications, and other factors</strong> in making objective enterprise decisions, from where we may want to focus our investments to how we execute those strategic investments."]}, "footer": {"text1": "Let’s explore what Enterprise Architecture is and how we use it at Citi."}}, "assessmentFinal": {"text1a": "Welcome to the final assessment.", "text1b": "To pass this assessment and receive credit for this course, you will need to answer the following {numberOfQuestions} questions and achieve a score of 80% or higher.", "list1": [["There is no limit to the number of attempts.", "Once you begin the assessment, you must complete all questions to register a score. ", "If you exit before completing all assessment questions, you’ll need to restart the assessment."]], "text2": "Scroll down to advance to the assessment.", "questionsSection": [{"id": "kc_bankA_1", "bank": "bankA", "title": " ", "text": ["Enterprise Architecture and Process Governance (EAPGP) was developed to identify opportunities to consolidate applications with duplicative functions.  ", "What other key aspects does EAPGP help us identify?"], "instructions": "Select the best response from the four options, and then select Submit.", "options": [{"label": "The cost of maintaining each application over time", "value": "a"}, {"label": "Which teams are responsible for data entry within each system", "value": "b"}, {"label": "Where manual inputs are associated with a Process", "value": "c"}, {"label": "All of the above", "value": "d"}], "correctResponse": "c", "correctFeedbackTitle": "That answer is correct.", "incorrectFeedbackTitle": "That answer is not correct.", "correctFeedbackText": ["EAPGP also helps us identify where manual inputs are associated with a Process. This helps us understand why those steps are manual, and supports teams in finding more efficient processes."], "incorrectFeedbackText": ["For this question, please review the <strong>Introduction</strong> section of the <strong>Welcome</strong> page."], "universalFeedbackText": ["universal - feedback"]}, {"id": "kc_bankA_2", "bank": "bankA", "title": " ", "text": ["Business Architecture governs the Process Taxonomy and the Function Taxonomy. Which of the following are also governed Taxonomies?"], "instructions": "Select the best response from the four options, and then select Submit.", "options": [{"label": "Enterprise Risk Mitigation Taxonomy", "value": "a"}, {"label": "Enterprise Audit Process Taxonomy ", "value": "b"}, {"label": "Data Concept Controlled Vocabulary", "value": "c"}, {"label": "Enterprise Business Application Taxonomy ", "value": "d"}], "correctResponse": "c", "correctFeedbackTitle": "That answer is correct.", "incorrectFeedbackTitle": "That answer is not correct.", "correctFeedbackText": ["Taxonomies create a common language that supports cross-enterprise communication and understanding, three of which are governed by Business Architecture. ", "The Process Taxonomy, the Function Taxonomy, and the Data Concept Controlled Vocabulary provide consistency across artifacts produced under CEAM Citi-wide."], "incorrectFeedbackText": ["For this question, please review the <strong>Foundation of Enterprise Architecture</strong> page."], "universalFeedbackText": ["universal - feedback"]}, {"id": "kc_bankA_3", "bank": "bankA", "title": " ", "text": ["How does Process Governance support us in simplifying Citi?"], "instructions": "Select the best response from the four options, and then select Submit.", "options": [{"label": "Provides an objective means to measure the quality of a process and opportunities for simplification and risk reduction.", "value": "a"}, {"label": "Defines taxonomies that reflect business unit language without requiring consistency across Citi.", "value": "b"}, {"label": "Provides a process repository for teams to reference during audits and reviews.", "value": "c"}, {"label": "Requires global teams to follow identical Processes, regardless of local needs or context.", "value": "d"}], "correctResponse": "a", "correctFeedbackTitle": "That answer is correct.", "incorrectFeedbackTitle": "That answer is not correct.", "correctFeedbackText": ["Process Governance supports simplification at Citi by making Process Owners accountable for understanding how things work today and identifying ways to improve them. Tools like Process Models, Application Mapping, and Process Intelligence help measure the quality of a process and find opportunities to simplify, reduce risk, and drive better outcomes."], "incorrectFeedbackText": ["For this question, please review the <strong>Introduction</strong> section of the <strong>Welcome</strong> page. "], "universalFeedbackText": ["universal - feedback"]}, {"id": "kc_bankA_4", "bank": "bankA", "title": " ", "text": ["What are the responsibilities of a Process Owner?"], "instructions": "Select the best response from the four options, and then select Submit.", "options": [{"label": "Use Process Intelligence and CEAM data points to objectively improve Processes.", "value": "a"}, {"label": "Oversight of CEAM and other artifacts associated with their assigned Process(es).", "value": "b"}, {"label": "Identify the inherent risks and controls to mitigate the risks of their Processes.", "value": "c"}, {"label": "All of the above.", "value": "d"}], "correctResponse": "d", "correctFeedbackTitle": "That answer is correct.", "incorrectFeedbackTitle": "That answer is not correct.", "correctFeedbackText": ["Process Owners are subject matter experts. They’re responsible for CEAM and other artifacts associated with their assigned Process(es). ", "Process Owners provide oversight and identify areas for improvement. They do this by increasing the quality and consistency of Citi’s Processes and identifying the inherent risks and controls to mitigate the risks of their Processes through Process Intelligence and CEAM data points. "], "incorrectFeedbackText": ["For this question, please review the sections on <strong>Who are Process Owners?</strong> and <strong>Who Is Responsible for Applying Enterprise Architecture and Process Governance?</strong>"], "universalFeedbackText": ["universal - feedback"]}, {"id": "kc_bankA_5", "bank": "bankA", "title": " ", "text": ["What is a taxonomy? "], "instructions": "Select the best response from the four options, and then select Submit.", "options": [{"label": "A taxonomy is a way to name, organize, and structure related terms.", "value": "a"}, {"label": "A taxonomy is a way to develop Functions at Citi. ", "value": "b"}, {"label": "A taxonomy is a discipline used to encourage efficient use of existing Processes.", "value": "c"}, {"label": "A taxonomy is a specific role dedicated to organizing Processes. ", "value": "d"}], "correctResponse": "a", "correctFeedbackTitle": "That answer is correct.", "incorrectFeedbackTitle": "That answer is not correct.", "correctFeedbackText": ["A taxonomy is a way to name, organize, and structure related terms. It allows for consistent communication, governance, and reporting throughout the enterprise. As a result, a taxonomy improves alignment and management of diverse stakeholders. "], "incorrectFeedbackText": ["For this question, please review the <strong>Foundation of Enterprise Architecture</strong> page."], "universalFeedbackText": ["universal - feedback"]}, {"id": "kc_bankA_6", "bank": "bankA", "title": " ", "text": ["What are the benefits of Enterprise Architecture and Process Governance? "], "instructions": "Select the best response from the four options, and then select Submit.", "options": [{"label": "Drives process simplification and provides an objective means to measure risk reduction of Processes.", "value": "a"}, {"label": "Focuses solely on technology updates without improving process or controls.", "value": "b"}, {"label": "Lets teams define governance on their own, without aligning to enterprise standards. ", "value": "c"}, {"label": "Favors quick business wins over long-term consistency and architectural discipline.", "value": "d"}], "correctResponse": "a", "correctFeedbackTitle": "That answer is correct.", "incorrectFeedbackTitle": "That answer is not correct.", "correctFeedbackText": ["Enterprise Architecture and Process Governance was established to offer a better client and employee experience here at Citi. Its process-centric approach supports the simplification of Processes and improves controls across the enterprise, breaking down silos across functions."], "incorrectFeedbackText": ["For this question, please review the <strong>Introduction</strong> section of the <strong>Welcome</strong> page or the <strong>Course Summary</strong> page."], "universalFeedbackText": ["universal - feedback"]}, {"id": "kc_bankA_7", "bank": "bankA", "title": " ", "text": ["Process Owners, Manager Control Assessment (MCA) Users, and EUC Owners are responsible for applying Enterprise Architecture and Process Governance. ", "What other roles are responsible for applying Enterprise Architecture and Process Governance?"], "instructions": "Select the best response from the five options, and then select Submit.", "options": [{"label": "Risk Owners ", "value": "a"}, {"label": "Managers at all levels", "value": "b"}, {"label": "Investment Sponsors ", "value": "c"}, {"label": "All of the Above", "value": "d"}, {"label": "None of the Above", "value": "e"}], "correctResponse": "d", "correctFeedbackTitle": "That answer is correct.", "incorrectFeedbackTitle": "That answer is not correct.", "correctFeedbackText": ["Enterprise Architecture and Process Governance is applied by various staff at Citi, including Process Owners, EUC Owners, Investment Sponsors, and Manager Control Assessment (MCA) Users as examples. ", "Additionally, it’s important to remember that all Citi staff, including contractors, are responsible for accurately selecting which Process(es) is appropriate to a particular application, project or control in the various systems where this is required. "], "incorrectFeedbackText": ["For this question, please review the following sections; <strong>How Do Different Roles Use Enterprise Architecture & Process Governance?</strong> and <strong>Who Is Responsible for Applying Enterprise Architecture and Process Governance?</strong>."], "universalFeedbackText": ["universal - feedback"]}, {"id": "kc_bankA_8", "bank": "bankA", "title": " ", "text": ["Which taxonomy includes an inventory of steps or tasks performed in Applications and executed within a Process?"], "instructions": "Select the best response from the four options, and then select Submit.", "options": [{"label": "Process Taxonomy", "value": "a"}, {"label": "Function Taxonomy", "value": "b"}, {"label": "Data Controlled Vocabulary", "value": "c"}, {"label": "Business Service", "value": "d"}], "correctResponse": "b", "correctFeedbackTitle": "That answer is correct.", "incorrectFeedbackTitle": "That answer is not correct.", "correctFeedbackText": ["The Function Taxonomy includes an inventory of steps, tasks, or “functions” executed within a Process. ", "It’s a one-level hierarchy of actions that produces a specific result (Function Name), where one function can be used by many Processes. "], "incorrectFeedbackText": ["For this question, please review the <strong>Foundation of Enterprise Architecture</strong> page."], "universalFeedbackText": ["universal - feedback"]}, {"id": "kc_bankA_9", "bank": "bankA", "title": " ", "text": ["Which of the following completes the set of Application Function Designations along with Strategic and Deprecate?"], "instructions": "Select the best response from the four options, and then select Submit.", "options": [{"label": "<PERSON><PERSON>", "value": "a"}, {"label": "Archive ", "value": "b"}, {"label": "Manage", "value": "c"}, {"label": "Maintain", "value": "d"}], "correctResponse": "d", "correctFeedbackTitle": "That answer is correct.", "incorrectFeedbackTitle": "That answer is not correct.", "correctFeedbackText": ["An Application Function Designation indicates the strategic intent of an Application Function’s use in the Process. ", "There are three values that can be assigned: Strategic (application functionality is strategic), Maintain (application functionality is not strategic), and Deprecate (application is not well suited to support the given functionality). "], "incorrectFeedbackText": ["For this question, please review the <strong>Process Model</strong> section of the <strong>Components of CEAM</strong> page."], "universalFeedbackText": ["universal - feedback"]}, {"id": "kc_bankA_10", "bank": "bankA", "title": " ", "text": ["What is the purpose of using Process Models in Enterprise Architecture?"], "instructions": "Select the best response from the three options, and then select Submit.", "options": [{"label": "Process Models outline the steps to create a Process.", "value": "a"}, {"label": "Process Models help us understand Processes, their activities, Applications Functions and Manual Inputs.", "value": "b"}, {"label": "Process Models are used to assign roles and responsibilities within project teams.", "value": "c"}], "correctResponse": "b", "correctFeedbackTitle": "That answer is correct.", "incorrectFeedbackTitle": "That answer is not correct.", "correctFeedbackText": ["Process Models help us understand Processes and their related activities in a structured consistent way. They provide visibility into how work flows across the organization and support alignment with the Process Taxonomy."], "incorrectFeedbackText": ["For this question, please review the <strong>Process Models</strong> section of the <strong>Components of CEAM</strong> page."], "universalFeedbackText": ["universal - feedback"]}], "finalPassSection": {"title": "Assessment Results", "text1": "You scored", "text2": "<strong>Congratulations!</strong> You have successfully passed the assessment, meeting the requirements for course completion.", "text3": "You can now <strong>Exit</strong> the course by selecting the “X” in the top right-hand corner of the browser window, or if desired, review your assessment attempt.", "text4": "You can also review the course content using the <strong>Menu</strong> button in the top left of the course toolbar.", "btnReturn": "Review Assessment"}, "finalFailSection": {"title": "Assessment Results", "text1": "You scored", "text2": "Unfortunately, you did not meet the minimum score required to pass the assessment. Consider reviewing the content before retaking the assessment.", "text3": "You can navigate back to the course content by selecting the Review Content button.", "text4": "If you’re ready to try the assessment again, select Retake Assessment.", "btnRetry": "Retake Assessment", "btnReturn": "Review Content"}, "congratsSection": {"text1": "You may now exit the course.", "text2": "To exit the course, select the “X” in the top right-hand corner of the browser window."}}, "assessment": {"course_level_test_out": {"this_course_contains_a_test_out_and_a_final_assessment": ["This course contains a final assessment. You must score 80% or higher on the assessment to receive credit for this training.", "This course also includes an optional test-out. If you pass, you can bypass the course content and final assessment and receive credit for completion.", "If you prefer you can skip the test-out and go straight to the content."], "btnTakeTestOut": "Take Test-Out", "btnSkipTestOut": "Skip Test-Out", "title1": "Test Out", "text1": "Welcome to the Test-Out.", "text2": "To pass this assessment you will need to answer the following {numberOfQuestions} questions and achieve a score of 80% or higher.", "text3": "If you’re unable to achieve a passing score, you will be required to review the full course content.", "list1": ["You have one attempt per question.", "Once you begin the assessment, you must complete all questions to register a score. ", "If you exit before completion, your progress is not saved, and you will be required to review the full course content."], "text4": "Scroll down to advance to the test-out.", "btnContinueTest": "Continue Test-Out", "btnContinuePage": "Review previous content", "btnContinueCourse": "Start Course", "btnReviewContent": "Review Assessment", "if_perfect_score": "IF PERFECT SCORE", "congrats": {"title1": null, "text1": "You scored", "text2": "<strong>Congratulations!</strong> You have successfully passed the assessment, meeting the requirements for course completion. ", "text3": "You can now <strong>Exit</strong> the course by selecting the “X” in the top right-hand corner of the browser window, or if desired, review your assessment attempt.", "text4": "You can also review the course content using the <strong>Menu</strong> button in the top left of the course toolbar."}, "if_score_is_80_100": "IF SCORE IS 80-100%", "passed": {"title1": null, "text1": "You scored", "text2": "<strong>Congratulations!</strong> You have successfully passed the assessment, meeting the requirements for course completion. ", "text3": "You can now <strong>Exit</strong> the course by selecting the “X” in the top right-hand corner of the browser window, or if desired, review your assessment attempt.", "text4": "You can also review the course content using the <strong>Menu</strong> button in the top left of the course toolbar."}, "failed": {"title1": null, "text1": "You scored", "text2": "Unfortunately, you did not meet the minimum score required to pass the Test-Out. You are now required to review the full course content and complete the assessment at the end of the course."}}, "test_out": {"title1": "Test-Out", "text1": "This optional Section-Level Test-Out has {numberOfQuestions} question(s). If you answer all questions correctly, you’ll be able to progress directly to the next section in this course.", "text2": "You have one attempt per question and must complete the Test-Out in a single attempt.", "text3": "If you’re unable to answer all the questions correctly, or exit before completing all questions, you’ll be required to review the section content and successfully answer the questions provided at the end of the section.", "btnTakeTest": "Take Test-Out", "btnContinueSection": "Continue to Section Content", "passed": {"title1": "Congratulations!", "text1": "You have demonstrated a strong understanding of the material covered in this section. You can proceed directly to the next section or if desired, review this section’s content before moving forward.", "btnContinueCourse": "Continue Course", "btnReviewSection": "Review Section Content"}, "failed": {"text1": "Unfortunately, you answered one or more questions incorrectly and are now required to review the section content and complete the assessment questions at the end of the section.", "btnContinueSection": "Continue to Section Content"}}, "knowledge_check": {"title1": "Assessment", "text1": "You must answer the following {numberOfQuestions} question(s) correctly before moving forward.", "text2": "If you answer one or more questions incorrectly or exit before completing all questions, you’ll need to retake the assessment.", "text3": "<strong>Scroll down to advance to the assessment.</strong>", "passed": {"title1": "Congratulations! You can now progress to the next section."}, "failed": {"title1": "Assessment Results", "text1": "Unfortunately, you’ve answered one or more questions incorrectly. Consider reviewing the content again before retaking the assessment", "btnReviewSection": "Review Section Content", "btnRetake": "Retake Assessment"}}, "questions": []}, "page2": {"sectionTop": {"title1": "What Is Enterprise Architecture?", "title2": "Defining Enterprise Architecture"}, "section1": {"title1": "Our Culture, Our Responsibilities, Our Decisions", "text1": "Enterprise Architecture is based on a methodology, commonly known as Citi Enterprise Architecture Methodology, or CEAM, that is used as a discipline to develop Process Models that comprehensively define, understand, organize, and standardize Processes and associated applications.", "text2": "To better understand its value at Citi, let’s start by exploring how concepts used in CEAM relate to everyday activities like making a payment.", "list1": ["When buying products and services online, chances are you pay using a credit card, debit card or mobile payment. ", "Clients expect their payments to be processed correctly, securely, and on time, and payment service providers such as Citi share that expectation.", "As a large global bank, payment processing is a functionality used throughout many of our Processes across Businesses as well as Functions. Over the years, we have accumulated many applications that perform such functionality. This creates complexity to maintain, improve and build robust controls to meet the objective of correct, timely and secure payments.", "CEAM enables us to identify applications with common functionality, such as payment initiation or validation. As we execute on consolidation of these identified opportunities, we create a simpler application landscape, one that can be managed more efficiently, with fewer applications to maintain in alignment with the latest requirements and controls. This drives and supports Technology Simplification."], "text3": "Enterprise Architecture Process and Governance allows us to objectively work toward simplification across the enterprise."}, "footer": {"text1": "Let’s further explore Taxonomies and Citi’s Enterprise Architecture components.", "btnText1": "Continue"}}, "page3": {"sectionTop": {"title1": "Foundation of Enterprise Architecture"}, "section1": {"title": "Process & Function Taxonomies", "text1": "Let’s start with a payment scenario that shows how using a common language drives simplification, and how Taxonomies in Enterprise Architecture achieve this.", "example_lorenzo": ["Meet <PERSON>, they are a current Citi client who runs a small business and uses Citi to pay their  suppliers around the world. ", "Ideally, making payments through Citi should be a seamless process for Lorenzo. Unfortunately, the reality is the process is complicated and challenging.", "Why? ", "Behind the scenes, Citi’s payment technology is complex. To support the needs of clients globally and the growing global product, numerous payment applications have been developed, many of which perform similar functions. ", "As a result, <PERSON> has to log into and navigate three different payment platforms just to pay their suppliers. Depending on where the suppliers are located, different regional applications are used to send funds. It’s a fragmented experience caused by overlapping systems designed to do the same function, sending funds to someone somewhere in the world.", "<PERSON>'s situation highlights where enterprise architecture frameworks can help Citi identify opportunities to reduce applications. ", "By applying enterprise architecture frameworks, new and existing clients will be provided with a better client experience and improved satisfaction with Citi’s offered products and services."], "text8": "Now let’s further explore how Taxonomies, most notably the Process and Function Taxonomies, create a common language that supports cross-enterprise communication and understanding, and serve as a foundation for enterprise architecture.", "cta_accordion": "Select each taxonomy definition to learn more.", "accordion1": [{"title": "What Is a Taxonomy?", "text": ["You may be familiar with the Managed Segment hierarchy, Managed Geography hierarchy, or the Citi Legal Vehicle designations. These are all examples of Enterprise Taxonomies at Citi.", "A <strong>taxonomy</strong> is a way to name, organize, and structure related terms. It allows for consistent communication, governance, and reporting throughout the enterprise. As a result, it improves the alignment and management of diverse stakeholders. "]}, {"title": "What Are Enterprise Taxonomies?", "text": ["The following Enterprise Taxonomies are managed by Business Architecture:"], "list": ["Process Taxonomy", "Function Taxonomy", "Data Concept Controlled Vocabulary"]}], "callout1": "<strong>Business Architecture</strong> is responsible for the Enterprise Architecture and Process Governance Policy.", "text9": "Let’s explore each of these Enterprise Taxonomies further."}, "section2": {"title1": "Process Taxonomy", "text1": "This taxonomy describes and organizes Processes. It consists of an inventory of Processes that are comprehensive to what we do across the enterprise, from Account Opening to Managing Talent or Managing Risks. ", "text2": "Please note:", "list1": ["A <strong>Process</strong> is a collection of activities or functions that are performed in a specific sequence to achieve a business objective, such as a Service, Product, or benefit to Citi. ", "They receive one or more inputs, which are transformed through the set of activities or functions to produce one or more outputs. These outputs may be delivered to another Process, a client, an employee, and/or a third party. "], "text3": "Citi’s <strong>Process Taxonomy</strong> is defined independently of business lines or products.", "text6": "Each Process has a Process Owner who is a Citi executive accountable for managing a process from end-to-end through simplification and risk reduction.", "text7": "You’ll learn more about Process Owners later in this training.", "title2": "Function Taxonomy", "text8": "This taxonomy includes an inventory of steps, tasks, or “functions” executed within an Application supporting a Process.", "text9": "The <strong>Function Taxonomy</strong> is an inventory of actions that produces a specific result (Function Name). It is important to note that one function can be performed by many Applications and in many Processes. Each application has a defined set of Functions, or Application Functions.", "text10": "For example, the Process for <strong>Payment Processing</strong> consists of multiple steps / functions and applications. The functions in the applications include but are not limited to:", "list3": ["Payment submission capture", "Payment validation", "Compliance screening", "Payment warehousing", "Funds transfer"], "title3": "Data Concept Controlled Vocabulary", "text11": "The <strong>Data Concept Controlled Vocabulary</strong> is a collection of Data Concepts organized into a hierarchical structure. ", "text12": "A Data Concept is a representation of a collection of data elements representing a person, place, or thing that participates in the firm’s business activities. They’re terms that have names and definitions that the business recognizes and understands. Examples of Data Concepts include Customers, Accounts, Trades, etc.", "text13": "It’s important to note that Data Concepts are created or used by Functions. ", "text14": "To learn more about the Process Taxonomy, the Function Taxonomy, and the Data Concept Controlled Vocabulary, review and bookmark the <a href='https://citi.sharepoint.com/sites/Enterprise-Architecture/SitePages/ReferenceMaterials.aspx' target='_blank'>Business Architecture Reference Material</a> page. ", "text15": "Remember, all three of these <strong>Taxonomies work together to provide insight into how Citi’s processes, functions, and data can be consistently leveraged and reused</strong> for similar products and services across the enterprise."}, "footer": {"text1": "Now let’s learn more about Process Owners and their responsibilities."}}, "page4": {"sectionTop": {"title1": "Who Are Process Owners?"}, "section1": {"text1": "Now that we have a defined set of Processes, who provides oversight and uses the data associated with Processes to help identify areas for improvement?", "text2": "This would be Process Owners. A Process Owner is accountable for managing a Process for simplification and risk reduction of their owned Process.", "text3": "Let’s explore this role further by learning more about their responsibilities.", "text4": "Process Owner Responsibilities", "text5": "Process Owners, assigned to each Process in the Process Taxonomy, have a set of responsibilities to govern the Processes that they own. ", "text5AltText": "This is an example of the three levels of the Business Process Taxonomy. Level 1 Process Group is Money Movement. Level 2 Parent Process is Payments. Level 3 Process is Payment Processing. Process Owners are responsible for the third level.", "text7": "Process Owners are responsible for: ", "list1": ["Creating and maintaining CEAM Process Models, which are then used to identify opportunities for Technology Simplification or consolidation of applications with similar functionality, in their Process.", "Creating and maintaining the Global Process MCA Profile (GPMP) for their Process. This includes:", ["Identifying the inherent risks and controls to mitigate the risks of their Processes. This forms the GPMP in the redesigned Citi Manager Control Assessment (MCA). ", "Reviewing and approving requests to add or remove GPMPs from the Global Assessment Units."], "Validating other identified linkages to the Process, for example EUCs, that are among various factors used to measure the health of their Process, or a Process Risk Profile Assessment. "], "text8": "To learn more about Process Ownership, review the <a href='https://citi.sharepoint.com/sites/Enterprise-Architecture/SitePages/Process-Ownership.aspx?csf=1&web=1&e=1oNYIZ' target='_blank'>Business Architecture Process Owner Resource page.</a>"}, "section_poa": {"title": "Process Ownership Assessment (POA) and Process Risk Assessment Score", "paragraphs": ["Process Ownership Assessment (POA) provides Process Owners a comprehensive view of the approved linkages, which includes Risk and Control details as adopted, and will include iCAP Issues, EUCs, and CEAM Process Model data for Tech Simplification and Process Automation. ", "The approved linkages are inputs to a Process Profile Assessment and resulting Process Risk Score. The score will support Process Owners in identifying opportunities to drive Simplification and Risk Reduction. "]}, "footer": {"text1": "Next, let’s get a better understanding of Citi’s business architecture landscape by exploring some of the specific components of CEAM Process Models."}}, "page5": {"sectionTop": {"title1": "Components of <span class='sr-only'> C E A M </span><span aria-hidden='true'>CEAM</span>"}, "section1": {"title1": "Process Models ", "text1": "Process Models are representations of how the process functions today with the associated applications. ", "text2": "We use Process Models to help us understand Processes and their activities for each Process in the taxonomy. ", "text3": "These models visually describe the steps of the Process by using the Function Taxonomy, required to successfully execute/complete a Process and deliver the required outcome. They link the Application Functions and Data Concepts to Processes.", "callout1": "Process Owners are accountable for the accuracy of the Process Models for their Process.", "text7": "Let’s take a closer look at Process Models by exploring the different components of an illustrative sample Process: <strong>Payments Processing</strong>. It’s important to note that the components in this sample portray a simplistic view. ", "cta_tabs": "Select each of the four tabs to learn more about the components required to execute the Process: Payments Processing.", "tabs1": [{"img": "../assets/page5/tab1.svg", "title": "Full Overview", "title_full": "Full Overview (illustrative purposes only)", "text": ["Remember, Process Models embody multiple components needed to execute a Process. "], "alt": "A graphic outlining all of the components of the  Process: Payments Processing. The first row consists of nine Functions, from input to output. These functions include 1. capture payment, 2. compliance screening, 3. payment warehousing, 4. request for quote, 5. credit facility processing, 6. Foreign Exchange deal execution, 7. instruction repair cancellation, 8. payment release, and 9. account and GL posting. These Functions are connected by Data Concepts. Functions one, two, three, and four are connected by the payment Data Concept. Functions four, five, and six are connected by the payment and FX Rate Data Concepts. Functions six, seven, eight, and nine are connected by the payment Data Concept. On top of the Functions, linked to Data Concepts by letters. The second row consists of four Master / Reference Data. These include account, clearing system cutoff, BIC/ABA - currency and country specific, and GL/Nostro account. The final row consists of three levels of automation. These include fully automated, hybrid manual and fully manual."}, {"img": "./assets/page5/tab2.svg", "title": "Application Functions", "title_full": "Application Functions (illustrative purposes only)", "text": ["These consist of nine steps/core activities within the Payments Processing (e.g., capture payment, compliance screening).", "These functions are associated to Applications with Functions, which can be repeated across multiple Processes."], "alt": "In this graphic, only the first row of Application Functions is highlighted. There are nine Application Functions, from input to output, for the Process: Payments Processing. These functions include 1. payment capture, 2. compliance screening, 3. payment warehousing, 4. request for quote, 5. credit facility processing, 6. FX deal execution, 7. instruction repair cancellation, 8. payment release, and 9. account and GL posting."}, {"img": "./assets/page5/tab3.svg", "title": "Data Concepts", "title_full": "Data Concepts (illustrative purposes only)", "text": ["These include data that is consumed or produced in Payments Processing (e.g., payment, FX rate).", "They’re based on the Data Concept Controlled Vocabulary."], "alt": "In this graphic, row two of the Master / Reference Data is highlighted, along with“payment” and “payment FX rate”, which connect each of the nine Functions. These Data Concepts are linked to the A and B controls on Functions one and nine."}, {"img": "./assets/page5/tab4.svg", "title": "Manual Inputs", "title_full": "Manual Inputs (illustrative purposes only)", "text": ["Manual inputs or manual processing steps are identified by the Process Owner for every Function in their Process.", "Where manual inputs are identified, they are further classified to the type of manual input or process step, e.g., necessary due to judgement versus automatable."], "alt": "In this graphic, the last row of Manual Inputs/Level of Automation and the corresponding indicator for automation for each Function is highlighted. Each level of automation is listed and includes fully automated, hybrid manual and fully manual. There are nine levels of automation represented for each Functions, from input to output, for the Process: Payments Processing. "}]}, "section2": {"title1": "Application Functions, Designations & Mapping", "text2": "The resulting Application Functions described in the Function Taxonomy section are mapped into Process Models.", "text5": "What Is an Application Function Designation?", "text6": "An Application Function Designation indicates the strategic intent of an Application Function’s use in the Process.  ", "text7": "The different values that can be assigned to each Application Function are:", "text8": "Strategic", "list2": ["Applications which Citi plans to invest in, and the specific Function is strategic, which indicates that other applications with this functionality can be consolidated to the “Strategic” designated application. "], "text9": "Maintain ", "list3": ["This application sufficiently meets requirements for the Function but is not strategic (e.g., migration from other applications should not be to this application). "], "text10": "Deprecate", "list4": ["This application is in scope for retirement and/or Application Function is not fit for purpose for the given Function and should be consolidated, within a reasonable timeframe, preferably to a Strategic application for the Function. "], "paragraphs_11": ["Application Function Designations are the inputs needed to evaluate our progress in driving application rationalization or reduction, and reuse of an application where possible to achieve Technology Simplification across our Processes. Data collected allows us to monitor our compliance as we execute technology projects.", "This ultimately allows us to: ", ["Discover where we have different applications supporting the same functions. ", "Understand if the appropriate application is being used for a function depending on the process.", "Support application-to-function rationalization and simplification by assigning each application function an “Application Function Designation” value."]], "text12": "Process Owners are responsible for the accuracy and completeness of their Process Models along with the Application Function Designations for their Process."}, "section3": {"title": "Technology Simplification ", "subtitle": "What is Technology Simplification and why is it important?", "paragraphs": ["<strong>Technology Simplification</strong> is a framework to improve Citi’s technology infrastructure through identification of applications performing common functionality for consolidation. It allows us to be more efficient in managing our application inventory with the best-in-class functionality, which ultimately reduces risk and improves our client experience. ", "Application Mapping allows for the identification and measurement of Technology Simplification in Processes through Process Models."]}, "section4": {"paragraphs": ["By reducing Citi’s inherent complexities, through Technology Simplification, the following improvements can be achieved:", ["Speed to market", "Overall agility", "Competitiveness", "Efficiency "], "For example, the functionality of <strong>Payment Validation</strong> has identified 156 applications used at Citi across many different Processes."], "callout": "For Example, the functionality of <strong>Payment Validation</strong> has identified 156 applications used at Citi across many different Processes."}, "footer": {"text1": "Now that we’ve covered Processes, Process Models and Process Owners, let’s jump into a scenario to better understand how Enterprise Architecture helps us simplify our processes."}}, "page6": {"sectionTop": {"title1": "Technology Simplification in Action"}, "section1": {"title1": "Outside of Citi", "text1": "Let’s Meet <PERSON><PERSON>.", "text2": "Greta loves to shop online and relies on the mobile payment application to purchase items. They then pay their funds provider through electronic bill pay from Citi. ", "text3": "<PERSON><PERSON> appreciates the seamlessness of one-click payment check-out.", "text4": "<strong>Let’s learn more about <PERSON><PERSON>’s experience. </strong>", "text6": "In addition to shopping online, Greta periodically invests in the market. When they place orders, they are using a Transacting Process which relies on Payment Processing to pay for the stocks in which they chose to invest. Their experience using these various Processes at Citi should all be seamless, accurate and secure.", "text7": "Providing these products and services is managed through a series of Processes that ensures a consistent end-to-end experience and leaves customers feeling satisfied.", "text8": "This is achieved by: ", "list1": ["Breaking down these Processes to examine their underlying activities and supporting Application Functions in the process. ", "Identifying what applications are needed to support each function in the process. "], "text9": "For example, if Greta places an order for groceries for the week or decides to renew their online streaming account, they expect the same level of security in the execution of the payment regardless of platform. ", "text10": "Regardless of what Greta is ordering—whether it’s groceries or an online streaming account renewal—the site will take Greta through the same checkout steps, process their payment, detect any signs of fraud, and manage any taxes owed. ", "text12": "When thinking about this from an enterprise architecture lens, it becomes clear that certain functions are reused across different platforms and need to be standardized across the site's different services. ", "text13": "This benefits both Greta and the site (enterprise) as it focuses on streamlining the shopping experience and creating efficiencies while reducing risk.", "title2": "How Does This Apply to Citi? ", "text14": "Being able to identify applications with duplicative functions will not only help improve the efficiency of how we deliver our products and services, but also allows us to reuse the same applications across multiple Processes as applicable.", "text15": "Thinking of <PERSON><PERSON>’s experience, let’s revisit our client, <PERSON>.", "text17": "<strong><PERSON>’s Journey</strong>", "text18": "In our first encounter with <PERSON>, remember that they didn’t have a very positive experience obtaining a Citi product. <PERSON> grew frustrated by the number of times they were contacted for the same information and voiced their dissatisfaction to the team. ", "text19": "Thankfully, since <PERSON> is pleased with the products beyond the initial client service experience, they’re looking into purchasing more products at the bank.", "text20": "Now, <PERSON> is also looking to secure a new credit card and obtain a home loan. As <PERSON> proceeds with the applications for these products, let’s learn more about how the Process Owner’s team used <span class='sr-only'> C E A M </span><span aria-hidden='true'>CEAM</span> artifacts and Process Intelligence to provide a more streamlined approach, integrate application infrastructure, align data across teams, and ultimately improve the client experience.", "lorenzos_journey_paragraphs": ["In our first encounter with Lorenzo, they used Citi to pay their suppliers, but they had to use multiple payment applications to process payments to their suppliers. ", "Technology Simplification drives the reduction of complexity in the application infrastructure through identification and consolidation of applications performing similar functions. ", "Citi’s commitment to deliver Technology Simplification for the payments space resulted in the launch of ‘Citi Payments Express’, a strategic application to support instant payment flows. ", "Remember how Lorenzo navigated three different payment platforms to pay their suppliers. Citi successfully simplified the client’s instant payment complexities and experience. ‘Citi Payments Express’ became the solution by integrating local payment features in the US and UK into one global instant payment application.", "For Lorenzo, the launch of ‘Citi Payments Express’ made their life easier since they can now use one single streamlined platform to pay their suppliers. Their digital payment needs are satisfied through a simplified experience. ", "For Citi, retiring excessive applications with a strategic application that can perform the same function, enables our progress towards a more efficient and less complex operating environment with reduced risk and lower operational cost given we have fewer applications to support across the organization.", "Let’s learn more about how the Process Owner team used CEAM artifacts and Process Intelligence to create an integrated application infrastructure, alignment in data across teams, and ultimately improved the client experience. "], "text21": "Technology Simplification Applied", "text22": "Using Enterprise Taxonomies, the team identified a distinct set of payment related functions and the supporting applications used across the Processes through CEAM. ", "text23": "Process Owners using payment functionality of applications designated as “Deprecate” undertook efforts to enhance their Processes to eliminate the deprecate functions in favor of ‘Citi Payments Express’. This resulted in fewer applications used for payment functionality, delivery of a better client experience and demonstrates Citi’s commitment to bring positive changes.", "text27": "<PERSON> no longer needs to use multiple payment applications to pay their US and UK suppliers. They can now use one platform to process and monitor their digital payments. With less complexity, <PERSON> can start exploring opportunities to save on cost by seeking suppliers in other parts of the world! ", "text28": "Through Technology Simplification, the bank has provided a more cohesive and efficient process for the internal team and a much smoother client experience for Lorenzo."}, "footer": {"text1": "Let’s now explore who uses Enterprise Architecture and Process Governance here at Citi. "}}, "page7": {"sectionTop": {"title1": "Who Is Responsible for Applying Enterprise Architecture and Process Governance?"}, "section1": {"text1": "Remember, the purpose of Enterprise Architecture is to align Citi’s business goals with our business applications strategy. It helps identify opportunities within our Processes to be more effective and efficient, benefitting our employees, our clients, and Citi as a whole. ", "text2": "Simplifying and managing our Processes won’t be achieved through siloed projects, but rather by each of us by: ", "list1": ["Changing how we perform our day-to-day responsibilities. ", "Using consistent language. ", "Linking information in our day-to-day to Processes to help form Process Intelligence. "], "text3": "Enterprise Architecture and Process Governance applies to all Citi staff broadly, when requested to associate artifacts to Processes in any Systems of Records. Based on their knowledge, they’re responsible for the accuracy of the linkage when selecting a Process for association.", "text3b": "For example, this may include associating Processes to End User Computing (EUCs) or Investment Requests.", "text4": "Let’s explore some of the ways we use Enterprise Architecture in various roles at Citi. ", "title1": "How Do Different Roles Use Enterprise Architecture & Process Governance? ", "text5": "<strong>Select each role to discover their responsibilities for applying Enterprise Architecture.</strong>", "cards1": [{"id": "card1", "title": "End User Computing <span class='sr-only'> (E U C) </span><span aria-hidden='true'>(EUC)</span> Owner", "text": ["When logging new EUCs, EUC Owners will be asked to select which Process(es) their EUC supports. <br><br>Process Owners are required to review and approve the linkage as an input to the risk score of their Process. Approved EUCs will be incorporated into the Process Ownership Assessment Platform (POA)."]}, {"id": "card2", "title": "Investment Sponsors", "text": ["Investment Sponsors creating an Investment Request will be asked to select which Process(es) their Investment Request impacts. <br><br>As part of the investment creation and approval journey, Investment Sponsors may need to provide Process Model inputs used to drive technology changes and support identification of Technology Simplification opportunities."]}, {"id": "card3", "title": "Manager Control Assessment (MCA) Users", "text": ["Process Owners create and maintain Global Process MCA Profiles (GPMPs), which link Processes with Risks and Reference Controls. <br><br>The Risk Owners in MCA identify and adopt the Process(es) and respective GPMPs to manage their risk of their Assessment Unit’s management objectives. <br><br>The resulting controls as adopted will be visible to the Process Owner through Process Ownership Assessment (POA)."]}], "text6": "Even though your role may not use Enterprise Architecture in everyday practices, you’re likely to engage with it at various points as the Process Taxonomy is used in core programs such as MCA and EUC. <br><br><strong>Therefore, you’re responsible for the accurate application of Enterprise Architecture as we simplify Citi together.</strong>", "text7": "To learn more about how different areas at Citi leverage Enterprise Architecture, you can review the <a href='https://citi.sharepoint.com/sites/Enterprise-Architecture/SitePages/ReferenceMaterials.aspx' target='_blank'>Business Architecture Reference Material</a> page."}, "footer": {"text1": "Let’s do a quick review of what we’ve covered in this course before the final assessment."}}, "page8": {"sectionTop": {"title1": "Course Summary"}, "section1": {"text1": "We’ve covered the following in this training:", "cards1": [{"text": "Citi implemented Enterprise Architecture & Process Governance to provide a process-centric approach for simplification and risk reduction of Processes, ultimately resulting in a better client experience."}, {"text": "Business Architecture governs three Taxonomies: Process Taxonomy, Function Taxonomy, and Data Concept Controlled Vocabulary. "}, {"paragraphs": ["Business Architecture and Process Governance supports us in better understanding where we are today and how we can get to where we want to be. ", "It does this by identifying both gaps and potential opportunities to help Citi’s business applications align with business goals."]}, {"text": "Technology Simplification drives the consolidation of applications with duplicative functions. Process Governance drives the overall improvement of Processes."}, {"paragraphs": ["There are specific roles that will apply Enterprise Architecture as part of their responsibilities. ", "However, the success of Enterprise Architecture in simplifying Citi Processes relies on each Citi employee taking accountability to properly apply Processes and adhere to Process Governance."]}], "text2": "Review the <a href='https://citi.hostedconnectedrisk.com/CitiCPD/react?page=O&CID=sid552268563&DeletedOnly=0&HTMFile=instancePolicyUser_View.htm&InstanceNo=2001487&NodeNo=182&inlineEdit=0&originalRequestNodeNo=182&pageType=42&resetTreeView=0&rightClickContextMenu=0&runOnce=1&scrollableViewer=1&treeViewNode0=253&treeViewNode0OverviewPage=instancePolicyVersionAuthor_Title.htm&treeViewNode1=281&treeViewNode10=371&treeViewNode10OverviewPage=instancePolicySectionLevel10VersionAuthorView.htm&treeViewNode1OverviewPage=instancePolicySectionLevel1VersionAuthor_View.htm&treeViewNode2=282&treeViewNode2OverviewPage=instancePolicySectionLevel2VersionAuthor_View.htm&treeViewNode3=283&treeViewNode3OverviewPage=instancePolicySectionLevel3VersionAuthor_View.htm&treeViewNode4=285&treeViewNode4OverviewPage=instancePolicySectionLevel4VersionAuthor_View.htm&treeViewNode5=284&treeViewNode5OverviewPage=instancePolicySectionLevel5VersionAuthor_View.htm&treeViewNode6=372&treeViewNode6OverviewPage=instancePolicySectionLevel6VersionAuthorView.htm&treeViewNode7=373&treeViewNode7OverviewPage=instancePolicySectionLevel7VersionAuthorView.htm&treeViewNode8=374&treeViewNode8OverviewPage=instancePolicySectionLevel8VersionAuthorView.htm&treeViewNode9=375&treeViewNode9OverviewPage=instancePolicySectionLevel9VersionAuthorView.htm&useCustomFieldForAddChild=0&useOriginalRequestNodeAsCurrent=1&usingpost=1' target='_blank'>Enterprise Architecture and Process Governance Policy (EAPGP)</a> to learn more about Enterprise Architecture and Process Governance that all Citi employees, contractors, consultants, and parties obligated to follow Citi policies must adhere to.", "text3": "Remember, applying Enterprise Architecture and Process Governance results in simplification and efficiencies, benefiting everyone at Citi and our clients."}, "footer": {"text1": "It’s now time to assess your knowledge."}}, "page11": {"sectionTop": {"title1": "Final Assessment"}, "section1": {"text1": "I acknowledge that I have completed the 2024 Code of Conduct training course and understand that I am obligated to read the Code and to comply with the principles, policies and laws outlined in the Code, including any amendments made by Citi.", "text2": "I understand that a current copy of the Code of Conduct is posted on Citi’s website at <a class='text-link' href='http://www.citigroup.com/citi/investor/corporate_governance.html' target='_blank'>http://www.citigroup.com/citi/<wbr>investor/corporate_governance.html</a>.", "text3": "Select the Confirm button below to receive course credit.", "text4": "Confirm"}}}